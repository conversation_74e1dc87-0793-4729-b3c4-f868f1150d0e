<template>
    <div class="selectRender">
        <el-select :multiple="true" ref="elSelectRef" @change="changeEvent" v-bind="$attrs" v-model="currentItem" v-on="$listeners"
            :loading="loading" placeholder="请选择">
            <el-option v-for="item in selectDataList" :key="item.value" :label="item.label" :value="item.value + ''">
            </el-option>
        </el-select>
    </div>
</template>
<script>
export default {
    name: 'selectRender',
    components: {},
    props: {

        //   // 表格信息>>dataFrom=talbe 才有效
        //   tableInfo:{
        //       type:Object,
        //       default(){
        //           return {}
        //       } 
        //   },
         // 是否多选
        multiple: {
            type: Boolean,
            default: false,
        },
        // 数据来信:表单form,表格talbe
        dataFrom: {
            type: String,
            default: "form"
        },
        // 当前字段:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
        // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
        dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
        // 当前字段:value
        currentValue: {
            type: [String, Number],
            default: ""
        },
        // 是否可用
        disabled: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
           // isMultiple: false,// 是否多选
            loading: false,
            currentItem:[],// 当前选择项
            selectDataList: [],// 当前列表数据
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.loadData()
           // debugger
            if(!!this.currentValue){
               this.currentItem =this.currentValue.split(',')
            }else{
                this.currentItem = []
            }
          
        })
    },
    methods: {
        // 格式化数据成下拉框所需的
        formatData(dataList = []) {
            let newData = []
            return newData
        },

        // 加载数据 
        // matchField: "field:描述#1:1#2:2#g:3"
        // searchParams: ""
        // sourceKey: ""
        loadData() {
            // debugger
            let matchField = this.paramsItem.matchField
            let _selectList = []
            if (!!matchField) {
                if (matchField.includes("#")) {
                    let keyValueArray = matchField.split("#")
                    if (keyValueArray && keyValueArray.length > 0) {
                        keyValueArray.forEach(item => {
                            //let tt =field:描述
                            let oldItem = item.split(':')
                            let newItem = {
                                value: oldItem[0],
                                label: oldItem[1]
                            }
                            _selectList.push(newItem)

                        })
                    }

                } else {
                    let oldItem = matchField.split(':')
                    let newItem = {
                        value: oldItem[0],
                        label: oldItem[1]
                    }
                    _selectList.push(newItem)
                }
            }
            this.selectDataList = _selectList
        },
        // 选择改变回调事件
        changeEvent(val) {
            //debugger
            let _self = this
            this.$nextTick(() => {
                let params = {
                    value: val.toString(),
                    text: _self.$refs["elSelectRef"].selectedLabel
                }
                // if(!!_self.currentValue){
                //       _self.currentItem =_self.currentValue
                //  }
                _self.$emit("changeEvent", params)
            })
        }
    }
}
</script>


<style lang="scss"></style>