<template>
    <div>
      <!-- <el-button @click="popupProSettingFn()" type="text">
        <span :style="[{ color: (!!optionModel.searchProSettingID) ? `green` : `` }]">{{ (!!optionModel.searchProSettingID)
          ?
          `编辑高级搜索` : `添加高级搜索` }}</span>
      </el-button> -->
      <div >
        <el-button @click="popupProSettingFn()" type="text">
        选择高级搜索
      </el-button>
      <el-button v-show="!!optionModel.searchProSettingID" @click="removeProSettingFn()" type="text">
        <span style="color:red;">移除高级搜索</span>
      </el-button>
      </div>
      
      <vxe-modal title="高级搜索列表" width="60%" :position="{ top: 50 }" 
        v-model="showEditDialogFlag" show-footer>
        <div class="flex justify-end">
            <div style="margin-bottom: 10px;" class="flex justify-center items-center">
                <span>方案名称 </span>
                <el-input  :clearable="true"  v-model="searchProName" placeholder="请输入方案名称" style="width: 200px;"></el-input>
                <span>描述 </span>
                <el-input  :clearable="true"  v-model="searchProRemark" placeholder="请输入描述" style="width: 200px;"></el-input>
                <el-button type="primary" @click="searchProSettingFn()">搜索</el-button>
            </div>

      </div>
        <vxe-table
            @cell-dblclick="cellDblclick"
            ref="xTable"
            maxHeight="400"
            border
            size="mini"
            stripe
            :row-config="{isCurrent: true, isHover: true}"
            :loading="tableDataLoading"
            :data="tableData">
            <vxe-column type="seq" width="60" align="center" header-align="center"></vxe-column>
            <vxe-column field="CSCHEME_NAME" header-align="center" title="方案名称"></vxe-column>
            <vxe-column field="CREMARK" header-align="center" title="描述"></vxe-column>
            </vxe-table>
            <vxe-pager
            :loading="tableDataLoading"
            :current-page="tablePage1.currentPage"
            :page-size="tablePage1.pageSize"
            :total="tablePage1.totalResult"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            @page-change="handlePageChange1">
            </vxe-pager>
        <template v-slot:footer>
          <el-button size="medium" @click="submitEditDialog()" type="primary">确定</el-button>
          <el-button size="medium" @click="showEditDialogFlag = false">取消</el-button>
        </template>
      </vxe-modal>
    
    </div>
  </template>
  <script>
//  选择高级搜索列表
import request from '@/libs/request'
  export default {
    name: "defaultmenubutton-searchProList",
    components: {  },

    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        selectRow:null,
        showEditDialogFlag:false,
        tableDataLoading:false,
        searchProName:"",
        searchProRemark:"",
        tableData: [],
        tablePage1: {
                currentPage: 1,
                pageSize: 10,
                totalResult: 0
              }
      }
    },
    watch:{
        showEditDialogFlag(n,o){
            if(n){
                this.loadData()
            }else{
                this.showEditDialogFlag = false
            }
        }
    },
    methods: {
        removeProSettingFn(){
            this.$confirm("确定删除高级搜索", '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
                 this.$set(this.optionModel, "searchProSettingID", "")
                 this.$set(this.optionModel, "searchProSettingCPARENT", "")
      }).catch(error => {
        //this.$message.error(error)
      })
               
        },
        cellDblclick(tableInfo){

           this.selectRow = tableInfo.row
        //    this.optionModel["searchProSettingID"] = this.selectRow.CID
        //    this.optionModel["searchProSettingCPARENT"] = this.selectRow.CPARENT
        this.$set(this.optionModel, "searchProSettingID", this.selectRow.CID)
                this.$set(this.optionModel, "searchProSettingCPARENT", this.selectRow.CPARENT)
           this.showEditDialogFlag = false
        },
        handlePageChange1 ({ currentPage, pageSize }) {
              this.tablePage1.currentPage = currentPage
              this.tablePage1.pageSize = pageSize
              this.loadData()
            },
        // 弹框 打开 搜索列表
        popupProSettingFn(){
            this.showEditDialogFlag = true
        },
        // 加载高级搜索列表
        // {
        //     "Table":"TBL_SYS_ADVANCED_SEARCH", // 表名 必填
        //     "ConnId":"", // 数据库连接id 如果表不是在maindb 则需要传入连接id
        //     "Select":"CID, CPARENT", // 查询字段 如：CID 、 CID,CPARENT
        //     "OrderBy": "",// 排序字段  如：CID、CID,CPARENT
        //     "PageSize": 10,// 默认10
        //     "PageNumber": 1,// 默认1
        //     "Where":"CPARENT like'54e60f72-607a-5416-ab01-8f0b776f6bc0'"// 查询条件
        // }
         async loadData(){
            this.tableDataLoading = true
            let _url = `/api/SYSTEM/Common/PageQuery`
            let params = {
                "table": "TBL_SYS_ADVANCED_SEARCH", // 固定参数
                //"where": `CPID is null`,
                PageNumber:this.tablePage1.currentPage,
                PageSize:this.tablePage1.pageSize
            }
            // params.where =`CPID is null`
            //  and CPID is null 说明 时只有系统添加 才查询，客户添加的不查询 and CPID is null
            if(!!this.searchProName){
                params.where =`CSCHEME_NAME like '%${this.searchProName}%'`
            }
            if(!!this.searchProRemark){
                params.where =`CREMARK like '%${this.searchProRemark}%'`
            }
            if(!!this.searchProName && !!this.searchProRemark){
                params.where =`CREMARK like '%${this.searchProRemark}%' and CSCHEME_NAME like '%${this.searchProName}%' `
            }
            this.tableData =[]
            this.tablePage1.totalResult=0
          
            await request['post'](_url, params).then(res=>{
                this.tableDataLoading = false
                if (!!res.Success && res.Datas && res.Datas.length > 0) {
                    this.tableData = res.Datas
                    this.tablePage1.totalResult = res.TotalRows
                }
            })
        },
        // 搜索高级搜索列表数据
        searchProSettingFn(){
            this.loadData()
        },
        // 确认选择的高级搜索
        submitEditDialog(){
            let $table = this.$refs["xTable"]
            if($table){
                this.selectRow = $table.getCurrentRecord()
                // this.optionModel["searchProSettingID"] = this.selectRow.CID
                // this.optionModel["searchProSettingCPARENT"] = this.selectRow.CPARENT
                this.$set(this.optionModel, "searchProSettingID", this.selectRow.CID)
                this.$set(this.optionModel, "searchProSettingCPARENT", this.selectRow.CPARENT)
                this.showEditDialogFlag = false
            }
           
        }
        
    }
  }
  </script>