<template>
  <div>


    <el-form-item label="">
      <span slot="label">关闭左偏移
        <el-tooltip effect="light" content="默认 样式>>左偏移padding-left:10px ">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.offPaddingLeft"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">关闭右偏移
        <el-tooltip effect="light" content="默认 样式>>右偏移padding-right:10px ">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.offPaddingRight"></el-switch>
    </el-form-item>
    <el-form-item label-width="0">
      <commonParamsOnChange contrlType="defaultmenubutton" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
      <selectDataSourceApi contrlType="vxetable" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></selectDataSourceApi>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider"></el-divider>
    </el-form-item>
    <el-form-item label="主表">
      <span slot="label">设为主表
        <el-tooltip effect="light" content="默认 设为主表，会存储当前行切换行的数据，一个页面只能有一个主表">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
         <el-switch  :active-value="1" :inactive-value="2" v-model="optionModel.layoutLevel"></el-switch>
      <!-- <el-radio-group >
        <el-radio-button @click.native="layoutLevelChange(1)" label="API">主表</el-radio-button>
        <el-radio-button @click.native="layoutLevelChange(2)" label="Detail">从表</el-radio-button>
      </el-radio-group> -->
    </el-form-item>
    <!-- <el-form-item :label="i18nt('designer.setting.tableWidth')">
			<el-input v-model="optionModel.tableWidth"></el-input>
		</el-form-item> -->

    <el-form-item label="">
      <span slot="label">设为被动加载
        <el-tooltip effect="light" content="默认 立即加载数据，设为被动加载后，数据需要通过其它方式触发加载，如 查询，点击等">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.isPassiveDataLoad"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">首次触发控件列表
        <el-tooltip effect="light" content="一般只有点击行，才会触发控件列表，但有时需要加载完成后第一行需要触发控件列表功能，需要勾选此项！">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.triggerFirstRowCtrlList"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">操作按钮触发
        <el-tooltip effect="light" content="一般只有点击行，才会触发控件列表，但有时需要点击操作按钮也需要触发控件列表功能，需要勾选此项！">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.operationBtnTriggerCtrlList"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">空数据触发控件
        <el-tooltip effect="light" content="如果查询参数返回数据为空，一般不会再次触发控件列表，但有时需要即使为空也要触发控件列表功能，需要勾选此项！">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.triggerWhenNoDataCtrlList"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">绑定控件
        <el-tooltip effect="light" content="目前支持TAB,绑定控件，暂时手动添加绑定控件。主要用途为绑定控件对外开放的selectedIndex 值和绑定值相同时，才触发加载数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input v-model.trim="optionModel.bandingCtrlName"></el-input>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">绑定值
        <el-tooltip effect="light" content="目前支持TAB,绑定值。主要用途为绑定控件对外开放的selectedIndex 值和绑定值相同时，才触发加载数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input v-model.trim="optionModel.bandingCtrlValue"></el-input>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">共享数据源控件
        <el-tooltip effect="light" content="设为TRUE 后，可以在图表 选择此表格控件作为共享数据源控件，使用表格数据作为数据源">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.isShareDataTableCtrl"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">固定高度
        <el-tooltip effect="light" content="默认为0,auto自动适应高度，小于1为占用百分比，大于1时为固定高度">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input-number v-model="optionModel.tableHeight" controls-position="right" :min="0"></el-input-number>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">高度偏移量
        <el-tooltip effect="light" content="当分栏作为容器时，需要调整高度偏移量，可正，可负（参考:-52）">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input-number v-model="optionModel.OffsetHeight" controls-position="right"></el-input-number>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">是否显示序号
        <el-tooltip effect="light" content="默认显示表格序号功能，当个别页面不想显示时，可以选择关闭">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.showSeqColumn"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">是否右键导出
        <el-tooltip effect="light" content="默认不显示自带的右键导出,勾选开启自带的带出功能">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.showContextMenu_Export"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.showContextMenu_Export" label="">
      <span slot="label">关联父控件
        <el-tooltip effect="light" content="主要是为了获取关联父的标签名称，方便导出时，导出的文件名称">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input v-model.trim="optionModel.showContextMenu_Export_fileNameCtrl"></el-input>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">是否显示勾选框
        <el-tooltip effect="light" content="默认不显示表格勾选框功能，当个别页面想显示时，可以选择开启">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.showCheckBoxColumn"></el-switch>
    </el-form-item>
    <!-- <el-form-item label="启用表格标题">
      <el-switch v-model="optionModel.showTopTitle"></el-switch>
    
    </el-form-item>
    <el-form-item v-show="!!optionModel.showTopTitle" label="表格标题">
      <el-input
          placeholder="请输入标题"
          v-model="optionModel.tableTopTitle"
         >
        </el-input>
    </el-form-item> -->
    <el-form-item label="">
      <span slot="label">开启编辑功能
        <el-tooltip effect="light" content="默认是查询列表，单元格是无法编辑的；当开启后，做对应的设置，即可实现单元格编辑功能">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.activeEditFn"></el-switch>
    </el-form-item>

    <el-form-item label="">
      <span slot="label">表格列编辑
        <el-tooltip effect="light" content="加载表格列头信息和列头字段配置信息等">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-button type="primary" plain round @click="openSetting">{{ i18nt('designer.setting.editAction') }}</el-button>
    </el-form-item>

    <el-form-item label-width="0">
      <el-divider class="custom-divider">分页配置
        <el-tooltip effect="light" content="配置表格分页信息，如：是否分页，每页条数等">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
  
   
    <el-form-item :label="i18nt('designer.setting.showPagination')">
      <el-switch v-model="optionModel.showPagination"></el-switch>
    </el-form-item>
  
    <el-form-item label-width="50px" v-show="optionModel.showPagination" label="页/条">
      <el-radio-group v-model="optionModel.tabelConfigs.footerConfig.pageConfig.pageSize">
        <el-radio-button :label="10">10条</el-radio-button>
        <el-radio-button :label="20">20条</el-radio-button>
        <el-radio-button :label="50">50条</el-radio-button>
        <el-radio-button :label="100">100条</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-show="optionModel.showPagination" label="分页自定义布局">
         <el-tooltip effect="light" content="默认（为空）全部属性，自定义分页布局，自由组合，属性按点击顺序排列，如：是否显示总条数，是否显示每页条数等">
          <i class="el-icon-info"></i></el-tooltip>
        
    </el-form-item>
    
      <vxe-checkbox-group v-show="optionModel.showPagination" class="flex " v-model="optionModel.paginationLayout">
           <div class="flex flex-wrap">
              <vxe-checkbox  style="width: 120px;margin-left:10px" :key="pIndex" v-for="(pageItem,pIndex) in pageOptionsList"  :label="pageItem.key" :content="pageItem.value"></vxe-checkbox>
           </div>  
        </vxe-checkbox-group>
      <el-form-item  v-show="optionModel.showPagination" label="">
        <span slot="label">页码按钮数量
          <el-tooltip effect="light" content="默认7，页码按钮数量">
           <i class="el-icon-info"></i></el-tooltip>
      </span>
            <el-input-number v-model="optionModel.pagerCount"></el-input-number>
      </el-form-item>
    <!-- <el-form-item label-width="0">
        <el-divider class="custom-divider"></el-divider>
      </el-form-item> -->
    <el-form-item label-width="0">
      <el-divider class="custom-divider">操作列配置
        <el-tooltip effect="light" content="配置表格操作列信息；如：是否显示，和对应的操作功能按钮">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.showButtonsColumn')">
      <el-switch v-model="optionModel.showOperationColumn" @change="handleShowOperationColumnChange"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn" prop="buttonsColumnTitle"
      :label="i18nt('designer.setting.buttonsColumnTitle')">
      <el-input v-model="optionModel.operationColumn[0].title"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn"
      label="luckySheet导出">
      <el-switch v-model="optionModel.LuckSheet_Export"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn && optionModel.LuckSheet_Export"
      label="luckySheet导出标题">
      <el-input placeholder="默认导出模板" v-model="optionModel.LuckSheet_ExportTitle"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn && optionModel.LuckSheet_Export"
      label="luckySheet导出类型">
      <el-select v-model="optionModel.LuckSheet_ExportType">
        <el-option value="Excel">Excel</el-option>
        <el-option value="PDF">PDF</el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn && optionModel.LuckSheet_Export"
      label="模板编码">
      <el-input placeholder="模板编码" v-model="optionModel.LuckSheet_templateNo"></el-input>
    </el-form-item>
    <el-form-item label="模板参数API">
            <el-select  v-model.trim="optionModel.LuckSheet_templateNo_Params" placeholder="请选择">
                <el-option value="">请选择</el-option>
                <el-option :key="queryIndex + queryItem.value"
                  v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                  :value="queryItem.value"></el-option>
             </el-select>
     </el-form-item>
    <!-- <el-form-item label="luckSheet绑定列" v-show="optionModel.showOperationColumn && optionModel.LuckSheet_Export">
        <span slot="label">luckySheet编码列
          <el-tooltip effect="light" content="选择绑定编码列，用于luckySheet导出参数">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-select  v-model="optionModel.LuckSheet_templateNo" placeholder="请选择luckySheet绑定列">
          <el-option label="请选择" value=""></el-option>
          <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in tableColumnsList"
            :label="fieldItem.title" :value="fieldItem.field"></el-option>
        </el-select>
      </el-form-item> -->
    <el-form-item v-show="optionModel.showOperationColumn" prop="buttonsColumnWidth"
      :label="i18nt('designer.setting.buttonsColumnWidth')">
      <span slot="label">{{ i18nt('designer.setting.buttonsColumnWidth') }}
        <el-tooltip effect="light" content="根据操作列的功能按钮来确定是否需要调整宽度大小">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input-number v-model="optionModel.operationColumn[0].width"></el-input-number>
    </el-form-item>
    <el-form-item v-show="optionModel.showOperationColumn" prop="buttonsColumnFixed"
      :label="i18nt('designer.setting.fixedColumn')">
      <span slot="label">{{ i18nt('designer.setting.fixedColumn') }}
        <el-tooltip effect="light" content="固定列，一般在字段过多，出现滚动条时候，是否跟随滚动条移动；注意：如出现需要拖拽时，不要设置为固定">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select v-model="optionModel.operationColumn[0].fixed">
        <el-option :value="null">false</el-option>
        <el-option value="left">left</el-option>
        <el-option value="right">right</el-option>
      </el-select>
    </el-form-item>
    <div v-show="optionModel.showOperationColumn">
      <span>操作列项</span>
      <el-tooltip effect="light" content="配置表格操作列功能按钮，勾选需要显示的按钮后，做相应的配置；右边编辑按钮为执行API前的拦截动作或逻辑修改；具体的可以 点击查看 自定义 代码案例">
        <i class="el-icon-info"></i></el-tooltip>
    </div>

    <el-form-item v-show="optionModel.showOperationColumn" label-width="0">
      <!-- 注意：引用其它控件 -->
      <opertationClonms :designer="designer" :selected-widget="selectedWidget"></opertationClonms>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">主从汇总配置
        <el-tooltip effect="light" content="用于主从表时设置">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <singleParamsOnChange :tableColumnsList="tableColumnsList" :optionModel="optionModel" :designer="designer"
      :selected-widget="selectedWidget"></singleParamsOnChange>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">动态列头配置
        <el-tooltip effect="light" content="数据源API接口，返回数据必须KEY,VALUE 字段的JSON 数据列表，类型下拉列表">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item label="启用动态列头">
      <el-switch v-model="optionModel.activeDynamicColumn"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.activeDynamicColumn" label="">
      <span slot="label">选择动态列头API
        <el-tooltip effect="light" content="数据源API接口，返回数据必须KEY,VALUE 字段的JSON 数据列表，类型下拉列表">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select v-model="optionModel.dynamicColumnApi" placeholder="请选择">
        <el-option value="">请选择</el-option>
        <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
          :label="queryItem.label" :value="queryItem.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="!!optionModel.activeDynamicColumn" label="">
      <span slot="label">是否可编辑动态列
        <el-tooltip effect="light" content="当前开启编辑后，一次性设置额外的动态列头字段信息，是否可以编辑">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.canEditDynamicColumn"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.activeDynamicColumn" label="">
      <span slot="label">是否汇总动态列
        <el-tooltip effect="light" content="是否将所有动态额外的列头字段值进行汇总后到指定的字段，前提是所有字段值是数字类型，可以累加的">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.needSumDynamicColumn"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.activeDynamicColumn && !!optionModel.needSumDynamicColumn" label="">
      <span slot="label">行汇总字段
        <el-tooltip effect="light" content="开启‘是否汇总动态列’后，指定最后累加值的字段">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select @change="change_rowSumDynamicColumnFieldEvent" v-model="optionModel.rowSumDynamicColumnField"
        placeholder="请选择">
        <el-option label="请选择" value=""></el-option>
        <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in tableColumnsList"
          :label="fieldItem.title" :value="fieldItem.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">底部按钮配置
        <el-tooltip effect="light" content="开启显示底部按钮后，可以配置一些功能按钮,一般用于主从表时，设置一些操作按钮，操作从表数据">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">显示底部操作按钮
        <el-tooltip effect="light" content="开启显示底部按钮">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.showFooterButtons" @change="handleShowFooterButtonsChange"></el-switch>
    </el-form-item>
    <div v-show="optionModel.showFooterButtons">
      <span>按钮列项</span>
      <el-tooltip effect="light" content="勾选后，即可显示在表格底部的操作按钮，也可以添加自定义按钮功能">
        <i class="el-icon-info"></i></el-tooltip>
    </div>
    <el-form-item v-show="optionModel.showFooterButtons" label-width="0">
      <!-- 注意：引用其它控件 -->
      <footerButtons :designer="designer" :selected-widget="selectedWidget"></footerButtons>
    </el-form-item>

    <el-form-item label-width="0">
      <el-divider class="custom-divider">配置树表格
        <el-tooltip effect="light" content="当返回数据具备树结构特点时，且为扁平结构数据，可以开启使用">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <div v-if="optionModel.treeConfig">
      <el-form-item label="启用表格树">
        <el-switch v-model="optionModel.treeConfig.transform"></el-switch>
      </el-form-item>
      <el-form-item v-show="optionModel.treeConfig.transform" label="">
        <span slot="label">默认展开
          <el-tooltip effect="light" content="默认树结构数据，不展开，开启后，默认加载数据后全部展开">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-switch v-model="optionModel.treeConfig.expandAll"></el-switch>
      </el-form-item>
      <el-form-item v-show="optionModel.treeConfig.transform" label="">
        <span slot="label">子ID
          <el-tooltip effect="light" content="获取数据模型 预加载后，此处就可以选择需要的 树结构的子ID,一般为CID">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <!-- <el-input v-model="optionModel.treeConfig.rowField"></el-input> -->
        <el-select v-model="optionModel.treeConfig.rowField">
          <el-option label="选择" value=""></el-option>
          <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
            :value="item.field"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="optionModel.treeConfig.transform" label="父ID">
        <!-- <el-input v-model="optionModel.treeConfig.parentField"></el-input> -->
        <span slot="label">父ID
          <el-tooltip effect="light" content="获取数据模型 预加载后，此处就可以选择需要的 树结构的父ID">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-select v-model="optionModel.treeConfig.parentField">
          <el-option label="选择" value=""></el-option>
          <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
            :value="item.field"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="optionModel.treeConfig.transform" label="">
        <span slot="label">树节点设置
          <el-tooltip effect="light" content="在表格列编辑设置好后，可以选择，意为在那个字段显示那个三角形 展开、收缩的按钮">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-select @change="change_columnsTreeNodeEvent" v-model="optionModel.columnsTreeNode" placeholder="请选择树节点">
          <el-option label="请选择" value=""></el-option>
          <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in tableColumnsList"
            :label="fieldItem.title" :value="fieldItem.field"></el-option>
        </el-select>
      </el-form-item>
    </div>
    <el-form-item label-width="0">
      <commonSettingEventDialog contrlType="vxetableEventList" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></commonSettingEventDialog>
    </el-form-item>
    <vxe-modal width="90%" title="表格列编辑" destroy-on-close :position="{ top: 20 }" v-model="dialogVisible"
      :show-footer="true">
      <div class="flex ">
        <div class="flex justify-start items-center ml-5">
          <div>【切换】选择加载列头API</div>
          <div class="ml-2">
            <el-select @change="loadColsData()" style="width:168px" v-model="actionName" placeholder="请选择查询">
              <el-option label="请选择" value=""></el-option>
              <el-option :key="queryIndex + queryItem.value"
                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                :value="queryItem.value"></el-option>
            </el-select>
          </div>
        </div>

       
        <div class="flex justify-start items-center ml-5">
          <el-button @click="removeAllCols()" type="danger">移除所有</el-button>
          <el-button :loading ="loadingCheck" @click="CheckTableColumns()" type="success">字段同步检查</el-button>
        </div>
        <div class="flex justify-center items-center" style="font-size: 14px; margin-left: 10px;">
          <span>同步检查结果颜色标识：</span> <span style="color:#90EE90;">1:新增</span><span
            style="color:#FFB6C1;margin:0 5px;">2:删除</span>
          <span style="color:#ADD8E6;">3:修改</span>
        </div>
        <div class="flex justify-start items-center ml-5">
          <div>开启拖拽列</div>
          <div class="ml-2">
            <el-switch v-model="optionModel.activeDragRowFn"></el-switch>
          </div>
        </div>
        <div v-show="optionModel.activeDragRowFn" class="flex justify-start items-center ml-5">
          <div>拖拽列API
            <el-tooltip effect="light" content="拖拽列需要对应API保存接口，否则无效！ 如：api/Co/TaskManager/ChangeSeq,保存参数：{CurrentId: 175646595592261,DataStruct: 0,TargetId: 177799897985093,Type: 10}">
              <i class="el-icon-info"></i></el-tooltip>
          </div>
          <div class="ml-2">
            <el-input v-model="optionModel.activeDragRowApiUrl" placeholder="拖拽列API保存接口地址"></el-input>
          </div>
        </div>
        <!-- <div v-show="!!optionModel.activeDragRowFn" class="flex justify-start items-center ml-5">
          <div>拖拽排序API</div>
          <div class="ml-2">
            <el-select style="width:168px" v-model="optionModel.DragRowSortApi" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option :key="queryIndex + queryItem.value"
                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                :value="queryItem.value"></el-option>
            </el-select>
          </div>
        </div> -->
      </div>
      <el-skeleton :loading="loading_container" animated>
        <div class="mt-5">
          <vxe-grid border :max-height="500" resizable keep-source size="mini" :show-overflow="true"
            :show-header-overflow="true" :row-style="rowStyleEvent" ref="editFormGrid" :loading="loading_container"
            :checkbox-config="{
        highlight: true,
        range: false,
        strict: true,
      }" :columns="editGridOptions.columns" :data="optionModel.tableColumns" :edit-rules="editGridOptions.validRules"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }">
            <template v-slot:operate="{ row, ...restItem }">
              <el-button size="mini" @click="deleteOption(restItem, row)" type="text">删除</el-button>
              <el-button size="mini" @click="rowGridOperateAction(restItem, row)" type="text"><span
                  :style="[{ color: !!(row.fieldRules && row.fieldRules != '[]') ? `green` : `` }]">{{
        !!(row.fieldRules && row.fieldRules != '[]') ? `编辑规则` : `添加规则` }}</span></el-button>
            </template>
            <template #controlConfigSlots="{ row }">
              <el-button @click="showCtrlConfigEvent(row)" size="mini" type="text">
                <span
                  :style="[{ color: (!!row.controlConfig && Object.keys(row.controlConfig).length > 0) ? `green` : `` }]">{{
        (!!row.controlConfig && Object.keys(row.controlConfig).length > 0) ?
          `编辑配置` : `添加配置` }}</span>
              </el-button>
            </template>

            <template v-slot:dragColumn="{ row }">
              <span style="cursor: move !important;" class="drag-btn">
                <i class="vxe-icon-sort"></i>
              </span>
            </template>
          </vxe-grid>
        </div>

      </el-skeleton>

      <!-- 重写footer 并且默认值 -->
      <template v-slot:footer>
        <div class="flex justify-between">
          <div>
            <el-button type="primary" size="small" icon="el-icon-plus" plain round
              @click="addNewField">添加新字段</el-button>
          </div>
          <div>

          </div>
          <div>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button @click="colSubmit()" type="primary">确定</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>
    <vxe-modal ref="fieldRulesRef" :title="`${fieldRulesRefTitle} 字段验证规则`" width="1000px" height="650px" show-footer
      :position="{ top: 10 }" v-model="showFieldRulesBox">
      <div style="margin-bottom:10px" class="flex">
        <div>

          <el-button @click="fieldRulesAdd()" icon="vxe-icon-add" size="mini" type="primary">&nbsp;新增条件</el-button>
          <el-button size="mini" type="success" @click="addGroupClick()"><i
              class="vxe-icon-add"></i>&nbsp;新增(条件组)</el-button>

        </div>
      </div>
      <vxe-grid row-id="id" border resizable keep-source size="mini" :show-overflow="true" :show-header-overflow="true"
        ref="gridFieldRulesBoxRef" @current-change="currentChange" :row-config="{ isHover: true, isCurrent: true }"
        :loading="isLoading_searchParamsRef" :checkbox-config="{
        highlight: true,
        range: false,
        strict: true,
      }" :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', expandAll: true }"
        :columns="gridFieldRules.columns" :data="gridFieldRules.data"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }">

        <template v-slot:default_field="{ row }">
          <template v-if="row.parentId != 0">
            <div class="flex ">
              <div style="color:red;" class="flex items-center" v-if="row.parentId == -1"> + </div>
              <vxe-select transfer :options="fieldOtions" v-model="row.field">
              </vxe-select>
            </div>
          </template>
          <template v-else>
            <span>条件组</span>
          </template>
        </template>
        <template v-slot:default_triggerField="{ row }">
         
          <template v-if="row.parentId == 0 || row.parentId == -1">
            <vxe-select transfer :options="fieldOtions" v-model="row.triggerField">
            </vxe-select>
          </template>
        </template>
        
        <template v-slot:default_color="{ row }">
          <template v-if="row.parentId == 0 || row.parentId == -1">
            <el-color-picker :predefine="predefineColors" v-model="row.color"></el-color-picker>
          </template>


        </template>
        <template v-slot:default_IconColor="{ row }">
          <template v-if="row.parentId == 0 || row.parentId == -1">
            <el-color-picker :predefine="predefineColors" v-model="row.IconColor"></el-color-picker>
          </template>


        </template>
        <template v-slot:default_IconValue="{ row }">
          <template v-if="row.parentId == 0 || row.parentId == -1">
            <vxe-input v-model="row.IconValue" placeholder="如：el-icon-eleme"></vxe-input>
          </template>
        </template>
        <template v-slot:default_condition="{ row }">
          <template v-if="row.parentId != 0">
            <vxe-select transfer :options="reporConditionType" v-model="row.condition">
            </vxe-select>
          </template>

        </template>
        <template v-slot:default_conditionType="{ row }">
          <template v-if="row.parentId == 0">
            <vxe-select disabled transfer :options="reporConditionOptions" v-model="row.conditionType">
            </vxe-select>
          </template>

        </template>
        <template v-slot:default_type="{ row }">
          <template v-if="row.parentId == 0 || row.parentId == -1">
            <vxe-select transfer :options="reportfontbgType" v-model="row.type">
            </vxe-select>
          </template>

        </template>
        <template v-slot:default_value="{ row }">
          <template v-if="row.parentId != 0">
            <vxe-input v-model="row.value" placeholder=""></vxe-input>
          </template>

        </template>
       
        
        <template v-slot:operate="{ row }">
          <el-button size="mini" @click="gridFieldRulesBoxAction('delete', row)" type="text">删除</el-button>
        </template>
      </vxe-grid>
      <!-- 重写footer 并且默认值 -->
      <template v-slot:footer>
        <div class="flex justify-between">
          <div>
          </div>
          <div>
          </div>
          <div>
            <el-button @click="showFieldRulesBox = false">关闭</el-button>
            <el-button @click="submitModalData('fieldRulesRef')" type="primary">确定</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>
    <el-dialog title="编辑字段列类型" :visible.sync="showEditFieldDialogFlag" v-if="showEditFieldDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form-item label="字段">
        <!-- <el-input readonly v-model="editFieldForm.field"></el-input> -->
        <span>{{ editFieldForm.field }}</span>
      </el-form-item>
      <el-form-item label="描述">
        <el-input readonly v-model="editFieldForm.title"></el-input>
      </el-form-item>
      <el-form-item label="列类型">
        <el-select @change="change_columnType" v-model="customRender.columnType" placeholder="请选择类型">
          <el-option label="纯文本" value="text"></el-option>
          <!-- <el-option label="标签" value="tab"></el-option> -->
          <el-option label="状态列" value="status"></el-option>
          <el-option label="自定义状态列" value="customStatus"></el-option>
        </el-select>
      </el-form-item>
      <!-- 动态参数 -->
      <div v-show="customRender.columnType == 'customStatus'" style="font-weight: bold;">参数</div>
      <el-form-item v-show="customRender.columnType == 'customStatus'" label="颜色类型">
        <el-radio-group v-model="customRender.colorType" style="margin-bottom: 30px;">
          <el-radio-button label="color">字体</el-radio-button>
          <el-radio-button label="background-color">背景</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <div v-show="customRender.columnType == 'customStatus'">
        <div :key="index" v-for="(item, index) in typeConditionList" class="flex justify-center items-center">
          <el-form-item label-width="66px" label="KEY">
            <el-input style="width:100px" placeholder="key" v-model="item.key"></el-input>
          </el-form-item>
          <el-form-item label-width="1px">
            <el-select style="width:100px" v-model="item.condition" placeholder="请选择条件">
              <el-option label="等于" value="eq"></el-option>
              <el-option label="大于" value="lg"></el-option>
              <el-option label="小于" value="le"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="66px" label="VALUE">
            <el-input style="width:100px" placeholder="value" v-model="item.value"></el-input>
          </el-form-item>
          <el-form-item label-width="1px">
            <el-select style="width:120px" v-model="item.color" placeholder="请选择颜色">
              <el-option label="默认" value="#409EFF"><span style="color:#409EFF">默认</span></el-option>
              <el-option label="成功" value="#67C23A"><span style="color:#67C23A">成功</span></el-option>
              <el-option label="错误" value="#F56C6C"><span style="color:#F56C6C">错误</span></el-option>
              <el-option label="警告" value="#E6A23C"><span style="color:#E6A23C">警告</span></el-option>
              <el-option label="进行中" value="#909399"><span style="color:#909399">进行中</span></el-option>
            </el-select>
            <!-- <el-color-picker  
              :predefine="predefineColors" v-model="item.color"></el-color-picker> -->
          </el-form-item>
          <div style="margin-bottom:20px;margin-left:5px"> <i @click="deleteParam(item, index)"
              class="el-icon-delete"></i>
          </div>
        </div>
      </div>
      <div v-show="customRender.columnType == 'customStatus'">
        <el-button type="text" @click="addParamsEvent()">+添加参数</el-button>
      </div>
      <!-- <el-form-item label="状态颜色">
          <el-select  v-model="editFieldForm.status" placeholder="请选择状态">
              <el-option label="默认" value="default"></el-option>
              <el-option label="成功" value="success"></el-option>
              <el-option label="错误" value="error"></el-option>
              <el-option label="警告" value="warning"></el-option>
              <el-option label="进行中" value="processing"></el-option>
          </el-select>
        </el-form-item> -->
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditField()">{{ i18nt('designer.hint.confirm')
          }}</el-button>
        <el-button size="large" type="" @click="showEditFieldDialogFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
      </div>
    </el-dialog>
    <mappongParamsDialog :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"
      @submitSuccess="submitSuccess_mappongParams" :currentRow="currentEditRow" ref="mappongParamsDialogRef">
    </mappongParamsDialog>
  </div>
</template>
<script>
import {
  // yesOrNoList,
  // reportControlType,
  // reportParamsInputType,
  reportColorList,
  fixedList,
  reporConditionType,
  reporConditionOptions,
  reportfontbgType,
  alignList,
  controlTypeList,
  fieldFormatList,
} from "@/enum/enumData";
import Sortable from '@/libs/Sortable.min.js'
import commonSettingEventDialog from '@/components/form-designer/setting-panel/commonSettingEventDialog.vue'
import singleParamsOnChange from "@/components/form-designer/setting-panel/singleParamsOnChange.vue"
import mappongParamsDialog from '@/components/form-designer/setting-panel/mappongParamsDialog.vue'
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
// import { controlTypeList } from '@/enum/enumData'
import i18n from "@/utils/i18n"
import Draggable from 'vuedraggable'
import opertationClonms from "./vxetable-opertationClonms"
import footerButtons from "./vxetable-footerButtons"
import { deepClone, generateId } from "@/utils/util"
import cloneDeep from "clone-deep"
import CodeEditor from '@/components/code-editor/index'
import { useFormatParams } from "@/hooks/useFormatParams"
import request from '@/libs/request'
//{'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total'}
const pageOptions = [
  {key:'PrevJump',value:'首页'},
  {key:'PrevPage',value:'上一页'},
  {key:'Number',value:'页码'},
  {key:'NextPage',value:'下一页'},
  {key:'NextJump',value:'尾页'},
  {key:'Sizes',value:'每页条数'},
  {key:'FullJump',value:'跳转'},
  {key:'Total',value:'总条数'},
]
const default_typeConditionList = [{ key: "", condition: 'eq', conditionType: 'and', value: "", color: "" }]
const default_customRender = {
  columnType: "text",
  colorType: "color" //background
}
const default_editFieldForm = {
  field: "",
  title: "",
}
export default {
  name: "vxetable-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    commonSettingEventDialog,
    singleParamsOnChange,
    Draggable,
    selectDataSourceApi,
    CodeEditor,
    opertationClonms,
    footerButtons,
    commonParamsOnChange,
    mappongParamsDialog,
  },
  data() {
    return {
      pageOptionsList:pageOptions,
      tableColumnsBackUp:[],// 备份列头信息
      loadingCheck:false,
      fieldOtions: [],
      reporConditionType,
      reporConditionOptions,
      reportfontbgType,
      currentSelectedRow: null,
      currentSelectedItem: null,
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        '#409EFF',
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
        '#303133',
        '#DCDFE6',
        // '#3799FF',
        // 'rgba(255, 69, 0, 0.68)',
        // 'rgb(255, 120, 0)',
        // 'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577'
      ],
      fieldRulesRefTitle: "",// 当前验证规则弹框 编辑字段名称
      isLoading_searchParamsRef: false,// 查询参数 是否加载中...
      currentEditModelRow: {},// 模型字段选择 编辑行
      showFieldRulesBox: false,// 字段验证规则 弹框是否显示
      gridFieldRules: {
        columns: [
          {
            fixed: 'right',
            slots: { default: 'operate' },
            title: '操作',
            headerAlign: 'center',
            align: 'center',
            width: 60,
          },

          { field: 'field', width: 150, title: '字段', treeNode: true, align: "left", headerAlign: "center", slots: { default: 'default_field' } },
          // { field: 'triggerField', width: 150, title: '触发字段', align: "left", headerAlign: "center", slots: { default: 'default_triggerField' } },
          { field: 'condition', title: '条件', align: "left", headerAlign: "center", slots: { default: 'default_condition' } },
          { field: 'value', title: '值', align: "left", headerAlign: "center", slots: { default: 'default_value' } },
          { field: 'type', width: 130, title: '渲染类型', align: "left", headerAlign: "center", slots: { default: 'default_type' } },
          { field: 'color', width: 80, title: '渲染颜色', align: "center", headerAlign: "center", slots: { default: 'default_color' } },
          { field: 'IconValue',width: 130, title: 'Icont图标', align: "left", headerAlign: "center", slots: { default: 'default_IconValue' },
          titlePrefix: {
              content: `使用Icont图标，需要将控件类型选择【Icon图文控件】,仅对当前行字段有效，图标仅支持elementUI图标，如：el-icon-eleme，可从elementUI官网复制图标代码`,
              //useHTML:false
            }, },
          // { field: 'IconColor', width: 80, title: 'Icon颜色', align: "center", headerAlign: "center", slots: { default: 'default_IconColor' } },
          { field: 'conditionType', width: 60, title: '关系', align: "left", headerAlign: "center", slots: { default: 'default_conditionType' } },
          // // { field: 'field', title: '字段',treeNode: true, align: "left", headerAlign: "center", editRender: { name: "customSelectList", options: [], defaultValue: '' }, },
          // { field: 'field', title: '字段',treeNode: true, align: "left", headerAlign: "center", slots:{default: 'default_field'} },
          // { field: 'condition', title: '条件', align: "left", headerAlign: "center", editRender: { name: "customSelectList", options: reporConditionType, defaultValue: 'gt' }, },
          // // { field: 'conditionType', title: '条件类型', align: "left", headerAlign: "center", editRender: { name: "customSelectList", options: reporConditionOptions, defaultValue: 'gt' }, },
          // { field: 'value', title: '值', align: "left", headerAlign: "center", editRender: { name: "input", autoselect: true, defaultValue: '' } },
          // { field: 'type', width: 100, title: '渲染类型', align: "left", headerAlign: "center", editRender: { name: "customSelectList", options: reportfontbgType, defaultValue: 'text' }, },
          // // { field: 'color', title: '颜色', align: "left", headerAlign: "center", editRender: { name: "customSelectList", options: reportColorList, defaultValue: '#67C23A' }, },
          // { field: 'color',width:60, title: '颜色', align: "center", headerAlign: "center", slots:{default: 'default_color'} },
        ],
        data: []
      },
      dataModelList: [],// 模型实体下拉
      needConfigType: ["statusColumn","reportSelect", "reportSelectTable", "customSelect", "standardFrame", "reportMulSelect", "customRenderCtrl", "popupTextarea"],
      currentEditRow: null,
      loading_container: false,
      loadingTableSubmit: false,
      tableTreeNodeField: "",//节点字段
      typeConditionList: [],//deepClone(default_typeConditionList),
      customRender: deepClone(default_customRender),
      editFieldForm: deepClone(default_editFieldForm),
      showEditFieldDialogFlag: false,// 字段编辑 是否显示
      loadingSubmit: false,// 开始加载按钮，进行中
      controlTypeList: controlTypeList,//控件类型
      actionName: "",// 执行API名称URL
      nameRules: [
        { required: true, trigger: ['blur', 'change'], message: this.i18nt('designer.setting.fieldValueRequired') },
      ],
      dialogVisible: false,
      showRenderDialogFlag: false,
      showButtonsEditDialog: false,
      useFormatParamsFn: useFormatParams(this),
      testTableData: [

      ],
      // 组织管理列头测试数据
      testData2: [

      ],
      editGridOptions: {
        columns: [
          {
            type: 'checkbox', width: 50, align: 'center',
            headerAlign: 'center', fixed: "left",
          },
          {
            type: 'seq', title: "序号", align: 'center',
            headerAlign: 'center', fixed: "left", width: 60
          },
          {
            fixed: 'right',
            slots: { default: 'operate' },
            title: '操作',
            headerAlign: 'center',
            align: 'center',
            width: 120,
          },
          { slots: { default: 'dragColumn' }, visible: true, title: '#', headerAlign: 'center', align: 'center', width: 60 },
          { field: 'field', fixed: "left", width: 150, title: '字段名', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          { field: 'title', fixed: "left", width: 150, title: '中文描述', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          { field: 'titleHelp', width: 120, title: '标题帮助', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          {
            field: 'fieldDefault', width: 120, title: '默认值', editRender: { name: "input", autoselect: true, defaultValue: '' },
            titlePrefix: {
              content: `(注意：与控件类型关联)如时间默认值 当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+1年:add-1-year`,
              //useHTML:false
            },
          },

          { field: 'iisReadOnly', width: 100, align: "center", headerAlign: "center", title: '只读？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisRequired', width: 100, align: "center", headerAlign: "center", title: '必填？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisShowList', width: 130, align: "center", headerAlign: "center", title: '列表显示？', editRender: { name: "vxeCheckbox", defaultValue: 1 } },

          { field: 'width', width: 100, align: "right", title: '列宽', editRender: { name: "input", autoselect: true, defaultValue: '100' },
          titlePrefix: {
                            content: `注意：单个为0时，自动占据所有剩余列宽，多个为0时，平均分配剩余列宽; 当为-1时，根据列头描述字段长度自动设置宽度，宽度=中文个数*30`,
                            //useHTML:false
                        }, },

          { field: 'labelWidth', width: 100, align: "right", title: '标题宽', editRender: { name: "input", autoselect: true, defaultValue: '110' }, },
          // { field: 'fieldOrder', width: 100, align: "right", title: '列表顺序', editRender: { name: "input", autoselect: true, defaultValue: '100' }, },
          {
              field: 'fixed', width: 125, align: "center", headerAlign: "center", title: '固定方式',
              titlePrefix: {
                  content: `当X轴出现滚动条，拖动时，该字段是否随滚动条一起滚动或固定某侧`,
                  //useHTML:false
              },
              editRender: { name: "customSelectList", options: fixedList, defaultValue: null },
          },
          { field: 'titleAlign', width: 130, align: "center", headerAlign: "center", title: '表头对齐', editRender: { name: "$select", options: alignList, defaultValue: 'center' }, },
          { field: 'align', width: 130, align: "center", headerAlign: "center", title: '表格对齐', editRender: { name: "$select", options: alignList, defaultValue: 'center' }, },
          { field: 'controlType', width: 130, align: "center", headerAlign: "center", title: '控件类型', editRender: { name: "$select", options: controlTypeList, defaultValue: 'text' }, },
          {
            field: 'controlConfig',
            title: '控件配置',
            width: 100,
            align: "center", headerAlign: "center",
            slots: { default: 'controlConfigSlots' },

          },
          { field: 'fieldFormat', width: 150, align: "center", headerAlign: "center", title: '列格式化', editRender: { name: "customSelectList", options: fieldFormatList, defaultValue: '' }, },
          { field: 'iisQuery', width: 120, align: "center", headerAlign: "center", title: '过滤', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          // { field: 'iisHQuery', width: 120, align: "center", headerAlign: "center", title: '高级查？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisSummary', width: 120, align: "center", headerAlign: "center", title: '汇总？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisSortable', width: 120, align: "center", headerAlign: "center", title: '排序？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },

        ],
        data: [

        ],
        //表格校验规则
        validRules: {
          // fieldName: [
          //     { required: true, message: '字段名必填' }
          // ],
          // title: [
          //     { required: true, message: '中文描述必填' }
          // ],
        }
      },
    }
  },
  computed: {
    tableColumnsList() {
      let list = this.optionModel.tableColumns.filter(item => {
        if (!!item.iisShowList && item.hasOwnProperty('field') && item.hasOwnProperty('title')) {
          return item
        }
      })
     // debugger
      return list
    }

  },
  mounted() {
    this.actionName = this.optionModel.requstConfig.postUrl

  },
  methods: {
    currentChange(tableInfo) {
      if (tableInfo.row.parentId == 0) {
        this.currentSelectedRow = tableInfo.row
      } else {
        this.currentSelectedRow = null
      }
      this.currentSelectedItem = tableInfo.row
    },
    // 新增条件组
    // 添加条件组
    async addGroupClick() {
      let randomID = Math.floor(Math.random() * 100000000 + 1)
      let record = {
        id: randomID,
        parentId: 0,
        field: this.fieldRulesRefTitle,
        triggerField: this.fieldRulesRefTitle,
        condition: 'eq',
        conditionType: 'and',
        type: 'text',
        color: '#67C23A',
        IconColor:'#67C23A',

      }
      const $table = this.$refs["gridFieldRulesBoxRef"]
      const { row: newRow } = await $table.insertAt(record, -1)
      $table.setCurrentRow(newRow) // 插入子节点
      this.currentSelectedRow = newRow
      this.currentSelectedItem = newRow
    },
    async addCommonRow() {
      let randomID = Math.floor(Math.random() * 100000000 + 1)
      let record = {
        id: randomID,
        parentId: -1,
        field: this.fieldRulesRefTitle,
        triggerField: this.fieldRulesRefTitle,
        condition: 'eq',
        conditionType: 'and',
        type: 'text',
        color: '#67C23A',
        IconColor:'#67C23A',
      }
      const $table = this.$refs["gridFieldRulesBoxRef"]
      //debugger
      if (this.currentSelectedItem && this.currentSelectedItem.parentId == -1) {
        const { row: newRow } = await $table.insertAt(record, this.currentSelectedItem)
        $table.setCurrentRow(newRow) // 插入子节点
      } else {
        const { row: newRow } = await $table.insertAt(record, -1)
        $table.setCurrentRow(newRow) // 插入子节点
      }

    },
    // 字段验证规则 添加
    async fieldRulesAdd() {
      if (!!this.currentSelectedRow) {
        await this.insertRow(this.currentSelectedRow, 'bottom')
      } else {
        this.addCommonRow()
      }
      // const $grid = this.$refs["gridFieldRulesBoxRef"]
      // let randomID = Math.floor(Math.random() * 100000000 + 1)
      // // 添加新规则并且赋值默认值 this.fieldRulesRefTitle 当前编辑字段
      // $grid.insertAt({  
      //     id: randomID,
      //     parentId: -1, field: this.fieldRulesRefTitle, condition: 'eq',conditionType: '@',  type: 'text', color: '#67C23A' }, -1)

    },
    async insertRow(currRow, locat) {
      let randomID = Math.floor(Math.random() * 100000000 + 1)
      const $table = this.$refs["gridFieldRulesBoxRef"]
      // 如果 null 则插入到目标节点顶部
      // 如果 -1 则插入到目标节点底部
      // 如果 row 则有插入到效的目标节点该行的位置
      if (locat === 'current') {

        const record = {
          id: randomID,
          parentId: currRow.parentId,
          field: this.fieldRulesRefTitle,
          triggerField: this.fieldRulesRefTitle,
          condition: 'eq',
          conditionType: 'and',
          type: 'text',
          color: '#67C23A',
          IconColor:'#67C23A',
        }
        const { row: newRow } = await $table.insertAt(record, currRow)
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      } else if (locat === 'top') {

        const record = {
          id: randomID,
          parentId: currRow.id,
          field: this.fieldRulesRefTitle,
          triggerField: this.fieldRulesRefTitle,
          condition: 'eq',
          conditionType: 'and',
          type: 'text',
          color: '#67C23A',
          IconColor:'#67C23A',
        }
        const { row: newRow } = await $table.insert(record)
        await $table.setTreeExpand(currRow, true) // 将父节点展开
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      } else if (locat === 'bottom') {

        const record = {
          id: randomID,
          parentId: currRow.id,
          field: this.fieldRulesRefTitle,
          triggerField: this.fieldRulesRefTitle,
          condition: 'eq',
          conditionType: 'and',
          type: 'text',
          color: '#67C23A',
          IconColor:'#67C23A',
        }
        const { row: newRow } = await $table.insertAt(record, -1)
        await $table.setTreeExpand(currRow, true) // 将父节点展开
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      }
    },
    // 行操作（字段模型）
    rowGridOperateAction(restItem, rowData) {
      // debugger
      this.fieldRulesRefTitle = rowData.field
      this.currentEditModelRow = rowData
      this.showFieldRulesBox = true // 打开字段验证规则 弹框
      this.$nextTick(() => {
        if (this.gridFieldRules.columns[1].field == 'field') {
          let dataList = this.formatTableDataToDDList()
          // 将表格字段赋值到验证规则弹框下拉中
          // this.gridFieldRules.columns[1].editRender.options = dataList
          this.fieldOtions = dataList
        }
        // 加载数据
        this.resetData_gridFieldRulesBox(true)
      })
    },
    // 格式表格数据为下拉数据列表
    formatTableDataToDDList() {
      // debugger
      let dataList = this.optionModel.tableColumns
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            label: !!oldItem.title ? oldItem.title : oldItem.field,
            value: oldItem.field
          }
          newDataList.push(newItem)
        })

      }
      return newDataList

    },
    // 行操作(验证规则)
    gridFieldRulesBoxAction(type = 'delete', rowData) {

      this.$nextTick(() => {
        const $grid = this.$refs["gridFieldRulesBoxRef"]
        let _fullData = $grid.getTableData().visibleData
        if (rowData.parentId == 0) {
          // 如果删除父类，需要删除对应的子类
          _fullData.forEach(item => {
            if (item.parentId == rowData.id) {
              $grid.remove(item)
            }
          })
        }
        $grid.remove(rowData)

        if (rowData.parentId == 0) {
          this.currentSelectedRow = null
          this.currentSelectedItem = null
        }
      })
    },
    // 提交自定义弹框数据
    submitModalData(refName = "") {
      if (refName != "") {
        switch (refName) {
          // 字段规则验证弹框
          case "fieldRulesRef":
            // debugger
            const $gridFieldRulesBoxRef = this.$refs["gridFieldRulesBoxRef"]
            if ($gridFieldRulesBoxRef) {
              let _fullData = $gridFieldRulesBoxRef.getTableData().visibleData
              let formatFullData = _fullData.filter(item => {
                delete item._X_ROW_CHILD
                return (item.parentId == 0 || item.parentId == -1)
              })
              // debugger
              this.currentEditModelRow.fieldRules = JSON.stringify(formatFullData)
            }
            this.resetData_gridFieldRulesBox() // 清空表格数据
            this.showFieldRulesBox = false
            break;

          default:
            break;
        }
      }
    },
    // 将 树状结构数据 转为 扁平数据
    toFlat(treeData = []) {
      let _self = this
      return treeData.reduce((result, node) => {
        if (node.children) {
          result.push(..._self.toFlat(node.children));
          // 如果有节点 添加前先移除
          delete node.children
        }
        result.push(node);
        return result;
      }, []);
    },
    // 重置，赋值 字段验证规则弹框
    resetData_gridFieldRulesBox(setData = false) {
      let _self = this
      if (setData) {
        // 赋值
        this.$nextTick(() => {
          let data = []
          const $grid = this.$refs["gridFieldRulesBoxRef"]
          if (!!this.currentEditModelRow.fieldRules) {
            let tempalteData = JSON.parse(this.currentEditModelRow.fieldRules)
            data = _self.toFlat(tempalteData)
          }
          $grid.reloadData(data)
        })
      } else {
        // 清空
        this.$nextTick(() => {
          const $grid = this.$refs["gridFieldRulesBoxRef"]
          $grid.remove()
        })
      }
    },
    // 非树结构拖动方法 
    rowDrop(tableRefName) {
      let _self = this
      this.$nextTick(() => {
        //debugger
        const xTable = _self.$refs[tableRefName]
        if (!!!xTable) {
          return
        }
        let fullData = xTable.getTableData().fullData
        _self.sortableRowDropObj = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          handle: '.drag-btn',
          ghostClass: "sortable-ghost",  // drop placeholder的css类名
          chosenClass: "sortable-chosen",  // 被选中项的css 类名
          onEnd: (evt) => {
            // debugger
            let { newIndex, oldIndex } = evt
            const currRow = fullData.splice(oldIndex, 1)[0]
            fullData.splice(newIndex, 0, currRow)
            _self.optionModel.tableColumns = fullData
            // xTable.loadData([])
            // xTable.reloadData([...fullData])

          }
        })
      })
    },
    // 控件配置信息
    submitSuccess_mappongParams(params) {
      //debugger
      /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
      ///调用方法：this.$set( target, key, value )
      if (!!params.onBeforeRender || !!params.sourceKey || !!params.searchParams || !!params.matchField || !!params.actionName || (!!params.CDATAS && params.CDATAS.length > 0)) {
        // this.currentEditRow.controlConfig = cloneDeep(params)
        this.$set(this.currentEditRow, "controlConfig", cloneDeep(params))
      } else {
        // this.currentEditRow.controlConfig = {}
        this.$set(this.currentEditRow, "controlConfig", {})
      }
    },
    showCtrlConfigEvent(row) {
      if (!this.needConfigType.includes(row.controlType)) {
        this.$message({
          type: 'warning',
          message: '当前控件无需配置,请选择其它控件类型！ '
        });
        return

      }
      this.currentEditRow = row
      let mappongParamsDialogRef = this.$refs["mappongParamsDialogRef"]
      if (mappongParamsDialogRef) {
        mappongParamsDialogRef.showEditCtrlDialogFlag = true
      }
    },
    tableRowClassName({ row, rowIndex }) {
      //debugger
      // if (rowIndex === 1) {
      //   return 'warning-selected-row';
      // } else if (rowIndex === 3) {
      //   return 'success-selected-row';
      // }
      return '';
    },
    // 设置汇总行字段
    change_rowSumDynamicColumnFieldEvent(val) {
      //debugger
      this.$set(this.optionModel, "rowSumDynamicColumnField", val)
    },
    // 表格树节点设置 改变
    change_columnsTreeNodeEvent(value) {
      //debugger
      this.optionModel.tableColumns = this.optionModel.tableColumns.map((item) => {
        if (item.hasOwnProperty("field") && item.field == value) {
          item.treeNode = true
        } else {
          item.treeNode = false
        }
        return item
      })
    },
    change_columnType(value) {
      this.typeConditionList = []
    },
    submitEditField() {
      this.editFieldForm.customRender = {
        columnType: this.customRender.columnType,
        colorType: this.customRender.colorType,
        condition: this.typeConditionList
      }
      this.showEditFieldDialogFlag = false
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.typeConditionList.length + 1
      let randomID = Math.floor(Math.random() * 100000000 + 1)
      this.typeConditionList.push(
        {
          key: `{{${this.editFieldForm.field}}}`, id: randomID,
          parentId: -1, condition: "eq", conditionType: "and", value: "", color: "#67C23A", IconColor:'#67C23A', type: 'text',
        }
      )
    },

    // 移除参数
    deleteParam(item, index) {
      this.typeConditionList.splice(index, 1)
    },
    // 重置数据源
    resetModifyData() {
      this.typeConditionList = []
      this.customRender = deepClone(default_customRender)
      this.editFieldForm = deepClone(default_editFieldForm)
    },
    // 编辑字段渲染事件
    showEditFieldDialogEvent(option) {
      this.showEditFieldDialogFlag = true
      this.resetModifyData()
      this.editFieldForm = option
      this.$nextTick(() => {
        this.initFieldDialog(option)
      })
    },
    // 初始化字段编辑弹框
    initFieldDialog(option) {
      // debugger
      if (option && option.hasOwnProperty("customRender")) {
        let itemRender = option.customRender
        this.typeConditionList = itemRender.condition
        this.customRender = {
          columnType: itemRender.columnType
        }
      }
    },
    // 预加载表格数据，获取配置参数
    loadTableData() {
      // debugger
      // 列头查询 preUrl 路径固定不变
      // let actionName = this.optionModel.requstConfig.postUrl
      // let _url = this.useFormatParamsFn.getVFormDataSearchUrl(actionName);
      // if(!!_url){
      //   this.loadingTableSubmit = true
      // }else{
      //   return
      // }
      // // 合并普通、高级查询参数
      // let params = this.useFormatParamsFn.getVFormSearchParams(actionName);

      // request["post"](_url, params).then((res) => {
      //   //debugger
      //   if(res && res.Data && res.Data.DataSet){
      //      this.optionModel.dataSetAllModel = res.Data
      //      this.optionModel.dataSetData = res.Data.DataSet
      //   }

      //   this.loadingTableSubmit = false
      // })
    },

    // 单元格样式
    // 表字段对比 差异 0: 相同 ；1/1: 新增1； 2: 删除 3: 修改
    rowStyleEvent(tableInfo) {
      let { row, rowIndex, column } = tableInfo
      if (!row || !row.Diff) {
        return
      }
      let type = row.Diff
      switch (type) {
        case 1:
          // 新增
          return {
            backgroundColor: '#90EE90',
            //color: '#ffffff'
          }
        case 2:
          // 删除
          return {
            backgroundColor: '#FFB6C1',
            // color: '#ffffff'
          }
        case 3:
          // 修改
          return {
            backgroundColor: '#ADD8E6',
            // color: '#ffffff'
          }
        default:
          break;
      }
    },
    // 字段同步检查,
   async CheckTableColumns() {
      //debugger
      this.loadingCheck = true
      let oldItems = cloneDeep(this.optionModel.tableColumns) 
      let newItems = await this.getNewColsData()
     
      newItems = newItems.map((newItem,newIndex)=>{
          // 如果没有符合条件的元素返回 -1
          let isExistIndex = oldItems.findIndex((oldItem)=>{
              return newItem.field ===oldItem.field
          })
          if(isExistIndex==-1){
              // 新的在旧的列表中查找，没有找到，新增
              newItem.Diff =1
          }
          if(isExistIndex>-1){
              // 新的在旧的列表中查找，找到了，替换,已有的
              newItem.Diff =0
              if(newItem.title != oldItems[isExistIndex].title) {
                  newItem.Diff =3
              }
              newItem = Object.assign({},newItem,oldItems[isExistIndex])
          }
          return newItem
      })
     oldItems.map(oldItem=>{
        // 如果没有符合条件的元素返回 -1
        let isExistIndex = newItems.findIndex((newItem,index)=>{
            return oldItem.field ===newItem.field
        })
        // 1: 新增 2: 删除 3: 修改
        if(isExistIndex==-1){
             oldItem.Diff =2
             newItems.push(oldItem)
        }
      })
      this.optionModel.tableColumns = newItems
      this.loadingCheck = false
    },
    // 开始加载列头信息
    async loadColsData() {
      // 列头查询 preUrl 路径固定不变
      let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.actionName);
      if (!!CDATASET_ID) {
        this.loading_container = true
      } else {
        return
      }
      let dataModelList = await this.getDataModelList(CDATASET_ID)
      this.optionModel.tableColumns = this.formatDataModel(dataModelList)
      this.tableColumnsBackUp= cloneDeep(this.optionModel.tableColumns)
      this.loading_container = false
    },
   async getNewColsData(){
       // 列头查询 preUrl 路径固定不变
       let newItems =[]
       try {
        let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.actionName);
        if (!!CDATASET_ID) {
        } else {
          return
        }
        let dataModelList = await this.getDataModelList(CDATASET_ID)
         newItems = this.formatDataModel(dataModelList)
       } catch (error) {
        newItems =[]
       }
       return newItems
    },
    // 格式化模型数据
    formatDataModel(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        let innerField = [
          "CID",
          "CDATETIME_CREATED",
          "CUSER_CREATED",
          "CDATETIME_MODIFIED",
          "CUSER_MODIFIED",
          "CINSTANCE_ID",
          "CROWREMARK",
          "CENTERPRISE_CODE",
          "CORG_CODE",
        ]
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: oldItem.CCOLUMN_DESC,
            CID: oldItem.CID,
            titleHelp: "",
            fieldDefault: "",
            iisShowList: 1,
            width: 200,
            titleAlign: "center",
            fieldOrder: 100,
            align: "center",
            controlType: "text",
            groupTitle: "",
            iisSummary: 0,
            fieldRules: "",
            iisQuery: 0,
            iisHQuery: 0,
            iisSummary: 0,
            iisReadOnly: 0,
            iisMoney: 0,
            iisSortable: 0,
            sourceKey: "",
            matchField: "",
            searchParams: "",
          }
          // 内置字段不显示
          if (innerField.includes(newItem.field)) {
            newItem.iisShowList = 0
          }
          newDataList.push(newItem)
        })
      }

      return newDataList
    },
    // 获取 表格列头数据信息
    async getDataModelList(datasetId) {
      let dataModelList = []
      let params = {
        condition: "",
        datasetId: datasetId
      }
      //let _url = "api/MD/DataSetModel/GetAll"
      //let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"

      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })
      return dataModelList
    },
    // 选中查询数据的URL时候，清空测试数据
    postUrlChangeEvent() {
      this.optionModel.tableData = []
      this.overWriteWidgetName()
    },
    layoutLevelChange(level = 1) {
      this.optionModel.layoutLevel = level
      // if (level == 1) {
      //   // 如果是主表，重置表名称
      //   this.$set(this.optionModel, "name", "mainTableRef")
      // } else {
      //   let randomName = "subTableRef" + this.useFormatParamsFn.getRandomNum()
      //   this.$set(this.optionModel, "name", randomName)
      // }
    },
    // 重写表格唯一名称
    overWriteWidgetName(level) {

      // this.$nextTick(() => {
      //   //debugger
      //   if (this.optionModel.requstConfig.actionName == 'API') {
      //     // 如果是主表，重置表名称
      //     this.$set(this.optionModel, "name", "mainTableRef")
      //   } else {
      //     let randomName = "subTableRef" + this.useFormatParamsFn.getRandomNum()
      //     this.$set(this.optionModel, "name", randomName)
      //   }
      // })

    },
    // 移除所有列表
    removeAllCols() {
      this.$confirm("确定删除所有列", this.i18nt('render.hint.prompt'), {
        confirmButtonText: this.i18nt('render.hint.confirm'),
        cancelButtonText: this.i18nt('render.hint.cancel')
      }).then(() => {
        this.optionModel.tableColumns = []
      }).catch(error => {
        //this.$message.error(error)
      })
    },
    // 移除指定列
    deleteOption(option, row) {
      // debugger
      let index = option.rowIndex
      this.$confirm("确定删除该项", this.i18nt('render.hint.prompt'), {
        confirmButtonText: this.i18nt('render.hint.confirm'),
        cancelButtonText: this.i18nt('render.hint.cancel')
      }).then(() => {
        this.optionModel.tableColumns.splice(index, 1)
      }).catch(error => {
      })
    },
    addNewField() {
      this.$set(this.optionModel, 'tableColumns', this.optionModel.tableColumns || [])
      let newId = generateId()
      this.optionModel.tableColumns.push({
        field: "newfield" + newId,
        title: "新字段名" + newId,
        iisShowList: 1,// 列表显示，
        titleAlign: "center",
        align: "left",
        controlType: "text",
        labelWidth: 110,
        width: "120",
      })
    },




    editButtonsColumn() {
      this.showButtonsEditDialog = true
    },
    handleShowFooterButtonsChange() {

    },
    handleShowOperationColumnChange(value) { // 刷新表格显示，防止行列显示错位！！
      if (!!value) {
        let dataTableInDesign = this.designer.formWidget.getSelectedWidgetRef()
        if (!!dataTableInDesign && !!dataTableInDesign.refreshLayout) {
          this.$nextTick(() => {
            dataTableInDesign.refreshLayout()
          })
        }
      }
    },



    // 确认表格列更改
    colSubmit() {
      // 提交确定是，需要把所有 Diff 标记删除
      this.optionModel.tableColumns = this.optionModel.tableColumns.map(item=>{
        delete item.Diff
        return item
      })
      this.dialogVisible = false;
    },


    openSetting() {
      // debugger
      this.loading_container = true
      this.dialogVisible = true;
      let _self = this
      this.$nextTick(() => {

        setTimeout(() => {
          _self.loading_container = false
          this.tableColumnsBackUp= cloneDeep(this.optionModel.tableColumns)
          _self.rowDrop("editFormGrid")
        }, 1000)
      })

    },
  }
}
</script>
<style>
.sortable-ghost {
  background-color: #E6A23C !important;
}

.sortable-chosen {
  background-color: #67C23A !important;

}

.el-table .warning-selected-row {
  background-color: #E6A23C !important;
}

.el-table .success-selected-row {
  background-color: #67C23A !important;
}
</style>
<style lang="scss" scoped>
.option-items-pane ul {
  height: auto;
  //overflow: auto;
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}

li.col-item {
  list-style: none;

  span.col-span-title {
    display: inline-block;
    font-size: 13px;
    width: 120px;
  }

  .col-delete-button {
    margin-left: 6px;
  }
}

.panes-setting {
  ul {
    padding-inline-start: 0;
    padding-left: 0;
    /* 重置IE11默认样式 */
    margin: 0;
  }

  .drag-option {
    cursor: move;
  }

  li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
  }
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 12px 15px;
  }
}

.button-row {
  border-bottom: 1px solid #e1e2e3;
  margin-bottom: 12px;
}

.drag-sort-col {
  padding-top: 8px;
  cursor: move;
}

// add by andy
::v-deep .el-row {
  .el-button--primary.is-plain {
    color: #409EFF !important;
    background: #ecf5ff !important;
    border-color: #b3d8ff !important;
  }
}
</style>