import { deepClone, getDSByName, overwriteObj, runDataSourceRequest, translateOptionItems } from "@/utils/util"
import FormValidators from '@/utils/validators'
import dayjs from "dayjs";
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
export default {
  inject: ['refList', 'getFormConfig', 'globalOptionData', 'globalModel', 'getOptionData',
    'getGlobalDsv', 'getReadMode', 'getSubFormFieldFlag', 'getSubFormName'],
  data() {
    return {
      publicAttribute:{ 
        value:"",
        valueString:"",
      },// 对外开发属性值
      fieldReadonlyFlag: false,
      fieldModel_Computed:null,
      treeSelectedKey:null,
      treeDataList:[],// 下拉树 数据列表
        // 下拉树 配置选项
        defaultProps: {
          children: "children",
          label: "label",
      },
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
    }
  },
  computed: {
    formConfig() {
      return this.getFormConfig()
    },

    subFormName() {
      return !!this.getSubFormName ? this.getSubFormName() : ''
    },

    subFormItemFlag() {
      return !!this.getSubFormFieldFlag ? this.getSubFormFieldFlag() : false
    },
    // 弹框属性
    popupFormItemConfig(){
      return this.$store.state.popupParams
    },
    formModel: {
      cache: false,
      get() {
        return this.globalModel.formModel
      }
    },
    
    isReadMode() {
      //return this.getReadMode() || this.fieldReadonlyFlag
      return !!this.getReadMode() ? true : this.fieldReadonlyFlag
    },

    optionLabel() {
      if (this.fieldModel === null) {
        return '--'
      } else {
        let resultContent = '--'
        this.field.options.optionItems.forEach(oItem => {
          if ((oItem.value === this.fieldModel) || (this.findInArray(this.fieldModel, oItem.value)) !== -1) {
            resultContent = resultContent === '--' ? oItem.label : resultContent + ' ' + oItem.label
          }
        })

        return resultContent
      }
    },

  },

  methods: {
    // 用于区分复制页面和原有的页面不同，页面的CID和保存的CID是否一致，但此时必须注意页面必须经过重新编码保存才能有效
    isCurrentPagePathID(){
      //debugger
        let _flag = true
        let _formConfigObj = this.formConfig
        if(_formConfigObj && _formConfigObj.pageCID){
            if(this.$route.fullPath.indexOf(_formConfigObj.pageCID) === -1){
                _flag = false
            }
        }
        return _flag
    },
    findInArray(arrayObject, element) {
      if (!Array.isArray(arrayObject)) {
        return -1
      }

      let foundIdx = -1
      arrayObject.forEach((aItem, aIdx) => {
        if (aItem === element) {
          foundIdx = aIdx
        }
      })

      return foundIdx
    },

    //--------------------- 组件内部方法 begin ------------------//
    getPropName() {
      if (this.subFormItemFlag && !this.designState) {
        return this.subFormName + "." + this.subFormRowIndex + "." + this.field.options.name + ""
      } else {
        return this.field.options.name
      }
    },

    initFieldModel() {
      // console.log("=========this.field.type =========3====="+this.field.type)
      if (!this.field.formItemFlag) {
        return
      }

      if (!!this.subFormItemFlag && !this.designState) {  //SubForm子表单组件需要特殊处理！！
        let subFormData = this.formModel[this.subFormName]
        if (((subFormData === undefined) || (subFormData[this.subFormRowIndex] === undefined) ||
          (subFormData[this.subFormRowIndex][this.field.options.name] === undefined)) &&
          (this.field.options.defaultValue !== undefined)) {
          this.fieldModel = this.field.options.defaultValue
            // add by andy
          
          subFormData[this.subFormRowIndex][this.field.options.name] = this.field.options.defaultValue
        } else if (subFormData[this.subFormRowIndex][this.field.options.name] === undefined) {
          this.fieldModel = null
          subFormData[this.subFormRowIndex][this.field.options.name] = null
        } else {
          this.fieldModel = subFormData[this.subFormRowIndex][this.field.options.name]
        }

        /* 主动触发子表单内field-widget的onChange事件！！ */
        setTimeout(() => {  //延时触发onChange事件, 便于更新计算字段！！
          this.handleOnChangeForSubForm(this.fieldModel, this.oldFieldValue, subFormData, this.subFormRowId)
        }, 800)
        this.oldFieldValue = deepClone(this.fieldModel)

        this.initFileList()  //处理图片上传、文件上传字段

        return
      }

      if ((this.formModel[this.field.options.name] === undefined) &&
        (this.field.options.defaultValue !== undefined)) {
        this.fieldModel = this.field.options.defaultValue
            // add by andy
      
      } else if (this.formModel[this.field.options.name] === undefined) {  //如果formModel为空对象，则初始化字段值为null!!
        this.formModel[this.field.options.name] = null
      } else {
        this.fieldModel = this.formModel[this.field.options.name]
      }
      this.oldFieldValue = deepClone(this.fieldModel)
  
      this.initFileList()  //处理图片上传、文件上传字段
    },

    initFileList() { //初始化上传组件的已上传文件列表
      if (((this.field.type !== 'picture-upload') && (this.field.type !== 'file-upload')) || (this.designState === true)) {
        return
      }

      if (!!this.fieldModel) {
        if (Array.isArray(this.fieldModel)) {
          this.fileList = deepClone(this.fieldModel)
        } else {
          this.fileList.splice(0, 0, deepClone(this.fieldModel))
        }
      }
    },

    initEventHandler() {
      this.$on('setFormData', (newFormData) => {
        //console.log('formModel of globalModel----------', this.globalModel.formModel)
        if (!this.subFormItemFlag) {
          this.setValue(newFormData[this.field.options.name])
        }
      })

      this.$on('field-value-changed', (values) => {
        if (!!this.subFormItemFlag) {
          let subFormData = this.formModel[this.subFormName]
          this.handleOnChangeForSubForm(values[0], values[1], subFormData, this.subFormRowId)
        } else {
          this.handleOnChange(values[0], values[1])
        }
      })

      /* 监听重新加载选项事件 */
      this.$on('reloadOptionItems', (widgetNames) => {
        if ((widgetNames.length === 0) || (widgetNames.indexOf(this.field.options.name) > -1)) {
          this.initOptionItems(true)
        }
      })
    },

    handleOnCreated() {
      if (!!this.designState) { //设计状态不触发事件
        return
      }
      if (!!this.field.options.onCreated) {
        let customFunc = new Function(this.field.options.onCreated)
        customFunc.call(this)
      }
    },

    handleOnMounted() {
      // debugger
      if (!!this.designState) { //设计状态不触发事件
        return
      }
         // 编辑时，是否禁用当前字段
        if (!!this.field.options.disabledWhenEdit) {
          if(this.popupFormItemConfig.value =="iisEdit"){
            this.setDisabled(true)
          }
          // if(this.formModel.hasOwnProperty('CID') && !!this.formModel.CID){
          //   this.setDisabled(true)
          // } 
        }
      if (!!this.field.options.onMounted) {
        let mountFunc = new Function(this.field.options.onMounted)
        mountFunc.call(this)
      }
   
    },

    registerToRefList(oldRefName) {
      if ((this.refList !== null) && !!this.field.options.name) {
        if (this.subFormItemFlag && !this.designState) { //处理子表单元素（且非设计状态）
          if (!!oldRefName) {
            delete this.refList[oldRefName + '@row' + this.subFormRowId]
          }
          this.refList[this.field.options.name + '@row' + this.subFormRowId] = this
        } else {
          if (!!oldRefName) {
            delete this.refList[oldRefName]
          }
          this.refList[this.field.options.name] = this
        }
      }
    },

    unregisterFromRefList() {  //销毁组件时注销组件ref
      if ((this.refList !== null) && !!this.field.options.name) {
        let oldRefName = this.field.options.name
        if (this.subFormItemFlag && !this.designState) { //处理子表单元素（且非设计状态）
          delete this.refList[oldRefName + '@row' + this.subFormRowId]
        } else {
          delete this.refList[oldRefName]
        }
      }
    },
     // 扁平数据转为树状结构数据,待确认，如果有问题，可以尝试buildTree方法
    toTree(dataList,treeRowField,treeParentField) {
      //debugger
      let sonId = treeRowField
      let parentId = treeParentField
      // debugger
      //先检测是不是数组类型
      if (!Array.isArray(dataList)) {
        return [];
      }
      // JS的对象就是hash表
      const obj = {};
      dataList.forEach((item) => {
        obj[item[sonId]] = item;
      });
      const targetArr = [];
      dataList.forEach((item) => {
        const parent = obj[item[parentId]];//有pId就说明他有父亲，找到他的父亲parent
        if (parent) {  //如果他有父亲，就给他添加children属性
          parent.children = parent.children || [];
          parent.children.push(item);
        } else {  //他没有父亲，就把当前项push进去（顶层）
          targetArr.push(item);
        }
      });
     
      return targetArr;
    },
     // 扁平数据转为树状结构数据
    buildTree(dataList, treeRowField, treeParentField) {
      // debugger
       let map = {};
       dataList.forEach((node) => {
           map[node[treeRowField]] = node;
           node.children = [];
       });
       let roots = [];
       dataList.forEach((node) => {
           if (!!node[treeParentField]) {
               map[node[treeParentField]].children.push(node);
           } else {
               roots.push(node);
           }
       });
       return roots;
   },
    async initOptionItems(keepSelected) {
      if (this.designState) {
        return
      }

      if ((this.field.type === 'radio') || (this.field.type === 'checkbox')
        || (this.field.type === 'select') || (this.field.type === 'cascader')) {
        /* 首先处理数据源选项加载 */
        if (!!this.field.options.dsEnabled) {
          // 使用数据源加载数据列表项 edit by andy
          this.field.options.optionItems.splice(0, this.field.options.optionItems.length) // 清空原有选项
         // debugger
          let itemOptions= await this.loadSelectOptions()

          this.loadOptions(itemOptions)
          // ====开启 下拉树===============
          this.treeDataList =[]
          if (!!this.field.options.dataTreeEnabled) {
            // 显示标签字段
            this.defaultProps.label = (this.field.options.labelKey || 'label')
            this.treeDataList = this.toTree(itemOptions,this.field.options.treeRowField,this.field.options.treeParentField)
            //  setCurrentKey	//通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
            this.setTreeCurrentKey()
          }
          // ====开启 下拉树===============
         // debugger
          // ====原有逻辑===注释===
          // let curDSName = this.field.options.dsName
          // let curDS = getDSByName(this.formConfig, curDSName)
          // if (!!curDS) {
          //   let gDsv = this.getGlobalDsv() || {}
          //   //console.log('Global DSV is: ', gDsv)
          //   let localDsv = new Object({})
          //   overwriteObj(localDsv, gDsv)
          //   localDsv['widgetName'] = this.field.options.name
          //   let dsResult = null
          //   try {
          //     dsResult = await runDataSourceRequest(curDS, localDsv, this.getFormRef(), false, this.$message)
          //     this.loadOptions(dsResult)
          //   } catch(err) {
          //     this.$message.error(err.message)
          //   }
          // }
          // ====原有逻辑===注释===
          return;
        }
       // debugger
        /* 异步更新option-data之后globalOptionData不能获取到最新值，改用provide的getOptionData()方法 */
        const newOptionItems = this.getOptionData()
        if (!!newOptionItems && newOptionItems.hasOwnProperty(this.field.options.name)) {
          if (!!keepSelected) {
            this.reloadOptions(newOptionItems[this.field.options.name])
          } else {
            this.loadOptions(newOptionItems[this.field.options.name])
          }
        }
      }
    },
    // 设置下拉初始化功能
    async loadSelectOptions() {
     // debugger
      let itemOptions = []
      let _url = `api/MD/DataSet/GetListByDataSetId`
      let datasetId = this.useFormatParamsFn.getVFormDataSetID(this.field.options.actionName)
      let params = {
        Id: datasetId, //数据集ID
        Parameter: {
          PageIndex: 1,
          PageSize: 999999999
        }
      }

      await request['post'](_url, params).then(res => {
        // debugger
        if (res.Datas && res.Datas.length > 0) {
          if ([4, 5].includes(res.Data)) {
           // let data = this.formatDatas(JSON.parse(res.Datas))
            itemOptions = JSON.parse(res.Datas)
          } else {
            itemOptions = res.Datas//this.formatDatas(res.Datas)
          }
        } else {
          itemOptions = [{ value: "", label: "请选择" }]
        }
      })
     
      return itemOptions
    },
    // 格式化返回数据
    formatDatas(dataList){
      let options =[{value:"",label:"请选择"}]
      if(dataList && dataList.length>0){
          dataList.forEach(oldItem=>{
            let newItem ={
              value:oldItem[(this.field.options.valueKey || 'value')],
              label:oldItem[(this.field.options.labelKey || 'label')]
            }
            options.push(newItem)
            
          })
      }
      return options
    },
    refreshDefaultValue() {
       // add by andy,fix 默认值，当自定义了 勾选，或不勾选的值时
       if(this.field.type === 'switch'){
        // debugger
         if(!!this.field.options.activeValue || !!this.field.options.inactiveValue){
            this.field.options.defaultValue = !!(this.field.options.defaultValue)? this.field.options.activeValue:this.field.options.inactiveValue
         }
         this.fieldModel = this.field.options.defaultValue
         // 直接重置表单中的字段默认值
         this.formModel[this.field.options.name] = this.field.options.defaultValue
       }
      if ((this.designState === true) && (this.field.options.defaultValue !== undefined)) {
        this.fieldModel = this.field.options.defaultValue
       
      }
    },

    clearFieldRules() {
      if (!this.field.formItemFlag) {
        return
      }

      this.rules.splice(0, this.rules.length)  //清空已有
    },

    buildFieldRules() {
      if (!this.field.formItemFlag) {
        return
      }

      this.rules.splice(0, this.rules.length)  //清空已有
      if (!!this.field.options.required) {
        this.rules.push({
          required: true,
          //trigger: ['blur', 'change'],
          trigger: ['blur'],  /* 去掉change事件触发校验，change事件触发时formModel数据尚未更新，导致radio/checkbox必填校验出错！！ */
          message: this.field.options.requiredHint || this.i18nt('render.hint.fieldRequired'),
        })
      }

      if (!!this.field.options.validation) {
        let vldName = this.field.options.validation
        if (!!FormValidators[vldName]) {
          this.rules.push({
            validator: FormValidators[vldName],
            trigger: ['blur', 'change'],
            label: this.field.options.label,
            errorMsg: this.field.options.validationHint
          })
        } else {
          this.rules.push({
            validator: FormValidators['regExp'],
            trigger: ['blur', 'change'],
            regExp: vldName,
            label: this.field.options.label,
            errorMsg: this.field.options.validationHint
          })
        }
      }

      if (!!this.field.options.onValidate) {
        //let customFn = new Function('rule', 'value', 'callback', this.field.options.onValidate)
        let customFn = (rule, value, callback) => {
          let tmpFunc = new Function('rule', 'value', 'callback', this.field.options.onValidate)
          return tmpFunc.call(this, rule, value, callback)
        }
        this.rules.push({
          validator: customFn,
          trigger: ['blur', 'change'],
          label: this.field.options.label
        })
      }
    },

    /**
     * 禁用字段值变动触发表单校验
     */
    disableChangeValidate() {
      if (!this.rules) {
        return
      }

      this.rules.forEach(rule => {
        if (!!rule.trigger) {
          rule.trigger.splice(0, rule.trigger.length)
        }
      })
    },

    /**
     * 启用字段值变动触发表单校验
     */
    enableChangeValidate() {
      if (!this.rules) {
        return
      }

      this.rules.forEach(rule => {
        if (!!rule.trigger) {
          rule.trigger.push('blur')
          rule.trigger.push('change')
        }
      })
    },

    disableOptionOfList(optionList, optionValue) {
      if (!!optionList && (optionList.length > 0)) {
        optionList.forEach(opt => {
          if (opt.value === optionValue) {
            opt.disabled = true
          }
        })
      }
    },

    enableOptionOfList(optionList, optionValue) {
      if (!!optionList && (optionList.length > 0)) {
        optionList.forEach(opt => {
          if (opt.value === optionValue) {
            opt.disabled = false
          }
        })
      }
    },

    //--------------------- 组件内部方法 end ------------------//

    //--------------------- 事件处理 begin ------------------//

    emitFieldDataChange(newValue, oldValue) {
      this.$emit('field-value-changed', [newValue, oldValue])

      /* 必须用dispatch向指定父组件派发消息！！ */
      this.dispatch('VFormRender', 'fieldChange',
        [this.field.options.name, newValue, oldValue, this.subFormName, this.subFormRowIndex])
    },

    syncUpdateFormModel(value) {
      
      if (!!this.designState) {
        return
      }

      if (!!this.subFormItemFlag) {
        let subFormData = this.formModel[this.subFormName] || [{}]
        let subFormDataRow = subFormData[this.subFormRowIndex]
        subFormDataRow[this.field.options.name] = this.getDateTimeFormatVal(value)
        if(!!this.field.options.isPostSelectedText){
          subFormDataRow[this.field.options.postSelectedTextField] = this.getSelectedFieldText(value)
        }
      } else {
        this.formModel[this.field.options.name] = this.getDateTimeFormatVal(value)
       
        if(!!this.field.options.isPostSelectedText){
          this.formModel[this.field.options.postSelectedTextField] = this.getSelectedFieldText(value)
        }
      }
      //debugger
    },
    // 格式时间值
    getDateTimeFormatVal(value){
      //debugger
      let _newValAfterFormat = value
      if(this.field.type === 'date'){
        // debugger
        let _dateValForm ='YYYY-MM-DD HH:mm:ss'
        let _valueFormat = this.field.options.valueFormat
        if(!!!_valueFormat && !!value && value!=null && value!=''&& value!=undefined &&  value!='null'){
          // 默认
          _valueFormat = 'yyyy-MM-dd'
        }
        switch (_valueFormat) {
          case 'yyyy-MM-dd':
            _dateValForm ='YYYY-MM-DD'
            break;
            case 'yyyy-MM-dd HH:mm:ss':
              _dateValForm ='YYYY-MM-DD HH:mm:ss'
            break;
            case 'yyyy-MM':
              _dateValForm ='YYYY-MM'
            break;
            case 'yyyy':
              _dateValForm ='YYYY'
            break;
          default:
            break;
        }
        _newValAfterFormat = dayjs(value).format(_dateValForm)
      }
      if(_newValAfterFormat=='Invalid Date'){
        _newValAfterFormat=null
      }
      return _newValAfterFormat
    },
    // 获取下拉选中文本值
    getSelectedFieldText(id){
      const thisLabel = this.field.options.optionItems.find(item => item.value == id).label
      return thisLabel
    },
    // 下拉树 选中节点 触发事件
    handleNodeClick(node) {
       let currentNodeKeyValue  = node[(this.field.options.valueKey || 'value')] // 选中值
       let currentNodeLabelValue  = node[(this.field.options.labelKey || 'label')] // 选中文本
      // debugger
       if((this.field.type === 'select') && this.field.options.multiple){
        if(!Array.isArray(this.treeSelectedKey)){
           this.treeSelectedKey =[]
        }
        this.fieldModel.push(currentNodeLabelValue)
        this.treeSelectedKey.push(currentNodeKeyValue)
       }else{
         this.fieldModel =  currentNodeLabelValue
         this.treeSelectedKey= currentNodeKeyValue
       }
     

       this.handleChangeEvent(this.treeSelectedKey)
       let elSelectRef = this.$refs[this.field.options.name]
       if(elSelectRef){
         // 关闭下拉框
         elSelectRef.blur();
       }
      
   },
    handleChangeEvent(value) { 
    //  debugger /* input的清除输入小按钮会同时触发handleChangeEvent、handleInputCustomEvent！！ */
     // console.log("=handleChangeEvent==this.field.type===="+this.field.type+"  value:"+value)
     if (!!this.designState) { //设计状态不触发事件
        return
      }
      //========触发过滤数据==========add by andy==========================
      try {
        this.publicAttribute.value = value // 赋值 对外开发属性值
       // debugger
        switch (this.field.type) {
          case "checkbox":
            this.publicAttribute.valueString = value.toString()
            break;
            case "time-range":
              this.publicAttribute.valueString = value.toString()
              break;
              case "date-range":
                this.publicAttribute.valueString = value.toString()
                break;     
        }
        let params ={
          formCtrlName:this.field.type,
          contrlName:this.field.options.name,
          value:value,// 过滤查询字段
          triggerCtrlNames:this.field.options.triggerCtrlNames, //重点： 触发控件名称
        }
        this.useHandleVFormEventFn.reSearchData(params);
      } catch (error) {
        console.error("=handleChangeEvent==this.field.type===="+this.field.type+"  value:"+value+" error"+error)
      }
     
      //========触发过滤数据==========add by andy==========================
      this.syncUpdateFormModel(value)
      this.emitFieldDataChange(value, this.oldFieldValue)

      //number组件一般不会触发focus事件，故此处需要手工赋值oldFieldValue！！
      this.oldFieldValue = deepClone(value)  /* oldFieldValue需要在initFieldModel()方法中赋初值!! */

      /* 主动触发表单的单个字段校验，用于清除字段可能存在的校验错误提示 */
      this.dispatch('VFormRender', 'fieldValidation', [this.getPropName()])
    },

    handleFocusCustomEvent(event) {
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      this.oldFieldValue = deepClone(this.fieldModel)  //保存修改change之前的值

      if (!!this.field.options.onFocus) {
        let customFn = new Function('event', this.field.options.onFocus)
        customFn.call(this, event)
      }
    },

    handleBlurCustomEvent(event) {
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      if (!!this.field.options.onBlur) {
        let customFn = new Function('event', this.field.options.onBlur)
        customFn.call(this, event)
      }
    },

    handleInputCustomEvent(value) {
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      this.syncUpdateFormModel(value)

      /* 主动触发表单的单个字段校验，用于清除字段可能存在的校验错误提示 */
      this.dispatch('VFormRender', 'fieldValidation', [this.getPropName()])

      if (!!this.field.options.onInput) {
        let customFn = new Function('value', this.field.options.onInput)
        customFn.call(this, value)
      }
    },

    emitAppendButtonClick() {
      if (!!this.designState) { //设计状态不触发点击事件
        return
      }

      if (!!this.field.options.onAppendButtonClick) {
        let customFn = new Function(this.field.options.onAppendButtonClick)
        customFn.call(this)
      } else {
        /* 必须调用mixins中的dispatch方法逐级向父组件发送消息！！ */
        this.dispatch('VFormRender', 'appendButtonClick', [this])
      }
    },

    handleOnChange(val, oldVal) {  //自定义onChange事件
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      if (!!this.field.options.onChange) {
        let changeFn = new Function('value', 'oldValue', this.field.options.onChange)
        changeFn.call(this, val, oldVal)
      }
    },

    handleOnChangeForSubForm(val, oldVal, subFormData, rowId) {  //子表单自定义onChange事件
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      if (!!this.field.options.onChange) {
        let changeFn = new Function('value', 'oldValue', 'subFormData', 'rowId', this.field.options.onChange)
        changeFn.call(this, val, oldVal, subFormData, rowId)
      }
    },

    handleButtonWidgetClick() {
      if (!!this.designState) { //设计状态不触发点击事件
        return
      }

      if (!!this.field.options.onClick) {
        let customFn = new Function(this.field.options.onClick)
        customFn.call(this)
      } else {
        this.dispatch('VFormRender', 'buttonClick', [this]);
      }
    },

    remoteQuery(keyword) {
      if (!!this.designState) { //设计状态不触发事件
        return
      }

      if (!!this.field.options.onRemoteQuery) {
        let remoteFn = new Function('keyword', this.field.options.onRemoteQuery)
        remoteFn.call(this, keyword)
      }
    },

    //--------------------- 事件处理 end ------------------//

    //--------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */

    getFormRef() { /* 获取VFrom引用，必须在VForm组件created之后方可调用 */
      return this.refList['v_form_ref']
    },

    getWidgetRef(widgetName, showError) {
      // debugger
      let foundRef = this.refList[widgetName]
      if (!foundRef && !!showError) {
        this.$message.error(this.i18nt('render.hint.refNotFound') + widgetName)
      }
      return foundRef
    },

    getFieldEditor() { //获取内置的el表单组件
      return this.$refs['fieldEditor']
    },

    /*
      注意：VFormRender的setFormData方法不会触发子表单内field-widget的setValue方法，
      因为setFormData方法调用后，子表单内所有field-widget组件已被清空，接收不到setFormData事件！！
    * */
    setValue(newValue) {
      /* if ((this.field.type === 'picture-upload') || (this.field.type === 'file-upload')) {
        this.fileList = newValue
      } else */ if (!!this.field.formItemFlag) {
        let oldValue = deepClone(this.fieldModel)
        this.fieldModel = newValue
        this.initFileList()

        this.syncUpdateFormModel(newValue)
        try {
          this.emitFieldDataChange(newValue, oldValue)
        } catch (error) {
          console.warn("=fieldMixin===setValue===emitFieldDataChange==error")
        }
        
      }
    },

    getValue() {
      /* if ((this.field.type === 'picture-upload') || (this.field.type === 'file-upload')) {
        return this.fileList
      } else */ {
        return this.fieldModel
      }
    },

    resetField() {
      if (!!this.subFormItemFlag) { //跳过子表单组件
        return
      }

      let defaultValue = this.field.options.defaultValue
      this.setValue(defaultValue)

      //清空上传组件文件列表
      if ((this.field.type === 'picture-upload') || (this.field.type === 'file-upload')) {
        this.$refs['fieldEditor'].clearFiles()
        this.fileList.splice(0, this.fileList.length)
      }
    },

    setWidgetOption(optionName, optionValue) { //通用组件选项修改API
      if (this.field.options.hasOwnProperty(optionName)) {
        this.field.options[optionName] = optionValue
        //TODO: 是否重新构建组件？？有些属性修改后必须重新构建组件才能生效，比如字段校验规则。
      }
    },

    setReadonly(flag) {
      this.field.options.readonly = flag
    },

    setDisabled(flag) {
      this.field.options.disabled = flag
    },

    setAppendButtonVisible(flag) {
      this.field.options.appendButton = flag
    },

    setAppendButtonDisabled(flag) {
      this.field.options.appendButtonDisabled = flag
    },

    setHidden(flag) {
      this.field.options.hidden = flag

      if (!!flag) {  //清除组件校验规则
        this.clearFieldRules()
      } else {  //重建组件校验规则
        this.buildFieldRules()
      }
    },

    setRequired(flag) {
      this.field.options.required = flag
      this.buildFieldRules()
    },

    setLabel(newLabel) {
      this.field.options.label = newLabel
    },

    focus() {
      if (!!this.getFieldEditor() && !!this.getFieldEditor().focus) {
        this.getFieldEditor().focus()
      }
    },

    clearSelectedOptions() {  //清空已选选项
      if ((this.field.type !== 'checkbox') && (this.field.type !== 'radio') && (this.field.type !== 'select')) {
        return
      }

      if ((this.field.type === 'checkbox') ||
        ((this.field.type === 'select') && this.field.options.multiple)) {
        this.treeSelectedKey=[]
        this.fieldModel = []
      } else {
        this.fieldModel = ''
        this.treeSelectedKey =''
      }
    },

    /**
     * 加载选项，并清空字段值
     * @param options
     */
    loadOptions(options) {
      //debugger
      /*
      this.field.options.optionItems = deepClone(options)
      //this.clearSelectedOptions()  //清空已选选项
       */

      this.field.options.optionItems = translateOptionItems(options, this.field.type,
        this.field.options.labelKey || 'label',
        this.field.options.valueKey || 'value')
         // 设置默认选中第一个
        //  单选时
        // debugger
        if((this.field.type === 'select') && !this.field.options.multiple){
          if(this.field.options.optionItems.length>1){
               this.fieldModel_Computed = this.field.options.optionItems[1].value
               this.setValue(this.fieldModel_Computed)
          }
        }
    },

    /**
     * 重新加载选项，不清空字段值
     * @param options
     */
    reloadOptions(options) {
      this.field.options.optionItems = translateOptionItems(options, this.field.type,
        this.field.options.labelKey || 'label',
        this.field.options.valueKey || 'value')
    },

    /**
     * 返回radio/checkbox/select/cascader的选项数据
     * @returns 选择项数组
     */
    getOptions() {
      return this.field.options.optionItems
    },

    disableOption(optionValue) {
      this.disableOptionOfList(this.field.options.optionItems, optionValue)
    },

    enableOption(optionValue) {
      this.enableOptionOfList(this.field.options.optionItems, optionValue)
    },

    /**
     * 返回选择项
     * @returns {*}
     */
    getOptionItems() {
      return this.field.options.optionItems
    },

    setUploadHeader(name, value) {
      this.$set(this.uploadHeaders, name, value)
    },

    setUploadData(name, value) {
      this.$set(this.uploadData, name, value)
    },

    setToolbar(customToolbar) {
      this.customToolbar = customToolbar
    },

    /**
     * 是否子表单内嵌的字段组件
     * @returns {boolean}
     */
    isSubFormItem() {
      return this.subFormItemFlag
    },

    /**
     * 是否子表单内嵌的字段组件
     * @returns {boolean}
     */
    isSubFormField() {
      return this.subFormItemFlag
    },

    /**
     * 设置或取消设置字段只读查看模式
     * @param readonlyFlag
     */
    setReadMode(readonlyFlag = true) {
      this.fieldReadonlyFlag = readonlyFlag
    },

    /**
     * 动态增加自定义css样式
     * @param className
     */
    addCssClass(className) {
      if (!this.field.options.customClass) {
        this.field.options.customClass = [className]
      } else {
        this.field.options.customClass.push(className)
      }
    },

    /**
     * 动态移除自定义css样式
     * @param className
     */
    removeCssClass(className) {
      if (!this.field.options.customClass) {
        return
      }

      let foundIdx = -1
      this.field.options.customClass.map((cc, idx) => {
        if (cc === className) {
          foundIdx = idx
        }
      })
      if (foundIdx > -1) {
        this.field.options.customClass.splice(foundIdx, 1)
      }
    },

    //--------------------- 以上为组件支持外部调用的API方法 end ------------------//

  }
}
