<template>
    <el-form-item label="设为被动加载">
      <el-switch v-model="optionModel.isPassiveDataLoad"></el-switch>
    </el-form-item>
  </template>
  
  <script>
    import i18n from "@/utils/i18n";
    export default {
      name: "passiveDataLoad-editor",// 数据是否被动加载（如：点击后再加载），反之，页面打开时，立即加载数据
      mixins: [i18n],
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
    }
  </script>
  
  <style scoped>
  
  </style>
  