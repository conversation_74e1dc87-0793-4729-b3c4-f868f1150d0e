<template>
    <div class="selectTableRender">
        <!-- @focus="loadData()" @filter-method="remoteFilterMethod"-->
        <el-select :filterable="true" :remote="false"  :remote-method="debouncedFn_Filter" ref="elSelectRef" @change="changeEvent" v-bind="$attrs" v-model="currentItem" v-on="$listeners"
            :loading="loading" placeholder="请选择">
            <el-option v-for="item in selectDataList" :key="item.value" :label="item.label" :value="item.value + ''">
            </el-option>
        </el-select>
    </div>
</template>
<script>
const cloneDeep = require("clone-deep");
import { useDebounceFn } from '@vueuse/core'
import config from '@/config'
import emitter from '@/libs/mitt'
import request from '@/libs/request'
export default {
    name: 'selectTableRender',
    components: {},
    props: {
        // 编辑状态是否变更>>仅用于表格talbe中 
        // updateFlag:{
        //     type: [String,Boolean],
        //     default: "",
        // },    
        // 是否立即加载数据
        //  isLoadNow:{
        //      type:Boolean,
        //      default:false,
        //  },    
        //    // 每次点击都查询
        //  reSearch:{
        //      type:Boolean,
        //      default:false,
        //  },  
        //   // 表格信息>>dataFrom=talbe 才有效
        //   tableInfo:{
        //       type:Object,
        //       default(){
        //           return {}
        //       } 
        //   },
          // 当前编辑字段名称
          field: {
            type: String,
            default: ""
        },
        // 数据来信:表单form,表格talbe
        dataFrom: {
            type: String,
            default: "form"
        },
        // 当前字段:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
        // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
        dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
        // 当前字段:value
        currentValue: {
            type: [String, Number],
            default: ""
        },
        // 当前字段:Text
        currentValueText: {
            type: [String, Number],
            default: ""
        },
        // 是否可用
        disabled: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            debouncedFn_Filter:null,
            config: config,
            //value1: [],
            loading: false,
            prefixIcon: "", // 头部图标,用于是否加载中 显示
            currentItem: '',// 当前选择项
            currentItemText: '',// 当前选择项描述
            selectDataList: [],// 当前列表数据
            selectDataListOriginClone: [],// 当前列表数据 克隆
        }
    },
    computed: {
        dataSourceID() {
            let _id = ""
            try {
                _id = this.dataSearchOptions.dbId
            } catch (error) {
                _id = ""
            }
            return _id
        },
        // 其它配置参数，如 联动参数
        otherParams() {
            let _otherParams = null
            try {
                if (this.paramsItem?.fieldExtension) {
                    let fieldExtension = JSON.parse(this.paramsItem.fieldExtension)
                    _otherParams = fieldExtension.otherParams
                }

            } catch (error) {
                _otherParams = null
            }
            return _otherParams
        },
        // 其它配置参数，是否联动下拉
        iisAssociated() {
            let _iisAssociated = false
            try {
                //debugger
                if (this.paramsItem?.fieldExtension) {
                    let fieldExtension = JSON.parse(this.paramsItem.fieldExtension)
                    _iisAssociated = fieldExtension.iisAssociated
                }

            } catch (error) {
                _iisAssociated = false
            }
            return _iisAssociated
        }

    },
    watch: {
        currentValue(n, o) {
            // 连续新增时，需要清空数据
            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
                //  this.currentItemText =''
            }
            // this.$nextTick(() => {
            //     this.loadData()
            // })
        },
        //  currentValueText(n,o){
        //      //debugger
        //     if(!!n){
        //        this.currentItemText = n
        //     }else{
        //         // 清空数据
        //         this.currentItemText =''
        //     }
        // }

    },
    beforeMount() {
        //  移除监听
        emitter.off("associatedDropdown")
    },
    mounted() {
        this.debouncedFn_Filter = useDebounceFn((params) => {
            this.remoteFilterMethod(params)
        }, 600)
        //首次赋值
        this.$nextTick(() => {
          
            this.loadData()
        })
        // 注册监听 标准弹框事件
        emitter.on("associatedDropdown", (params) => {
            this.associatedDropdownFn(params)
        })
    },
    destroyed(){
        // 组件销毁 移除监听
        emitter.off("associatedDropdown")
    },
    methods: {
        // 触发调用联动事件
        associatedDropdownFn(params) {
            if(params.field == this.field){
                return
            }
            if(this.iisAssociated){
                //debugger
              //console.log("触发调用联动事件this.selectDataList",JSON.stringify(this.selectDataList))
                if(this.otherParams && this.otherParams.includes(params.field)){
                    if(params.statusType=='change'){
                        this.currentItem=""
                    }
                    // console.log("触发调用联动事件 params",params)
                     console.log("字段名称：",this.field)
                     let copyOtherParams = this.otherParams
                     //filter==> key:value
                     let filterVal = copyOtherParams.replace('@'+params.field,params.value)   
                    // console.log("过滤字段Value：",filterVal)
                     let filterDataList = this.selectDataListOriginClone.filter(item=>{
                            let filterValArr= filterVal.split(":")
                           // debugger
                            if(item[filterValArr[0]]==filterValArr[1]){
                                return item
                            }
                     })
                     //this.selectDataList =[]
                     if(filterDataList.length>0){
                        this.selectDataList =this.formatData(filterDataList)
                     }
                     // debugger
                    //  if(params.field == this.field){
                    //       // 继续过滤它的子集
                       
                    //  }
                    let currentParams = {
                                field:this.field,
                                value:this.currentItem,
                                text:"",
                                statusType:params.statusType
                            }
                            setTimeout(()=>{
                                emitter.emit("associatedDropdown",currentParams)
                            },300)
                     
                }else{
                    if(this.field == "CITEM_ID" || this.field=='CITEM_CODE_CATL'){
                        console.log("else 字段名称：",this.field)
                    }
                    
                }
               
               // this.loadData(true, params)
            }
        },
        // 格式化数据成下拉框所需的
        formatData(dataList = []) {
            // debugger
            let newData = []
            let matchField = this.paramsItem.matchField
            // let matchField = "bpArtId,bpArtCode"
            let keyValueArr = matchField.split(',')
            //debugger
            if (dataList && dataList.length > 0) {
                dataList.forEach((item,index) => {
                    // 限制3000
                    // if(index>3000){
                    //     return
                    // }
                    let newItem = {
                        value: item[keyValueArr[0]] + "",
                        label: item[keyValueArr[1]],
                        row:item,
                    }
                    newData.push(newItem)
                })
            }
            return newData
        },
        remoteFilterMethod(query){
            // debugger 
            if(query!=""){
               // console.log("==remoteFilterMethod===")
                try {
                    let matchField = this.paramsItem.matchField
                    let keyValueArr = matchField.split(',')
                    let searchKey = keyValueArr[1]
                    this.loadData(searchKey,query)
                } catch (error) {
                    
                }
            }else{
                if (this.selectDataList.length == 0) {
                   this.loadData()
                }
            }
           
           
        },
        // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
        loadData(searchKeyVal="",searchValue="") {
            //debugger
            if (this.selectDataList.length > 1 && searchValue=="") {
                return
            }
            this.loading = true
            let sourceKey = this.paramsItem.sourceKey
            //debugger
            let searchParams = this.paramsItem.searchParams

            let _url = this.config.moduleSub + "ComboSource"
            let params = {
                SourceType: "Table", //Dict Table Tree
                SourceID: this.dataSourceID,
                Params: {
                    sourceKey,// 表名
                    filter: {} // 字段过滤参数
                }
            };
            // 远程搜索
            if(!!searchKeyVal && !!searchValue){
                params.Params.filter[searchKeyVal] = searchValue
            }
            // 配置参数过滤
            if (!!searchParams) {
                // 参数分解>>"user:andy,age:18" 转换为JSON
                if (searchParams.includes(',')) {
                    //多个参数
                    let searchParamsArr = searchParams.split(',')
                    searchParamsArr.forEach(item => {
                        let paramItem = item.split(':')
                        params.Params.filter[paramItem[0]] = paramItem[1]
                    })
                } else {
                    //单个参数
                    let paramItem = searchParams.split(':')
                    params.Params.filter[paramItem[0]] = paramItem[1]
                }
            }
            let _self = this
            request['post'](_url, params).then(res => {
                //debugger
                if (res.Success) {
                    let resData = res.Datas
                    _self.loading = false
                    _self.selectDataList = _self.formatData(resData)
                    _self.selectDataListOriginClone = cloneDeep(resData)
                    // debugger
                    _self.$nextTick(() => {

                        if (!!_self.currentValue) {
                                // 初始化 默认值
                                _self.currentItem = _self.currentValue + ''
                                //
                                let inistParams = {
                                    field:_self.field,
                                    value:  _self.currentItem,
                                    text: "",
                                    statusType:'init'
                                }
                                if(!!_self.iisAssociated){
                                    // 联动初始化>>触发联动事件
                                    setTimeout(()=>{
                                        emitter.emit("associatedDropdown",inistParams)
                                    },300)
                                }
                        }
                    })

                }
            })
        },
        // 选择改变回调事件
        changeEvent(val) {
            let _self = this
            this.$nextTick(() => {
                let params = {
                    field:_self.field,
                    value: val,
                    text: _self.$refs["elSelectRef"].selectedLabel,
                    statusType:'change'
                }
                _self.$emit("changeEvent", params)
                if(!!_self.iisAssociated){
                     // 触发联动事件
                    //  if(params.field == this.field){

                    //  }
                     setTimeout(()=>{
                        emitter.emit("associatedDropdown",params)
                    },300)
                    
                }
                 
            })
        }
    }
}
</script>


<style lang="scss"></style>