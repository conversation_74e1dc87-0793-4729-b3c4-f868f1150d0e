<template>
  <vxeGridTemplate row-id="ID" ref="nvxeTableRef" @checkbox-change="checkboxChangeEvent"
    @checkbox-all="checkboxAllEvent" @edit-closed="editClosedEvent" @pageChange='pageChangeEvent' @scroll="scrollEvent"
    @current-change="currentChangeEvent" :cell-style="!!cellStyle ? cellStyle : cellStyleLocal"
    :row-style="!!rowStyle ? rowStyle : rowStyleLocal" :data="gridDefaultOptions.dataDefault"
    :columns="gridDefaultOptions.columns" :border="gridDefaultOptions.border" :size="gridDefaultOptions.size"
    :stripe="gridDefaultOptions.stripe" :resizable="gridDefaultOptions.resizable"
    :show-overflow="gridDefaultOptions.showOverflow" :show-footer-overflow="gridDefaultOptions.showFooterOverflow"
    :highlight-current-row="gridDefaultOptions.highlightCurrentRow"
    :show-header-overflow="gridDefaultOptions.showHeaderOverflow" :keep-source="keepSource"
    :highlight-hover-row="gridDefaultOptions.highlightHoverRow" :filter-config="gridDefaultOptions.filterConfig"
    :toolbar-config="gridDefaultOptions.toolbarConfig" :loading="gridDefaultOptions.loading || loading"
    :tree-config="localTreeConfig" :params="gridDefaultOptions.params" :mouse-config="{ selected: true }"
    :keyboard-config="{ isArrow: true, isEsc: true, isEnter: true, isTab: true, isDel: true, isEdit: true, isChecked: true }"
    :print-config="{}" :import-config="{}" :export-config="{}" :tabelConfigs="tabelConfigs"
    :show-footer="showFooterSetting" :footer-method="footerMethod" :dataSearchOptions="dataSearchOptions"
    :originColumns="originColumns" :edit-config="editConfig" :layoutLevel="layoutLevel" :menu-config="menuConfig"
    @menu-click="contextMenuClickEvent" :scroll-y="{enabled: true, gt: 40}"  v-bind="$attrs" v-on="$listeners">
    <!-- 重写 bottom 并且默认值分页控件，可以重写-->
    <template v-slot:bottom>
      <template v-if="$slots.bottom">
        <!-- 自定义插槽 -->
        <slot name="bottom"></slot>
      </template>
      <template v-else>
        <!-- 默认显示 分页控件 && 底部按钮  showFooterButtons footerButtons-->
        <div class="flex items-center justify-between">
          <div v-show="!!showFooterButtons" style="height:45px;" class="flex items-center">
            <div style="margin-left:8px;">
              <tableFooterActionBtttons :footerButtons="footerButtons"></tableFooterActionBtttons>
            </div>
          </div>
          <div></div>
          <div>
            <!-- :layouts="layouts" 
             :layouts="
        ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"-->
            <vxe-pager v-if="showPagination" align="right" size="small"
              :current-page.sync="tabelConfigs.footerConfig.pageConfig.currentPage"
              :page-size.sync="tabelConfigs.footerConfig.pageConfig.pageSize"
             
               :layouts="layouts"
              :pager-count="pagerCount"
              
              :total="Number(tabelConfigs.footerConfig.pageConfig.total)" @page-change="pageChangeEvent">
            </vxe-pager>
          </div>
        </div>

      </template>
    </template>
    <template v-slot:top>
      <template v-if="$slots.top">
        <slot name="top"></slot>
      </template>
    </template>
    <template v-slot:operate="{ row }">
      <template v-if="$scopedSlots.operate">
        <!-- 自定义 操作 按钮 -->
        <slot :row="row" name="operate"></slot>
      </template>
      <template v-else>
      
       
        <div v-if="operationButtons.length > 0 || dataSearchOptions.LuckSheet_Export" class="flex w-full justify-center items-center">
           <!-- <div>导出luckySheet模板</div> -->
          <el-link :underline="false" v-if="!!dataSearchOptions.LuckSheet_Export && !!dataSearchOptions.LuckSheet_templateNo" style="margin-right:10px;font-size: 12px;" 
              @click.native="LuckSheet_ExportFn(row)"
              :key="index" type="primary">
            {{ !!dataSearchOptions.LuckSheet_ExportTitle?dataSearchOptions.LuckSheet_ExportTitle:'导出模板' }}
            </el-link>
          <template v-for="(item, index) in operationButtons">
            <el-link :underline="false" style="margin-right:10px;font-size: 12px;" :icon="item.iconClass"
              :size="item.size" v-if="getShowOperationButtons(row, item)" @click.native="handleEventByCsofti(row, item)"
              :key="index" :type="(!!item.textColor ? item.textColor : `primary`)">{{ item.label
              }}</el-link>
          </template>
        </div>

      </template>
    </template>
    <!-- <template #inputBox_filter="{ column, $panel }">
          <input type="type" v-for="(option, index) in column.filters" :key="index" v-model="option.data" @input="$panel.changeOption($event, !!option.data, option)" @keyup.enter="enterFilterEvent({ column, $panel })">
    </template> -->
    <template v-slot:dragColumn="{ row }">
      <template v-if="$scopedSlots.dragColumn">
        <!-- 拖拽列 操作 按钮 -->
        <slot :row="row" name="dragColumn"></slot>
      </template>
      <template v-else>
        <span class="drag-btn">
          <i class="vxe-icon-sort"></i>
        </span>
      </template>
    </template>
  </vxeGridTemplate>
</template>
<script>
// v3.7+默认关闭虚拟滚动，需手动 enabled 开启或者 setup 全局开启
import dayjs from 'dayjs'
//import { useDebounceFn } from '@vueuse/core'
// const AsyncFunction = Object.getPrototypeOf(async function () {}).constructor;
import vxeGridTemplate from "@/components/override/vxe-table/templates/vxe-grid-template.vue";
//import vxeGridTemplate from "@/components/override/vxe-table/templates/vxe-table";
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import { useHandleVFormComponents } from "@/hooks/useHandleVFormComponents"
import { handleReportSelect } from "@/components/override/vxe-table/renderConfig/datahandle/handleReportSelect"
import { handleReportSelectTable } from "@/components/override/vxe-table/renderConfig/datahandle/handleReportSelectTable"
import tableFooterActionBtttons from "@/components/basic/tableFooterActionBtttons.vue"
import Sortable from '@/libs/Sortable.min.js'
import request from '@/libs/request'
import emitterFn from '@/libs/mitt'
import { trim } from "lodash-es";
// import { deepClone } from '@/utils/util';
const _loadDataByPartConfig = {
  pageIndex: 1,// 当前页码
  pageSize: 100, // 每页条数
  totalPages: 0,  // 表格总页数
  showTip: false, // 是否已经提示到底了
}
const cloneDeep = require("clone-deep");
const XEUtils = require('xe-utils')
export default {
  name: "nvxe-grid",
  components: { vxeGridTemplate, tableFooterActionBtttons },
  // 注入列表页面整体实体
  inject: ['getPageInstance','sourceVFormRenderState'],
  props: {
    
    pagerCount: {
      type: [String,Number],
      default: 7
    },
    layouts:{
      type: Array,
      default() {
        return ['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total'];
      },
    },
    // 控件 上级ref名称 add by andy
    refName: {
      type: String,
      default: ""
    },
    // 控件来源父集 add by andy
    sourceVFormRender: {
      type: String,
      default: ""
    },
    //field: Object,
    designer: Object,
    formConfig: Object,
    designState: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    treeConfigCustom: {
      type: Object,
      default() {
        return {
          //transform: false, rowField: 'CID', parentField: 'CPARENT_ORG_ID' // 父级组织ID 所属上级ID
        };
      },

    },
    // 单元格添加样式
    cellStyle: {
      type: Function,
    },
    rowStyle: {
      type: Function,
    },
    // 参数>>主要用于子表
    params: {
      type: Object,
      default() {
        return {};
      },
    },
    // 树结构>>树节点
    columnsTreeNode: {
      type: String,
      default: "",
    },
    // 查询返回多集合时候，确定选中的集合
    dataSetSelectedModel: {
      type: String,
      default: "",
    },
    //  查询表格列头信息 表单名称
    searchColumnsName: {
      type: String,
      default: "",
    },
    // 层级级别 1:主表，2:从表，3:孙表
    layoutLevel: {
      type: Number,
      default: 1,
    },
    // 表格列头原始查询信息(主要布局保存时需要用到原始数据) 用于保存布局
    originColumns: {
      type: Object,
      default() {
        return {
          ChildTables: [],
          MainTable: {
            TableDetail: [],
            TableHeader: {}
          }
        };
      }
    },
    // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
    dataSearchOptions: {
      type: Object,
      default() {
        return {};
      },
    },
    // 是否需要格式化列头信息,默认需要格式化
    needFormatColunms: {
      type: Boolean,
      default: false,
    },
    requstConfig: {
      type: Object,
      default() {
        return {
          // postUrl: "",
          // actionName: "Page",
          // postParams: {},
        };
      },
    },
    // 是否分页，默认显示 /是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 是否显示序号列，默认显示
    showSeqColumn: {
      type: Boolean,
      default: true,
    },
    // 默认不显示自带的右键导出,勾选开启自带的带出功能
    showContextMenu_Export: {
      type: Boolean,
      default: false,
    },
    //  导出文件的父控件名称
    showContextMenu_Export_fileNameCtrl: {
      type: String,
      default: "",
    },
    // 是否显示勾选框列，默认不显示
    showCheckBoxColumn: {
      type: Boolean,
      default: false,
    },

    // 是否显示底部按钮，默认不显示
    showFooterButtons: {
      type: Boolean,
      default: false,
    },
    // 底部按钮 列表
    footerButtons: {
      type: Array,
      default() {
        return []
      },
    },
    // 是否显示操作列，默认不显示
    showOperationColumn: {
      type: Boolean,
      default: false,
    },
    operationColumn: {
      type: Array,
      default() {
        return [{ showOverflow: true, fixed: 'left', slots: { default: 'operate' }, visible: true, title: '操作', headerAlign: 'center', align: 'center', width: 200 }]
      },
    },
    // 操作列 按钮项组
    operationButtons: {
      type: Array,
      default() {
        return []
      },
    },
    // 重写>> 表格列头,默认为空
    columns: {
      type: Array,
      default() {
        return [];
      },
    },
    // 是否显示拖拽列，默认不显示
    showDragColumn: {
      type: Boolean,
      default: false,
    },
    //重写>> 表格数据,默认为空
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    // 三方插件配置 
    tabelConfigs: {
      type: Object,
      default() {
        return {
          rowId: 'CID',
          menuConfig: {}, // 右键菜单配置
          footerConfig: {
            leftToolShow: true, // 是否显示左侧工具栏列筛选
            rightToolShow: true, // 是否显示右侧工具栏分页
            pageConfig: { // 分页配置
              total: 0,
              currentPage: 1,
              pageSize: 10,
            }
          }
        };
      },
    },
  },
  data() {
    return {
      renderRowConfig: {
        isMatch: false,
        fieldRules: null,
      },
      loadDataByPartConfig: Object.assign({}, _loadDataByPartConfig),
      originResData: [], // 数据过多，且不分页时使用,存在原始数据列表
      isLoadDataByPart: false,// 是否启用分部加载，数据过多，且不分页时使用
      keepSource: false, // 只有启用编辑时才启用, 保持原始值的状态，被某些功能所依赖，比如编辑状态、还原数据等（开启后影响性能，具体取决于数据量）
      showFooterSetting: false,// 是否显示汇总行信息
      summaryFieldList: [],//需要汇总的字段列表
      handleReportSelectFn: handleReportSelect(this),
      handleReportSelectTableFn: handleReportSelectTable(this),
      // 开启编辑功能的配置
      editConfig: {
        //trigger: 'click', mode: 'cell', showStatus: true
      },
      publicAttribute: {
        row: null, // 当前选中行
        pageIndex: 1,// 当前页码
        pageSize: 10, // 每页条数
        totalRows: 0,  // 表格总条数
        tableData: [],// 当前表格数据
        selectedRows: [],// 当前选择或选中行
        selectedRowsCIDS: [],// 当前选择或选中行CIDS
        checkboxRecords: [],// 获取当前已选中的行数据
      },// 对外暴露属性值
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
      useHandleVFormComponentsFn:useHandleVFormComponents(this),
      currentRow: null,// 当前选中行
      refInstance: null,// 表格单实例
      // 表格默认配置属性
      gridDefaultOptions: {
        dataDefault: [],
        size: "medium",
        border: true,
        stripe: true,
        resizable: true,
        showOverflow: true,
        showFooterOverflow: true,
        showHeaderOverflow: true,
        highlightCurrentRow: true,
        highlightHoverRow: true,
        filterConfig: {},
        loading: false,
        params: {},
        height: 0,
        align: "left",
        columns: [],
        data: [],
      },
      // 设置 操作列 模板
      checkBoxColumn: [{ fixed: 'left', type: 'checkbox', headerAlign: 'center', align: 'center', width: 60 }],
      // 表格树开启配置
      //localTreeConfig: { transform: false, rowField: 'CID', parentField: 'CPARENT_ORG_ID' },
      localTreeConfig: { children: 'children', expandAll: false },
      // 设置 操作列 模板
      seqColumn: [{ showOverflow: true, fixed: 'left', type: 'seq', visible: true, title: '#', headerAlign: 'center', align: 'center', width: 60 }],
      // 可以重写覆盖，参考 formSetting.vue
      // operationColumn:[ {showOverflow: true, fixed:'left', slots: { default: 'operate' }, visible: true, title:'操作', headerAlign:'center',align:'center', width:200}],
      // 拖拽列 fixed: 'left',
      dragColumn: [{ slots: { default: 'dragColumn' }, visible: true, title: '#', headerAlign: 'center', align: 'center', width: 60 }],
      menuConfig: {

        body: {
          options: [
            [
              { code: 'myExport', name: '打开高级导出弹框', prefixIcon: 'vxe-icon-download', visible: true, disabled: false },
              //{ code: 'hostExport', name: '使用模板服务器导出', prefixIcon: 'vxe-icon-download', visible: false, disabled: false }
            ]
          ]
        },

        visibleMethod: this.visibleMethod
      },
    };
  },
  computed: {
    pageInstance() {
      // 获取列表示例
      return this.getPageInstance()
    },
    // 拖拽列保存API接口
    activeDragRowApiUrl(){
      let _url =""
       try {
        _url =this.dataSearchOptions.activeDragRowApiUrl
       } catch (error) {
        _url =""
       }
       return _url
    }
  },
  watch: {

    // 监听，表单设计 切换时，刷新列头
    showOperationColumn(n, o) {
      this.initColumns();
    },
    // 监听，表单设计 切换时，刷新列头
    showSeqColumn(n, o) {
      this.initColumns();
    },
    // 监听，表单设计 切换时，刷新列头
    showCheckBoxColumn(n, o) {
      this.initColumns();
    },
    // 监控参数变化，主要用于从表数据查询
    params: {
      handler(n, o) {

        this.reloadInitData(n, o);
      },
      deep: true,
      immediate: false,
    },
    // 非查询，直接赋值（表头）数据
    columns: {
      handler(n, o) {
        this.initColumns();
      },
      deep: true,
      immediate: false,
    },
    // 非查询，直接赋值（表格）数据 和树发生冲突
    data: {
      handler(n, o) {
        console.log("data change... nvxe-gird")
        //this.initData();
      },
      deep: true,
      immediate: false,
    },
    // 是否设计状态
    designState(n, o) {
      this.designStateEvent()
    }
  },
  beforeMount() {
    //  移除监听 触发监听 emitterFn.emit("sumTableRowData", state)
    emitterFn.off("sumTableRowData")
    //emitterFn.off("afterLoadData")
  },
  mounted() {
    // 首次加载初始化
    this.initColumns();
    // 是否被动加载数据
    if (!!!this.dataSearchOptions.isPassiveDataLoad) {
      //debugger
      this.initData()
    }

    // 注册监听 
    emitterFn.on("sumTableRowData", (params) => {
      // debugger
      if (this.refName == params.tableName) {
        //console.log("=====emitterFn.on====currentTableRefName==refName===="+this.refName)
        this.handleCustomSumData(params)
      }

    })
  },
  destroyed() {
    // 移除监听 
    emitterFn.off("sumTableRowData")
    let tempData = {
      key: this.refName,
      value: []
    }
    this.$store.commit("set_ctrlShareData", tempData)
  },
  methods: {
    LuckSheet_ExportFn(row){
      try {
        let _templateNo = this.dataSearchOptions.LuckSheet_templateNo
        if(_templateNo){
          if(this.dataSearchOptions.LuckSheet_ExportType=='PDF'){
            setTimeout(()=>{
              this.LuckSheet_PDFExportFn(_templateNo)
            },1000)
          }else{
            setTimeout(()=>{
              this.LuckSheet_ExecelExportFn(_templateNo)
            },1000)
            
          }
        }else{
          this.$message.error("模板编号不存在，请检查配置")
        }
      } catch (error) {
          this.$message.error("导出模板异常，请检查配置")
      }
    },
  async LuckSheet_ExecelExportFn(templateNo=''){
      let _url ="/api/md/TemplateDesign/ExportExcel/download"
      let params ={
        Parameter: {},//动态参数
        TemplateNo: templateNo // luckysheet模板编号
      }
      if(!!this.dataSearchOptions.LuckSheet_templateNo_Params){
        let _LuckSheet_templateNo_Params = await this.useFormatParamsFn.getVFormSearchParams(this.dataSearchOptions.LuckSheet_templateNo_Params);
        //debugger
        if(_LuckSheet_templateNo_Params.Parameter && Object.keys(_LuckSheet_templateNo_Params.Parameter).length>0){
          params.Parameter = _LuckSheet_templateNo_Params.Parameter
        }
      }
  
    await  request["postBlob"](_url, params).then(res => {
        //debugger
          let result = res;
          let blob = new Blob([result], { type: "application/vnd.ms-excel" });
          let url = window.URL.createObjectURL(blob);
          let link = document.createElement('a');
          link.download = templateNo+"-" + dayjs().format('YYYYMMDDHHmmss') + this.randomString(10) + ".xlsx";
          link.href = url;
          link.click();
      });
    },
   async LuckSheet_PDFExportFn(templateNo=''){
      let _url ="/api/md/TemplateDesign/ExportPdf/download"
      let params ={
        Parameter: {},//动态参数
        TemplateNo: templateNo // luckysheet模板编号
      }
      if(!!this.dataSearchOptions.LuckSheet_templateNo_Params){
        let _LuckSheet_templateNo_Params = await this.useFormatParamsFn.getVFormSearchParams(this.dataSearchOptions.LuckSheet_templateNo_Params);
        if(_LuckSheet_templateNo_Params.Parameter && Object.keys(_LuckSheet_templateNo_Params.Parameter).length>0){
          params.Parameter = _LuckSheet_templateNo_Params.Parameter
        }
      }
    await request["postBlob"](_url, params).then(res => {
        //debugger
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        let url = URL.createObjectURL(blob);
        // 创建一个临时的<a>标签用于下载
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = templateNo+"-" + dayjs().format('YYYYMMDDHHmmss') + this.randomString(10)+'.pdf'; //'downloaded-pdf.pdf'; // 指定下载文件的名称
        // 添加链接到文档并模拟点击
        document.body.appendChild(downloadLink);
        downloadLink.click();
        // 清理并移除元素
        document.body.removeChild(downloadLink);
        URL.revokeObjectURL(url);
      });
    },
    enterFilterEvent ({ $panel, column }, event) {
        if (event.keyCode === 13) {
          console.log('筛选回车事件')
          $panel.confirmFilter()
        }
    },
    visibleMethod({ type, options, column }) {
      // 示例：只有 name 列允许操作，清除按钮只能在 age 才显示
      // 显示之前处理按钮的操作权限 CY
      options.forEach(list => {
        list.forEach(item => {

          if (['myExport'].includes(item.code)) {
            item.visible = this.showContextMenu_Export
          }
        })
      })
      return true
    },
    getCtrlDataFromCacheByKey(cacheKey) {
      let ctrlData = null
      try {
        ctrlData = this.$store.state.ctrlData.value[cacheKey] || null
      } catch (error) {
        ctrlData = null
      }
      return ctrlData
    },
    contextMenuClickEvent({ menu, row, column }) {
      const $grid = this.getRefInstance(true);
      let filename = "导出数据" //tablabel
      if (!!this.showContextMenu_Export_fileNameCtrl) {
        let cachekey = this.$route.fullPath + '_' + this.showContextMenu_Export_fileNameCtrl
        filename = this.getCtrlDataFromCacheByKey(cachekey)
        if (!filename) {
          filename = "导出数据"
        }
      }
      switch (menu.code) {
        case 'myExport':
          setTimeout(() => {
            let _exportOptions = {
              filename: filename + "-" + dayjs().format('YYYYMMDDHHmmss') + this.randomString(10),
              sheetName: filename,
              mode: "all",
            }
            $grid.openExport(_exportOptions)
            // this.exportTableDataFn(filename)
          }, 100)
          break
        // case 'hostExport':
        //   this.hostExportFn()
        //   break
      }
    },
    // 导出数据
    exportTableDataFn(filename) {
      //let filename = "导出数据"
      if (!filename) {
        filename = "导出数据"
      }
      const $grid = this.getRefInstance(true);
      let _exportOptions = {
        filename: filename + "-" + dayjs().format('YYYYMMDDHHmmss') + this.randomString(10),
        sheetName: filename,
        mode: "all",
      }
      //$grid.openExport(_exportOptions)
      $grid.openExport()
    },
    randomString(len) {
      len = len || 32;
      let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
      let maxPos = $chars.length;
      let pwd = '';
      for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
      }
      return pwd;
    },
    checkboxChangeEvent(params) {
      this.setPublicAttr()
    },
    checkboxAllEvent(params) {
      this.setPublicAttr()
    },
    // 编辑框关闭后触发事件 只对 edit-config 配置时有效，单元格编辑状态下被关闭时会触发该事件
    editClosedEvent(tableInfo) {
      //debugger
      let rowSumDynamicColumnField = this.dataSearchOptions.rowSumDynamicColumnField
      if (!!rowSumDynamicColumnField) {
        // CFIELDS 固定字段 ，动态列数据值组名
        let _sum = 0
        if (tableInfo.row.hasOwnProperty("CFIELDS")) {
          // 汇总 动态列 数据 到指定行字段
          for (const [key, val] of Object.entries(tableInfo.row.CFIELDS)) {
            if (!!key && !!val) {
              try {
                _sum = _sum + parseInt(val)
              } catch (error) {

              }

            }
          }
        }
        tableInfo.row[rowSumDynamicColumnField] = _sum
        // 启用主从表汇总功能调用
        this.emitSumTableRowData()
      }
    },
    // 启用主从表汇总功能调用
    emitSumTableRowData() {
      // 是否启用主从表汇总功能
      if (!!this.dataSearchOptions.enableSumDataToMainTable) {
        let emitterParams = {
          tableName: this.dataSearchOptions.sumDataToMainTableName,// 被汇总表名称
          sumTableDataObj: this.formatCustomSumDataList(),// 汇总对应字段列表
        }
        emitterFn.emit("sumTableRowData", emitterParams)
      }

    },
    // 格式化自定义的汇总字段信息 
    // keyValueList =[{key:'COST',value:'TCOST'},{...}] KEY 为从表字段，VALUE主表字段
    // tableData 当前表格数据
    formatCustomSumDataList() {
      const $grid = this.getRefInstance();
      let keyValueList = this.dataSearchOptions.sumTableKeyValueList
      let tableData = $grid.getTableData().fullData
      let sunkeyList = []
      let sunValueList = []
      if (keyValueList && keyValueList.length > 0) {
        keyValueList.forEach(item => {
          // 必须保证两个字段都不为空
          if (!!item.key && !!item.value) {
            sunkeyList.push(item.key)
            sunValueList.push(item.value)
          }
        })
      }
      let newItemObj = {}
      if (sunkeyList.length > 0 && sunValueList.length > 0) {
        // 初始化默认值
        sunValueList.forEach(item => {
          newItemObj[item] = 0
        })
        if (tableData && tableData.length > 0) {
          tableData.forEach(item => {
            sunkeyList.forEach((subitem, subIndex) => {
              if (item.hasOwnProperty(subitem)) {
                // 逐行累加值 
                newItemObj[sunValueList[subIndex]] = newItemObj[sunValueList[subIndex]] + Number(item[subitem])
              }
            })
          })
        }
      }
      this.$nextTick(() => {
        // 手动刷新底部汇总数据
        $grid.updateFooter()
      })
      return newItemObj
    },
    //处理自定义的汇总字段信息 
    handleCustomSumData(params) {
      let sumTableDataObj = params.sumTableDataObj
      const $grid = this.getRefInstance();
      let _currentRow = $grid.getCurrentRecord()
      for (const [key, val] of Object.entries(sumTableDataObj)) {
        _currentRow[key] = val
      }
    },
    // 表尾合计的计算方法
    footerMethod(tableInfo) {
      let sums = []
      sums = this.getSummaryData(tableInfo)
      return [sums]
    },
    // 查询汇总数据列表
    getSummaryData(params) {
      // debugger
      const sums = []
      let { columns, data } = params
      columns.forEach((column, columnIndex) => {
        if (columnIndex === 0) {
          sums.push('合计')//汇总
        } else {
          let sumCell = null
          if (this.summaryFieldList.includes(column.field)) {
            sumCell = XEUtils.sum(data, column.field)
            // if(!!column.formatter){
            //        let dataParams ={
            //          cellValue :sumCell
            //        }
            //       sumCell = dataFormatsList[column.formatter](dataParams)
            // }
          }
          sums.push(sumCell)
        }
      })
      return sums
    },
    // 根据设置获取是否显示操作按钮
    getShowOperationButtons(row, item) {

      let flag = item.check
      /////////////////////////////onBeforeHiding///按钮是否显示 前，拦截器////////////////////////////////////////
      let paramsData = {
        currentRow: row,
        buttonOptions: item,
      }
      let onBeforeHidingStr = this.useFormatParamsFn.getOtherParamsValue("onBeforeHiding", item)
      let onBeforeHiding = new Function('paramsData', onBeforeHidingStr)
      if (!!onBeforeHidingStr) {
        //debugger
        try {
          flag = onBeforeHiding.call(this, paramsData)
          //console.log("=========getShowOperationButtons flag value====:"+flag)
        } catch (error) {
          this.$message({
            message: '(nvxe-grid===onBeforeHiding) 错误，请检查！！！',
            type: 'error'
          });
          return item.check
        }
      }
      if (flag == null || flag == undefined) {
        // 如果返回参数为空，按原本的逻辑显示按钮
        return item.check
      }
      /////////////////////////////onBeforeHiding///按钮是否显示 前，拦截器////////////////////////////////////////
      return flag
    },
    // 获取当前表格的数据（完整的全量表体数据、处理条件之后的全量表体数据、当前渲染中的表体数据、当前渲染中的表尾数据
    getTableFullData(from) {
      // debugger
      let fullData = []
      try {
        const $grid = this.getRefInstance();
        if ($grid) {
          fullData = $grid.getTableData().fullData
        }
      } catch (error) {
        fullData = []
      }

      return fullData
    },
    // 用于 type=checkbox，获取当前已选中的行数据（当前列表，如果 isFull=true 则获取全表已选中的数据）
    getCheckboxRecords() {
      let selectedData = []
      //debugger
      const $grid = this.getRefInstance();
      if ($grid) {
        let _checkboxRecords = $grid.getCheckboxRecords()
        if (_checkboxRecords && _checkboxRecords.length > 0) {
          selectedData = _checkboxRecords
        }

      }
      return selectedData
    },
    getCheckboxRecordsCIDS() {
      let selectedData = []
      const $grid = this.getRefInstance();
      if ($grid) {
        let _checkboxRecords = $grid.getCheckboxRecords()
        if (_checkboxRecords && _checkboxRecords.length > 0) {
          _checkboxRecords.forEach(item =>{
            selectedData.push(item.CID)
          })
        }
      }
      return selectedData.toString()
    },
    // 获取选中、选择行的数据列表
    getTableSelectedRows() {
      let selectedData = []
      const $grid = this.getRefInstance();
      if ($grid) {
        let fullData = $grid.getTableData().fullData
        if (fullData && fullData.length > 0) {
          selectedData = [$grid.getCurrentRecord()]
        }

      }
      return selectedData
    },
    
    // 设计状态事件触发
    designStateEvent() {
      console.log('======designStateEvent======')
    },
    cellStyleLocal(params) {
      //debugger
      let { row, rowIndex, column, columnIndex,$grid } = params;
      // let field = column.field
      // console.log("==cellStyleLocal==========field:",field)
      let fieldRules = []
      let cssStyle = {
        //color:'#606266'
      }
      if (columnIndex != 0 && params.column.hasOwnProperty("params") && params.column.params && Object.keys(params.column.params).length > 0 && !!params.column.params.customRender) {
        let paramsItem = params.column.params.customRender
        if (paramsItem.columnType == "customStatus") {
          let reWriteObj = {}
          let conditionList = paramsItem.condition
          if (conditionList && conditionList.length > 0) {

            conditionList.forEach(item => {
              let keyField = item.key.replace("{", '').replace("{", '').replace("}", '').replace("}", '')
              let rowFieldValue = row[keyField]

              if (rowFieldValue == item.value) {
                reWriteObj[`${paramsItem.colorType}`] = (!!(item.color) ? item.color : 'red')
              }
            })
          }

          return reWriteObj
        }

      }

      if (column.params && column.params.fieldRules && column.params.fieldRules != [] && column.params.fieldRules != '[]') {

        if (!!column.params.fieldRules) {
          let rulesStr = cloneDeep(column.params.fieldRules)
          fieldRules = JSON.parse(rulesStr)
          // 先判断是是整行还是单元格字段
          //let isRenderRow = false
          // 获取数组符合条件的第一个元素 如果没有符合条件的元素返回 undefined
          let firstMatchRow = fieldRules.filter(ruleItem => {
            if (ruleItem.type == 'text_Row' || ruleItem.tpye == 'bg_Row') {
              return ruleItem
            }
          })
          //debugger
          if (!!firstMatchRow && !this.renderRowConfig.fieldRules) {
            if (this.showSeqColumn && columnIndex == 1) {
              this.renderRowConfig.isMatch = true
              //this.renderRowConfig.rowIndex = rowIndex
              this.renderRowConfig.fieldRules = column.params.fieldRules
              //console.log("====firstMatchRow====渲染整行1", this.renderRowConfig.fieldRules)
            } else {
              if (columnIndex == 0) {
                this.renderRowConfig.isMatch = true
                //this.renderRowConfig.rowIndex = rowIndex
                this.renderRowConfig.fieldRules = column.params.fieldRules
               // console.log("====firstMatchRow====渲染整行0")
              }
            }

          }

          // 单元格
          if (fieldRules && fieldRules.length > 0) {
            fieldRules.forEach(item => {
              let fieldVal = Number(row[item.field])
              let compareVal = Number(item.value)
              if(item.type ==''||item.type =='IconValue'){
                return
              }
              if(!["text","bg"].includes(item.type)){
                return
              }
              let renderType = ((item.type == 'text' || item.type == 'text_Row') ? 'color' : 'backgroundColor')
              let _parentId = Number(item.parentId)
              let _childrenRules = item.children
              //debugger
              if (_parentId == -1 || _parentId > 0) {
                if (item.condition == 'gt') {
                  // 大于
                  if (fieldVal > compareVal) {
                    cssStyle[renderType] = item.color
                  }
                } else if (item.condition == 'gte') {
                  if (fieldVal >= compareVal) {
                    cssStyle[renderType] = item.color
                  }
                }
                else if (item.condition == 'lt') {
                  // 小于
                  if (fieldVal < compareVal) {
                    cssStyle[renderType] = item.color
                  }
                } else if (item.condition == 'lte') {
                  // 小于或等于
                  if (fieldVal <= compareVal) {
                    cssStyle[renderType] = item.color
                  }
                }
                else if (item.condition == 'eq') {
                 // debugger
                  // 等于 时 可以时字符串 比较
                  if (trim(row[item.field]) == trim(item.value)) {
                    //row[item.field] = "测试"
                    cssStyle[renderType] = item.color
                    //row[item.field] = "停机1111"
                  }
                }
                else if (item.condition == 'neq') {
                  // 不等于 时 可以时字符串 比较
                  if (row[item.field] != item.value) {
                    //row[item.field] = "测试"
                    cssStyle[renderType] = item.color
                  }
                }
                else if (item.condition == 'in') {
                  // 包含 时 可以时字符串 比较
                  if (row[item.field].includes(item.value)) {
                    cssStyle[renderType] = item.color
                  }
                }

              
              } else {
                // 条件组 _parentId == 0 
                if(_childrenRules && _childrenRules.length>0){
                  console.log("======条件组 _parentId == 0======",_childrenRules)
                }
              }
              if (renderType == 'backgroundColor') {
                if (!!cssStyle["backgroundColor"]) {
                  // 如果是背景，则字体设置为白色
                  cssStyle['color'] = '#fff'
                }
              }
            })
          }

        }
        // debugger
        return cssStyle
      }

      // 是否渲染整行
      if (!!this.renderRowConfig.isMatch && columnIndex == 0 && this.showSeqColumn) {

         //console.log("===this.renderRowConfig.isMatch===rowIndex",rowIndex)
        if (!!this.renderRowConfig.fieldRules) {
          let rulesStr = cloneDeep(this.renderRowConfig.fieldRules)
          // fieldRules =[{"field":"CWORK_TIME","condition":"gt","conditionType":"@","type":"text","color":"#67C23A","value":"1","CID":"row_250"}]
          fieldRules = JSON.parse(rulesStr)
          // 单元格
          if (fieldRules && fieldRules.length > 0) {
            fieldRules.forEach(item => {
              let fieldVal = Number(row[item.field])
              let compareVal = Number(item.value)
              if(item.type ==''||item.type =='IconValue'){
                 return
              }
              let renderType = (item.type == 'text_Row' ? 'color' : 'backgroundColor')
              let _parentId = Number(item.parentId)
               let _childrenRules = item.children
              if (_parentId == -1 || _parentId > 0) {
                if (item.condition == 'gt') {
                // 大于
                if (fieldVal > compareVal) {
                  cssStyle[renderType] = item.color

                }
              } else if (item.condition == 'gte') {
                if (fieldVal >= compareVal) {
                  cssStyle[renderType] = item.color
                }
              }
              else if (item.condition == 'lt') {
                // 小于
                if (fieldVal < compareVal) {
                  cssStyle[renderType] = item.color
                }
              } else if (item.condition == 'lte') {
                // 小于或等于
                if (fieldVal <= compareVal) {
                  cssStyle[renderType] = item.color
                }
              }
              else if (item.condition == 'eq') {
                // 等于 时 可以时字符串 比较
                  if (trim(row[item.field]) == trim(item.value)) {
                  //row[item.field] = "测试"
                  cssStyle[renderType] = item.color
                  // let tt = column
                  // debugger
                  // let allCols = $grid.getColumns()
                  // allCols = allCols.map(item=>{
                  //   debugger
                  //    if(item.field ="CSTATUS_NAME"){
                  //       item.type="html"
                  //    }
                  //    return item
                  // })
                  // //$grid.reloadColumn(allCols)
                  // row["CSTATUS_NAME"] = "<div style='color:blue;'>停机1111</div>"
                }
              }
              else if (item.condition == 'neq') {
                // 不等于 时 可以时字符串 比较
                if (row[item.field] != item.value) {
                  //row[item.field] = "测试"
                  cssStyle[renderType] = item.color
                }
              }
              else if (item.condition == 'in') {
                // 包含 时 可以时字符串 比较
                if (row[item.field].includes(item.value)) {
                  cssStyle[renderType] = item.color
                }

              }
           
              }else{
                if (this.checkChildrenRules(_childrenRules,row)) {
                   cssStyle[renderType] = item.color
                }
              }
              if (renderType == 'backgroundColor') {
                if (!!cssStyle["backgroundColor"]) {
                  // 如果是背景，则字体设置为白色
                  cssStyle['color'] = '#fff'
                }

              }
            })
          }

        }

        return cssStyle
      }
    },
    conditionTypeJudge(fieldRules = [], row,) {
      // fieldRules =[{"field":"CWORK_TIME","condition":"gt","conditionType":"@","type":"text","color":"#67C23A","value":"1","CID":"row_250"}]
      let cssStyle = {}
      if (fieldRules && fieldRules.length > 0) {
        fieldRules.forEach(item => {
          let fieldVal = Number(row[item.field])
          let compareVal = Number(item.value)
          if(item.type ==''||item.type =='IconValue'){
             return
          }
          let renderType = ((item.type == 'text_Row' || item.type == 'text') ? 'color' : 'backgroundColor')
         
          if (item.condition == 'gt') {
            // 大于
            if (fieldVal > compareVal) {
              cssStyle[renderType] = item.color
            }
          } else if (item.condition == 'gte') {
            if (fieldVal >= compareVal) {
              cssStyle[renderType] = item.color
            }
          }
          else if (item.condition == 'lt') {
            // 小于
            if (fieldVal < compareVal) {
              cssStyle[renderType] = item.color
            }
          } else if (item.condition == 'lte') {
            // 小于或等于
            if (fieldVal <= compareVal) {
              cssStyle[renderType] = item.color
            }
          }
          else if (item.condition == 'eq') {
            // 等于 时 可以时字符串 比较
            //if (row[item.field] == item.value) {
              if (trim(row[item.field]) == trim(item.value)) {
              cssStyle[renderType] = item.color
            }
          }
          else if (item.condition == 'neq') {
            // 不等于 时 可以时字符串 比较
            if (row[item.field] != item.value) {
              //row[item.field] = "测试"
              cssStyle[renderType] = item.color
            }
          }
          else if (item.condition == 'in') {
            // 包含 时 可以是字符串 比较
            if (row[item.field].includes(item.value)) {
              cssStyle[renderType] = item.color
            }

          }

          if (renderType == 'backgroundColor') {
            if (!!cssStyle["backgroundColor"]) {
              // 如果是背景，则字体设置为白色
              cssStyle['color'] = '#fff'
            }

          }
        })
      }
      return cssStyle
    },
    // 给行附加样式，也可以是函数
    // any | (({ row, rowIndex, $rowIndex }) => any)
    rowStyleLocal(params) {
      let { row, rowIndex, $rowIndex } = params;
      let cssStyle = {}
      let _rowStyle = null
     // let _columns = params.$table.getColumns()
     let _columns = params.$table.getTableColumn().fullColumn
      if (this.showSeqColumn && _columns && _columns.length > 0) {
        try {
          _rowStyle = _columns[1].params.fieldRules
        } catch (error) {
          _rowStyle = null
        }
      }
      if (!this.showSeqColumn && _columns && _columns.length > 0) {
        try {
          _rowStyle = _columns[0].params.fieldRules
        } catch (error) {
          _rowStyle = null
        }
      }
      if (_rowStyle) {
        let fieldRules = JSON.parse(_rowStyle)
        fieldRules.forEach(item => {
          let fieldVal = Number(row[item.field])
          let compareVal = Number(item.value)
          if(item.type ==''||item.type =='IconValue'){
            return
          }
          let renderType = ((item.type == 'text_Row') ? 'color' : 'backgroundColor')
          if(!["text_Row","bg_Row"].includes(item.type)){
                return
          }
          let _parentId = Number(item.parentId)
          let _childrenRules = item.children
          if (_parentId == -1 || _parentId > 0) {
            if (item.condition == 'gt') {
              // 大于
              if (fieldVal > compareVal) {
                cssStyle[renderType] = item.color
              }
            } else if (item.condition == 'gte') {
              // 大于或等于
              if (fieldVal >= compareVal) {
                cssStyle[renderType] = item.color
              }
            }
            else if (item.condition == 'lt') {
              // 小于
              if (fieldVal < compareVal) {
                cssStyle[renderType] = item.color
              }
            } else if (item.condition == 'lte') {
              // 小于或等于
              if (fieldVal <= compareVal) {
                cssStyle[renderType] = item.color
              }
            }
            else if (item.condition == 'eq') {
              // 等于 时 可以时字符串 比较
              //if (row[item.field] == item.value) {
                if (trim(row[item.field]) == trim(item.value)) {
                cssStyle[renderType] = item.color
              }
            }
            else if (item.condition == 'neq') {
              // 不等于 时 可以时字符串 比较
              if (row[item.field] != item.value) {
                //row[item.field] = "测试"
                cssStyle[renderType] = item.color
              }
            }
            else if (item.condition == 'in') {
              // 包含 时 可以时字符串 比较
              if (row[item.field].includes(item.value)) {
                cssStyle[renderType] = item.color
              }
            }
           
          } else {
            // 条件组 _parentId == 0 
            if(_childrenRules && _childrenRules.length>0){
              //console.log("==rowStyleLocal====条件组 _parentId == 0======",_childrenRules)
                if (this.checkChildrenRules(_childrenRules,row)) {
                  cssStyle[renderType] = item.color
                }
            }

          }
          if (renderType == 'backgroundColor') {
              if (!!cssStyle["backgroundColor"]) {
                // 如果是背景，则字体设置为白色
                cssStyle['color'] = '#fff'
              }
          }
         
        })
      }
      return cssStyle
    },
    checkChildrenRules(_childrenRules=[],row={}){
        let boolenList=[]
        if(_childrenRules && _childrenRules.length>0 && Object.keys(row).length>0){
          _childrenRules.forEach(item=>{
            let fieldVal = Number(row[item.field])
            let compareVal = Number(item.value)
            if (item.condition == 'gt') {
              // 大于
              boolenList.push((fieldVal > compareVal)+"")
            } else if (item.condition == 'gte') {
              // 大于或等于
              boolenList.push((fieldVal >= compareVal)+"")
            
            }
            else if (item.condition == 'lt') {
              // 小于
              boolenList.push((fieldVal < compareVal)+"")
            
            } else if (item.condition == 'lte') {
              // 小于或等于
              boolenList.push((fieldVal <= compareVal)+"")
           
            }
            else if (item.condition == 'eq') {
              // 等于 时 可以时字符串 比较  if (trim(row[item.field]) == trim(item.value)) {
              boolenList.push((trim(row[item.field]) == trim(item.value))+"")
              
            }
            else if (item.condition == 'neq') {
              // 不等于 时 可以时字符串 比较
              boolenList.push((row[item.field] != item.value)+"")
             
            }
            else if (item.condition == 'in') {
              // 包含 时 可以时字符串 比较
              boolenList.push((row[item.field].includes(item.value))+"")
             
            }
          })
        }
       // debugger
       let _flag= boolenList.includes('false')?false:true
      return _flag
    },
    // 统一接受VFORM表单触发事件处理
    // item =  { label: '编辑', value: "iisEdit", check: true, canRemove: false, otherParams: {}, actionParams: {actionName: "query3" ,query: Object} }
    async handleEventByCsofti(row, subItem) {
      //debugger
      this.currentRow = row
      this.publicAttribute.row = row // 设置对外属性值 选择行
      await this.setCurrentRowEvent(row)
      this.publicAttribute.totalRows = this.tabelConfigs.footerConfig.pageConfig.total // 当前选中行
      await this.setPublicAttr()
      this.useHandleVFormEventFn.handleCommonClickEvent(subItem)
      // 操作按钮 是否触发 控件列表
      if (!!this.dataSearchOptions.operationBtnTriggerCtrlList) {
        setTimeout(() => {
          // 触发控件列表
          this.triggerCtrlNames(5)
        }, 300)

      }
    },
    // 判断是是否从表并且带有参数 && 并且和上次参数是否没有变更
    judgeLevelAndParams() {
      let canNext = true
      if (this.layoutLevel != 1 && this.params) {
        //debugger
        let { updateFlag, resetPage, ...otherParams } = this.params;
        if (otherParams && !!!otherParams.pid) {
          // 如果pid 为空直接返回
          canNext = false
        }
        // debugger;
        let paramsList = Object.keys(this.params);
        if (
          otherParams &&
          Object.keys(otherParams).length > 0 &&
          paramsList.length > 0 &&
          !this.params[paramsList[0]]
        ) {
          canNext = false
        }
      }
      return canNext
    },
    // 重新加载数据（主要用于判断从表参数是否变化而决定重置查询）
    reloadInitData(n, o) {
      //debugger
      if (this.layoutLevel != 1 && n) {
        // 是否重置翻页，默认重置
        let { updateFlag, resetPage, ...otherParams } = n;
        // 重置翻页
        if (!!resetPage) {
          this.tabelConfigs.footerConfig.pageConfig.currentPage = 1
          // this.publicAttribute.pageIndex =1
          this.setPublicAttr()
        }

        if (otherParams && !!!otherParams.pid) {
          // 如果pid 为空直接返回
          return
        }
        // debugger;
        let paramsList = Object.keys(n);
        if (
          otherParams &&
          Object.keys(otherParams).length > 0 &&
          paramsList.length > 0 &&
          !!n[paramsList[0]]
        ) {
          let isTheSame = JSON.stringify(n) == JSON.stringify(o);
          if (!isTheSame) {
            this.$nextTick(() => {
              this.initData();
            })
          }
        }
      }

    },

    // 获取表格 Ref 实例,方便操作原生控件方法，属性等
    // newRef 每次都重新获取实例？
    getRefInstance(newRef = false) {
      try {
        let _self = this
        //表格单实例
        if (!_self.refInstance || newRef) {
          _self.refInstance = _self.$refs["nvxeTableRef"].$refs["csoftiTableRef"];
        }
        return _self.refInstance
      } catch (error) {
        return null
      }

    },
    getRequest(name) {
      let urlStr = window.location.search
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      let r = urlStr.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      };
      return null;
    },
    // 动态表单页面的缓存
    viewdetailCache(row = null) {
      //debugger
      if (this.layoutLevel != 1) {
        // 非主表 不储存
        return
      }
      if (!!!row) {
        row = this.currentRow
      }
      //let urlParams = useUrlSearchParams() 
      let formName = this.getRequest("formName")
      let params = {
        key: formName,//urlParams.formName,
        value: row
      }
      this.$store.commit("set_currentRow_viewdetail", params);
    },
    // 保存表格数据到VUEX,主要用于多集合时候，图表中使用
    saveTableToVuex(tableData = []) {
      let formName = this.getRequest("formName")
      let params = {
        key: formName,
        value: tableData
      }
      if (!!formName) {
        // formName不为空才存储
        this.$store.commit("set_mainTableDataList", params);
      }

    },

    // 当前行改变前 逻辑处理，仅供 custoomJSCode eventList
    async onBeforeCurrentChangeFn(e) {
      // debugger
      let flag = true
      let beforeCurrentChange_eventList = []
      try {
        beforeCurrentChange_eventList = this.dataSearchOptions.eventList.filter(item => {
          if (item.otherParams && item.otherParams.eventType && item.otherParams.eventType == "onBeforeCurrentChange") {
            return item
          }
        })
      } catch (error) {
        beforeCurrentChange_eventList = []
        // 为空快速返回
        return true
      }

      /////////////////////////////onBeforeHiding///按钮是否显示 前，拦截器////////////////////////////////////////
      if (beforeCurrentChange_eventList && beforeCurrentChange_eventList.length > 0) {
        // 当前行改变前 事件列表，目前支持一个事件，多个事件时，只处理第一个 自定义代码控件
        let beforeCurrentChangeItem = beforeCurrentChange_eventList[0]
        // otherParams: Object
        //     actionType: "logicCode"
        if (beforeCurrentChangeItem.otherParams && beforeCurrentChangeItem.otherParams.actionType && beforeCurrentChangeItem.otherParams.actionType == "logicCode") {
          // JSCODE 逻辑代码
          let paramsData = {
            oldSelectedRow: this.currentRow,
            newSelectedRow: e.row,
          }
          let onBeforeCurrentChangeStr = this.useFormatParamsFn.getOtherParamsValue("onBeforeCurrentChange", beforeCurrentChangeItem)
          let onBeforeCurrentChange = new Function('paramsData', onBeforeCurrentChangeStr)
          //let onBeforeCurrentChange = new AsyncFunction('paramsData', onBeforeCurrentChangeStr);

          if (!!onBeforeCurrentChangeStr) {
            //debugger
            try {
              //bind的传参和call相同
              flag = await onBeforeCurrentChange.call(this, paramsData)

              if (flag == undefined || flag == null) {
                // 如果 自定义 没有返回值，默认返回真
                flag = true
              }
            } catch (error) {
              this.$message({
                message: '(nvxe-grid===onBeforeCurrentChange) 错误，请检查！！！',
                type: 'error'
              });
              return true
            }
          }
        } else {
          // 非JScode 逻辑处理
          // debugger
          await this.useHandleVFormEventFn.handleCommonClickEvent(beforeCurrentChangeItem)
          return true
        }

      }
      /////////////////////////////onBeforeHiding///按钮是否显示 前，拦截器////////////////////////////////////////
      return flag
    },
     currentChangeEvent(e) {
      clearTimeout(this.currentChangeEventTimer)
      this.currentChangeEventTimer =  setTimeout(()=>{
          this.currentChangeEvent_next(e)
        },50)
    },
    // 手动选中行并且值发生改变时触发的事件
    async currentChangeEvent_next(e) {
      //return
      ///////////////////当前行改变前 事件拦截///////////////////////
      let _onBeforeCurrentChangeFlag = await this.onBeforeCurrentChangeFn(e)
      if (!_onBeforeCurrentChangeFlag) {
        // 回滚选择行
        setTimeout(() => {
          e.$grid.setCurrentRow(this.currentRow)
        }, 300)
        return
      }
      ///////////////////当前行改变前 事件拦截///////////////////////
      let _self = this
      this.currentRow = e.row // 当前选中行
      this.publicAttribute.row = e.row // 当前选中行
      this.publicAttribute.totalRows = this.tabelConfigs.footerConfig.pageConfig.total // 当前选中行
      // this.publicAttribute.tableData = this.getTableFullData("currentChangeEvent")
      // this.publicAttribute.selectedRows = this.getTableSelectedRows()
      // this.publicAttribute.checkboxRecords= this.getCheckboxRecords()
      await this.setPublicAttr()
      // 保存当前路由
      this.$store.commit("set_routerName", this.$route.name);
      if (this.$route.name == "viewdetail") {
        //动态表单页面的缓存
        this.viewdetailCache()
      }

      if (this.layoutLevel == 1 && !this.designState) {
        // 保存(主表)当前选中行

        this.$store.commit("set_currentRow", this.currentRow);

      }

      let params = {
        currentRow: e.row, // 当前选中行
        tableInfo: e
      }
      this.$emit('currentChange', params)
      setTimeout(() => {
        // 触发控件列表
        _self.triggerCtrlNames(1)
      }, 300)

    },
    // 触发控件列表
    triggerCtrlNames(from="") {
      //debugger
       if (!!this.dataSearchOptions.triggerCtrlNames) {
            let params = {
              formCtrlName: "vxetable",
              triggerCtrlNames: this.dataSearchOptions.triggerCtrlNames, //重点： 触发控件名称
            }
            console.log("====triggerCtrlNames====",from)
            this.useHandleVFormEventFn.reSearchData(params);
      }
    },
    // 设置选中行
    async setCurrentRowEvent(row) {

      let _self = this
      this.currentRow = row // 当前选中行
      this.publicAttribute.row = row // 当前选中行
      const $grid = this.getRefInstance();
      await $grid.setCurrentRow(row);
      // 保存当前路由
      this.$store.commit("set_routerName", this.$route.name);
      if (this.layoutLevel == 1 && !this.designState) {
        // 保存(主表)当前选中行
        this.$store.commit("set_currentRow", row);
      }
    },

    // 格式化查询数据地址
    getSearchUrl() {
       //debugger
      let searchUrl = ""
      if (Object.keys(this.requstConfig).length == 0) {
        // 没有配置查询参数，直接返回
        return
      } else {
        if (Object.keys(this.requstConfig).length > 0 && !!this.requstConfig.actionName && this.requstConfig.actionName != "API") {
          searchUrl = this.requstConfig.postUrl + this.requstConfig.actionName
        } else {
          searchUrl = this.useFormatParamsFn.getVFormDataSearchUrl(this.requstConfig.postUrl);
          this.getPostTypeFn(searchUrl)
        }
      }
      return searchUrl
    },
    getPostTypeFn(_url){
      this.requstConfig.postType = 'post'
      if(_url.indexOf("GetListByDataSetId") > -1){
       // debugger
        return
      }
      try {
        this.requstConfig.postType = this.useFormatParamsFn.getPostTypeFn(this.requstConfig.postUrl)
      } catch (error) {
         this.requstConfig.postType = 'post'
      }
      
    },
    // 合并页面查询参数
    mergePageParams(params1, params2) {
      let params = Object.assign({}, params1, params2)
      return params
    },
    //  获取基本查询参数
    getBaseParams() {
      let params = {}
      if (this.requstConfig.actionName && this.requstConfig.actionName != "Detail" && this.requstConfig.actionName != "API") {
        params["condition"] = ""
        params["start"] = this.tabelConfigs.footerConfig.pageConfig.currentPage
        params["length"] = this.tabelConfigs.footerConfig.pageConfig.pageSize
      }
      return params
    },
    // 获取查询参数
    async getSearchParams(paramsOptions) {
      // debugger
      let params = {}
      let _homePageSearch = {}
      if (Object.keys(paramsOptions).length > 0) {
        //  解构翻页，其他查询参数
        let { resetPage, homePageSearch, ...otherSearchParams } = paramsOptions
        if (!!resetPage) {
          // 重置翻页
          this.tabelConfigs.footerConfig.pageConfig.currentPage = 1
          this.tabelConfigs.footerConfig.pageConfig.total = 0
          await this.setPublicAttr()
        }
        _homePageSearch = homePageSearch
        // 合并动态表单 普通查询参数,控件自身的参数，覆盖VFORM表单的参数
        params = this.mergePageParams(params, otherSearchParams)
      }
      //debugger
      // 此处为VFORM表单的通用参数
      let vformSettingParams = await this.useFormatParamsFn.getVFormSearchParams(this.requstConfig.postUrl);

      params = this.mergePageParams(params, vformSettingParams)
      // debugger
      // 合并基础 翻页参数
      let baseParams = this.getBaseParams()
      params = this.mergePageParams(params, baseParams)
      //===========特殊处理 首页列表查询=============
      if (_homePageSearch && Object.keys(_homePageSearch).length > 0) {
        params.condition = _homePageSearch
      }
      //========================
      //覆盖分页初始化,不分页的情况下，且分页参数为：length，设置页码为最大
      if (!!!this.showPagination && !!params.hasOwnProperty("Parameter")) {
        if (params.Parameter.hasOwnProperty("length")) {
          params.Parameter.length = this.dataSearchOptions.tabelConfigs.footerConfig.pageConfig.pageSize
        }
      }
      // 合并页面查询初始参数
      if (!!this.requstConfig.postParams && Object.keys(this.requstConfig.postParams).length > 0) {
        params = this.mergePageParams(params, this.requstConfig.postParams)
      }
      // 高级搜索 参数设置
      //params.Parameter.SearchPro=""// 默认为空
      if (this.$store.state.searchType && this.$store.state.searchType.value == 'searchPro') {
        // try {

        //   params.Parameter.SearchPro = JSON.stringify(this.$store.state.searchProDrawerBoxChange.value.postData.conditionalList)
        // } catch (error) {
        //   console.error(" 高级搜索参数 错误，请检查！！")
        //   if (params.Parameter && params.Parameter.hasOwnProperty("SearchPro")) {
        //     params.Parameter.SearchPro = ""
        //   }

        // }
      } else {
        try {
          // 普通查询时，移除高级查询参数值，因为SearchPro 优先基本比较高
          delete params.Parameter.SearchPro
        } catch (error) {

        }

      }
      return params
    },
    // 设置对外公开属性值
    async setPublicAttr() {
      // debugger
      let params = {
        publicAttribute: {
          totalRows: this.tabelConfigs.footerConfig.pageConfig.total, // 当前选中行
          pageIndex: this.tabelConfigs.footerConfig.pageConfig.currentPage,
          pageSize: this.tabelConfigs.footerConfig.pageConfig.pageSize,
          row: this.publicAttribute.row,
          tableData: this.getTableFullData("setPublicAttr"),
          selectedRows: this.getTableSelectedRows(),
          checkboxRecordsCIDS: this.getCheckboxRecordsCIDS(),
          checkboxRecords: this.getCheckboxRecords(),
        }
      }
      this.publicAttribute = params.publicAttribute
      this.$emit("setPublicAttr", params)
      return this.$nextTick
    },
    // 页面改变事件，重新查询数据
    async pageChangeEvent(pageObj) {
      // this.publicAttribute.pageIndex = pageObj.currentPage
      this.tabelConfigs.footerConfig.pageConfig.currentPage = pageObj.currentPage
      await this.setPublicAttr()
      let _self = this
      this.$nextTick(() => {
        _self.searchTableData()
      })
    },
    // 查询>>表格数据 res=>>
    // current: 1
    // orders: []
    // pages: "1"
    // records: (16) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
    // searchCount: true
    // size: 16
    // total: 279
    async searchTableData(paramsOptions = {}) {
      //debugger
      if (!!this.designState) {
        // 设计状态时，不查询数据
        return
      }
      // 解决非当前页，但已经打开的缓存页面重复执行查询,注意：要包含弹框页面PAGECID
      // if(this?.designer?.formConfig?.pageCID){
      //   //debugger
      //     if(this.$route.fullPath.indexOf(this.designer.formConfig.pageCID) === -1 && this.designer.formConfig.popupList.length==0){
      //       this.gridDefaultOptions.loading = false
      //        return
      //      }
      //    }
      // let canNext = this.judgeLevelAndParams()
      // if (!canNext) {
      //   // 从表首次查询时，参数为空，不可执行
      //   return
      // }
      this.gridDefaultOptions.loading = true
      //debugger
      if (Object.keys(this.requstConfig).length == 0 || !this.requstConfig.postUrl) {
        // 没有配置查询参数，直接返回
        this.gridDefaultOptions.loading = false
        return
      }
      // 加载数据前>> 用于>>如果不是主表，需要清空从表数据
      this.$emit('beforeLoadData', this.layoutLevel)
      let _self = this;
      // 列头查询 preUrl 路径固定不变
      let _url = this.getSearchUrl();

      if(!_url){
        this.gridDefaultOptions.loading = false
        // 如果为空，直接退出
        return
      }
      // 合并普通、高级查询参数
      // debugger
      let params = await this.getSearchParams(paramsOptions);
      let hasRequireParamsNull = await this.useFormatParamsFn.checkRequireParamsIsNull(this.requstConfig.postUrl);
      // 校验请求是否为本地API 或可直连API
      if(this.useFormatParamsFn.checkIsWebAPI(this.requstConfig.postUrl)){
         if(params && params.hasOwnProperty('Parameter')){
            params = params.Parameter
          }
      }
      //console.error(" hasRequireParamsNull:",hasRequireParamsNull)
      if(hasRequireParamsNull){
        this.gridDefaultOptions.loading = false
        // console.error(" 必填参数为空，跳过查询！！")
        return
      }

      try {
        if (this.requstConfig.postType && this.requstConfig.postType == 'get') {
          //debugger
          // 查询类型为get时，参数格式与post不同
        
          await request["get"](_url, null, params).then(res => {
            this.tabelConfigs.footerConfig.pageConfig.total = res.TotalRows // 总条数
            //=======处理返回的数据=====

            this.handleData(res)
          });
        } else {
      
          await request["post"](_url, params).then(res => {
            this.tabelConfigs.footerConfig.pageConfig.total = res.TotalRows // 总条数
            //=======处理返回的数据=====

            this.handleData(res)
          });
        }

        this.gridDefaultOptions.loading = false
      } catch (error) {
        this.gridDefaultOptions.loading = false
      }

    },
    async searchTableDataFn(subItem) {
      await this.searchTableData()
    },
   
    getRowByCIDInFullDataChildren(dataList, oldCurrentRow) {
      let row = null
      if (dataList && dataList.length > 0) {
        dataList.forEach(item => {
          let hasData = false
          if (item.CID == oldCurrentRow.CID) {
            hasData = true
            row = item
          }
          if (!hasData && item.children && item.children.length > 0 && !row) {
            row = this.getRowByCIDInFullDataChildren(item.children, oldCurrentRow)
          }
        })
      }
      return row
    },
    // 从最新的实例中，重新获取当前选中（即使被修改了）的对象实例
    getRowByCIDInFullData(oldCurrentRow) {
      let fulldata = this.getTableFullData()
      let newCurrentRow = null
      let dataList = fulldata.filter(item => {
        let hasData = false
        if (item.CID == oldCurrentRow.CID) {
          hasData = true
          return item
        }
        // debugger
        if (!hasData && item.children && item.children.length > 0 && !newCurrentRow) {
          newCurrentRow = this.getRowByCIDInFullDataChildren(item.children, oldCurrentRow)
        }

      })
      if (dataList && dataList.length > 0) {
        newCurrentRow = dataList[0]
      }
      return newCurrentRow
    },
    // 刷新数据并且选中原来的数据源
    async searchTableDataAndSelectdOldData(subItem) {
      // debugger
      let _currentRow = this.getCurrentRecordFromCache()
      // debugger
      let _oldRow = cloneDeep(_currentRow)
      await this.searchTableData()
      setTimeout(async () => {
        // debugger
        let _newRow = this.getRowByCIDInFullData(_oldRow)
        if (_newRow) {
          await this.setCurrentRowEvent(_newRow)
        }

      }, 300)
    },
    // 
     reSearchData(params = {}) {
      //debugger
        clearTimeout(this.reSearchDataTimer)
        this.reSearchDataTimer = setTimeout(()=>{
         
            try {
             // debugger
                   // 检验绑定控件和绑定值是否相等
            if(this.checkBandingCtrlVal()){
              console.warn("对外暴露事件，重新加载:reSearchData")
             
              // 防止打开的缓存页面或子弹框页面重复执行相同接口，故此逻辑判断
              if(params?.pageCID && this.$route.fullPath.indexOf(params.pageCID) != -1){
                if(this.$store.state.firstActionPageCID ==params.pageCID){
                  this.searchTableData({resetPage:true})
                }
                
               }else if(this?.designer?.formConfig?.parentPageCID){
                  if(this.$route.fullPath.indexOf(this.designer.formConfig.parentPageCID) != -1){

                      if(this.$store.state.firstActionPageCID ==params.pageCID){
                          this.searchTableData({resetPage:true})
                      }
                   }
              }else if(!params.hasOwnProperty("pageCID")){
                // 多TAB 页面
                this.searchTableData({resetPage:true})
              }else if(params.hasOwnProperty("pageCID") && !params.pageCID){
                // 有 pageCID 但 pageCID 为空
                this.searchTableData({resetPage:true})
              }
              
            }
              } catch (error) {
                //debugger
                this.searchTableData({resetPage:true})
              }
        },300)
    },
    // 检验绑定控件和绑定值是否相等
    checkBandingCtrlVal(){
      // debugger
      let _flag = true
      try {
        if (!!this.dataSearchOptions.bandingCtrlName && !!this.dataSearchOptions.bandingCtrlValue){
            let bandingCtlVal = this.useHandleVFormComponentsFn.getCtrlBandingValue(this.dataSearchOptions.bandingCtrlName)
            if(bandingCtlVal == this.dataSearchOptions.bandingCtrlValue){
              _flag = true
            }else{
              _flag = false
            }
        }
      } catch (error) {
         _flag = true
      }
     return _flag
    },
    // 查询>>表格表头
    searchTableColumns() {
      if (!!!this.searchColumnsName) {
        return
      }
      let _self = this;
      // 列头查询 preUrl 路径固定不变
      let _url = `common/api/commonSetting/initLoad/otherFn`;
      let params = {
        "functionName": "formInit",
        "otherParams": {
          "formName": _self.searchColumnsName
        }
      };
      request["post"](_url, params).then(res => {
        // if (res && Object.keys(res).length > 0) {
        //   _self.handleColumns(res);
        // }
      });
    },
    // 处理查询表格返回数据后续，(主要用于处理数据不同结构数据)
    handleData(res) {

      ///////////如果不分页，且数据过多时，采取分部加载/////////////////// 
      this.isLoadDataByPart = false // 重置
      this.loadDataByPartConfig = Object.assign({}, _loadDataByPartConfig) // 重置
      // if (!!!this.showPagination) {
      //   if (res.Datas.length > 100) {
      //     this.isLoadDataByPart = true
      //     this.originResData = cloneDeep(res)
      //     this.loadDataByPartConfig.totalPages = (this.originResData.TotalRows % this.loadDataByPartConfig.pageSize == 0 ? this.originResData.TotalRows / this.loadDataByPartConfig.pageSize : Math.ceil(this.originResData.TotalRows / this.loadDataByPartConfig.pageSize))
      //     console.log("this.loadDataByPartConfig.totalPages", this.loadDataByPartConfig.totalPages)
      //     res.Datas = this.originResData.Datas.slice(0, 100)
      //     console.warn("====数据量过大，将采取滚动局部加载...====")
      //   }
      // }
      ////////////////如果不分页，且数据过多时，采取分部加载//////////////
      if (this.requstConfig.actionName && this.requstConfig.actionName != "Detail") {
        // 列表 查询
        this.gridDefaultOptions.data = []
        // 需要反序列化数据[get,post,文件]
        if ([4, 5].includes(res.Data)) {
          res.Datas = JSON.parse(res.Datas)
        }
        if (res && res.Datas) {
          if (!!this.dataSetSelectedModel) {
            // 返回多集合时候
            this.gridDefaultOptions.data = res.Datas[this.dataSetSelectedModel]
          } else {
            this.gridDefaultOptions.data = res.Datas
          }
          if (Object.keys(this.treeConfigCustom).length > 0 && this.treeConfigCustom.transform && this.layoutLevel == 1) {
            // 如果是为树结构，存储表格树数据
            this.$store.commit("set_tableFullData", cloneDeep(res.Datas));
          }

          if (res.Datas.length > 0 && !this.designState && this.layoutLevel == 1) {
            let firstRow = res.Datas[0]
            this.publicAttribute.row = firstRow
            this.publicAttribute.totalRows = this.tabelConfigs.footerConfig.pageConfig.total // 当前选中行
            // this.publicAttribute.tableData = this.getTableFullData()
            this.setPublicAttr()
            if (this.layoutLevel == 1 && !this.designState) {
              this.$store.commit("set_currentRow", firstRow);
            }
           
          }
          // 保存查询数据到缓存中
          this.saveTableToVuex(res.Datas)
        }
        // 渲染数据
        this.reloadData()
      } else {
        //debugger
        //详情查询
        this.gridDefaultOptions.data = []
        if (res && res.Datas.length > 0) {
          if (!!this.dataSetSelectedModel) {
            // 返回多集合时候
            this.gridDefaultOptions.data = res.Datas[this.dataSetSelectedModel]
          } else {

            this.gridDefaultOptions.data = res.Datas
          }
          // 保存查询数据到缓存中
          this.saveTableToVuex(res.Datas)
          // 储存详细信息列表
          this.$store.commit("set_currentRowDetail", this.gridDefaultOptions.data);
        }
        // 渲染数据
        this.reloadData()
      }
    },
    // 处理表头信息
    async handleColumns(res) {
      let columnslist = res.columns
      this.gridDefaultOptions.columns = await this.formatColumns(columnslist)
    },
    // 加载列头
    reloadColumn() {
      if (!this.gridDefaultOptions.columns) {
        // 如果为空，直接返回
        return;
      }
      nextTick(() => {
        const $grid = this.getRefInstance();
        $grid.reloadColumn(this.gridDefaultOptions.columns);
        // 加载列头完毕
        this.$emit('afterLoadColumns', this.gridDefaultOptions.columns)
      });
    },
    // 加载数据
    async reloadData() {
      //debugger
      let _self = this
      console.log("======reloadData event=======")
      //=================reloadData=====================
      this.currentRow = null;
      const $grid = this.getRefInstance();
      if (!!!$grid) {
        return
      }
      if (!!this.treeConfigCustom.transform) {
        let formatTeeData = this.toTree(cloneDeep(this.gridDefaultOptions.data))

        $grid.reloadData(formatTeeData);
      } else {
        $grid.reloadData(this.gridDefaultOptions.data);
      }

      //==================setCurrentRowEvent============
      this.$nextTick(() => {

        let afterLoadDataParams = {
          currentRow: null,// 当前选中行
          layoutLevel: this.layoutLevel,
          tableData: [],
          refName: this.refName,
        }
        if (this.gridDefaultOptions.data && this.gridDefaultOptions.data.length > 0) {
          //debugger
          this.currentRow = this.gridDefaultOptions.data[0]
          // 添加控制条件，是否立即选中行后立即触发控件列表
          if (!!this.dataSearchOptions.triggerFirstRowCtrlList) {
           setTimeout(() => {
              // 触发控件列表 
                if(_self.currentRow){
                  _self.triggerCtrlNames(2)
                }
              
              }, 300)
          }
          this.viewdetailCache(this.currentRow)
          // 设置第一行高亮,
          _self.setCurrentRowEvent(this.currentRow)
          setTimeout(() => {
            // 设置第一行高亮,
            _self.setFirstRowAsCurrentRow()
          }, 300)
          //================afterLoadData===================
          afterLoadDataParams = {
            currentRow: this.currentRow,// 当前选中行
            layoutLevel: this.layoutLevel,
            tableData: this.gridDefaultOptions.data,
            refName: this.refName,
          }
        } else {
          // fixed 主从表时，主表数据为空，从表需要this.publicAttribute.row 查询数据，因此需要清空主表 上次选择的当前行
          this.publicAttribute.row = {}
          // 触发控件列表// 左右结构，左树过滤表格，死循环，默认表格被动加载，空数据
          if (!!this.dataSearchOptions.triggerWhenNoDataCtrlList) {
            this.triggerCtrlNames(3)
          }
        }    
        console.log("=======afterLoadData========")
        // 绑定行拖动方法
        this.bindingDrapData()
        this.$emit('afterLoadData', afterLoadDataParams)
        // 共享数据源控件
        //debugger
        if (!!this.dataSearchOptions.isShareDataTableCtrl) {
          let tempData = {
            key: this.refName,
            value: this.gridDefaultOptions.data
          }
          this.$store.commit("set_ctrlShareData", tempData)
          emitterFn.emit("afterLoadTableData", afterLoadDataParams)
          console.log("=======afterLoadTableData共享数据源控件========")
        }
      });
    },
    // 当数据过多且不分页时，采取分部加载数据，
    loadDataByPart() {

    },
    async formatColumns(columns) {
      let formatDataList = []
      let _dynamicColumns = []
      // 是否用启用动态列头
      if (!!this.dataSearchOptions.activeDynamicColumn) {
        _dynamicColumns = await this.dynamicColumns()
      }
      // 普通列 合并 动态列
      let newMergeCol = columns.concat(_dynamicColumns)
      formatDataList = await this.filterColumns(newMergeCol)
      return formatDataList
    },
    filterColumnDataMethod(tableInfo){
      let { option, row,column} = tableInfo
      return row[column.field].includes(option.data) 
    },
    isChinese(character) {
      return /^[\u4e00-\u9fff]+$/.test(character);
    },
    async filterColumns(columns) {
      let initList = []
      let _self = this
      for (let i = 0; i < columns.length; i++) {
        let item = columns[i]
        // 表头默认格式对象
        let newItem = {
          field: item.field,
          // filters: [{ data: '' }],
          // filteRender:{},
          // showOverflow:true,
          // showHeaderOverflow:true,
          // showFooterOverflow:true,
          title: !!item.title ? item.title : item.field,
          headerClassName: "fieldName-" + item.field,
          width: Number(item.width) > 0 ? item.width : '',//如果为空则均匀分配剩余宽度
          minWidth: Number(item.width) == 0 ? 200 : item.width,
          align: !!item.align ? item.align : "center",
          headerAlign: !!item.headerAlign ? item.headerAlign : "center",
          visible: !!item.iisShowList,// 是否列表显示
          treeNode: false,// 默认非节点字段
          params: {
            controlType: item.controlType,// 控件类型,
            insertAtDefaultValue: item.fieldDefault// 自定义新增时，默认值

          },// 自定义参数
        }
           // 是否固定
        if (!!item.fixed) {
          newItem.fixed = item.fixed
        }
        if(Number(item.width) ==-1 && newItem.title){
         // debugger
             try{
              if(this.isChinese(newItem.title)){
                  //中文个数*30
                  newItem.width = (newItem.title.length) * 30
                  newItem.minWidth = null
              }else{
                // 英语个数*15
                newItem.width = (newItem.title.length) * 15
                newItem.minWidth = null
              }
             }catch (e){
               newItem.width = 200
             }
         }
        // 显示过滤 filters: 
        if (!!item.iisQuery) {
          newItem.filters = [{ data: '' }]
          newItem.filterMethod = this.filterColumnDataMethod
          if(newItem.hasOwnProperty("slots")){
            newItem.slots.filter = "inputBox_filter"
          }else{
            newItem.slots= {filter:'inputBox_filter'}
          }
        }
        //NEW 格式化指定格式
        if (!!item.fieldFormat) {
          newItem.formatter = item.fieldFormat
        }
        // 配置节点字段
        if (!!_self.columnsTreeNode && item.field == _self.columnsTreeNode && !!item.iisShowList) {
          newItem.treeNode = true
        }
        // 自定义验证规则
        if (item.hasOwnProperty('fieldRules') && !!item.fieldRules) {
          newItem.params["fieldRules"] = item.fieldRules
        }
        // 自定义渲染字段cellClass
        if (item.hasOwnProperty("customRender")) {
          // console.log("===item.customRender===="+item.customRender);
          newItem.customRender = item.customRender
          if (newItem.customRender.columnType == "customStatus") {
            if (newItem.hasOwnProperty("params")) {
              newItem.params.customRender = item.customRender
            } else {
              newItem.params = {}
              newItem.params.customRender = item.customRender
            }
          } else if (newItem.customRender.columnType == "status") {
            newItem.cellRender = { name: 'statusColumn' }
          }
        }
        // 需要汇总的字段
        if (!!item.iisSummary) {
          this.summaryFieldList.push(item.field)
        }

        // 标题前缀图标配置项
        if (!!item.titleHelp) {
          newItem.titlePrefix = {
            content: `${item.titleHelp}`
          }
        }
        // 默認 全部添加文本框过滤 cellRender:item.cellRender,itemType:item.cellRender.name
        //  if(true){
        //     newItem.filters = [{data: null}]
        //     newItem.filterRender= {name:'FilterContent'}
        // }
        // 格式化指定格式
        if (!!item.formatter) {
          newItem.formatter = item.formatter
        }
        // if (!!item.children && item.children.length > 0) {
        //   // 子表头处理
        //   newItem.children = _self.formatColumns(item.children)
        // }
        // 添加排序
        if (!!item.iisSortable) {
          newItem.sortable = true
        }
        // 查询资料
        if (!!item.controlType && item.controlType === 'popupTextarea') {
          // item 字段配置信息
          newItem.cellRender = { name: 'popupTextarea', attrs: { item: item } }
        }
        // 添加渲染的自定义控件
        if (!!item.cellRender && item.cellRender.name === 'checkbox') {
          newItem.cellRender = { name: 'vxeCheckbox' }
        }
        // 添加渲染的自定义控件
        if (!!item.cellRender && item.cellRender.name === 'disvxeCheckbox') {
          newItem.cellRender = { name: 'vxeCheckbox' }
        }
        // 添加渲染的自定义控件
        if (!!item.controlType && item.controlType === 'statusColumn') {
          let statusColumn_cellRender={
              paramsItem: item,// 控件配置信息
              defaultValue: '',// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'statusColumn'// 控件类型
          }
          newItem.cellRender = { name: 'statusColumn',options: statusColumn_cellRender, defaultValue: '' }
        }
        if (!!item.controlType && item.controlType === 'statusColumnYN') {
          newItem.cellRender = { name: 'statusColumnYN' }
        }
        if (!!item.controlType && item.controlType === 'statusColumnTF') {
          newItem.cellRender = { name: 'statusColumnTF' }
        }
        if (!!item.controlType && item.controlType === 'statusColumn10') {
          newItem.cellRender = { name: 'statusColumn10' }
        }
        // ICON图文控件
        if (!!item.controlType && item.controlType === 'IconTextColumn') {
          let IconTextColumn_cellRender={
              paramsItem: item,// 控件配置信息
              //defaultValue: '',// 默认值
              field: item.field,  // 字段名称
              fieldRules:item.fieldRules,
              //disabled: item.iisReadOnly,// 是否可以编辑
              //controlType: 'IconTextColumn'// 控件类型
          }
          newItem.cellRender = { name: 'IconTextColumn',options: IconTextColumn_cellRender, defaultValue: '' }
        }
        //================只读项==特殊控件==================================
        // if (!!item.controlType && item.controlType === 'customSelect' && !!item.iisReadOnly) {

        //   let customSelectOptions_cellRender = {
        //       paramsItem: item,// 控件配置信息
        //       defaultValue: item.fieldDefault,// 默认值
        //       field: item.field,  // 字段名称
        //       disabled: item.iisReadOnly,// 是否可以编辑
        //       controlType: 'customSelect'// 控件类型
        //     }
        //     newItem.cellRender = { name: "customSelect", options: customSelectOptions_cellRender, defaultValue: item.fieldDefault };
        // }

        //==============编辑配置==非只读项才能编辑=&& !item.iisReadOnly===================================
        this.keepSource = false
        if (this.dataSearchOptions.activeEditFn) {
          this.keepSource = true // 编辑时，需要启用，保持原始值的状态，被某些功能所依赖，比如编辑状态、还原数据等（开启后影响性能，具体取决于数据量）
          this.editConfig = {
            trigger: 'click', mode: 'cell', showStatus: true
          }
          //=======================下拉框===============================
          if (!!item.controlType && item.controlType === 'customRenderCtrl') {

            let customRenderCtrlOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'customRenderCtrl',// 控件类型
            }

            // 只读 cellRender
            newItem.cellRender = { name: "customRenderCtrl", options: customRenderCtrlOptions, defaultValue: item.fieldDefault };
          }
          //=======================下拉框===============================
          if (!!item.controlType && item.controlType === 'reportSelect') {

            let reportSelectOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'reportSelect',// 控件类型
              optionDataList: []
            }
            reportSelectOptions.optionDataList = await this.handleReportSelectFn.getOptionDataList(reportSelectOptions)
            if (!!item.iisReadOnly) {
              // 只读 cellRender
              newItem.cellRender = { name: "reportSelect", options: reportSelectOptions, defaultValue: item.fieldDefault };
            } else {
              // 可编辑 editRender
              newItem.editRender = { name: "reportSelect", options: reportSelectOptions, defaultValue: item.fieldDefault };
            }
          }
          //=======================下拉框(多选)===============================
          if (!!item.controlType && item.controlType === 'reportMulSelect') {

            let reportMulSelectOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'reportMulSelect',// 控件类型
              optionDataList: []
            }
            reportMulSelectOptions.optionDataList = await this.handleReportSelectFn.getOptionDataList(reportMulSelectOptions)
            // debugger

            if (!!item.iisReadOnly) {
              // 只读 cellRender
              newItem.cellRender = { name: "reportMulSelect", options: reportMulSelectOptions, defaultValue: item.fieldDefault };
            } else {
              // 可编辑 editRender
              newItem.editRender = { name: "reportMulSelect", options: reportMulSelectOptions, defaultValue: item.fieldDefault };
            }
          }
          //=======================下拉框(表)===============================
          if (!!item.controlType && item.controlType === 'reportSelectTable') {
            let reportSelectTableOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'reportSelectTable',// 控件类型
              optionDataList: []
            }
            reportSelectTableOptions.optionDataList = await this.handleReportSelectTableFn.getOptionDataList(reportSelectTableOptions)

            if (!!item.iisReadOnly) {
              // 只读 cellRender
              newItem.cellRender = { name: "reportSelectTable", options: reportSelectTableOptions, defaultValue: item.fieldDefault };
            } else {
              // 可编辑 editRender
              newItem.editRender = { name: "reportSelectTable", options: reportSelectTableOptions, defaultValue: item.fieldDefault };
            }
          }
          //=======================下拉框(自定义)===============================
          if (!!item.controlType && item.controlType === 'customSelect') {

            let customSelectOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'customSelect'// 控件类型
            }

            if (!!item.iisReadOnly) {
              // 只读 cellRender
              newItem.cellRender = { name: "customSelect", options: customSelectOptions, defaultValue: item.fieldDefault };
            } else {
              // 可编辑 editRender
              newItem.editRender = { name: "customSelect", options: customSelectOptions, defaultValue: item.fieldDefault };
            }
          }
          //=======================标准弹框===============================
          if (!!item.controlType && item.controlType == 'standardFrame') {
            //console.log("========standardFrame========")

            let standardFrameOptions = {
              paramsItem: item,// 控件配置信息
              defaultValue: item.fieldDefault,// 默认值
              field: item.field,  // 字段名称
              disabled: item.iisReadOnly,// 是否可以编辑
              controlType: 'standardFrame'// 控件类型
            }

            if (!!item.iisReadOnly) {
              // 只读 cellRender
              newItem.cellRender = { name: "standardFrame", options: standardFrameOptions, defaultValue: item.fieldDefault };
            } else {
              // 可编辑 editRender
              newItem.editRender = { name: "standardFrame", options: standardFrameOptions, defaultValue: item.fieldDefault };
            }
          }

          if (!!item.controlType && item.controlType === 'number' && !item.iisReadOnly) {
            newItem.editRender = { name: '$input', placeholder: "0", autoselect: true, props: { clearable: true, transfer: true, type: item.controlType }, defaultValue: 0 }
          }
          if (!!item.controlType && item.controlType === 'date' && !item.iisReadOnly) {
            newItem.editRender = { name: '$input', placeholder: "", autoselect: true, props: { clearable: true, transfer: true, type: item.controlType }, defaultValue: null }
          }
          if (!!item.controlType && item.controlType === 'dateTime' && !item.iisReadOnly) {
            newItem.editRender = { name: '$input', placeholder: "", autoselect: true, props: { clearable: true, transfer: true, type: item.controlType }, defaultValue: null }
          }
          if (!!item.controlType && item.controlType === 'month' && !item.iisReadOnly) {
            newItem.editRender = { name: '$input', placeholder: "", autoselect: true, props: { clearable: true, transfer: true, type: item.controlType }, defaultValue: null }
          }
          // 默认编辑类型
          if (!!item.controlType && item.controlType === 'text' && !item.iisReadOnly) {
            newItem.editRender = { name: '$input', placeholder: "", autoselect: true, props: { clearable: true, transfer: true, type: 'text' }, defaultValue: '' }
          }

        }
        //==============编辑配置=============this.dataSearchOptions=========================
        initList.push(newItem)
      }
      // 是否显示汇总行
      this.showFooterSetting = false
      if (this.summaryFieldList.length > 0) {
        this.showFooterSetting = true
      }

      if (!!this.dataSearchOptions.treeConfig.expandAll) {
        console.log("=====this.dataSearchOptions.expandAll=====" + this.dataSearchOptions.treeConfig.expandAll)
        this.localTreeConfig.expandAll = true
      }
      return initList
    },
    // 获取动态列
    async dynamicColumns() {
      //debugger
      let _newColumnsList = []
      let _dynamicColumnApi = this.dataSearchOptions.dynamicColumnApi
      if (!!_dynamicColumnApi) {
        let _VFormSearchParams = await this.useFormatParamsFn.getDBDataByActionName(_dynamicColumnApi);
        if (_VFormSearchParams && _VFormSearchParams.length > 0) {
          // 格式化动态列头信息
          _VFormSearchParams.forEach(oldItem => {
            // CFIELDS 固定 字段 不变，主要是 动态列的数据值的 对象组
            let newItemCol = {
              align: "right",
              controlType: "number", // 默认控件类型,后期可扩展
              field: `CFIELDS.${oldItem.Key}`,// 数据值 字段名称
              title: oldItem.Value,
              fieldDefault: 0,// 默认值
              fieldOrder: 100,
              fieldRules: "",
              groupTitle: "",
              iisHQuery: 0,
              iisMoney: 0,
              iisQuery: 0,
              iisReadOnly: !this.dataSearchOptions.canEditDynamicColumn,// 是否可以编辑
              iisShowList: 1,
              iisSortable: 0,
              iisSummary: !!this.dataSearchOptions.needSumDynamicColumn,// 是否需要汇总
              matchField: "",
              searchParams: "",
              sourceKey: "",
              titleAlign: "center",
              titleHelp: "",
              width: 200,// 默认宽度,后期可扩展
            }
            _newColumnsList.push(newItemCol)
          })

        }
      }
      return _newColumnsList
    },
    // 初始化基础数据和其它设置等
    initData() {
      // debugger
      let _self = this;
      if (Object.keys(this.requstConfig).length > 0 && !!this.requstConfig.postUrl) {
        _self.searchTableData()
      } else {
        if (_self.data && _self.data.length > 0) {
          _self.gridDefaultOptions.data = _self.data;
          // 渲染数据
          _self.reloadData()
          return
        }
      }
    },
    // 设置自定义列头信息
    setCustomColumns(dataColumnsList) {

      // 添加操作列
      if (this.showOperationColumn) {
        // 检查是否存在 操作列表
        let operationColumnIndex = dataColumnsList.findIndex(item => {
          if (!!item.slots && item.slots.default == 'operate') {
            return true
          } else {
            return false
          }

        })
        if (operationColumnIndex == -1) {
          //操作列 插入 指定位置
          dataColumnsList.splice(1, 0, ...this.operationColumn)
          //  操作列 插入 第一个位置
          // dataColumnsList.unshift(...this.operationColumn)
        }
      } else {
        // 检查是否存在 如果存在则移除
        let operationColumnIndex = dataColumnsList.findIndex(item => {
          if (!!item.slots && item.slots.default == 'operate') {
            return true
          } else {
            return false
          }

        })
        if (operationColumnIndex != -1) {
          // 移除
          dataColumnsList.splice(operationColumnIndex, 1)
        }
      }
      if (this.showDragColumn) {
        // 检查是否存在 操作列表
        let dragColumnIndex = dataColumnsList.findIndex(item => {
          if (!!item.slots && item.slots.default == 'dragColumn') {
            return true
          } else {
            return false
          }

        })
        if (dragColumnIndex == -1) {
          //操作列 插入 指定位置
          dataColumnsList.splice(1, 0, ...this.dragColumn)
          //  操作列 插入 第一个位置
          // dataColumnsList.unshift(...this.operationColumn)
        }
      } else {
        // 检查是否存在 如果存在则移除
        let dragColumnIndex = dataColumnsList.findIndex(item => {
          if (!!item.slots && item.slots.default == 'dragColumn') {
            return true
          } else {
            return false
          }

        })
        if (dragColumnIndex != -1) {
          // 移除
          dataColumnsList.splice(dragColumnIndex, 1)
        }
      }
      // 是否添加序号列
      if (this.showSeqColumn) {
        // 检查是否存在 默认下标
        let seqColumnIndex = dataColumnsList.findIndex(item => {
          return item.type == 'seq'
        })
        if (seqColumnIndex == -1) {
          // 默认下标  插入 第一个位置
          dataColumnsList.unshift(...this.seqColumn)
        }
      } else {
        // 检查是否存在 如果存在，则需要移除
        let seqColumnIndex = dataColumnsList.findIndex(item => {
          return item.type == 'seq'
        })
        if (seqColumnIndex != -1) {
          // 移除
          dataColumnsList.splice(seqColumnIndex, 1)
        }
      }
      // 是否添加序号列
      if (this.showCheckBoxColumn) {
        // 检查是否存在 默认下标
        let checkboxColumnIndex = dataColumnsList.findIndex(item => {
          return item.type == 'checkbox'
        })
        if (checkboxColumnIndex == -1) {
          // 默认下标  插入 第一个位置
          dataColumnsList.unshift(...this.checkBoxColumn)
        }
      } else {
        // 检查是否存在 如果存在，则需要移除
        let checkboxColumnIndex = dataColumnsList.findIndex(item => {
          return item.type == 'checkbox'
        })
        if (checkboxColumnIndex != -1) {
          // 移除
          dataColumnsList.splice(checkboxColumnIndex, 1)
        }
      }
      return dataColumnsList
    },
    // 初始化列表信息和其它配置
    async initColumns() {
      // debugger
      let _self = this;
      let columnsData = []
      // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）
      if (Object.keys(this.dataSearchOptions).length > 0) {
        //是否显示分页
        // debugger
        if (!!!this.showPagination) {
          this.dataSearchOptions.tabelConfigs.footerConfig.pageConfig.pageSize = 999999999
        }
        this.tabelConfigs.footerConfig.pageConfig.pageSize = this.dataSearchOptions.tabelConfigs.footerConfig.pageConfig.pageSize
        this.publicAttribute.pageSize = this.dataSearchOptions.tabelConfigs.footerConfig.pageConfig.pageSize
      }
      // 直接使用参数 columns 的数据
      if (_self.columns && _self.columns.length > 0) {
        //debugger
        if (this.needFormatColunms) {
          // 格式化列头
          columnsData = await _self.formatColumns(_self.columns);
          //debugger

        } else {
          columnsData = _self.columns;
          //debugger
        }
        // 设置自定义列头信息
        columnsData = this.setCustomColumns(columnsData)
        // 赋值列头信息
        _self.gridDefaultOptions.columns = columnsData
        _self.$nextTick(() => {
          // 加载列头完毕
          _self.$emit('afterLoadColumns', columnsData)
        })

        return
      }
      // 根据searchColumnsName 查询
      if (_self.searchColumnsName != "") {
        _self.searchTableColumns()
      }
    },
    /////////////行拖 方法/////////////
    // 绑定数据拖拽数据 orderFields
    bindingDrapData() {
      //debugger
      if (this.showDragColumn) {
        // debugger
        if (this.treeConfigCustom.transform) {
          this.treeDrop()
        } else {
          this.rowDrop()
        }
      }
    },
    //扁平数据转为树状结构数据
    toTree(arr) {
      // debugger
      //先检测是不是数组类型
      if (!Array.isArray(arr)) {
        return [];
      }
      // JS的对象就是hash表
      const obj = {};
      arr.forEach((item) => {
        obj[item[this.treeConfigCustom.rowField]] = item;
      });
      const targetArr = [];
      arr.forEach((item) => {
        const parent = obj[item[this.treeConfigCustom.parentField]];//有pId就说明他有父亲，找到他的父亲parent
        if (parent) {  //如果他有父亲，就给他添加children属性
          parent.children = parent.children || [];
          parent.children.push(item);
        } else {  //他没有父亲，就把当前项push进去（顶层）
          targetArr.push(item);
        }
      });
      return targetArr;
    },
    // 保存拖拽后的数据
    ///DataStruct 0:常规结构
    ///DataStruct 1:树形结构
    saveDragRowData(CurrentId, TargetId, moveType = 'down', DataStruct = 1) {
      if(!this.activeDragRowApiUrl){
        this.$message({
            type: 'error',
            message: '没有对应的保存接口API,拖拽保存失败!'
          });
        return
      }
      let moveTypeValue = 10
      if (moveType == 'center') {
        moveTypeValue = 20
      } else {
        moveTypeValue = (moveType == 'down' ? 10 : 0)
      }
      //let _url = `api/Co/TaskManager/ChangeTreeSeq?cid=${CurrentId}&rid=${TargetId}&type=${moveTypeValue}&tableName=${tableName}`
      let _url = this.activeDragRowApiUrl//`api/Co/TaskManager/ChangeSeq`
      let params = {
        CurrentId,
        TargetId,
        Type: moveTypeValue,
        DataStruct,
      }

      request["post"](_url, params).then(res => {
        if (!!res.Success) {
          this.$message({
            type: 'success',
            message: !!res.Content ? res.Content : '拖拽保存成功!'
          });
          this.searchTableDataAndSelectdOldData()
        } else {
          this.$message({
            type: 'error',
            message: !!res.Content ? res.Content : '拖拽保存失败!'
          });
        }

      });
    },
    treeDrop() {
      this.$nextTick(() => {
        //debugger
        const xTable = this.getRefInstance();
        this.sortableTreeDropObj = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          handle: '.drag-btn',
          // chosenClass: "sortable-chosen",  // Class name for the chosen item
          animation: 150,
          direction: "vertical",
          onEnd: (evt) => {
            this.$store.commit("set_routerName", this.$route.name);
            let { item, newIndex, oldIndex } = evt
            let offsetX = evt.originalEvent.offsetX
            // debugger
            console.log("======= offsetX=====" + evt.originalEvent.offsetX)
            let moveType = ((offsetX > 100) ? 'center' : (newIndex - oldIndex) > 0 ? 'down' : 'up')
            const options = { children: 'children' }
            // debugger
            const targetTrElem = item
            const wrapperElem = targetTrElem.parentNode
            const prevTrElem = targetTrElem.previousElementSibling
            const nextTrElem = targetTrElem.nextElementSibling
            const tableTreeData = xTable.getTableData().fullData
            console.warn("==tree==moveType=====" + moveType, "   offsetX:" + offsetX)
            const selfRow = xTable.getRowNode(targetTrElem).item
            const selfNode = XEUtils.findTree(tableTreeData, row => row === selfRow, options)
            if (moveType == 'up') {
              //debugger
              if (nextTrElem) {
                // 移动到节点
                const nextRow = xTable.getRowNode(nextTrElem).item
                const nextNode = XEUtils.findTree(tableTreeData, row => row === nextRow, options)
                if (XEUtils.findTree(selfRow[options.children], row => nextRow === row, options)) {
                  // 错误的移动
                  const oldTrElem = wrapperElem.children[oldIndex]
                  wrapperElem.insertBefore(targetTrElem, oldTrElem)
                  this.$message({
                    type: 'warning',
                    message: '不允许自己给自己拖动！'
                  });
                  return
                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]

                if (xTable.isTreeExpandByRow(nextRow)) {
                  try {
                    // 移动到当前的子节点
                    console.log("TargetId====nextRow.index===CID:" + nextRow.CID)
                    console.log("CurrentId====currRow.index===CID:" + currRow.CID)
                    let CurrentId = currRow.CID
                    let TargetId = nextRow.CID
                    if (this.layoutLevel == 1 && !this.designState) {
                        this.$store.commit("set_currentRow", currRow);
                    }
                  
                    this.saveDragRowData(CurrentId, TargetId, moveType)
                  } catch (error) {

                  }
                  nextRow[options.children].splice(0, 0, currRow)
                } else {
                  try {
                    // // 移动到相邻节点
                    console.log("TargetId====prevNode.index===CID:" + nextNode.item.CID)
                    console.log("CurrentId====selfNode.index===CID:" + selfNode.item.CID)
                    let CurrentId = selfNode.item.CID
                    let TargetId = nextNode.item.CID
                    // 意为拖到最后一行
                    // if(!!!targetTrElem.nextElementSibling){
                    //      TargetId =0
                    // }
                    if (this.layoutLevel == 1 && !this.designState) {
                       this.$store.commit("set_currentRow", selfNode.item);
                    }
                   
                    this.saveDragRowData(CurrentId, TargetId, moveType)
                  } catch (error) {

                  }

                  nextNode.items.splice(nextNode.index + (selfNode.index < nextNode.index ? 0 : 1), 0, currRow)
                  //debugger

                }
              } else {
                try {
                  // 移动到第一行
                  console.log("TargetId====移动到第一行===CID:" + tableTreeData[0].CID)
                  console.log("CurrentId====移动到第一行===CID:" + selfNode.item.CID)
                  let CurrentId = selfNode.item.CID
                  let TargetId = tableTreeData[0].CID
                  // let TargetId =0 // 直接设置为0 
                  if (this.layoutLevel == 1 && !this.designState) {
                     this.$store.commit("set_currentRow", selfNode.item);
                  }
                 
                  this.saveDragRowData(CurrentId, TargetId, moveType)
                } catch (error) {

                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                //debugger
                tableTreeData.unshift(currRow)
              }
            } else {
              if (prevTrElem) {
                // 移动到节点
                const prevRow = xTable.getRowNode(prevTrElem).item
                // const nextRow = xTable.getRowNode(nextTrElem).item
                const prevNode = XEUtils.findTree(tableTreeData, row => row === prevRow, options)
                // const nextNode = XEUtils.findTree(tableTreeData, row => row === nextRow, options)
                if (XEUtils.findTree(selfRow[options.children], row => prevRow === row, options)) {
                  // 错误的移动
                  const oldTrElem = wrapperElem.children[oldIndex]
                  wrapperElem.insertBefore(targetTrElem, oldTrElem)
                  this.$message({
                    type: 'warning',
                    message: '不允许自己给自己拖动！'
                  });
                  return
                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                if (xTable.isTreeExpandByRow(prevRow)) {
                  try {
                    // 移动到当前的子节点
                    console.log("TargetId====prevRow.index===CID:" + prevRow.CID)
                    console.log("CurrentId====currRow.index===CID:" + currRow.CID)
                    let CurrentId = currRow.CID
                    let TargetId = prevRow.CID
                    if (this.layoutLevel == 1 && !this.designState) {
                       this.$store.commit("set_currentRow", currRow);
                    }
                   
                    this.saveDragRowData(CurrentId, TargetId, moveType)
                  } catch (error) {

                  }
                  prevRow[options.children].splice(0, 0, currRow)
                } else {
                  try {
                    // // 移动到相邻节点
                    console.log("TargetId====prevNode.index===CID:" + prevNode.item.CID)
                    console.log("CurrentId====selfNode.index===CID:" + selfNode.item.CID)
                    let CurrentId = selfNode.item.CID
                    let TargetId = prevNode.item.CID
                    // 意为拖到最后一行
                    // if(!!!targetTrElem.nextElementSibling){
                    //      TargetId =0
                    // }
                    if (this.layoutLevel == 1 && !this.designState) {
                      this.$store.commit("set_currentRow", selfNode.item);
                    }
                    
                    this.saveDragRowData(CurrentId, TargetId, moveType)
                  } catch (error) {

                  }

                  prevNode.items.splice(prevNode.index + (selfNode.index < prevNode.index ? 0 : 1), 0, currRow)
                  //debugger

                }
              } else {
                try {
                  // 移动到第一行
                  console.log("TargetId====移动到第一行===CID:" + tableTreeData[0].CID)
                  console.log("CurrentId====移动到第一行===CID:" + selfNode.item.CID)
                  let CurrentId = selfNode.item.CID
                  let TargetId = tableTreeData[0].CID
                  // let TargetId =0 // 直接设置为0 
                  if (this.layoutLevel == 1 && !this.designState) {
                     this.$store.commit("set_currentRow", selfNode.item);
                  }
                 
                  this.saveDragRowData(CurrentId, TargetId, moveType)
                } catch (error) {

                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                //debugger
                tableTreeData.unshift(currRow)
              }
            }
            // 如果变动了树层级，需要刷新数据
            xTable.reloadData([...tableTreeData])

          }
        })
      })
    },
    // 非树结构拖动方法
    rowDrop() {
      let _self = this
      this.$nextTick(() => {
        this.$store.commit("set_routerName", this.$route.name);
        const xTable = this.getRefInstance();
        let fullData = xTable.getTableData().fullData
        let copyFullData = cloneDeep(fullData)
        this.sortableRowDropObj = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          handle: '.drag-btn',
          animation: 150,
          direction: "vertical",
          onEnd: (evt) => {
            let { newIndex, oldIndex } = evt
            const currRow = fullData.splice(oldIndex, 1)[0]
            fullData.splice(newIndex, 0, currRow)
            let moveType = (newIndex - oldIndex) > 0 ? 'down' : 'up'
            try {
              let CurrentId = copyFullData[oldIndex].CID
              let TargetId = copyFullData[newIndex].CID
              this.saveDragRowData(CurrentId, TargetId, moveType, 0)
              // console.log("====moveType=====" + moveType)
              // console.log("=onEnd===oldIndex==CurrentId========" + oldIndex + "===fullData[oldIndex].CID：" + copyFullData[oldIndex].CID)
              // console.log("=onEnd===newIndex==TargetId========" + newIndex + "===fullData[newIndex].CID：" + copyFullData[newIndex].CID)
            } catch (error) {

            }

            copyFullData = cloneDeep(fullData)
          }
        })
      })
    },
    //////////////////////////
    //====================VForm 对外暴露方法配置=========================
    // 获取自定义默认值
    async getColumnsDefaultValue(subItem) {
      let _self = this
      const $grid = this.getRefInstance();
      let _columns = $grid.getTableColumn().fullColumn
      let _columnFieldsDefaultValObj = {}
      if (_columns && _columns.length > 0) {
        _columns.forEach(item => {
          // debugger
          if (item.hasOwnProperty("params") && !!item.params && item.params.hasOwnProperty("insertAtDefaultValue")) {
            //debugger
            // console.log("=======item.params.insertAtDefaultValue=====:"+item.params.insertAtDefaultValue)
            if (item.params.insertAtDefaultValue != null && item.params.insertAtDefaultValue != undefined && item.params.insertAtDefaultValue != '') {
              //debugger
              switch (item.params.controlType) {
                case "date":
                case "dateTime":
                case 'month':
                  let newDefaultVal = _self.setDateTimeDefaultVal(item.params.controlType, item.params.insertAtDefaultValue)
                  _columnFieldsDefaultValObj[item.field] = newDefaultVal
                  break
                case 'number':
                  _columnFieldsDefaultValObj[item.field] = Number(item.params.insertAtDefaultValue)
                  break
                default:
                  if ((item.params.insertAtDefaultValue + "").includes("{{")) {
                    _columnFieldsDefaultValObj[item.field] = _self.setUserInfoDefaultVal(item.params.controlType, item.params.insertAtDefaultValue)
                  } else {
                    _columnFieldsDefaultValObj[item.field] = item.params.insertAtDefaultValue
                  }

                  break
              }

            }
          }
        })
      }
      if (subItem) {
        // console.log('====subItem====')
        // 获取提交接口的参数值
        //debugger
        let _fieldsDefalutValues = await _self.useFormatParamsFn.getNormalParamsValue(subItem)
        if (_fieldsDefalutValues && Object.keys(_fieldsDefalutValues).length > 0) {
          _columnFieldsDefaultValObj = Object.assign({}, _columnFieldsDefaultValObj, _fieldsDefalutValues)
        }
        //debugger
      }
      // debugger
      return _columnFieldsDefaultValObj
    },

    // 获取用户字段信息
    getUserInfoValue(newValueList) {
      // debugger
      let controlValue = ""
      // debugger
      newValueList.forEach((item, index) => {
        // 逐个参数解析并获取其值
        if (index == 0) {
          controlValue = this.$UserInfo
        } else {
          controlValue = controlValue[item]
        }
      })
      return controlValue
    },
    //(type, defaultVal){
    setUserInfoDefaultVal(type, defaultVal) {
      //debugger
      // 过滤多余字符串：{{$UserInfo.CID}}
      let controlValue = ""
      let newValue = defaultVal.replace('{{', '').replace('}}', '')
      // 分离各个参数
      let newValueList = newValue.split('.')
      if (newValueList && newValueList.length > 0) {
        let _modeltype = newValueList[0]
        if (_modeltype.includes("$UserInfo")) {
          controlValue = this.getUserInfoValue(newValueList)
        }
      }
      return controlValue
    },
    // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
    setDateTimeDefaultVal(type, defaultVal) {
      //debugger
      let dValue = null
      let dateFormatStr = ""
      // 指定日期 格式化 格式
      switch (type) {
        case "date":
          dateFormatStr = 'YYYY-MM-DD'
          break;
        case "dateTime":
        case "datetime":
          dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
          break;
        case 'month':
          dateFormatStr = 'YYYY-MM'
          break
        default:
          dateFormatStr = ""
          break
      }
      if (!!defaultVal) {
        if (defaultVal == 'curdate') {
          // 当前日期
          dValue = dayjs().format(dateFormatStr);
        } else if (defaultVal.includes('-')) {
          // 指定日期加减
          //dayjs().add(7, 'day').format('YYYY-MM-DD');
          //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
          let daysetArray = defaultVal.split('-')
          dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
        } else {
          //空日期
          dValue = null
        }
      }

      return dValue
    },
    // 获取执行方法前 操作类型
    getOnBeforeActionFunctionType() {
      let onBeforeActionFunctionType = ""
      try {
        let _deleteOption = this.dataSearchOptions.operationButtons.filter(item => {
          if (item.value == "iisDelete") {
            return item
          }
        })
        onBeforeActionFunctionType = this.useFormatParamsFn.getActionParamsValue("onBeforeActionFunctionType", _deleteOption[0])

      } catch (error) {
        onBeforeActionFunctionType = ""
      }
      return onBeforeActionFunctionType
    },

    // 添加行
    async insertRowFn(subItem) {
      //debugger
      let _self = this
      //console.log('========insertRowFn OK=======')
      const $grid = this.getRefInstance();
      if ($grid) {
        // 往表格插入临时数据，从指定位置插入一行或多行；第二个参数：row 指定位置、null从第一行插入、-1 从最后插入
        let newItem = {}
        // 获取新增时，字段的自定义默认值
        let defaultItem = await _self.getColumnsDefaultValue(subItem)
        // debugger
        newItem = Object.assign({}, defaultItem)
        $grid.insertAt(newItem, null)
        // 启用主从表汇总功能调用
        this.emitSumTableRowData()
        setTimeout(() => {
          this.setFirstRowAsCurrentRow()
        }, 100)
      }
    },
    // 添加行
    //  async insertRowByCurrentFn(subItem) {
    //     //debugger
    //     let _self = this
    //     //console.log('========insertRowFn OK=======')
    //     const $grid = this.getRefInstance();
    //     if ($grid) {
    //       // 往表格插入临时数据，从指定位置插入一行或多行；第二个参数：row 指定位置、null从第一行插入、-1 从最后插入
    //       let newItem = {}
    //       // 获取新增时，字段的自定义默认值
    //       let defaultItem = await _self.getColumnsDefaultValue(subItem)
    //       // debugger
    //       newItem = Object.assign({}, defaultItem)
    //       let _currentRow = this.getCurrentRecord()
    //       $grid.insertAt(newItem, _currentRow)
    //       // 启用主从表汇总功能调用
    //       this.emitSumTableRowData()
    //       setTimeout(()=>{
    //         this.setFirstRowAsCurrentRow()
    //       },100)
    //     }
    //   },
    // 设置首行为默认选中行
    async setFirstRowAsCurrentRow() {
      //debugger
      let _self = this
      let fullData = this.getTableFullData()
      try {
        this.setCurrentRowEvent(fullData[0])
        // setTimeout(() => {
        //   // 触发控件列表
        //   _self.triggerCtrlNames(4)
        // }, 100)
      } catch (error) {
        this.currentRow = null // 当前选中行
        this.publicAttribute.row = null// 当前选中行
        if (this.layoutLevel == 1 && !this.designState) {
           this.$store.commit("set_currentRow", null);
        }
       
      }

    },
    // 删除行
    async removeRowFn() {
      //debugger
      //console.log('========removeRowFn OK=======')
      let _onBeforeActionFunctionType = this.getOnBeforeActionFunctionType()
      const $grid = this.getRefInstance();
      if ($grid) {
        if (_onBeforeActionFunctionType == "1") {
          // 删除指定行数据，指定 row 或 [row, ...] 删除多条数据，如果为空则删除所有数据
          let fullData = this.getTableFullData("removeRowFn")
          if (fullData && fullData.length > 1) {
            //debugger
            if (this.currentRow) {
              await $grid.remove(this.currentRow)
              this.setFirstRowAsCurrentRow()
              // 启用主从表汇总功能调用
              this.emitSumTableRowData()
            }
          } else {
            this.$message.error('删除失败，至少保留一条记录！')
          }

        } else if (_onBeforeActionFunctionType == "2") {
          // 删除前，确认信息提示
          this.$confirm("确定要删除该数据?", "提示", {
            confirmButtonText: "确认",
            cancelButtonText: "取消",
            type: 'warning'
          }).then(async () => {
            await $grid.remove(this.currentRow)
            this.setFirstRowAsCurrentRow()
          }).catch(error => {

          })
        } else {
          // 什么都不做，直接删除
          if (this.currentRow) {
            await $grid.remove(this.currentRow)
            this.setFirstRowAsCurrentRow()
            // 启用主从表汇总功能调用
            this.emitSumTableRowData()
          }
        }
      }
    },
    // 设置表格数据
    setTableData(dataList = []) {
      const xTable = this.getRefInstance();
      xTable.reloadData(dataList)
    },
    // 获取表格数据
    getTableData() {
      const xTable = this.getRefInstance();
      let fullData = xTable.getTableData().fullData
      return fullData
    },
    //获取表格数据集（获取插入、删除、更改的数据，对于增删改查表格非常方便）{insertRecords, removeRecords, updateRecords}
    getRecordset() {
      //debugger
      const xTable = this.getRefInstance();
      let _recordset = xTable.getRecordset()
      return _recordset
    },
    getCurrentRecordFromCache() {
      let routerName = this.$route.name
      return this.$store.state.currentRow.value[routerName];
    },
    // 获取当前选中行 对象
    getCurrentRecord() {
      let selectedData = {}
      const $grid = this.getRefInstance();
      if ($grid) {
        let fullData = $grid.getTableData().fullData
        if (fullData && fullData.length > 0) {
          selectedData = $grid.getCurrentRecord()
        }

      }
      return selectedData
    },
    //====================VForm 对外暴露方法配置=========================

    // 滚动条事件
    // 滚动条到底部的条件即为 scrollTop + bodyHeight = scrollHeight
    scrollEvent(tableInfo) {
      return
      if (!!!this.showPagination) {
        let { scrollTop, scrollHeight, bodyHeight } = tableInfo
        let _loadMoreDataList = []
        if (scrollHeight - (scrollTop + bodyHeight) < 10) {
          // 如果当前页码小于总页码
          if (this.loadDataByPartConfig.pageIndex <= this.loadDataByPartConfig.totalPages) {
            this.gridDefaultOptions.loading = true
            this.loadDataByPartConfig.pageIndex = (this.loadDataByPartConfig.pageIndex + 1)
            let startData = ((this.loadDataByPartConfig.pageIndex - 1) * this.loadDataByPartConfig.pageSize) //101
            let endData = (this.loadDataByPartConfig.pageIndex * this.loadDataByPartConfig.pageSize)//200
            //如果  endData 大于数组长度， slice 也会一直提取到原数组末尾
            _loadMoreDataList = this.originResData.Datas.slice(startData, endData)
            //debugger
            this.gridDefaultOptions.data = this.gridDefaultOptions.data.concat(_loadMoreDataList)
            const xTable = this.getRefInstance();
            xTable.loadData(this.gridDefaultOptions.data)
            setTimeout(() => {
              this.gridDefaultOptions.loading = false
            }, 300)
          } else {
            if (!this.loadDataByPartConfig.showTip && this.loadDataByPartConfig.totalPages > 1) {
              // this.$message({
              //   type: 'info',
              //   message: '已经到底了'
              // });
              console.warn("====分页已经到底了====")
              this.loadDataByPartConfig.showTip = true
            }

          }
        }

      }
      // console.log('======滚动条事件========scrollTop==:',scrollTop,"scrollHeight:",scrollHeight,"bodyHeight:",bodyHeight)
      // console.log('======滚动条到底部的条件即为scrollHeight-(scrollTop + bodyHeight):',scrollHeight -(scrollTop + bodyHeight))
    }
  },
};
</script>
<style>
.theme--primary .vxe-button--content {
  color: #fff !important;
}
</style>
<style lang="scss" scoped>
::v-deep .vxe-pager--jump {
  .vxe-pager--goto{
    border: 0px solid #dcdfe6;
  }
}
// 表格排序图标位置调整
::v-deep .vxe-table .vxe-cell--sort-vertical-layout{
  height: 1.2em !important
}
</style>
