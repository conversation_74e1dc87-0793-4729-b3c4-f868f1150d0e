<template>
    <div >
        <!-- <div>aaaa</div> -->
        <div>
            <vxe-pager
            align="right"
            size="small"
            :current-page.sync="pageConfig.currentPage"
            :page-size.sync="pageConfig.pageSize"
            :total="pageConfig.totalResult">
          </vxe-pager>
        </div>
    </div>
</template>
<script>
export default {
    name:"nvxe-page",// 自定义分页控件
    data(){
        return {
            pageConfig: {
                currentPage: 1,
                pageSize: 10,
                totalResult: 200
              },
        }
    },
    methods:{

    }
}
</script>
<style lang="scss" scoped>

</style>