<template>
    <vxe-modal :mask-closable="getItemField('maskClosable', false)" :esc-closable="getItemField('escClosable', false)"
        :show-zoom="getItemField('showZoom', false)" :resize="getItemField('resize', false)"
        :show-close="getItemField('showClose', true)" :draggable="getItemField('draggable', false)"
        :dblclickZoom="getItemField('dblclickZoom', false)" :lock-view="getItemField('lockView', true)"
        :mask="getItemField('showMask', true)" :width="getWidth()" :height="getHeight('max')"
        :position="{ top: getTop(), left: getPositionType() }" ref="nvxeModalRef" :title="title"
        :showFooter="getItem<PERSON>ield('showFooter', false)" v-model="showEditBox">
        <!-- :key="$route.fullPath" 注释modal的 transfer 是否将弹框容器插入于 body 内 :transfer="true"，解决每次编辑不断弹框的问题-->
        <el-skeleton :loading="loading_nvxeModal" animated :throttle="10">
            <template v-if="isBasicForm">
                <div style="overflow: auto;" :style="{ height: (getHeight() - 70) + 'px' }" class="">
                    <component :isLoading="loadingForm" :dataSearchOptions="TableHeader"
                        @changeEvent="formRenderChangeEvent" ref="formItemRenderRef"
                        :formRenderConfig="formRenderConfig" :is="dynamicComponent"></component>
                </div>
                <!-- <formItemRender :isLoading="loadingForm"  :dataSearchOptions="TableHeader"  @changeEvent="formRenderChangeEvent" ref="formItemRenderRef" :formRenderConfig="formRenderConfig"></formItemRender> -->
            </template>
            <template v-else>
                <VFormRender style="overflow-y: auto;overflow-x: hidden;" :contentBoxHeight="(getHeight() - 80)"
                    sourceVFormRender="editContentBoxRef.editPreForm" :key="randomKey" :designer="designer"
                    ref="editPreForm">
                </VFormRender>
            </template>
        </el-skeleton>
        <template v-slot:footer>
            <div>
                <!-- 可视化提交API -->
                <el-button v-if="!isBasicForm" size="medium" ref="btn_submit" :loading="loadingBtn"
                    @click="preSubmitEvent()" type="primary">确定
                </el-button>
                <!-- 基础表单提交API -->
                <el-button v-if="isBasicForm" size="medium" ref="btn_submit" :loading="loadingBtn"
                    @click="basicForm_preSubmitEvent()" type="primary">确定
                </el-button>
                <el-button size="medium" ref="btn_cancel" @click="cancelBtn()">取消</el-button>
            </div>
        </template>
    </vxe-modal>
</template>

<script>
const cloneDeep = require("clone-deep");
import config from '@/config'
import request from '@/libs/request'
import {
    useFormatParams
} from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import { useHandleVFormApi } from "@/hooks/useHandleVFormApi"
import VFormRender from '@/components/form-render/index'
export default {
    name: "edit-formDesign",
    components: {
        VFormRender
    },
    // 注入列表页面整体实体 ,'sourceVFormRenderState'
    inject: ['getPageInstance'],
    props: {
        formConfig: Object,
    },
    provide() {
        // 主要解决深层次的组件嵌套，祖先组件向子孙组件之间传值。
        return {
            //  返回当前页面整体实例
            sourceVFormRenderState: "editContentBoxRef.editPreForm",
        }
    },
    data() {
        return {
            maxLoadTime_formItemRenderRef: 6,
            config,
            loadingForm: false,
            TableHeader: null,
            TableDetail: null,
            TableDetailCopy: null,
            dynamicComponent: null,
            formRenderConfig: {
                formData: {}, // 表单字段
                formItems: [], //表单字段渲染条件
                formRules: {}
            },
            isBasicForm: false,
            firstMounted: false,
            refreshKey: "",// 刷新token
            windowHeight: 0,
            windowWidth: 0,
            btn_submit_disabled: true,// 是否禁用提交按钮
            sourceVFormRenderState: "editContentBoxRef.editPreForm",
            randomKey: "edit-formDesign",
            width: 50,// 弹框宽度百分比
            title: "", // 标题
            loading_nvxeModal: false,
            loadingBtn: false,
            showEditBox: false,
            designer: null,
            useFormatParamsFn: useFormatParams(this),
            useHandleVFormApiFn: useHandleVFormApi(this),
            useHandleVFormEventFn: useHandleVFormEvent(this),
            //useFormatParamsFn: useFormatParams(this, "editContentBoxRef"),
            vxeModalAttrs: {
                position: {

                }
            }
        }
    },
    computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
        //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
        actionType_state() {
            return this.$store.state.actionType;
        },
        // 当前主表选中行
        currentRowCache() {
            return this.$store.state.currentRow
        },
        // 表单配置的其它扩展参数信息
        otherParamsObj() {
            let _defaultotherParams = { virtualTableGetDataAPI: null, virtualTablePostDataAPI: null }
            try {
                let _otherParamsObj = JSON.parse(this.TableHeader.otherParams)
                //debugger
                if (_otherParamsObj) {
                    _defaultotherParams = Object.assign({}, _defaultotherParams, _otherParamsObj)
                }

            } catch (error) {
                _defaultotherParams = { virtualTableGetDataAPI: null, virtualTablePostDataAPI: null }
            }
            return _defaultotherParams
        }
    },
    watch: {
        // 监听当前行是否变化
        currentRowCache: {
            handler(n, o) {
                if (!!n) {
                    if (this.showEditBox && this.isBasicForm) {
                        this.currentRowChange()
                    }

                }
            },
            immediate: false,
            deep: true
        },
        // 监听弹框是否打开
        showEditBox(n, o) {
            //debugger
            if (!!n) {
                this.loading_nvxeModal = true
                this.loadingBtn = false,
                    this.randomKey = Math.floor(Math.random() * 10000 + 1)
                this.$nextTick(() => {

                    this.checkActionType()
                    setTimeout(() => {
                        this.loading_nvxeModal = false
                    }, 300)
                })


            } else {
                if (!this.isBasicForm) {
                    this.firstMounted = false
                    this.resetData()
                }

            }
        },
        refreshKey(n) {
            if (this.firstMounted && !this.isBasicForm) {
                this.loadingBtn = false,
                    this.randomKey = Math.floor(Math.random() * 10000 + 1)
                this.$nextTick(() => {
                    this.init()
                })
            }

        }
    },
    mounted() {

    },
    methods: {
        // 初始化主表表单
        initDefaultFormData() {
            // debugger
            let formItems = this.TableDetail // 获取主表 或从表 列头信息
            let filterData = this.getFilterFormData(formItems)
            let formRules = this.getFormRules(formItems)
            this.formRenderConfig.formItems = formItems // 渲染条件
            this.formRenderConfig.formData = filterData // 渲染字段
            this.formRenderConfig.formRules = formRules // 验证规则
            this.$nextTick(() => {
                this.clearFormValidate()
            })

        },
        // 清除 表单验证
        clearFormValidate() {
            // debugger
            let _self = this
            if (!!_self.$refs["formItemRenderRef"] && !!_self.$refs["formItemRenderRef"].$refs["originFormRef"]) {
                _self.$refs["formItemRenderRef"].$refs["originFormRef"].clearValidate()
                _self.maxLoadTime_formItemRenderRef = 6
            } else {
                if (_self.maxLoadTime_formItemRenderRef > 0) {
                    setTimeout(() => {
                        _self.maxLoadTime_formItemRenderRef = _self.maxLoadTime_formItemRenderRef - 1
                        _self.clearFormValidate()
                    }, 300)
                }
            }
        },
        // 获取表单验证规则 ,必填字段
        getFormRules(itemsList) {
            let rulesObj = {}
            itemsList.forEach(item => {

                if (!!item.iisRequired) {
                    // debugger
                    // 必填字段
                    rulesObj[item.field] = [
                        { required: true, message: '请输入' + item.title, trigger: 'blur' },
                    ]
                }
                // 唯一性字段校验的，必须为必填字段才能校验
                if (!!item.iisRequired && !!item.iisCheckUniCode) {
                    let newItem = { validator: this.checkUnicode, trigger: 'blur' }
                    rulesObj[item.field].push(newItem)
                }
                // 其它字段 验证规格
                if (!!item.customRules && item.customRules != "[]") {

                    let customRulesList = JSON.parse(item.customRules)
                    customRulesList.forEach(subItem => {
                        let otherItem = {
                            validator: function (rule, value, cb) {
                                //自定义正则表达式 .replace('/','').replace('/','').replace('/','').replace('/','')
                                let regCode = new Function('return ' + subItem.ruleCode)();//eval(subItem.ruleCode)

                                if (subItem.ruleTest) {
                                    if (!regCode.test(value)) {
                                        return cb()
                                    }
                                    cb(new Error(subItem.message))
                                } else {
                                    if (regCode.test(value)) {
                                        return cb()
                                    }
                                    cb(new Error(subItem.message))
                                }
                            },
                            trigger: 'blur'
                        }
                        if (!Array.isArray(rulesObj[item.field])) {
                            rulesObj[item.field] = []
                        }
                        rulesObj[item.field].push(otherItem)
                    })
                }
            })
            return rulesObj
        },
        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
        checkUnicode(rule, value, callback) {
            let _url = this.config.module + "CheckField"
            let params = {
                "CheckType": 0, // 校验类型 唯一验证
                "Pid": '', // fornName
                "CID": "",// 新增为空，编辑时需要附上
                "Fields": {} // 需要校验的字段，可以多个
            }
            params.Fields[rule.field] = value // 需要验证字段
            params.Pid = this.getRequest("formName")
            if (!params.Pid) {
                callback();
                return
            }
            //debugger
            if (this.actionType_state.value == this.actionType.iisEdit) {
                // 编辑时候，应该赋值CID，【fixed 上一条，下一条 时，此处出现问题】
                //let rowItem =  this.$store.state.currentRow_viewdetail.value[params.Pid];
                let rowItem = this.$store.state.currentRow_viewdetailPreNext.value[params.Pid];
                params.CID = rowItem.CID
            }
            // debugger
            request['post'](_url, params).then(res => {
                //debugger
                if (res && res.Datas && res.Datas.length > 0) {
                    let passOrNot = res.Datas[0].Result
                    if (!passOrNot) {
                        callback(new Error('已存在，不能重复添加！'));
                    } else {
                        callback();
                    }
                }
            })
        },
        // 从渲染条件中过滤表单字段列表和设置初始默认值 表单默认值 设置
        getFilterFormData(dataList) {
            let newFormObj = {}
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (item && !!item.field) {
                        if (item.fieldDefault != "0" && !!item.fieldDefault) {
                            switch (item.controlType) {
                                case "text":
                                    newFormObj[item.field] = item.fieldDefault
                                    // 特殊默认值 唯一ID 生成
                                    if (item.fieldDefault == '{{guid}}') {
                                        newFormObj[item.field] = this.guid()
                                    }
                                    break;
                                case "date":
                                case "dateTime":
                                case "datetime":
                                    // 时间格式必须正确，否则控件会导致无法使用 
                                    if (!!item.fieldDefault) {
                                        newFormObj[item.field] = this.setDateTimeDefaultVal(item.controlType, item.fieldDefault)
                                    } else {
                                        newFormObj[item.field] = null
                                    }

                                    break;
                                case "number":
                                    newFormObj[item.field] = item.fieldDefault + 0
                                    break;
                                default:
                                    newFormObj[item.field] = item.fieldDefault
                                    // 特殊默认值 唯一ID 生成
                                    if (item.fieldDefault == '{{guid}}') {
                                        newFormObj[item.field] = this.guid()
                                    }
                                    break;
                            }
                        } else {
                            newFormObj[item.field] = item.fieldDefault
                        }


                    }
                })
            }
            return newFormObj
        },
        guid() {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        },
        // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year)
        setDateTimeDefaultVal(type, defaultVal) {
            let dValue = null
            let dateFormatStr = ""
            // 指定日期 格式化 格式
            switch (type) {
                case "date":
                    dateFormatStr = 'YYYY-MM-DD'
                    break;
                case "dateTime":
                case "datetime":
                    dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                    break;
                // case 'month':
                //     dateFormatStr = 'YYYY-MM'
                //     break    
                default:
                    dateFormatStr = ""
                    break
            }
            if (!!defaultVal) {
                if (defaultVal == 'curdate') {
                    // 当前日期
                    dValue = dayjs().format(dateFormatStr);
                } else if (defaultVal.includes('-')) {
                    // 指定日期加减
                    //dayjs().add(7, 'day').format('YYYY-MM-DD');
                    //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                    let daysetArray = defaultVal.split('-')
                    dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                } else {
                    //空日期
                    dValue = null
                }
            }

            return dValue
        },
        // 表单回调函数
        formRenderChangeEvent(params) {

        },
        // 延时/动态加载组件
        loadComponent() {
            return new Promise((resolve, reject) => {
                import('@/components/basicForm/formItemRender.vue').then(module => {
                    this.dynamicComponent = module.default || module;
                    resolve()
                }).catch(error => {
                    console.error('Error loading component:', error);
                    reject()
                });
            })

        },
        // 根据ID加载表单模板
        async loadFormTempaleItemByID() {

            let _url = `/api/md/form/Info`
            let _actionParamsObj = this.getItemField("actionParams", { actionName: null })
            let params = {
                pid: _actionParamsObj.actionName//"196854017826886"
            }
            if (!params.pid) {
                return
            }
            //debugger
            let res = await request["post"](_url, params)
            //debugger
            this.TableHeader = res.Datas.MainTable.TableHeader
            this.TableDetail = res.Datas.MainTable.TableDetail
            this.TableDetailCopy = cloneDeep(this.TableDetail)
            this.$nextTick(async () => {
                this.initDefaultFormData()
                this.currentRowChange()
            })
        },
        async currentRowChange() {

            if (this.actionType_state.value == 'iisEdit') {
                this.loadingForm = true
                let resFormItem = await this.getFormItemData()
                setTimeout(() => {
                    this.loadingForm = false
                }, 300)
                this.formRenderConfig.formData = this.getFilterFormData(this.TableDetailCopy)
                if (Array.isArray(resFormItem)) {
                    this.formRenderConfig.formData = Object.assign({}, this.formRenderConfig.formData, resFormItem[0])
                } else {
                    this.formRenderConfig.formData = Object.assign({}, this.formRenderConfig.formData, resFormItem)
                }

            } else {
                this.formRenderConfig.formData = this.getFilterFormData(this.TableDetailCopy)
            }
        },
        // 基础表单->获取表单数据
        async getFormItemData() {

            let _url = `/api/MD/DataSet/GetListByDataSetId`
            let popupName = this.getItemField("value", null)
            let _otherParamsObj = this.getItemField("otherParams", null)
            let paramsList = await this.useFormatParamsFn.getVFormPopupSearchParamsNoDataSetId(popupName, _otherParamsObj.actionType)
            let params = {
                Id: this.otherParamsObj.virtualTableGetDataAPI, // DataSetId
                Parameter: paramsList,//Object.keys(paramsList).length>0? paramsList:this.getCurrentRow()
            }
            if (!params.Id) {
                this.loadingForm = false
                console.warn("==基础表单->获取表单数据==Id 为空！")
                return
            }
            let res = await request["post"](_url, params)

            if (!!res.Success) {
                return res.Datas
            } else {
                return {}
            }

        },
        // 基础表单->提交表单数据【结果集路径查查询路径不一样】
        async postFormItemData() {

            //debugger
            //return
            let _url = `/api/MD/DataSet/ExecuteByDataSetId`
            let params = {
                Id: this.otherParamsObj.virtualTablePostDataAPI, // DataSetId
                Parameter: this.formRenderConfig.formData
            }
            if (!params.Id) {
                console.warn("==基础表单->提交表单数据==Id 为空！")
                return
            }
            let res = await request["post"](_url, params)
            if (!!res.Success) {
                this.$message({
                    message: '提交成功！',
                    type: 'success'
                });
                this.showEditBox = false
                this.basicForm_afterSuccessEvents(res,"basicForm")
            }

        },
        basicForm_afterSuccessEvents(res,boxType='popup') {
            //debugger
            let subItem = this.getItemField(null)
            setTimeout(() => {
                this.useHandleVFormApiFn.afterSuccessEvents(res, subItem)
            }, 600)
            if (subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
                let eventList = subItem.afterSuccessOrErrorEvents
                if (eventList && eventList.length > 0) {
                    for (let i = 0; i < eventList.length; i++) {
                        let eventItem = eventList[i]

                        this.useHandleVFormEventFn.handleCommonClickEvent(eventItem,boxType)
                    }
                }

            }
        },
        getTop() {
            let defaultTop = 0
            try {
                let _top = this.getItemField('position_top')
                if (!!_top) {
                    defaultTop = _top
                }
            } catch (error) {
                defaultTop = 0
            }
            return defaultTop
        },
        getHeight(type = '') {
            //debugger
            let defaultHeight = 600
            try {
                let _height = this.getItemField('height')
                if (!!_height && _height > 0) {
                    defaultHeight = _height
                }
                if (_height == 0) {
                    defaultHeight = this.windowHeight
                    if (!!this.getItemField('showFooter') && type == '') {
                        defaultHeight = defaultHeight - 30
                    }
                }
            } catch (error) {
                defaultHeight = 600
            }

            return defaultHeight
        },
        getPositionType() {
            //debugger
            let _left = 0
            try {
                let _type = this.getItemField('positionType')
                switch (_type) {
                    case "center":
                        _left = 0
                        break;
                    case "left":
                        _left = 1
                        break;
                    case "right":
                        _left = (this.windowWidth - this.getWidth())
                        break;
                    default:
                        _left = 0
                        break;
                }
            } catch (error) {
                _left = 0
            }
            return _left
        },
        getWindowHeightAndWidth() {
            this.windowHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight; // 浏览器高度
            this.windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth; // 浏览器宽度
            // return {windowHeight, windowWidth}
        },

        // 通过字段获取指定的属性
        getItemField(field, defaultValue = null) {
            // debugger
            let itemFieldVal = defaultValue
            try {
                let popupParams = this.$store.state.popupParams;
                let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
                let popupItem = this.getPopupItemByUrl(selfActionName)
                if (!!popupItem && field) {
                    if (popupItem.hasOwnProperty(field)) {
                        itemFieldVal = (popupItem[field])
                    }
                } else {
                    itemFieldVal = popupItem
                }
            } catch (error) {
                itemFieldVal = defaultValue
            }

            return itemFieldVal
        },
        // 表单提交按钮前 自定义验证
        onBeforeSubmitValidateFn() {
            //  debugger
            let _validateFlag = false
            let onBeforeSubmitValidateStr = this.designer.formConfig.onBeforeSubmitValidate
            let onBeforeSubmitValidate = new Function('postParams', onBeforeSubmitValidateStr)
            if (!!onBeforeSubmitValidateStr) {
                try {
                    let postParams = {}
                    _validateFlag = onBeforeSubmitValidate.call(this, postParams)
                    if (_validateFlag == null || _validateFlag == "" || _validateFlag == undefined) {
                        _validateFlag = false
                    }
                } catch (error) {
                    this.$message({
                        message: '提交前，格式化（onBeforeSubmitValidate）参数方法错误，请检查！！！',
                        type: 'error'
                    });
                    _validateFlag = false
                }
            }
            return _validateFlag
        },
        checkActionType() {
            this.isBasicForm = false
            let _otherParamsObj = this.getItemField("otherParams", null)
            if (_otherParamsObj.actionType == 'basicForm') {
                this.getWindowHeightAndWidth()

                // this.title = this.getItemField('label', '详情')
                let subItem = ""
                if (this.actionType_state.value == 'iisEdit') {
                    subItem = '编辑'
                } else if (this.actionType_state.value == 'iisAdd') {
                    subItem = '新增'
                }
                this.title = subItem + " " + this.getItemField('label', '详情')
                this.isBasicForm = true
                this.loadComponent().then(res => {
                    this.loadFormTempaleItemByID()
                })
            } else {
                this.init()
            }
        },
        async init() {
            this.getWindowHeightAndWidth()
            this.loading_nvxeModal = true
            let newFormJson = await this.getRowTemplateData()
            this.loading_nvxeModal = false
            let _self = this
            this.$nextTick(() => {

                this.$refs['editPreForm'].setFormJson(newFormJson)
                if (newFormJson) {
                    this.designer = JSON.parse(newFormJson)

                }
                // 重置表单数据，并清除校验状态
                this.$refs['editPreForm'].resetForm()
                let popupParams = this.$store.state.popupParams;
                //debugger
                if (popupParams && popupParams.value == 'iisEdit') {
                    // 编辑状态
                    let rowData = this.getCurrentRow()
                    this.$refs['editPreForm'].setFormData(rowData)
                }
                this.firstMounted = true
            })
        },
        resetData() {
            // 重置表单数据，并清除校验状态
            this.$refs['editPreForm'].resetForm()
        },
        cancelBtn() {
            this.showEditBox = false
        },
        // 获取当前选中行模板数据
        getCurrentRow() {
            let routerName = this.$route.name
            let rowData = this.$store.state.currentRow.value[routerName];
            let currentRow = rowData
            //debugger
            return currentRow
        },
        preSubmitEvent() {
            //debugger
            this.loadingBtn = true
            this.$refs['editPreForm'].validateForm((valid) => {
                if (valid) {
                    // 提交前，自定义验证
                    let customValidate = this.onBeforeSubmitValidateFn()
                    if (!customValidate) {
                        this.submitEvent()
                    } else {
                        this.loadingBtn = false
                    }

                } else {
                    this.loadingBtn = false
                }
            });
            setTimeout(() => {
                this.loadingBtn = false
            }, 3000)
        },
        // 表单提交接口
        async submitEvent() {
            // debugger
            let _self = this
            let formData = await this.$refs['editPreForm'].getFormData()
            let datasetId = null
            let selfUpdateActionName = this.designer.formConfig.postModelUrl
            let selfAddActionName = this.designer.formConfig.postAddModelUrl
            let executeApiUrl = this.designer.formConfig.executeApiUrl
            // debugger
            // 参数提交前 格式化 处理
            let onBeforeSubmitStr = this.designer.formConfig.onBeforeSubmit
            let onBeforeSubmit = new Function('postParams', onBeforeSubmitStr)
            let _url = `api/MD/DataSet/ExecuteByDataSetId` // 固定取值地址GetListByDataSetId
            let popupParams = this.$store.state.popupParams;
            //添加，编辑成功后==》调用事件列表"
            // let afterSuccessOrErrorEvents = popupParams.afterSuccessOrErrorEvents

            // 默认添加
            let queryItem = this.useFormatParamsFn.getQueryItemByUrl(selfAddActionName)

            if (popupParams && popupParams.value == 'iisEdit') {
                // 编辑
                queryItem = this.useFormatParamsFn.getQueryItemByUrl(selfUpdateActionName)
            }
            if (!!executeApiUrl) {
                // 不区分功能统一接口API
                queryItem = this.useFormatParamsFn.getQueryItemByUrl(executeApiUrl)
            }
            // 获取提交接口的参数值
            let postParams = await this.useFormatParamsFn.getCommonParamsValue(queryItem)
            // debugger
            // 表单赋值
            //  提交表单参数必须包含 $formData，其它可以自定义
            for (const [key, val] of Object.entries(postParams)) {
                if (val == '$formData') {
                    postParams[key] = formData
                }
            }

            // 提交数据前，数据拦截，格式化参数格式后再提交
            if (!!onBeforeSubmitStr) {
                //  如果需要格式化，需要提供当前实例 this
                postParams.pageInstance = this.pageInstance
                try {
                    postParams = onBeforeSubmit.call(this, postParams)
                } catch (error) {
                    this.$message({
                        message: '提交前，格式化（onBeformSubmit）参数方法错误，请检查！！！',
                        type: 'error'
                    });
                    return
                }
            }
            datasetId = this.useFormatParamsFn.getActionParamsValue("CDATASET_ID", queryItem)

            let params = {
                Id: datasetId,
                Parameter: postParams
            }
            let res = await request["post"](_url, params)
            if (res.Success) {
                this.$message({
                    message: '执行成功！',
                    type: 'success'
                });
                setTimeout(() => {
                    let SuccessParams = {}
                    _self.$emit("submitSuccess", SuccessParams)
                    _self.loadingBtn = false
                    _self.showEditBox = false
                    // 执行成功或失败后的处理接口或操作
                    _self.afterSuccessOrErrorEvents(popupParams)
                }, 1000)
            }

        },
        //添加，编辑成功后==》调用事件列表"
        afterSuccessOrErrorEvents(subItem) {
            if (subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
                let eventList = subItem.afterSuccessOrErrorEvents
                if (eventList && eventList.length > 0) {
                    for (let i = 0; i < eventList.length; i++) {
                        let eventItem = eventList[i]
                        // 添加额外的属性,是否刷新的父控件
                        eventItem["isFromParent"] = true
                        this.useHandleVFormEventFn.handleCommonClickEvent(eventItem)
                    }
                }

            }
        },
        // 从vFORM全局表单查询列表中，获取查询项
        getPopupItemByUrl(postUrl = "") {
            let popupItem = null
            // 注意：this.formConfig 此处为列表配置信息，非编辑弹框配置
            if (!!!this.formConfig) {
                return ""
            }
            let popupList = this.formConfig.popupList
            if (popupList && popupList.length > 0) {
                let filterList = popupList.filter((item) => {
                    if (item.value == postUrl) {
                        return item
                    }
                })
                if (filterList && filterList.length > 0) {
                    popupItem = filterList[0]
                }
            }
            if (!!popupItem) {
                if (!!popupItem.width) {
                    this.width = Number(popupItem.width)
                }
            }
            return popupItem
        },

        getVFormPopupActionName(postUrl = "") {
            let actionName = ""
            let popupItem = this.getPopupItemByUrl(postUrl)
            if (!!popupItem) {
                actionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupItem)
            }
            return actionName
        },
        // 获取当前选中行模板数据
        async searchTemplateByCID(rowCID = "") {
            let formJsonData = ""
            let _url = `api/MD/VisualFormDesigner/GetByID`

            let params = {
                id: rowCID
            }
            if (!!rowCID) {
                await request["get"](_url, null, params).then(res => {
                    if (res.Success && res.Datas) {
                        formJsonData = res.Datas.CJSON_DATA
                    }
                    //debugger
                });
            }
            return formJsonData
        },
        // 获取弹框宽度
        getWidth() {
            //debugger
            let width = 500
            let popupParams = this.$store.state.popupParams;
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let popupItem = this.getPopupItemByUrl(selfActionName)
            if (!!popupItem) {
                if (!!popupItem.width) {
                    width = Number(popupItem.width)
                }
            }
            return width
        },
        // 获取当前选中行模板数据
        async getRowTemplateData() {
            //debugger
            let popupParams = this.$store.state.popupParams;
            this.title = this.getItemField('label', '详情')//popupParams.label
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let commonActionName = this.getVFormPopupActionName(selfActionName)
            let formJsonDataList = await this.searchTemplateByCID(commonActionName)
            if (formJsonDataList && formJsonDataList.length > 0) {
                //return formJsonDataList[0].CJSON_DATA // 接口变更
                return formJsonDataList
            } else {
                return ""
            }

        },
        // 基础表单-预提交
        basicForm_preSubmitEvent() {
            this.loadingBtn = true
            setTimeout(() => {
                this.basicForm_submitEvent()
            }, 300);
            setTimeout(() => {
                this.loadingBtn = false
            }, 3000);
        },
        // 基础表单-提交
        basicForm_submitEvent() {
            // debugger
            this.$refs["formItemRenderRef"].$refs["originFormRef"].validate(async (valid) => {
                if (valid) {
                    //表单>>校验通过
                    this.postFormItemData()
                } else {
                    //表单>>校验不通过
                    this.$message({
                        message: '缺少必填信息，请检查！',
                        type: 'error'
                    });
                    return false;
                }
            });
        },
    }

}
</script>

<style lang="scss" scoped>
.customModalClass {
    // overflow: scroll;
    max-height: 600px;
}

::v-deep .el-form-item {
    .el-form-item__error {
        // 修复弹框里面的表单错误提示信息
        line-height: 0.3 !important;
    }
}

::v-deep .vxe-modal--body {
    .vxe-modal--content {
        // 隐藏弹框多余的滚动条
        overflow: hidden !important;
    }
}

.controlWidth {
    // 表单控件宽度
    width: 500px
}
</style>
