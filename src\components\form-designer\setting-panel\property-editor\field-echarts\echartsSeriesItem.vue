<template>
    <div class="option-items-pane">

        <draggable tag="ul" :list="optionModel.series"
            v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
            <li v-for="(option, idx) in optionModel.series" :key="idx">
                <el-checkbox v-model="option.show">
                    <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.seriesName" size="mini"
                        style="width: 150px"></el-input>
                    <i class="iconfont icon-drag drag-option"></i>
                    <el-button circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
                        icon="el-icon-minus" class="col-delete-button"></el-button>
                </el-checkbox>
            </li>
        </draggable>



        <div>
            <el-button type="text" @click="addQueryOption">+添加</el-button>
        </div>

        <el-dialog v-dialog-drag title="编辑序列" :visible.sync="showEditOptionDialogFlag" v-if="showEditOptionDialogFlag" :show-close="true"
            class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
            :destroy-on-close="true">
            <el-form :model="editItemForm" :rules="editItemFormRules" ref="editItemForm" label-width="100px">
                <el-form-item label="字段" prop="columnName">
                    <div>{{ editItemForm.columnName }}</div>
                </el-form-item>
                <el-form-item label="作为目标线">
                    <el-switch @change="change_isTargetLine" v-model="editItemForm.isTargetLine"></el-switch>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标线颜色">
                    <el-color-picker  :predefine="predefineColors" v-model="editItemForm.targetLineColor" >
                     </el-color-picker>
                </el-form-item>

                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标线类型">
                    <el-select v-model="editItemForm.targetLineType">
                        <el-option label="实线" value="solid"></el-option>
                        <el-option label="横虚线" value="dashed"></el-option>
                        <el-option label="点虚线" value="dotted"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标值">
                    <el-input-number v-model="editItemForm.targetLineValue"  :min="0" label="目标值"></el-input-number>
                </el-form-item>
                <el-form-item  label="目标线宽">
                    <el-input-number v-model="editItemForm.targetLineWidth"  :min="0" label="目标线宽"></el-input-number>
                </el-form-item>
                <el-form-item label="名称" prop="seriesName">
                    <el-input style="width:409px" v-model="editItemForm.seriesName"></el-input>
                </el-form-item>
                <el-form-item label="数据列" prop="columnName">
                    <el-select :disabled="!!editItemForm.isTargetLine" @change="change_columnName" ref="columnNameRef" style="width:409px"
                        v-model="editItemForm.columnName" placeholder="请选择">
                        <el-option label="选择" value=""></el-option>
                        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
                            :value="item.field"></el-option>
                    </el-select>
                </el-form-item>
                <div v-if="optionModel.chartConfig.compType=='mix' && !editItemForm.isTargetLine">
                    <el-form-item label-width="0">
                    <el-divider class="custom-divider">混合图配置</el-divider>
                </el-form-item>

                <el-form-item label="X轴标记线">
                    <el-button type="text" @click="addMarkLineItem(editItemForm)">+添加</el-button>
                </el-form-item>
                <el-form-item label-width="0">
                    
                  <div style="margin-left: 40px;" :key="markIndex" v-for="(markLineItem,markIndex) in editItemForm.markLineData" class="flex">
                    <div> &nbsp;字段值&nbsp;<el-input style="width:120px" v-model="markLineItem.xAxis"></el-input></div>
                    <div> &nbsp;值类型&nbsp;
                        <el-tooltip effect="light" content="默认文本，取X轴对应的字段，数字则取X轴数组下标，0开始计算【特殊文本字段:最大值 max,最小值 min,平均值 average,中位数 median】">
                            <i class="el-icon-info"></i></el-tooltip>
                        <el-select style="width:120px" v-model="markLineItem.valueType">
                            <el-option label="文本" value="text"></el-option>
                            <el-option label="数字" value="number"></el-option>
                        </el-select>
                    </div>
                    <div class="flex  items-center"> &nbsp;颜色&nbsp; <el-color-picker  :predefine="predefineColors" v-model="markLineItem.color" >
                     </el-color-picker>
                    </div>
                    <div> &nbsp;标签名&nbsp;<el-input style="width:120px" v-model="markLineItem.formatLabelStr"></el-input></div>
                    <div> &nbsp;类型&nbsp;
                        <el-select style="width:120px" v-model="markLineItem.type">
                            <el-option label="实线" value="solid"></el-option>
                            <el-option label="横虚线" value="dashed"></el-option>
                            <el-option label="点虚线" value="dotted"></el-option>
                        </el-select>
                    </div>
                    <div> &nbsp; <el-button circle plain size="mini" type="danger" @click="deleteMarkLineItem(editItemForm, markIndex)"
                        icon="el-icon-minus" class="col-delete-button"></el-button></div>
                  </div>
                </el-form-item>
                <el-form-item label-width="0">
                    <el-divider class="custom-divider"></el-divider>
                </el-form-item>
                <el-form-item label="图表类型">
                    <el-select @change="change_compType" v-model="editItemForm.chartConfig.compType">
                        <el-option label="请选择" value=""></el-option>
                        <el-option label="柱状图" value="bar"></el-option>
                        <el-option label="折线图" value="line"></el-option>
                        <!-- <el-option label="饼图" value="pie"></el-option> -->
                    </el-select>
                </el-form-item>
                        <el-option label="折线图" value="line"></el-option>
                <el-form-item v-show="editItemForm.chartConfig.compType =='line'" label="折线线类型">
                    <el-select v-model="editItemForm.chartConfig.compTypeLineType">
                        <el-option label="实线" value="solid"></el-option>
                        <el-option label="横虚线" value="dashed"></el-option>
                        <el-option label="点虚线" value="dotted"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <span slot="label">Y轴下标
                            <el-tooltip effect="light" content="默认为0,必须设置了多条Y轴时才有效">
                            <i class="el-icon-info"></i></el-tooltip>
                        </span>
                    <el-select  v-model="editItemForm.chartConfig.comp.yAxisIndex">
                        <el-option label="0" :value="0"></el-option>
                        <el-option label="1" :value="1"></el-option>
                        <el-option label="2" :value="2"></el-option>
                        <el-option label="3" :value="3"></el-option>
                        <el-option label="4" :value="4"></el-option>
                        <el-option label="5" :value="5"></el-option>
                        <el-option label="6" :value="6"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label-width="0">
                    <el-divider class="custom-divider">样式</el-divider>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType != 'pie'" label="显示标签">
                    <el-switch v-model="editItemForm.chartConfig.comp.showLabel"></el-switch>
                </el-form-item>
                <!-- <el-form-item  label="隐藏图例">
                    <el-switch  v-model="editItemForm.chartConfig.comp.hideLegend"></el-switch>
                </el-form-item> -->
                <el-form-item labelWidth="200" v-show="!!editItemForm.chartConfig.comp.showLabel" label="标签后缀修饰符" >
                    <!-- <span slot="label">
                          <el-tooltip effect="light" content='比如：%'>
                            <i class="el-icon-info"></i></el-tooltip>
                    </span> -->
                    <el-input placeholder="" style="width:409px" v-model="editItemForm.chartConfig.comp.formatLabelStr"></el-input>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.chartConfig.comp.showLabel" label-width="0">
                    <el-divider class="custom-divider">标签条件列表</el-divider>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.chartConfig.comp.showLabel" label="标签颜色条件">
                    <el-button type="text" @click="addLabelValItem(editItemForm)">+添加</el-button>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.chartConfig.comp.showLabel" label-width="0">
                    
                  <div style="margin-left: 40px;" :key="markIndex" v-for="(labelValItem,labelValIndex) in editItemForm.labelValCtrlData" class="flex">
                  
                    <div>（当前值） &nbsp;条件&nbsp; 
                        <el-tooltip effect="light" content="比如：“当前值” 与 “比较值” 条件比较  显示设置颜色" > 
                            <i class="el-icon-info"></i></el-tooltip>
                        <el-select style="width:120px" v-model="labelValItem.conditionType">
                            <el-option label="大于" value="GreaterThan"></el-option>
                            <el-option label="大于等于" value="GreaterThanOrEqual"></el-option>
                            <el-option label="等于" value="Equal"></el-option>
                            <el-option label="小于" value="LessThan"></el-option>
                            <el-option label="小于等于" value="LessThanOrEqual"></el-option>
                        </el-select>
                    </div>
                    <div> &nbsp;比较值&nbsp;<el-input-number style="width:120px" v-model="labelValItem.value"></el-input-number></div>
                    <div class="flex  items-center"> &nbsp;（显示）颜色&nbsp; 
                        <!-- <el-color-picker  :predefine="predefineColors" v-model="labelValItem.color" >
                       </el-color-picker> -->
                       <el-select style="width:120px" v-model="labelValItem.color">
                            <el-option label="默认" value=""></el-option>
                            <el-option label="红色" value="error"><span style="color: #F56C6C;">红色</span></el-option>
                            <el-option label="绿色" value="normal"><span style="color: #67C23A;">绿色</span></el-option>
                            <el-option label="橙色" value="warning"><span style="color: #E6A23C;">橙色</span></el-option>
                            <el-option label="蓝色" value="info"><span style="color: #409EFF;">蓝色</span></el-option>
                        </el-select>
                    </div>
                 
                    <div> &nbsp; <el-button circle plain size="mini" type="danger" @click="deleteLabelValItem(editItemForm, labelValIndex)"
                        icon="el-icon-minus" class="col-delete-button"></el-button></div>
                  </div>
                </el-form-item>
                <el-form-item  label="" >
                    <span slot="label">提示框组件 值后缀修饰
                    <el-tooltip effect="light" content="默认为空">
                        <i class="el-icon-info"></i></el-tooltip></span>
                    <el-input placeholder="" style="width:409px" v-model="editItemForm.chartConfig.comp.formatTooltipSuffix"></el-input>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'bar'" label-width="80" label="柱状图类型">
                    <el-radio-group v-model="editItemForm.chartConfig.comp.type">
                        <el-radio-button label="basic">基础柱状图</el-radio-button>
                        <el-radio-button label="total">堆叠柱状图</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'line'" label="折线图类型">
                    <el-select v-model="editItemForm.chartConfig.comp.type">
                        <el-option label="基础折线图" value="basic"></el-option>
                        <el-option label="堆叠折线图" value="stack"></el-option>
                        <el-option label="区域面积图" value="areaStyle"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'line'" label="平滑曲线">
                    <el-switch v-model="editItemForm.chartConfig.comp.smooth"></el-switch>
                </el-form-item>
                <!-- @change="change_hidetemNode" -->
                <el-form-item v-show="editItemForm.chartConfig.compType == 'line'" label="隐藏折线点">
                    <el-switch  v-model="editItemForm.chartConfig.comp.hideItemNode"></el-switch>
                </el-form-item>
              
                <el-form-item v-show="editItemForm.chartConfig.compType == 'line'" label="折线颜色">
                    <el-color-picker @change="change_LineColor" :predefine="predefineColors" v-model="editItemForm.chartConfig.comp.lineColor.value" >
                     </el-color-picker>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'bar'" label="柱图颜色">
                    <el-color-picker  :predefine="predefineColors" v-model="editItemForm.chartConfig.comp.barColor.value" >
                     </el-color-picker>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'bar'" label="柱图高亮颜色">
                    <el-color-picker  :predefine="predefineColors" v-model="editItemForm.chartConfig.comp.barLightColor.value" >
                     </el-color-picker>
                </el-form-item>
                <el-form-item  label="点击触发事件">
                    <el-select v-model="editItemForm.clickEventType">
                        <el-option label="选择" value=""></el-option>
                        <el-option label="点击高亮" value="showHightLight"></el-option>
                        <!-- <el-option label="提示&高亮" value="showModalHightLightFn"></el-option> -->
                    </el-select>
                </el-form-item>
                <!-- <el-form-item v-show="!!editItemForm.clickEventType && editItemForm.clickEventType!=''" label="提示框标题" >
                    <el-input style="width:409px" v-model="editItemForm.modalTitle"></el-input>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.clickEventType && editItemForm.clickEventType!=''" label="提示框内容" >
                    <el-input style="width:409px"  type="textarea"
                    :rows="2"
                    placeholder="请输入内容" v-model="editItemForm.modalContent"></el-input>
                </el-form-item> -->
                <el-form-item v-show="editItemForm.chartConfig.compType == 'line'" label="折线点颜色">
                    <el-color-picker :predefine="predefineColors" v-model="editItemForm.chartConfig.comp.itemColor.value" >
                     </el-color-picker>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'scatter'" label="散点形状">
                    <el-select v-model="editItemForm.chartConfig.comp.type">
                        <el-option label="圆形" value="auto"></el-option>
                        <el-option label="矩形" value="auto1"></el-option>
                        <el-option label="三角形" value="auto2"></el-option>
                        <el-option label="图钉" value="auto"></el-option>
                        <el-option label="菱形" value="auto1"></el-option>
                        <el-option label="箭头" value="auto2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'pie'" label="饼图类型">
                    <el-select v-model="editItemForm.chartConfig.comp.roseType">
                        <el-option label="基础饼图" :value="false"></el-option>
                        <el-option label="圆环图" :value="true"></el-option>
                        <el-option label="玫瑰图" value="area"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'pie'" label="是否显示内容">
                    <el-checkbox v-model="editItemForm.chartConfig.comp.showLabel"></el-checkbox>
                </el-form-item>
                <el-form-item v-show="editItemForm.chartConfig.compType == 'pie'" label="是否显示数值">
                    <el-checkbox @change="change_showLabelValue"
                        v-model="editItemForm.chartConfig.comp.showLabelValue"></el-checkbox>
                </el-form-item>
                </div>
             
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm')
                }}</el-button>
                <el-button size="large" type="" @click="showEditOptionDialogFlag = false">{{ i18nt('designer.hint.cancel')
                }}</el-button>
            </div>
        </el-dialog>
        
    </div>
</template>

<script>
const default_editItemForm = {
    "columnName": "",
    "seriesName": "",
    isTargetLine:false,
    clickEventType:"",// 点击事件类型
    modalTitle:"提示",
    modalContent:"",
    targetLineValue:0,
    targetLineWidth:2,
    targetLineColor:'#E3B76D',
    targetLineType:"dashed",
    chartConfig: {
      compType: "",
      compTypeLineTyoe:"solid",
      comp: {
        type: "basic",
        yAxisIndex:0,// 默认为0，Y轴的下标
        roseType:true,
        showLabel: false, 
        hideLegend:false,// 隐藏图例
        formatLabelStr:"", // 标签修饰符
        showLabelValue:false,// 饼图 是否显示数值 lineColor
        shape: "circle",
        smooth: false, 
        hideItemNode:false,// 是否显示折线点，默认显示
        lineColor: {
            "value": ""
        },
        barColor: {
            "value": ""
        },
        barLightColor: {
            "value": ""
        },
        itemColor: {
            "value": ""
        }
      },
      tooltip:{
        trigger:"item"
      }
    },
    markLineData:[
        // {
        //     xAxis: 'Mon',
        //     symbol: "none",
        //     label:{
        //         color: "red",
        //         formatter: '1111'
        //     },
        //     lineStyle: {
            //     color: "red",
            //     type:'solid',
        //     }
        // },
    ],
    labelValCtrlData:[]
}

// import cloneDeep from "clone-deep" 
import { defaultsDeep } from "lodash-es";
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n";

export default {
    name: "echartsSeriesItem",
    mixins: [i18n],
    components: {
        Draggable,
    },
    props: {
        designer: Object,
        selectedWidget: Object,
        globalDsv: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#c71585',
            '#c7158577'							

            ],
            showEditOptionDialogFlag: false,
            optionLines: '',
            currentEditOption: {},// 当前编辑菜单按钮
            editItemForm: Object.assign({}, default_editItemForm),

            editItemFormRules: {
                label: [
                    { required: true, message: '请输入标签名称', trigger: 'blur' },
                ],
                value: [
                    { required: true, message: '请输入唯一编码', trigger: 'blur' },
                ],
            }
        }
    },
    computed: {
        optionModel() {
            return this.selectedWidget.options
        },

    },
    methods: {
        // 不显示折线点
        change_hidetemNode(val){
            this.$set(this.editItemForm.chartConfig.comp, "hidetemNode", val)
        },
        // 改变为目标线
        change_isTargetLine(val){

        },
        // 改变折线颜色，可以直接修改折线点颜色，但反之不行
        change_LineColor(val){
            this.$set(this.editItemForm.chartConfig.comp.itemColor, "value", val)
        },
      
        
        change_showLabelValue(val){
            this.$set(this.editItemForm.chartConfig.comp, "showLabelValue", val)
        },
        change_compType(val) {
            if (!this.editItemForm.hasOwnProperty('chartConfig')) {
                this.$set(this.editItemForm, "editItemForm", {
                    compType: val
                })
            }
        },
        change_columnName(val) {
            //debugger
            this.$nextTick(() => {
                let selectedLabel = this.$refs["columnNameRef"].selectedLabel
                this.editItemForm.seriesName = selectedLabel
            })
        },
        // 提交修改按钮菜单属性
        submitEditMenu() {
           // debugger
            this.showEditOptionDialogFlag = false
            this.currentEditOption.columnName = this.editItemForm.columnName
            this.currentEditOption.seriesName = this.editItemForm.seriesName
            this.currentEditOption.isTargetLine = this.editItemForm.isTargetLine 
            
            this.currentEditOption.clickEventType = this.editItemForm.clickEventType 
            this.currentEditOption.modalTitle = this.editItemForm.modalTitle 
            this.currentEditOption.modalContent = this.editItemForm.modalContent 
            this.currentEditOption.targetLineValue = this.editItemForm.targetLineValue
            this.currentEditOption.targetLineWidth = this.editItemForm.targetLineWidth
            this.currentEditOption.targetLineColor = this.editItemForm.targetLineColor
            this.currentEditOption.targetLineType = this.editItemForm.targetLineType
            
            this.currentEditOption.chartConfig = this.editItemForm.chartConfig
            this.currentEditOption.markLineData = this.editItemForm.markLineData
            this.currentEditOption.labelValCtrlData = this.editItemForm.labelValCtrlData
            
        },
        // 弹框编辑菜单属性--- 初始化弹框属性参数
        showEditDialogEvent(option) {
            //debugger
            this.currentEditOption = option // 当前菜单属性
            this.showEditOptionDialogFlag = true
            this.editItemForm.columnName = option.columnName
            this.editItemForm.seriesName = option.seriesName
            this.editItemForm.isTargetLine = !!option.isTargetLine?option.isTargetLine:false
          
             this.editItemForm.clickEventType = option.clickEventType
             this.editItemForm.modalTitle = option.modalTitle
             this.editItemForm.modalContent = option.modalContent
            this.editItemForm.targetLineValue = !!option.targetLineValue?option.targetLineValue:0
            this.editItemForm.targetLineWidth = !!option.targetLineWidth?option.targetLineWidth:2
            this.editItemForm.targetLineColor = !!option.targetLineColor?option.targetLineColor:'#E3B76D'
            this.editItemForm.targetLineType = !!option.targetLineType?option.targetLineType:'dashed'
            // 达不到要求  this.editItemForm.chartConfig = Object.assign({},default_editItemForm.chartConfig,option.chartConfig)
            // 改为_.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });  => { 'a': { 'b': 2, 'c': 3 } }
            this.editItemForm.chartConfig = defaultsDeep(option.chartConfig,default_editItemForm.chartConfig)
            this.editItemForm.markLineData = !!option.markLineData?option.markLineData:[]
            this.editItemForm.labelValCtrlData = !!option.labelValCtrlData?option.labelValCtrlData:[]
            
        },
        deleteOption(option, index) {
            this.selectedWidget.options.series.splice(index, 1)
        },
        // 添加查询
      
        addQueryOption() {
            let newValue = this.selectedWidget.options.series.length + 1
            this.selectedWidget.options.series.push(
                { seriesName: '序列' + newValue, columnName: "seriesItem" + newValue,clickEventType:"",modalTitle:"提示",modalContent:"",isTargetLine:false,targetLineValue:0,targetLineWidth:2, targetLineColor:"#E3B76D",targetLineType:"dashed",show: false, dataIndex: newValue }
            )
        },
        
        addMarkLineItem(editItemForm){
            let newValue = editItemForm.markLineData.length + 1
            editItemForm.markLineData.push(
                {xAxis:"", color:"#7C260B", type:'solid',formatLabelStr:"",valueType:'text'}
            )
        },
        deleteMarkLineItem(editItemForm, index) {
            editItemForm.markLineData.splice(index, 1)
        },
        addLabelValItem(editItemForm){
            //let newValue = editItemForm.labelValCtrlData.length + 1 value conditionType color
            editItemForm.labelValCtrlData.push(
                {value:50, color:"error", conditionType:'LessThan'}
            )
        },
        deleteLabelValItem(editItemForm, index) {
            editItemForm.labelValCtrlData.splice(index, 1)
        },

    }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
}

li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
}

.drag-option {
    cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
}

.dialog-footer .el-button {
    width: 100px;

}
</style>
