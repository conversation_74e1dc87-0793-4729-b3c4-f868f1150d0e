
import { useFormatParams } from "@/hooks/useFormatParams"
export function handleReportSelect(vueInstance) {
   return {
      data: [],
      vueInstance: vueInstance,
      selectOptions:[],
      optionConfig:{
         key:"",
         label:""
     },
      useFormatParamsFn: useFormatParams(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      testFn() {
         console.log("hello world")
      },
      actionName(itemOptions) {
         let _actionName = ""
         try {
            _actionName = itemOptions.paramsItem.controlConfig.actionName

         } catch (error) {
            _actionName = ""
         }
         return _actionName
      },
      // 控件配置信息
      controlConfig(itemOptions) {
         let _config = {}
         try {
            _config = itemOptions.paramsItem.controlConfig
         } catch (error) {
            _config = {}
         }
         return _config
      },
      // 下拉列表数据
      CDATAS(itemOptions) {
         let _dataList = []
         try {
            _dataList = itemOptions.paramsItem.controlConfig.CDATAS
         } catch (error) {
            _dataList = []
         }
         return _dataList
      },
      getDataFromCacheByKey(cacheKey) {
         let ctrlData = null
         try {
            ctrlData = vueInstance.$store.state.ctrlData.value[cacheKey] || null
         } catch (error) {
            ctrlData = null
         }
         return ctrlData
      },
      // 获取下拉数据列表
      async getOptionDataList(paramsItem) {
        // this.selectOptions = []
         this.optionConfig={ // 必须重置
               key:"",
               label:""
         }
         let _optionConfig = this.getOptionsKeyAndValue(paramsItem)
         let cacheKey = "reportSelect_getOptionDataList"+paramsItem.field
         let _actionName = this.actionName(paramsItem)
         let dataList = this.getDataFromCacheByKey(cacheKey)
         if ((dataList == null || dataList.length == 0) && !!_actionName) { // && this.selectOptions.length == 0
            console.log('====end====getOptionDataList=========')
            dataList = await this.useFormatParamsFn.getDBDataByActionName(_actionName);
            dataList = this.optionsDataFormat(dataList)
            let tempData = {
               key: cacheKey,
               value: dataList
            }
            vueInstance.$store.commit("set_ctrlData", tempData)
         }
        // this.selectOptions = dataList
         return dataList

      },
      optionsDataFormat(dataList) {
         // debugger
         let optionsList = []
         if (dataList && dataList.length > 0) {
            dataList.forEach(item => {
               let newItem = {
                  key: item[this.optionConfig.key],
                  label: !!item[this.optionConfig.label] ? item[this.optionConfig.label] : item[this.optionConfig.key],
                  originData:item,
               }
               optionsList.push(newItem)
            })

         }
         return optionsList
      },
      // 获取下拉数组的KEY & VALUE
      getOptionsKeyAndValue(paramsItem) {
        //  debugger
         let cacheKey = "reportSelect_getOptionsKeyAndValue"+paramsItem.field
         let _CDATAS =this.CDATAS(paramsItem)
         let dataList = this.getDataFromCacheByKey(cacheKey)
         if (!!!dataList && _CDATAS.length > 0) {
            //  console.log('=====end===getOptionsKeyAndValue=========')
            for (let i = 0; i < _CDATAS.length; i++) {
               let item = _CDATAS[i]
               if (!!item.isSelectTextField && !this.optionConfig.label) {
                  this.optionConfig.label = item.field

               }
               if (!!item.isSelectKeyField && !this.optionConfig.key) {
                  this.optionConfig.key = item.field
               }
            }
            let tempData = {
               key: cacheKey,
               value: this.optionConfig
            }
            vueInstance.$store.commit("set_ctrlData", tempData)
         } else {
            this.optionConfig = dataList
         }
         return this.optionConfig
      },
    

   }
}