<template>
    <el-date-picker  @change="changeEvent" v-bind="$attrs" v-on="$listeners" >
    </el-date-picker>
</template>
<script>
// 时间范围默认格式：curdate#curdate，subtract-1-month#subtract-1-month，add-1-day#add-1-day，#作为分割符号
import baseMixin from './minxin'
import moment from "moment";
export default {
    name: "reportDateAndTimeRange",
    mixins:[baseMixin],
    props: {
        defaultValue:String, // 默认值
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: String,// 当前查询字段名称
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
             // 日期范围快捷方式
        pickerOptions: {
        shortcuts: [{
          text: '当前班次',
          onClick(picker) {
            let currentHours = Number(moment().format('HH'))
            let currentHoursString_1 = " 08:00:00"
            let currentHours2String_2 = " 20:00:00"
            let addDay = 0
            if (currentHours >= 8 && currentHours < 20) {
              currentHoursString_1 = " 08:00:00";
              currentHours2String_2 = " 20:00:00";
              addDay = 0
            } else {
              currentHoursString_1 = " 20:00:00";
              currentHours2String_2 = " 08:00:00";
              addDay = 1
            }
            let d1 = moment().format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment().add(addDay, 'day').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);
          }
        }
          , {
          text: '上一班次',
          onClick(picker) {
            let _minDate = picker.minDate
            let _maxDate = picker.maxDate
            let d1 = moment(_minDate).subtract(12, 'hours').format('YYYY-MM-DD HH:mm:ss')
            let d2 = moment(_maxDate).subtract(12, 'hours').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        }, {
          text: '下一班次',// 
          onClick(picker) {
            let _minDate = picker.minDate
            let _maxDate = picker.maxDate
            let d1 = moment(_minDate).add(12, 'hours').format('YYYY-MM-DD HH:mm:ss')
            let d2 = moment(_maxDate).add(12, 'hours').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        }
          , {
          text: '当天',
          onClick(picker) {
            let currentHoursString_1 = " 08:00:00"
            let currentHours2String_2 = " 08:00:00"
            let addDay = 1
            let d1 = moment().format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment().add(addDay, 'day').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);

          }
        },
        {
          text: '上一天',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let currentHoursString_1 = " 08:00:00"
            let d1 = moment(_minDate).subtract(1, 'day').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(d1).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        }, {
          text: '下一天',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let currentHoursString_1 = " 08:00:00"
            let d1 = moment(_minDate).add(1, 'day').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(d1).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        },
        {
          text: '本周',
          onClick(picker) {
            let currentHoursString_1 = " 08:00:00"
            let currentHours2String_2 = " 08:00:00"
            let addDay = 7
            let d1 = moment().startOf('week').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(d1).add(addDay, 'day').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);

          }
        },
        {
          text: '上一周',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let currentHoursString_1 = " 08:00:00"
            let d1 = moment(_minDate).startOf('week').subtract(1, 'week').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(d1).add(1, 'week').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        }, {
          text: '下一周',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let currentHoursString_1 = " 08:00:00"
            let d1 = moment(_minDate).startOf('week').add(1, 'week').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(d1).add(1, 'week').format('YYYY-MM-DD HH:mm:ss')
            picker.$emit('pick', [d1, d2]);
          }
        }
        ,
        {
          text: '本月',
          onClick(picker) {
            let currentHoursString_1 = " 08:00:00"
            let currentHours2String_2 = " 08:00:00"
            let d1 = moment().startOf('month').format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment().endOf('month').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);

          }
        },
      
        {
          text: '白班',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let _maxDate = picker.maxVisibleDate
            let currentHoursString_1 = " 08:00:00";
            let currentHours2String_2 = " 20:00:00";
            let addDay = 0
            let d1 = moment(_minDate).format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(_maxDate).add(addDay, 'day').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);
          }
        }, {
          text: '晚班',
          onClick(picker) {
            let _minDate = picker.minVisibleDate
            let _maxDate = picker.maxVisibleDate
            let currentHoursString_1 = " 20:00:00";
            let currentHours2String_2 = " 08:00:00";
            let addDay = 1
            let d1 = moment(_minDate).format('YYYY-MM-DD') + currentHoursString_1
            let d2 = moment(_maxDate).add(addDay, 'day').format('YYYY-MM-DD') + currentHours2String_2
            picker.$emit('pick', [d1, d2]);
          }
        }
        ]
        },
            fieldModel:""
        }
    },
    mounted(){
        this.init()
        // if(this.defaultValue ){
        //     // 设置默认值
        //     if(this.defaultValue.includes(',')){
        //         this.fieldModel = this.defaultValue.split(',')
        //     }else{
        //         this.fieldModel = [this.defaultValue]
        //     } 
        // }
    },
    methods: {
      

    }
}

</script>