<template>
  <div class="selectTreeRender">

    <el-select @click.native="loadData()" ref="elSelectRef" clearable @clear="clearEvent" filterable :filter-method="filterMethodEvent" v-model="currentItem" style="width: 100%" v-bind="$attrs"
      v-on="$listeners" placeholder="请选择">
      <el-option v-for="(item, index) in formatDataList" :key="index" :label="item[defaultProps.label]"
        :value="item[treeRowField]" style="display: none" />
      <el-tree class="main-select-el-tree" ref="selecteltree" :data="selectDataList" :node-key="treeRowField"
       :filter-node-method="filterNode"
        highlight-current :props="defaultProps" @node-click="handleNodeClick" :current-node-key="currentNodeKeyValue"
        :expand-on-click-node="false" default-expand-all>
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <i class=""></i> {{ data[defaultProps.label] }}</span>
      </el-tree>
    </el-select>
  </div>
</template>
<script>
import config from '@/config'
const cloneDeep = require("clone-deep");
// import request from '@/libs/request'
export default {
  name: 'selectTreeRender',
  components: {},
  props: {
   // 当前字段
   field:{
          type:String,
          default:""
      },
    // 数据来信:表单form,表格talbe
    dataFrom: {
      type: String,
      default: "form"
    },
    // 当前字段:其它配置信息
    paramsItem: {
      type: Object,
      default() {
        return {}
      }
    },
     // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
     dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
    // 当前字段:value
    currentValue: {
      type: [String, Number],
      default: ""
    },
    // 当前字段:Text
    currentValueText: {
      type: [String, Number],
      default: ""
    },
    // 是否可用
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      hasLoadData: false,// 是否已经加载过数据
      treeRowField: "CID",
      treeParentField:"CPARENT_ORG_ID",
      config: config,
      currentNodeKeyValue: "",
      //expandOnClickNode: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      loading: false,
      prefixIcon: "", // 头部图标,用于是否加载中 显示
      currentItem: '',// 当前选择项
      currentItemText: '',// 当前选择项描述
      selectDataList: [],// 当前列表数据
      formatDataList: [],
      // testDataTree: [
      //   {
      //     id: 1,
      //     label: "表单设计",
      //     children: [
      //       {
      //         id: 2,
      //         label: "表单页",
      //       },
      //     ],
      //   },
      //   {
      //     id: 3,
      //     label: "项目管理",
      //     children: [
      //       { id: 4, label: "参数配置维护" },
      //       { id: 5, label: "员工薪资维护" },
      //     ],
      //   },
      //   {
      //     id: 6,
      //     label: "系统管理",
      //     children: [
      //       { id: 7, label: "系统菜单" },
      //       { id: 8, label: "系统图标" },
      //     ],
      //   },
      //   {
      //     id: 9,
      //     label: "项目管理",
      //     children: [
      //       {
      //         id: 10,
      //         label: "表格示例",
      //         children: [
      //           { id: 11, label: "普通表格" },
      //           { id: 12, label: "表格统计" },
      //         ],
      //       },
      //       {
      //         id: 13,
      //         label: "表单示例",
      //         children: [
      //           { id: 14, label: "表单验证" },
      //           { id: 15, label: "表单字段" },
      //         ],
      //       },
      //     ],
      //   },
      // ],
    }
  },
  computed:{
    dataSourceID(){
            let _id =""
            try {
                _id = this.dataSearchOptions.dbId
            } catch (error) {
                _id =""
            }
            return _id
        },
      //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
      actionType_state() {
            return this.$store.state.actionType;
        },
  },
  watch: {
    currentValue(n, o) {
      //debugger
      if (!!n) {
        this.currentItem = n
      } else {
        // 清空数据
        this.currentItem = ''
        this.currentItemText = ''
      }
    },
    currentValueText(n, o) {
      //debugger
      if (!!n) {
        this.currentItemText = n
      } else {
        // 清空数据
        this.currentItemText = ''
      }
    }

  },
  mounted() {
    //debugger
    //首次赋值
    this.$nextTick(() => {
      // 设置默认值
      if (!!this.currentValueText) {
        // debugger
        let newItem = {
          value: this.currentValue,
          label: this.currentValueText
        }
        this.selectDataList.push(newItem)
        this.currentItem = this.currentValue
      }
     // debugger
      this.loadData()
    })
  },
  methods: {
    filterMethodEvent(searchVal){
      
      this.$refs["selecteltree"].filter(searchVal);
    },
    // 树数据过滤查询
    filterNode(value, data) {
        //debugger
        if (!value) return true;
        return data[this.defaultProps.label].indexOf(value) !== -1;
    },
    // 当前主表选中行
    getTableFullData() {
      let dataList = this.$store.state.tableFullData
      //debugger
      return dataList
    },
    //格式化结构 （处理后全部为一级数据）
    formatData(data) {
      let options = [];
      for (let i = 0; i < data.length; i++) {
        let item = data[i]

        options.push({ label: item[this.defaultProps.label], value: item[this.treeRowField] })
        if (item && item.children && item.children.length > 0) {
          let dataList = this.formatData(item.children)
          options = options.concat(dataList)
        }
      }
      return options;
    },
    // 清空 回调事件
    clearEvent(val){
      this.currentItem = ""
      let params = {
        value:null,
        text:null
      }
      this.$emit("changeEvent", params)
    },
    handleNodeClick(node) {
      // debugger
      this.currentItem = node[this.treeRowField];
      this.$attrs.value =node[this.treeRowField];
      let params = {
        value: node[this.treeRowField],
        text: node[this.defaultProps.label]
      }
      this.$emit("changeEvent", params)
      this.$refs["elSelectRef"].blur();
    },
      // 将 树状结构数据 转为 扁平数据
    toFlat(treeData=[]) {
      let _self = this
        return treeData.reduce((result, node) => {
          if (node.children) {
            result.push(..._self.toFlat(node.children));
            // 如果有节点 添加前先移除
            delete node.children 
          }
          result.push(node);
          return result;
        }, []);
    },
    // 加载数据 
    // 配置信息>> treeRowField:"CID", treeParentField:"CPARENT_ORG_ID",label:"CORG_NAME"
    loadData() {
      // debugger
      this.hasLoadData = true
      this.loading = true
      //let sourceKey = this.paramsItem.sourceKey
      let searchParams = this.paramsItem.searchParams
      let dataList = cloneDeep(this.getTableFullData())
      let newFlatDataList =this.toFlat(dataList) 
      let hasCacheCurrentRow = this.getViewdetailFromCache()
      if(this.actionType_state.value == 'iisEdit'){
          // 只有编辑的时候，需要过滤除去自身不能被选择
          if(hasCacheCurrentRow && Object.keys(hasCacheCurrentRow).length>0){
                newFlatDataList = newFlatDataList.filter(item=>{
                    if(item.CID != hasCacheCurrentRow.CID){
                      return item
                    }
              })
            }
          }
    
    
      if(!!searchParams){
          // 参数分解>> treeRowField:CID, treeParentField:CPARENT_ORG_ID,label:CORG_NAME
          if(searchParams.includes(',')){
              //多个参数
          let searchParamsArr = searchParams.split(',')
          searchParamsArr.forEach(item=>{
              let paramItem = item.split(':')
              let keyName = paramItem[0].trim() // 过滤空格
              //debugger
              switch (keyName) {
                case "treeRowField":
                  this.treeRowField = paramItem[1]
                  break;
                case "treeParentField":
                this.treeParentField =paramItem[1]
                  break;
                case "label":
                this.defaultProps.label =paramItem[1]
                  break;
                default:
                  break;
              }
          })
          }else{
              //单个参数
              let paramItem = searchParams.split(':')
              // params.Params.filter[paramItem[0]] =paramItem[1]
          }
      }
      this.loading = false
      this.selectDataList = this.toTree(newFlatDataList)
     
      this.formatDataList = newFlatDataList
      //setCurrentKey 通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
     this.$nextTick(()=>{
       // debugger
        let hasCacheCurrentRow = this.getViewdetailFromCache()
        let selecteltreeObj = this.$refs["selecteltree"]
        if(selecteltreeObj){
          if(this.currentValue){
            selecteltreeObj.setCurrentKey(this.currentValue)
          }else{
           //  只有添加的时候，才设置默认选中行作为父类
           if(this.actionType_state.value == 'iisAdd'){
              if(hasCacheCurrentRow && hasCacheCurrentRow[this.treeRowField]){
                this.currentItem = hasCacheCurrentRow[this.treeRowField]
                let params = {
                  value: hasCacheCurrentRow[this.treeRowField],
                  text: hasCacheCurrentRow[this.defaultProps.label]
                }
                this.$emit("changeEvent", params)
                selecteltreeObj.setCurrentKey(hasCacheCurrentRow.CID)
              }
           }
          

            //  if(hasCacheCurrentRow && hasCacheCurrentRow[this.field]){
            //   this.currentItem = hasCacheCurrentRow[this.field]
            //   let params = {
            //     value: hasCacheCurrentRow[this.field],
            //     text: hasCacheCurrentRow[this.defaultProps.label]
            //   }
            //   this.$emit("changeEvent", params)
            //   selecteltreeObj.setCurrentKey(hasCacheCurrentRow.CID)
            // }
          }
        }
     })

    },
    getRequest(name) {
      let urlStr = window.location.search
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      let r = urlStr.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      };
      return null;
    },
   // 从缓存中获取当前选中行信息【主表】
   getViewdetailFromCache(){
      let _detail =null
      let formName = this.getRequest("formName")
      try {
        if(!!this.$store.state.currentRow_viewdetail.value[formName]){
         _detail = this.$store.state.currentRow_viewdetail.value[formName];
         }
      } catch (error) {
        _detail =null
      }  
      return _detail
    },

    //扁平数据转为树状结构数据
    toTree(arr) {
      // debugger
      //先检测是不是数组类型
      if (!Array.isArray(arr)) {
        return [];
      }
      // JS的对象就是hash表
      const obj = {};
      arr.forEach((item) => {
        obj[item[this.treeRowField]] = item;
      });
      const targetArr = [];
      arr.forEach((item) => {
        const parent = obj[item[this.treeParentField]];//有pId就说明他有父亲，找到他的父亲parent
        if (parent) {  //如果他有父亲，就给他添加children属性
          parent.children = parent.children || [];
          parent.children.push(item);
        } else {  //他没有父亲，就把当前项push进去（顶层）
          targetArr.push(item);
        }
      });
      return targetArr;
    },


    // 选择改变回调事件
    changeEvent(val) {
      // debugger
      let _self = this
      this.$nextTick(() => {
        let params = {
          value: val,
          text: _self.$refs["elSelectRef"].selectedLabel
        }
        _self.$emit("changeEvent", params)
      })
    }
  }
}
</script>


<style lang="scss" scoped>
.main-select-el-tree {
  .custom-tree-node {
    font-size: 14px;
    //background-color: transparent;
  }
}
</style>