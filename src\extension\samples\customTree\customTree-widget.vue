<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div class="flex flex-col">
      <div v-if="!!field.options.showTitle " class="treeTitleClass flex justify-start">
        {{ field.options.title }}</div>
      <div v-if="!!field.options.title" style="border:1px solid #F1F1F1"></div>
      <el-skeleton :loading="loading_data" animated :throttle="10">
        <div v-if="!!field.options.showFilterInput" class="flex justify-between " style="margin:2px 5px;">
           <el-input placeholder="输入关键字" clearable
            v-model="filterText">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
          <el-button @click="expandAllNodeFn()" v-if="!!field.options.showExpandFoldBtn" style="margin-left:5px;" type="primary">{{ !!expandAllNode?"折叠":"展开" }}</el-button>
        </div>
        <div v-if="!!field.options.showFilterInput" style="margin:5px;border:1px solid #F1F1F1"></div>
        <el-tree  :filter-node-method="filterNode" style="overflow: auto;" :style="[{ height: getSettingHeight() }]"
          class="main-select-el-tree " :publicAttribute="publicAttribute" :ref="field.options.name" :data="selectDataList"
          :node-key="nodeKey" highlight-current :props="defaultProps" @node-click="handleNodeClick"
          :current-node-key="currentNodeKeyValue" :expand-on-click-node="false" 
          :default-expand-all="false"
           :default-expanded-keys="expandedKeys"
          >
          <div class="custom-tree-node w-full flex justify-between" slot-scope="{ node, data }">
            <div>{{ data[defaultProps.label] }}</div>
            <!-- 显示项额外事件按钮 -->
            <div v-if="showExtraItem(data)" style="padding-right: 8px;font-size: 16px;">
              <el-button :key="index" v-for="(item, index) in field.options.itemEventList"
                @click="clickExtraEvent(item, data, index)" size="mini" type="text"><i
                  :class="item.iconClass"></i></el-button>
            </div>
          </div>

        </el-tree>
      </el-skeleton>

    </div>
  </static-content-wrapper>
</template>
  
<script>
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import {
  useDebounceFn
} from '@vueuse/core'
import i18n from "@/utils/i18n"
import cloneDeep from "clone-deep"
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import { useHandleVFormApi } from "@/hooks/useHandleVFormApi"
import { useHandleVFormPopup } from "@/hooks/useHandleVFormPopup"
export default {
  name: "customTree-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  // 注入列表页面整体实体
  inject: ['getPageInstance', 'sourceVFormRenderState'],
  components: {
    StaticContentWrapper,
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    contentBoxHeight: {
      type: [Number, String],
      default: 0
    },
    // 控件来源父集 add by andy
    sourceVFormRender: {
      type: String,
      default: ""
    },
    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  data() {
    return {
      nodeKey:"CID",
      expandedKeys:[],// 默认展开KEYS
      ExpandFoldBtnTitle:"展开",
      expandAllNode:false,
      filterText: "",
      publicAttribute: {
        value: "",
        node: null,
      },// 对外开发属性值
      loading_data: false,
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
      useHandleVFormApiFn: useHandleVFormApi(this),
      useHandleVFormPopupFn: useHandleVFormPopup(this),
      maxLoadTime: 10,
      currentNodeKeyValue: "",
      selectDataList: [
        {
          "label": "亚洲",
          "value": "asia",
          "children": [
            {
              "label": "中国",
              "value": "china",
              "children": [
                {
                  "label": "北京",
                  "value": "beijing"
                },
                {
                  "label": "上海",
                  "value": "shanghai"
                }
              ]
            },
            {
              "label": "日本",
              "value": "japan"
            }
          ]
        },
        {
          "label": "欧洲",
          "value": "europe",
          "disabled": true,
          "children": [
            {
              "label": "英国",
              "value": "england"
            },
            {
              "label": "法国",
              "value": "france",
              "checkable": false
            },
            {
              "label": "德国",
              "value": "germany",
              "disableCheckbox": true
            }
          ]
        },
        {
          "label": "北美",
          "value": "northAmerica"
        }
      ],
      selectDataListCopy:[],
      defaultProps: {
        children: "children",
        label: "label",
      },
    }
  },
  computed: {
    pageInstance() {
      // 获取列表示例
      return this.getPageInstance()
    },
  },
  watch: {
    filterText(val) {
      //debugger
      //this.$refs[this.field.options.name].filter(val);
      this.debouncedFn_filterTreeData(val)
    }
  },
  created() {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  mounted() {
    this.nodeKey = !!this.field.options.treeRowField?this.field.options.treeRowField:"CID"
    this.$nextTick(() => {
      // 是否被动加载数据
      if(!!!this.field.options.isPassiveDataLoad){
            this.tryTillGetData()
      }
      if(this.field.options.isPassiveDataLoad){
        this.selectDataList=[]
      }
    })
    
    this.debouncedFn_filterTreeData = useDebounceFn((val) => {
      this.$refs[this.field.options.name].filter(val);
    }, 500)
  },
  methods: {
    // 折叠、展开
    expandAllNodeFn(){
      this.expandAllNode = !this.expandAllNode
      // debugger
      if(this.selectDataList && this.selectDataList.length>0){
         if(this.expandAllNode){
          this.selectDataList.forEach(item=>{
            this.expandedKeys.push(item[this.nodeKey])
          })
         }else{
           this.expandedKeys =[]
           this.selectDataList = cloneDeep(this.selectDataListCopy)
         }
      }else{
        this.expandedKeys =[]
      }
    },
      // 对外暴露事件，重新加载
      async reSearchData(paramsOptions = {}) {
            await this.loadData() 
      },
    clickExtraEvent(subItem, data, index) {
      // debugger
      let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", subItem)
      if (!!actionType && actionType != "-1") {
        // 自定义执行功能
        // switch (actionType) {
        //   case "api": // 执行API
        //     this.useHandleVFormApiFn.handleApiByItem(subItem)
        //     break;
        //   case "popup": //  弹框功能
        //     this.useHandleVFormPopupFn.handlePopupByItem(subItem)
        //     break;
        //   default:
        //     break;
        // }
        this.useHandleVFormEventFn.handleCommonClickEvent(subItem)
      } else {
        // 自定义且未配置功能
        this.$message.error('暂未配置功能,请联系管理员！');
      }
    },
    // 获取是否显示额外条件
    showExtraItem(data) {
      let showFlag = false
      if (!!this.field.options.showExtraItemEvent && this.field.options.itemEventList && this.field.options.itemEventList.length > 0) {
        let type = this.field.options.showExtraItemFilter
        switch (type) {
          case "1": //仅显示无子项的
            if (!!!data.hasOwnProperty('children')) {
              showFlag = true
            }
            break;
          case "2"://仅显示有父项的
            if (data.hasOwnProperty('children')) {
              showFlag = true
            }
            break;
          default:
            showFlag = true
            break;
        }


      }
      return showFlag
    },
    // 过滤查询
    filterNode(value, data) {
      //debugger
      if (!value) return true;
      return data[this.field.options.showTreeLabel].indexOf(value) !== -1;
    },
    // 点击节点 事件 回调
    handleNodeClick(node) {
      //  
      let params = {
        formCtrlName: "customTree",
        contrlName: this.field.options.name,
        value: node[this.field.options.filterTreeField],// 过滤查询字段
        triggerCtrlNames: this.field.options.triggerCtrlNames, //重点： 触发控件名称
      }
      this.publicAttribute.value = node[this.field.options.filterTreeField] // 赋值 对外开发属性值
      this.publicAttribute.node = node // 赋值 对外开发属性值
      this.useHandleVFormEventFn.reSearchData(params);
      let tempData ={
                        key:this.$route.fullPath+'_'+"customTree",
                        value:node
                    }
      this.$store.commit("set_customTreeNode",tempData)
    },
    // 加载数据
    async loadData() {
      //debugger
      this.loading_data = true
      let dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
      let treeData = this.toTree(dataList, this.field.options.treeRowField, this.field.options.treeParentField)
      if (!!this.field.options.showTopFilter) {

        let topItem = {
          "children": treeData
        }
        // 动态标签名称
        topItem[this.field.options.showTreeLabel] = this.field.options.topFilterText
        treeData = [topItem]

      }
      this.selectDataList = treeData
      this.selectDataListCopy = cloneDeep(treeData)
      this.expandAllNodeFn()
      this.defaultProps.label = this.field.options.showTreeLabel
      this.loading_data = false
    },
    // 循环直到获取到数据
    tryTillGetData() {
      //debugger
      let _self = this
      let dataObj = this.$refs[this.field.options.name]
      if (!!dataObj) {
        // 干正事
        this.loadData()
        this.maxLoadTime = 20
      } else {
        if (_self.maxLoadTime > 0) {
          setTimeout(() => {
            _self.maxLoadTime = _self.maxLoadTime - 1
            this.tryTillGetData()
          }, 300)
        }
      }
    },
    // 计算高度
    getSettingHeight() {
      // debugger
      let _self = this
      let size = 0
      let contentBoxHeight = this.contentBoxHeight
      if (typeof (contentBoxHeight) == 'string') {
        contentBoxHeight = contentBoxHeight.replace("px", '')
      }
      if (contentBoxHeight == 0 && (!!this.designState || this.previewState)) {
        // 设计模式适，直接返回默认值
        return `300px`
      }
      // 默认
      if (contentBoxHeight > 0) {
        size = parseInt(contentBoxHeight)-60 + 'px'
      } else {
        size = ``
      }

      return size

    },
    //扁平数据转为树状结构数据
    toTree(dataList, treeRowField, treeParentField) {
      //debugger
      let sonId = treeRowField
      let parentId = treeParentField
      // debugger
      //先检测是不是数组类型
      if (!Array.isArray(dataList)) {
        return [];
      }
      // JS的对象就是hash表
      const obj = {};
      dataList.forEach((item) => {
        obj[item[sonId]] = item;
      });
      const targetArr = [];
      dataList.forEach((item) => {
        const parent = obj[item[parentId]];//有pId就说明他有父亲，找到他的父亲parent
        if (parent) {  //如果他有父亲，就给他添加children属性
          parent.children = parent.children || [];
          parent.children.push(item);
        } else {  //他没有父亲，就把当前项push进去（顶层）
          targetArr.push(item);
        }
      });

      return targetArr;
    },

  }
}
</script>
  
<style lang="scss" scoped>
.treeTitleClass {
  // background-color: #FFF;
  // width: 100%;
  // height: 16px;
  font-size: 16px;
  font-weight: bold;
  // color: #666666;
  // line-height: 16px;
  // padding-top: 16px;
  padding-left: 7px;
  padding-bottom: 5px;
  border-bottom: 1px solid #F1F1F1
}

.main-select-el-tree {
  .custom-tree-node {
    font-size: 16px;
    //padding-bottom: 120px;
    //background-color: transparent;
  }
}
</style>
  