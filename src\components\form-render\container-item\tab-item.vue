<template>
  <container-item-wrapper :widget="widget">
    <!-- overflow-hidden -->
    <div  :style="{'padding': `0px ${!!widget.options.onPaddingRight?'10px':'0px'}  0px ${!!widget.options.onPaddingLeft?'10px':'0px'}`}" :key="widget.id" class="tab-container  andyTabItem"
         v-show="!widget.options.hidden">
         <div v-html="tabItemColorStyle"></div>
         <div v-html="tabColorStyle"></div> 
         <div v-html="tabsHeaderStyle"></div> 
         <div v-html="tabsContentStyle"></div> 
      <el-tabs :id="`TABSTOP_${widget.id}`" style="padding: 2px;" :style="{'background-color': widget.options.bgColor,'border-top':`1px solid ${widget.options.bgColor}`}" v-model="activeTabName" @tab-click="handleTabClick" :type="widget.displayType" :ref="widget.id" :class="[customClass]">
        <div v-html="tabsHeaderTopStyle"></div> 
        <el-tab-pane  v-for="(tab, index) in visibleTabs" :key="index" :label="tab.options.label"
                      style="overflow-x: hidden;overflow-y: auto;"
                     :style="[{ 'height': (maxHeight+5) + 'px' }]" 
                     :disabled="tab.options.disabled" :name="tab.options.name">
          <template v-for="(subWidget, swIdx) in tab.widgetList">
            <template v-if="'container' === subWidget.category">
                <component :sourceVFormRender="sourceVFormRender" :contentBoxHeight="getContentBoxHeight(subWidget)" :is="getComponentByContainer(subWidget)" :widget="subWidget" :key="swIdx" :parent-list="tab.widgetList"
                              :index-of-parent-list="swIdx" :parent-widget="widget"
                              :sub-form-row-id="subFormRowId" :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex">
                  <!-- 递归传递插槽！！！ -->
                  <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                    <slot :name="slot" v-bind="scope"/>
                  </template>
                </component>

           
            </template>
            <template v-else>
             
              <component :sourceVFormRender="sourceVFormRender" :contentBoxHeight="getContentBoxHeight(subWidget)" :is="subWidget.type + '-widget'" :field="subWidget" :key="swIdx" :parent-list="tab.widgetList"
                            :index-of-parent-list="swIdx" :parent-widget="widget"
                            :sub-form-row-id="subFormRowId" :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex">
                <!-- 递归传递插槽！！！ -->
                <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                  <slot :name="slot" v-bind="scope"/>
                </template>
              </component>
            </template>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

  </container-item-wrapper>
</template>

<script>
  import { useFormatParams } from "@/hooks/useFormatParams"
  import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
  import cloneDeep from "clone-deep"
  import emitter from '@/utils/emitter'
  import i18n from "../../../utils/i18n"
  import refMixin from "../../../components/form-render/refMixin"
  import ContainerItemWrapper from './container-item-wrapper'
  import containerItemMixin from "./containerItemMixin";
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "tab-item",
    componentName: 'ContainerItem',
    mixins: [emitter, i18n, refMixin, containerItemMixin],
    components: {
      ContainerItemWrapper,
      ...FieldComponents,
    },
    props: {
      widget: Object,

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
    
      previewState: { //是否表单预览状态
        type: Boolean,
        default: false
      },
      contentBoxHeight:{
          type: Number,
          default: 0
        },
             // 控件来源父集 add by andy
      sourceVFormRender:{
        type: String,
        default: ""
      },
    },
      // 注入列表页面整体实体
    inject: ['refList', 'sfRefList', 'globalModel','getPageInstance', 'sourceVFormRenderState'],
    data() {
      return {
        copyTabs:[],// 当前原始数据备份
        displayTabItemIndexList:[],
        publicAttribute: {
            value: "",
            selectedIndex:null,
        },// 对外开发属性值
        tabColorStyle:"",
        tabItemColorStyle:"",
        tabsContentStyle:"",
        tabsHeaderStyle:"",
        tabsHeaderTopStyle:"",
        activeTabName: '',
        useFormatParamsFn: useFormatParams(this),
        useHandleVFormEventFn: useHandleVFormEvent(this),
        hasLoadDataTabs:[],// 已经加载过的TAB标签
      }
    },
    computed: {
      maxHeight(){
        let _maxH = Number(this.contentBoxHeight-50)
          // 隐藏TAB项
        if(!!this.widget.options.hideTabsName){
           _maxH = _maxH + 47
        }
        // 高度偏移量
        if(!!this.widget.options.OffsetHeight){
          _maxH = _maxH + (this.widget.options.OffsetHeight)
        }
        return _maxH
      },
      visibleTabs() {
        return this.widget.tabs.filter(tp => {
          return !tp.options.hidden
        })
      },
      pageInstance() {
        // 获取列表示例
        return this.getPageInstance()
      },
      customTabActiveClass() {
        return this.widget.options.activeTabColor+this.widget.options.activeTabTextColor+this.widget.options.tabItemTextColor+this.widget.options.tabBarColor+this.widget.options.tabBodyColor
      },
    },
    watch: {
      customTabActiveClass(n,o){
        this.getTabSettingColor()
      }
    },
    created() {
      this.initRefList()
    },
    mounted() {
      this.initActiveTab()
      this.getTabSettingColor()
    },
    beforeDestroy() {
      this.unregisterFromRefList()
    },
    methods: {
      reSearchData(params){
        //debugger
        let currentWidgetId = this.widget.id
        let formCtrlName = params.formCtrlName
        if(formCtrlName == 'tabMenu'||formCtrlName == 'tab'){
            if(params.triggerCtrlNames && params.triggerCtrlNames.length>0){
              let _triggerCtrlName =  params.triggerCtrlNames[0]
              if(!!_triggerCtrlName && _triggerCtrlName.value ==currentWidgetId){

                this.activeTabName = params.value.activeTabItem
                if(this.activeTabName){
                  let activePanes = this.widget.tabs.filter((tp) => {
                    return tp.options.name ===  this.activeTabName
                  })
                  if(activePanes && activePanes.length>0){
                    this.handleTabClick(activePanes[0].options)
                  }
                }
               
              }
             
            }
           

        }else {
          //this.hasLoadDataTabs =[]
          // 隐藏指定的TAB项
          this.tryTillGetData()
          setTimeout(() => {
            this.getCurrentActiveTab()
          }, 300);
          console.log("tab-item .... reSearchData")
        }
      },
        // 触发控件列表>>触发事件 >>循环直到获取到数据
    async tryTillGetData() {
      if(this.copyTabs && this.copyTabs.length>0){
         this.widget.tabs = cloneDeep(this.copyTabs)
      }
      this.displayTabItemIndexList =[]
      try {
        if(this.widget.options.actionName){
        let _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.widget.options.actionName);
        if (_dataList && _dataList.length > 0) {
          //debugger
            this.displayTabItemIndexList = _dataList // [1,2,3]
            this.copyTabs = cloneDeep(this.widget.tabs)
            if(this.displayTabItemIndexList && this.displayTabItemIndexList.length>0){
                this.widget.tabs = this.widget.tabs.map(item=>{
                 if(!this.displayTabItemIndexList.includes(item.options.IndexNumber)){
                    item.options.hidden = true
                 }
                 return item
              })
            }
        }
      }
      } catch (error) {
        this.displayTabItemIndexList=[]
      }
     
    },
      getTabSettingColor(){
        this.tabColorStyle =""
        this.tabItemColorStyle =""
        this.tabsHeaderStyle =""
        this.tabsHeaderTopStyle =""
        this.tabsContentStyle =""
       if(!!this.widget.options.tabBodyColor){
            this.tabsContentStyle =`<style type="text/css">
              .andyTabItem .el-tabs__content {
                  background-color: ${this.widget.options.tabBodyColor} !important;
                }
            </style>`
        }
        if(!!this.widget.options.tabBarColor){
            this.tabsHeaderStyle =`<style type="text/css">
              .andyTabItem .el-tabs__header {
                  background-color: ${this.widget.options.tabBarColor} !important;
                }
            </style>`
        }
        // 隐藏TAB标签 E4E7ED
        if(!!this.widget.options.hideTabsName){
            this.tabsHeaderTopStyle =`<style type="text/css">
              #TABSTOP_${this.widget.id} .el-tabs__header  {
                  display:none;
                  border-bottom:0px solid #E4E7ED !important;
                
                }
                #TABSTOP_${this.widget.id}  {
                     border: ${!!this.widget.options.hideTabsBorder?'0px':'1px'} solid #DCDFE6 !important;
                  }
            </style>`
        }
        if(!!this.widget.options.tabItemTextColor){
            this.tabItemColorStyle =`<style type="text/css">
              .andyTabItem .el-tabs__item {
                color: ${this.widget.options.tabItemTextColor} !important;
                }
            </style>`
        }
        if(!!this.widget.options.activeTabColor || !!this.widget.options.activeTabTextColor){
          this.tabColorStyle =`<style type="text/css">
            .andyTabItem .is-active{
                color: ${this.widget.options.activeTabTextColor} !important;
                background-color: ${this.widget.options.activeTabColor} !important;
              }
          </style>`
        }
      },
      // 处理TAB标签点击时，事件回调处理 add by andy 20230425
      handleTabClick(tab, event) {

          ///////////// 设置当前选中TAB项为激活状态//////////////////
          this.widget.tabs.map((tp) => {
              tp.options.active = false
              if(tp.options.name === tab.name){
                tp.options.active = true
              }
          })
          ///////////////////////////////
         this.publicAttribute.selectedIndex = tab.name // 对外空开的 绑定值
         let currentTabLabel = tab.label
         let tempData ={
                            key:this.$route.fullPath+'_'+this.widget.id,
                            value:currentTabLabel
                        }
         this.$store.commit("set_ctrlData",tempData)
        ////#endregiondebugger
         let loadDataFlag = true
        let isExistIndex =  this.hasLoadDataTabs.findIndex(item=>{
            return item.name == tab.name
        })
        if(isExistIndex !=-1){
          // 已经存在时，就不必再次重新加载数据
          loadDataFlag = false
        }else{
           this.hasLoadDataTabs.push(tab)
        }
        let activePanes = this.widget.tabs.filter((tp) => {
            return tp.options.name === tab.name
          })
         if(activePanes && activePanes.length>0){
            let params = {
              triggerCtrlNames: activePanes[0].options.triggerCtrlNames, //重点： 触发控件名称
            }
            // 设置为总是触发
            if(!!activePanes[0].options.isReloadEveryTime){
                loadDataFlag = true
            }
            // 是否重新加载数据,默认仅仅加载一次，下次不再触发，但如果设置了 isReloadEveryTime 为真，则每次都重新加载
            if(loadDataFlag){
                // 触发控件重新加载数据
                if(!!params.triggerCtrlNames){
                  this.useHandleVFormEventFn.reSearchData(params);
                }
              
            }
            try {
              // 两个TAB关联，然后隐藏隐藏TAB项
             let _linkOtherTab = activePanes[0].options.linkOtherTab
             if(!!_linkOtherTab){
              let _activeTabItem = activePanes[0].options.activeTabItem
              let params ={
                  formCtrlName:this.widget.type,
                  contrlName:this.widget.options.name,
                  value:activePanes[0].options,// 过滤查询字段
                  triggerCtrlNames:this.widget.options.triggerCtrlNames, //重点： 触发控件名称
              }
              this.useHandleVFormEventFn.reSearchData(params);
             }
            } catch (error) {
              
            }
        
        }
      },
      getCurrentActiveTab(){
           this.hasLoadDataTabs =[]
          let activePanes = this.widget.tabs.filter((tp) => {
            return tp.options.active === true 
          })
        
            if (activePanes.length > 0) {
              let isExistIndex =  this.hasLoadDataTabs.findIndex(item=>{
              return item.name == activePanes[0].options.name
            })
            if(isExistIndex !=-1){
              // 已经存在时，就不必再次重新加载数据
            }else{
              this.hasLoadDataTabs.push(activePanes[0].options)   
            }
          }
      },
      initActiveTab(activeTab="") {
        let currentTabLabel =""
        if ((this.widget.type === 'tab') && (this.widget.tabs.length > 0)) {
          let activePanes = this.widget.tabs.filter((tp) => {
            return tp.options.active === true
            
          })
          if (activePanes.length > 0) {
            this.publicAttribute.selectedIndex = activePanes[0].options.name // 对外空开的 绑定值
            this.activeTabName = activePanes[0].options.name
            // 首次加载时，把默认已经加载过的数据TAB放到hasLoadDataTabs 中
            this.hasLoadDataTabs.push(activePanes[0].options)
             currentTabLabel = activePanes[0].options.label
            
          } else {
            this.activeTabName = this.widget.tabs[0].options.name
            this.publicAttribute.selectedIndex = this.widget.tabs[0].options.name // 对外空开的 绑定值
            this.hasLoadDataTabs.push(this.widget.tabs[0].options)
            currentTabLabel = this.widget.tabs[0].options.label
          }
          let tempData ={
                            key:this.$route.fullPath+'_'+this.widget.id,
                            value:currentTabLabel
                        }
         this.$store.commit("set_ctrlData",tempData)
        }
      },
       // 获取sub 内容高 add by andy
       getContentBoxHeight(pane){
          // debugger
          if(this.previewState){
            return `300px`
          }
          let totalHeight = Number(this.contentBoxHeight)
          if(!!this.widget.options.hideTabsName){
            // 添加TAB 缺少 高度
             totalHeight = totalHeight + 40
          }
             // 高度偏移量
          if(!!this.widget.options.OffsetHeight){
            totalHeight = totalHeight + (this.widget.options.OffsetHeight)
          }
          return totalHeight -50
        }
    },
  }
</script>

<style lang="scss" scoped>
.andyTabItem .el-tabs--border-card {
    ::v-deep .el-tabs__content {
      padding: 5px;
    }
  }
</style>
