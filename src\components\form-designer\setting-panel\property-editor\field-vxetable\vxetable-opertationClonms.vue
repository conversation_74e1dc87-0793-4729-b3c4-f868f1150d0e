<template>
  <div class="option-items-pane">


    <draggable tag="ul" :list="optionModel.operationButtons"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in optionModel.operationButtons" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input @click.native="showEditMenuDialogEvent(option)" readonly v-model="option.label" size="mini"
            style="width: 160px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button
            circle
            plain
            size="mini"
            type="info"
            @click="customJsEventCodeOption(option, idx)"
            icon="el-icon-edit"
          ></el-button>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>

    <commonEventDialog :functionName="functionName" :jsCodeTitle="jsCodeTitle" :eventCodeConfig="currentEditSettingBtnOption" @submitEvent="submitCommonEventDialog" ref="commonEventDialogRef"></commonEventDialog>

    <div>
      <el-button type="text" @click="addOption">+添加按钮</el-button>
    </div>

    <el-dialog title="编辑 操作列" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input v-model="editMenuForm.label"></el-input>
        </el-form-item>
      
        <el-form-item label="" prop="iconClass">
          <span slot="label">图标icon
            <el-tooltip effect="light" content="文字描述是否需要ICON修饰，直接添加可用的ICON样式即可">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-input placeholder="el-icon-plus" v-model="editMenuForm.iconClass"></el-input>
          <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
        </el-form-item>
        <el-form-item label="" prop="textType">
          <span slot="label">字体样式
            <el-tooltip effect="light" content="默认蓝色，特殊的按钮可以选择；如：删除 选择 danger 红色">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-select v-model="editMenuForm.textColor" placeholder="请选择字体类型">
            <el-option label="primary" value="primary"><el-link type="primary">primary</el-link></el-option>
            <el-option label="success" value="success"><el-link type="success">success</el-link></el-option>
            <el-option label="warning" value="warning"><el-link type="warning">warning</el-link></el-option>
            <el-option label="danger" value="danger"><el-link type="danger">danger</el-link></el-option>
            <el-option label="info" value="info"><el-link type="info">info</el-link></el-option>
            <el-option label="text" value="text"><el-link type="text">text</el-link></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="按钮大小" prop="size">
          <el-select v-model="editMenuForm.size" placeholder="请选择大小">
            <el-option label="大medium" value="medium"></el-option>
            <el-option label="中small" value="small"></el-option>
            <el-option label="小mini" value="mini"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="事件标签" prop="size">
          <el-select disabled v-model="editMenuForm.value" placeholder="请选择标签">
            <el-option label="请选择" value=""></el-option>
            <el-option v-for="(item,index) in actionTypeList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item> -->
        <eventTypeByCtrlDialog contrlType="common" :editItemForm="editMenuForm" :designer="designer"
          :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>

        <!-- 动态参数 -->
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'"
          style="font-weight: bold;">参数</div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
            <!-- <el-form-item label-width="60px" label="KEY" prop="key">
              <el-input placeholder="key" v-model="item.key"></el-input>
            </el-form-item>
            <el-form-item label-width="60px" label="VALUE" prop="value">
              <el-input  placeholder="value" v-model="item.value"></el-input>
            </el-form-item> -->
            <div>KEY</div>
            <div style="margin:0 10px;" l-form-item label="KEY" prop="key">
              <el-input placeholder="参数名" v-model="item.key"></el-input>
            </div>
            <div>VALUE</div>
            <div style="margin:0 10px;" l-form-item label="KEY" prop="key">
              <el-input style="width: 300px;" placeholder="参数值" v-model="item.value"></el-input>
            </div>
            <div>
              <el-button type="text" @click="setParams(item)">+选择</el-button>
              <el-tooltip content="选择控件与参数绑定值 " effect="light"> <i class="el-icon-info"></i></el-tooltip>
              <span style="margin-left:5px;cursor: pointer;"> <i @click="deleteParam(item, index)"
                class="el-icon-delete"></i></span>
            </div>
           
          </div>
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
        </div>
        <div style="font-weight: bold;">提交后 成功或失败后事件处理
          <el-tooltip effect="light" content="一般指执行查询API后，需要执行的后续动作，如：刷新表格列表数据，或其它">
                         <i class="el-icon-info"></i></el-tooltip>
        </div>
        <el-form-item label-width="0">
          <!-- 注意：成功后或失败后事件处理-->
          <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
            :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type=""
          @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="参数控件值 选择" :visible.sync="showDataSourceialogFlag" v-if="showDataSourceialogFlag" v-dialog-drag
        append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
        :close-on-press-escape="false" :destroy-on-close="true">
        <div>
            <!-- <el-button type="text" @click="addQueryOption">展开全部</el-button> -->
            <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
            highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
        </div>
        <div slot="footer" class="dialog-footer">

            <el-button type="primary" size="large" @click="submitNodeEvent()">
            确定</el-button><el-button size="large"  @click="showDataSourceialogFlag = false">
            取消</el-button>
        </div>
        </el-dialog>
  </div>
</template>

<script>
const { actionType } = require('@/enum/enumData')
const default_editMenuForm = {
  label: "",// 标签
  iconClass: "",// 图表ICON
  size: "small",
  textColor:"primary",
  otherParams: {
    hidden: false, // 隐藏
    disabled: false,// 激活
    actionType: "-1",// 动作
    eventType: "click",// 事件类型
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
  },
}
import commonEventDialog from "@/components/form-designer/setting-panel/commonEventDialog.vue"
import afterSuccessOrErrorSetting from '@/components/form-designer/setting-panel/afterSuccessOrErrorSetting'
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import CodeEditor from '@/components/code-editor/index'
import i18n from "@/utils/i18n";
import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
export default {
  name: "operationButtonsSetting",
  mixins: [i18n],
  components: {
    Draggable,
    //CodeEditor: () => import('@/components/code-editor/index'),
    CodeEditor,
    eventTypeByCtrlDialog,
    afterSuccessOrErrorSetting,
    commonEventDialog,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    //contrlType: String,// 控件类型名称
  },
  data() {
    return {
      clickTime: 0, // 用于记录点击时间
      curSetParamsItem: null,// 当前参数设置 编辑 项值
      showDataSourceialogFlag: false,//弹框树 是否打开
      actionTypeList:actionType,
      jsCodeTitle:"",
      functionName:"onBeforeSubmit",
      currentEditSettingBtnOption:{},// 当前操作按钮配置
      // nodeCtrlValue:"",// 当前选中节点值
      // showFlag:false,// 是否显示控件列表 弹框
      showEditMenuDialogFlag: false,
      optionLines: '',
      currentEditOption: {},// 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm),
      actionParamsList: [],
      editMenuFormRules: {
        label: [
          //{ required: true, message: '请输入标签名称', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
    optionModel() {
      return this.selectedWidget.options
    },

  },
  watch: {
    'selectedWidget.options': {
      deep: true,
      handler(val) {
        //console.log('888888', 'Options change!')
      }
    },
  },
  methods: {
    // 参数值设置 弹框 树节点 点击事件
    onNodeTreeClick(params) {
        let _canSelect = false
        this.nodeCtrlValue =""
        let hasChildren = params.hasOwnProperty("children")
        let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
        let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
        let parentNodeLabel = parentsNode.data.label
        let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
            ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params)
        if(!hasChildren){
          _canSelect = true
          this.nodeCtrlValue = ctrlValue
        // this.curSetParamsItem.value = ctrlValue
        }else{
          _canSelect = false
          this.$message.warning('此节点不可选！')
        }
        const now = new Date().getTime();
        if (now - this.clickTime < 500) { // 双击时间间隔设置为500毫秒
          console.log("双击时间间隔设置为500毫秒")
          if(_canSelect){
            this.submitNodeEvent() // 双击处理函数
          }
          
        }
      this.clickTime = now; // 更新点击时间
    },
     // 获取第三层 控件名称
     tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params){
      //debugger
        let newCtrlVal = ctrlValue
        let layoutList =["defaultmenubutton"] //|| parentsNode.parent.data.label.includes('card'),"card"
        let level3NodeType=""
        try {
           level3NodeType = parentsNode.parent.data.text
           if(layoutList.includes(level3NodeType)){
            newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
           }
        } catch (error) {
           level3NodeType=""
        }
        return newCtrlVal
    },
     // 弹框选择 参数 控件 来源
     setParams(item) {
      this.curSetParamsItem = item
      this.showDataSourceialogFlag = true
    },
    getNodeTreeData(){
       let copyData = cloneDeep(this.designer.nodeTreeData)
       let dataList = this.setPublicAttribute(copyData)
       return dataList
    },
    // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData){
      let dataList =[]
      for(let i=0;i<nodeTreeData.length;i++){
          let item = nodeTreeData[i]
          if(item.hasOwnProperty("children")){
              this.setPublicAttribute(item.children)
              dataList.push(item)
          }else{
            let randomNum = Math.floor(Math.random() * 100000000 + 1)
            // 优先子集对外开放属性
            if(item.hasOwnProperty("publicSubAttribute")){
              //debugger
               item.children = []
             
               if(item.publicSubAttribute && item.publicSubAttribute.length>0){
                  item.publicSubAttribute.forEach((subItem,index)=>{
                     
                       let valueItem = {
                          label:subItem.value,
                          id:subItem.value+"-"+randomNum+index
                       }
                       let keyItem = {
                          label:subItem.key,
                          id:subItem.key+"-"+randomNum+index,
                          children:[valueItem]
                       }
                       //debugger
                       item.children.push(keyItem)
                  })
                 // debugger
                  dataList.push(item)
               }

            }else{
              // 对外开放属性
              if(item.hasOwnProperty("publicAttribute")){
               item.children = []
               if(item.publicAttribute && item.publicAttribute.length>0){
                  item.publicAttribute.forEach((subItem,index)=>{
                       let kidItem = {
                          label:subItem,
                          id:subItem+"-"+randomNum
                       }
                       item.children.push(kidItem)
                  })
                  dataList.push(item)
               }
              }else{
                  dataList.push(item)
              }
            }
          }
        
      }
      return dataList
    
    },
     // 提交参数设置
     submitNodeEvent() {
      this.curSetParamsItem.value = this.nodeCtrlValue
      this.showDataSourceialogFlag = false
    },
     // 提交
     submitCommonEventDialog(params){
       let code = params.code
      /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
       this.$set(this.currentEditSettingBtnOption, this.functionName, code)
    },
     // 自定义代码功能
     customJsEventCodeOption(option, idx){
      //debugger
      // 自定义代码 弹框 方法标题+参数名称
       this.jsCodeTitle = option.label+"."+option.value+"."+this.functionName+"(paramsData)"
      this.currentEditSettingBtnOption = option; // 当前菜单按钮属性
      let commonEventDialogRef = this.$refs["commonEventDialogRef"]
      if(commonEventDialogRef){
        commonEventDialogRef.showDialog = true
      }
    },
   
    // 设置组合后的动态参数
    setQueryList() {
      let queryParams = {}
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach(item => {
          if (!!item.key) {
            queryParams[item.key] = item.value
          }
        })
      }
      //debugger
      return queryParams
    },
    // 获取组合后的动态参数
    getQueryList() {
      let queryParamsList = []
      //debugger
      if (this.editMenuForm.actionParams && this.editMenuForm.actionParams.query && Object.keys(this.editMenuForm.actionParams.query).length > 0) {

        for (const [key, value] of Object.entries(this.editMenuForm.actionParams.query)) {
          if (!!key) {
            let newItem = { key, value }
            queryParamsList.push(newItem)
          }
        }
      }
      return queryParamsList
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editMenuForm.label
          this.currentEditOption.value = this.editMenuForm.value
          this.currentEditOption.iconClass = this.editMenuForm.iconClass
          this.currentEditOption.textColor = this.editMenuForm.textColor
          this.currentEditOption.size = this.editMenuForm.size
          this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setQueryList()

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑菜单属性--- 初始化弹框属性参数
    showEditMenuDialogEvent(option) {
      this.currentEditOption = option // 当前菜单属性
      this.showEditMenuDialogFlag = true
      this.editMenuForm.label = option.label
      this.editMenuForm.value = option.value
      this.editMenuForm.iconClass = option.iconClass
      this.editMenuForm.textColor = option.textColor
      
      this.editMenuForm.size = option.size
      this.actionParamsList = [] // 默认
      this.editMenuForm.otherParams = cloneDeep(option.otherParams)
      this.editMenuForm.actionParams = cloneDeep(option.actionParams)
      //debugger
      if (this.editMenuForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getQueryList()
      }
      //debugger
      let tt = this.editMenuForm.otherParams.actionType

    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.optionModel.operationButtons.splice(index, 1)
      }

    },
    // 添加按钮
    addOption() {
      let newValue = this.optionModel.operationButtons.length + 1
      this.optionModel.operationButtons.push(
        { label: '新按钮', value: "", check: false, canRemove: true, otherParams: {}, actionParams: {} }
      )
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1
      this.actionParamsList.push(
        { key: '', value: "" }
      )
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1)
    },


  }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}</style>
