<template>
    <el-dialog title="事件处理" :visible.sync="showDialog" v-if="showDialog" :show-close="true" class="small-padding-dialog"
        v-dialog-drag append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
        :destroy-on-close="true">
        <demoOfJSEditCode></demoOfJSEditCode>
        <el-alert type="info" :closable="false" :title="`${jsCodeTitle}{`"></el-alert>
        <code-editor :mode="'javascript'" :readonly="false" v-model="commonEventHandlerCode"
            ref="ecEditor"></code-editor>
        <el-alert type="info" :closable="false" title="}"></el-alert>
        <div slot="footer" class="dialog-footer">
            <el-button @click="showDialog = false">
                取消</el-button>
            <el-button type="primary" @click="saveCommonEventHandler">
                确认</el-button>
        </div>
    </el-dialog>
</template>
<script>
import demoOfJSEditCode from '@/components/form-designer/setting-panel/demoOfJSEditCode.vue'
import i18n from "@/utils/i18n"
import CodeEditor from '@/components/code-editor/index'
// import { deepClone, generateId } from "@/utils/util"
export default {
    name: "commonEventDialog",
    mixins: [i18n],
    components: {
        CodeEditor,
        demoOfJSEditCode,
    },
    props: {
        designer: Object,
        eventCodeConfig: [Object,Array],
        functionName:{
            type:String,
            default:"onSubmit"
        },
        jsCodeTitle:{
            type:String,
            default:""
        }
    },
    data() {
        return {
            showDialog: false,
            commonEventHandlerCode: '',
        }
    },
    watch:{
        eventCodeConfig:{
            handler(n,o){
                //debugger
                this.bindingCode()
            },
            deep:true
        }
    },
    mounted(){
        this.bindingCode()
    },
    methods: {
        // 初始化代码绑定
        bindingCode(){
           // debugger
            if(this.eventCodeConfig && this.eventCodeConfig.hasOwnProperty(this.functionName)){
                this.commonEventHandlerCode =  this.eventCodeConfig[this.functionName]
            }else{
                this.commonEventHandlerCode =  ""
            }
        },
        saveCommonEventHandler() {
            //debugger
            const codeHints = this.$refs.ecEditor.getEditorAnnotations()
            let syntaxErrorFlag = false
            if (!!codeHints && (codeHints.length > 0)) {
                codeHints.forEach((chItem) => {
                    if (chItem.type === 'error') {
                        syntaxErrorFlag = true
                    }
                })

                if (syntaxErrorFlag) {
                    this.$message.error(this.i18nt('designer.setting.syntaxCheckWarning'))
                    return
                }
            }
            /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            // this.$set(this.formConfig, this.curEventName, this.commonEventHandlerCode)
            let _self = this
            this.$nextTick(() => {
                let params = {
                    code: _self.commonEventHandlerCode
                }
                _self.$emit("submitEvent", params)
                _self.showDialog = false
            })

        },
    }
}
</script>
<style lang="sass">

</style>