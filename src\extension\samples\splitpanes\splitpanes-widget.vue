

<template>
    <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                       :index-of-parent-list="indexOfParentList">
  
      <div :key="widget.id" class="splitpanes-container"
           :class="{'selected': selected}" @click.stop="selectWidget(widget)">
      
        <splitpanes class="default-theme" 
          :horizontal="widget.options.horizontal" 
          :push-other-panes="widget.options.pushOtherPanesDefault"
          :dbl-click-splitter="widget.options.dblClickSplitter" 
          :rtl="widget.options.RightToLeft"
          :first-splitter="widget.options.firstSplitter"
          :style="[{ 'height': getHeight()+'px' }]"
          >
            <pane  :size="pane.options.wtPercent" style="background-color: #ccc;" :label="pane.options.label" :name="pane.options.name"  @click.native.stop="selectWidget(widget)" v-for="(pane, index) in widget.panes" :key="index" >
                    <div v-html="bgColorStyle"></div>
                    <div v-html="btnColorStyle"></div>
                    <draggable :list="pane.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
                       handle=".drag-handler"
                       @add="(evt) => onContainerDragAdd(evt, pane.widgetList)"
                       @update="onContainerDragUpdate" :move="checkContainerMove">
                    <transition-group name="fade" tag="div" class="form-widget-list">
                        <template v-for="(subWidget, swIdx) in pane.widgetList">
                        <template v-if="'container' === subWidget.category">
                            <component :sourceVFormRender="sourceVFormRender" :contentBoxHeight="getSettingHeight(pane)" :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id" :parent-list="pane.widgetList"
                                            :index-of-parent-list="swIdx" :parent-widget="widget"></component>
                        </template>
                        <template v-else>
                            <component :sourceVFormRender="sourceVFormRender" :contentBoxHeight="getSettingHeight(pane)" :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id" :parent-list="pane.widgetList"
                                        :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
                        </template>
                        </template>
                    </transition-group>
                  </draggable>
                <!-- </div>  -->
            </pane>
        </splitpanes>
      </div>
  
    </container-wrapper>
  </template>
  
  <script>
    import Draggable from 'vuedraggable'
    import i18n from "@/utils/i18n"
    import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
    import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
    import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
    import refMixinDesign from "@/components/form-designer/refMixinDesign"
  
    export default {
      name: "splitpanes-widget",
      componentName: 'ContainerWidget',
      mixins: [i18n, containerMixin, refMixinDesign],
      inject: ['refList','sourceVFormRenderState'], 
      components: {
        ContainerWrapper,
        Draggable,
  
        ...FieldComponents,
      },
      props: {
        widget: Object,
        parentWidget: Object,
        parentList: Array,
        indexOfParentList: Number,
        designer: Object,
        previewState: { //是否表单预览状态
          type: Boolean,
          default: false
        },
        contentBoxHeight:{
          type: Number,
          default: 0
        },
            // 控件来源父集 add by andy
     sourceVFormRender:{
        type: String,
        default: ""
      },
        designState: {
          type: Boolean,
          default: false
        },
      },
      data() {
        return {
          bgColorStyle:"",// 背景颜色
          btnColorStyle:"",// 拖拽按钮颜色
          predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
        }
      },
      computed: {
        selected() {
          return this.widget.id === this.designer.selectedId
        },
  
        customClass() {
          return this.widget.options.customClass || ''
        },
  
      },
      watch: {
        //
      },
      created() {
        this.initRefList()
      },
      mounted() {
        this.setBgColor()
        this.setBtnColor()
      },
      methods: {
        // 设计模式 默认高度 获取 asMainContainer作为主容器时,需要距离顶部10PX
        getHeight(){
            // 设置了固定高度
          let tableHeight = Number(this.widget.options.settingHeight)
          if(tableHeight && !!tableHeight && tableHeight>0){
            return tableHeight
          }
          let testHeight = 300
            if(!!this.widget.options.asMainContainer){
                  testHeight = 600
                }else{
                  testHeight = 300
                }
            return testHeight
        },
           // 设置拖拽条背景样式颜色
        setBgColor(){
          if(!!this.widget.options.bgColor){
            let bgColor= this.widget.options.bgColor
            this.bgColorStyle =`<style type="text/css">
          .splitpanes.default-theme .splitpanes__pane {
              background-color: ${bgColor};
          }
          .splitpanes.default-theme .splitpanes__splitter {
            background-color: ${bgColor};
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            position: relative;
            -ms-flex-negative: 0;
            flex-shrink: 0
        }
          .default-theme.splitpanes--vertical>.splitpanes__splitter,
          .default-theme .splitpanes--vertical>.splitpanes__splitter {
              width: 7px;
              border-left: 1px solid ${bgColor};
              margin-left: -1px
          }
          .default-theme.splitpanes--horizontal>.splitpanes__splitter,
          .default-theme .splitpanes--horizontal>.splitpanes__splitter {
              height: 7px;
              border-top: 1px solid ${bgColor};
              margin-top: -1px
          }
          </style>`
          }
        
        },
        setBtnColor(){
          let btnColor= this.widget.options.btnColor
          if(!!btnColor){
            this.btnColorStyle =`<style type="text/css">
            .splitpanes.default-theme .splitpanes__splitter:before,
            .splitpanes.default-theme .splitpanes__splitter:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  background-color: ${btnColor};
                  -webkit-transition: background-color .3s;
                  -o-transition: background-color .3s;
                  transition: background-color .3s
              }

              .splitpanes.default-theme .splitpanes__splitter:hover:before,
              .splitpanes.default-theme .splitpanes__splitter:hover:after {
                  background-color: ${btnColor};
              }
          </style>`
          }
        
        },
        // 设计默认 不许动态获取高度，请查看 splitpanes-item
        getSettingHeight(pane){
          let testHeight = 300
          // 设置了固定高度
          let tableHeight = Number(this.widget.options.settingHeight)
          if(tableHeight && !!tableHeight && tableHeight>0){
              return tableHeight
          }
          if(!!pane){
             // 是否上下布局
            if(!!this.widget.options.horizontal){
              let size = pane.options.wtPercent
              let height = size/100 * testHeight
              return height
            }else{
              return testHeight
            
            }
          }else{
            return testHeight
          }
         
        }
      
      }
    }
  </script>
  
  <style lang="scss" scoped>
    .splitpanes-container {
      //padding: 5px;
      margin: 2px;
  
      .form-widget-list {
        min-height: 28px;
      }
    }
  
    .splitpanes-container.selected {
      outline: 2px solid $--color-primary !important;
    }
  
  </style>
  