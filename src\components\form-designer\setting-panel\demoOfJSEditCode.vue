<template>
    <div>
        <el-alert
        title="点击查看 自定义 代码案例"
        type="warning"
        @click.native ="showModel=true"
        show-icon
        :closable="true">
    </el-alert>
    <vxe-modal title="自定义 代码案例" v-model="showModel" width="888px" height="650px">
        <el-collapse v-model="activeName" accordion>
                <el-collapse-item title="this.$refs.getWidgetRef 获取控件实例" name="1">
                    <div>inputPopup.onAfterPopupChange(value, originData, otherOptions) {} </div>
                    <div>使用this.getWidgetRef('控件唯一名称')获取控件实例 (唯一名称 field.options.name) </div>
                    <div>value 是返回的配置数据，originData 是标准弹框 返回的原始数据,otherOptions其它属性如：表单，字段配置属性等</div>
                    <div>直接使用(数据集ID)查询数据 格式如下：</div>
                    <div>this.$request["post"]("api/MD/DataSet/GetListByDataSetId", {"Id":数据集ID,"Parameter":{其它参数}})</div>
                    <div>因为返回的数据为字符串 必须使用 数据转换 JSON.parse(返回数据数组)</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo1" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="格式化提交数据和自定义数据" name="2">
                    <div>form.onBeforeSubmit(postParams){} </div>
                    <div>postParams 是提交的原始参数</div>
                    <div>--PostData  是接口必填的参数</div>
                    <div>--ParentTableRow 是自定义传递父表格选中行的数据</div>
                    <div>此处使用this.$refs.editPreForm.getWidgetRef 非直接this.$refs.getWidgetRef 获取控件实例</div>
                    <div>最后提交的参数必须时PostData对象</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo2" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="表格操作列隐藏按钮逻辑.onBeforeHiding(paramsData){...}" name="3">
                    <div>paramsData.currentRow 获取当前选中行</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo3" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="表格行操作按钮，根据不同状态，显示与否" name="4">
                    <div>paramsData.currentRow 为当前选中行</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo4" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="表格行操作按钮，提交前代码拦截" name="5">
                    <div>获取实例为：this.vueInstance.pageInstance.$refs.preForm.getWidgetRef</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo5" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="表格 交互事件之有趣的异步弹框" name="6">
                    <div>获取实例为：this.pageInstance.$refs.preForm.getWidgetRef，少了vueInstance</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo6" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="禁用默认菜单某个按钮 是否可以点击" name="7">
                    <div>获取表格实例为：let vxetable54796Obj = this.pageInstance.$refs.preForm.getWidgetRef("vxetable54796")
                     let vxetable54796Ref = vxetable54796Obj.$refs["vxetable54796"]</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo7" ></code-editor>
                </el-collapse-item>
                <el-collapse-item title="获取弹框表格选中数据和主表选中行" name="8">
                    <div>postParams 默认参数，包含postParams.pageInstance.$refs 可获取主页面实例</div>
                    <code-editor :mode="'javascript'" :readonly="true" v-model="demo8" ></code-editor>
                </el-collapse-item>
            </el-collapse>
          </vxe-modal>
           
    </div>
</template>
<script>
import CodeEditor from '@/components/code-editor/index'
export default {
    name:"demoOfJSEditCode",// 样式代码
    components: {
        CodeEditor,
    },
    data(){
        return{
            showModel:false,
            activeName: '1',
            demo1:`// 工艺路线下拉实例\nlet selectRef = this.getWidgetRef(\"select21411\")\n// 产品规则表实例\nlet vxetable64253Ref=this.getWidgetRef(\"vxetable64253\")\n// 产品模板表实例\nlet vxetable89511Ref=this.getWidgetRef(\"vxetable89511\")\n// 工艺路线ID\nlet _croute_id = originData.originData[0].CROUTE_ID\n// 产品CID\nlet _CID = originData.originData[0].CID\nif(!!_croute_id){\n   // 设置工艺路线\n  selectRef.setValue(_croute_id)\n}\ntry{\n  //  获取产品规则配置===数据源ID\n  let _dataSetId_1=\"6506212136255493\" \n  // 使用数据源ID 固定查询 指定数据\n  this.$request[\"post\"](\"api/MD/DataSet/GetListByDataSetId\",\n  {\"Id\":_dataSetId_1,\"Parameter\":{\"ItemID\":_CID}}\n  ).then(res => { \n     // 数据转换\n    let _dataList= JSON.parse(res.Datas)\n    // 产品规则===表格赋值\n    vxetable64253Ref.$refs.vxetable64253.setTableData(_dataList)\n  })\n}catch(error){\n  console.error(\"=======获取产品规则配置=====inputPopup28269.onAfterPopupChange=========\")\n}\n\n\ntry{\n  //  获取产品模板配置 ==数据源ID\n  let _dataSetId_2=\"6506835768508421\" \n  // 使用数据源ID 固定查询 指定数据\n  this.$request[\"post\"](\"api/MD/DataSet/GetListByDataSetId\",\n  {\"Id\":_dataSetId_2,\"Parameter\":{\"ItemID\":_CID}}\n  ).then(res => { \n     // 数据转换\n    let _dataList= JSON.parse(res.Datas)\n    // 产品模板配置 ==表格赋值\n    vxetable89511Ref.$refs.vxetable89511.setTableData(_dataList)\n  })\n}catch(error){\n  console.error(\"=======获取产品模板配置 ==inputPopup28269.onAfterPopupChange=========\")\n}"`,
            demo2:`// 产品规则表实例\nlet vxetable64253Ref=this.$refs.editPreForm.getWidgetRef(\"vxetable64253\")\n// 产品模板表实例\nlet vxetable89511Ref=this.$refs.editPreForm.getWidgetRef(\"vxetable89511\")\n// 获取自定义参数,父表格选中行数据\nlet PostData = postParams.ParentTableRow\n// 获取指定TAB-1表格数据\nPostData.RuleList =vxetable64253Ref.$refs.vxetable64253.getTableData()\n// 获取指定TAB-2表格数据\nPostData.TempList =vxetable89511Ref.$refs.vxetable89511.getTableData()\nPostData.PackageRuleList =[]\nPostData.KeyPartsList =[]\nPostData.MachineLinkList =[]\n// 自定义返回格式参数，但必须是PostData对象内\nlet params ={\n  PostData\n}\nreturn params`,
            demo3:`let hasCID = parseInt(paramsData.currentRow.CID)\nif(hasCID>0){\n  return false\n}else{\n  return true\n}`,
            demo4:`let CSTATUS = parseInt(paramsData.currentRow.CSTATUS)\n// 0 创建中 10-20 已提交，30已审核\nif(CSTATUS=='0'){\n  return true\n}else{\n  return false\n}`,
            demo5:`try{\n  // 主表实例 注意此处获取实例的方式，所处位置不同，获取实例的方式也不同 \nlet vxetable25093Ref=this.vueInstance.pageInstance.$refs.preForm.getWidgetRef(\"vxetable25093\")\n// 从表实例 \nlet vxetable48522Ref=this.vueInstance.pageInstance.$refs.preForm.getWidgetRef(\"vxetable48522\")\n// 主表当前选中行\nlet currrentRow = vxetable25093Ref.$refs.vxetable25093.getCurrentRecord()\n// 从表表格数据\nlet subTableData =vxetable48522Ref.$refs.vxetable48522.getTableData()\n// 组装提交数据格式\ncurrrentRow.CDETAILS = subTableData\nparamsData.Parameter.PostData=[currrentRow]\nreturn paramsData\n}catch(error){\n  console.error(\"==保存..onBeforeSubmit===error\")\n  return paramsData\n}`,
            demo6:`let _self = this\r\n// paramsData 包含oldSelectedRow，newSelectedRow\r\n// 从表实例 \r\nlet _data = paramsData\r\n// 注意此处 获取表格实例方法为:=this.pageInstance.$refs.preForm.getWidgetRef(\"vxetable48522\")\r\nlet vxetable48522Ref=this.pageInstance.$refs.preForm.getWidgetRef(\"vxetable48522\")\r\n// 获取从表格数据集（获取插入、删除、更改的数据，对于增删改查表格非常方便） {insertRecords, removeRecords, updateRecords}\r\nlet subTableObj =vxetable48522Ref.$refs.vxetable48522.getRecordset()\r\n// 如果有更新的或新添加的数据时\r\nif(subTableObj.insertRecords.length>0 || subTableObj.updateRecords.length>0 || subTableObj.removeRecords.length>0){\r\n  //使用异步弹框函数 \r\n  return new Promise(function(resolve,reject){\r\n        _self.$confirm('还未保存该数据，确定切换吗?', '提示', {\r\n                      confirmButtonText: '确定',\r\n                      cancelButtonText: '取消',\r\n                      type: 'warning'\r\n                  }).then(() => {\r\n                    // 注意：非使用 return true\r\n                    resolve(true)\r\n                  }).catch(() => {\r\n                    // 注意 此处使用 resolve(false) 非reject(false)\r\n                    resolve(false)\r\n                  });\r\n    })\r\n}else{\r\n  // 直接返回 真\r\n  return true\r\n}\r\n`,
            demo7:`// 必须选择了项目类别 且表格当前选中行不为空 才能添加 子任务 且CID 不为空\r\n//debugger\r\nlet _currentRow =null\r\ntry{\r\n  let defaultmenubutton25359Obj = this.pageInstance.$refs.preForm.getWidgetRef(\"defaultmenubutton25359\")\r\n  let vxetable54796Obj = this.pageInstance.$refs.preForm.getWidgetRef(\"vxetable54796\")\r\n  let vxetable54796Ref = vxetable54796Obj.$refs[\"vxetable54796\"]\r\n      if(vxetable54796Ref){\r\n        _currentRow = vxetable54796Ref.currentRow\r\n      }\r\n  if(defaultmenubutton25359Obj){\r\n    let ddlVal= defaultmenubutton25359Obj.$refs[\"searchKey32194\"][0].value\r\n    if(ddlVal==null || ddlVal==undefined ||ddlVal==''){\r\n      if(ddlVal=='0' ){\r\n        if(_currentRow && !!_currentRow.CID && _currentRow.CID!='0'){\r\n           return false\r\n        }else{\r\n           return true\r\n        }\r\n      }else{\r\n         return true\r\n      }\r\n     \r\n    }else{\r\n      if(_currentRow && !!_currentRow.CID && _currentRow.CID!='0'){\r\n           return false\r\n        }else{\r\n           return true\r\n        }\r\n    }\r\n    \r\n  }\r\n}catch(error){\r\n  return true\r\n}`,
            demo8:`let tableObj_list=postParams.pageInstance.$refs.preForm.getWidgetRef(\"vxetable50398\")\nlet tableObj_box = this.$refs.editPreForm.getWidgetRef(\"vxetable20539\")\n// 获取自定义参数,父表格选中行数据\nlet selectData = tableObj_box.$refs.vxetable20539.getCheckboxRecords()\n// 列表页面选择行\nlet currentRow = tableObj_list.$refs.vxetable50398.currentRow\nlet formatData =[]\nif(selectData && selectData.length>0){\n  \n  selectData.forEach(item=>{\n    let newItem ={\n      CLABOR_ID:item.ID,\n      CGROUP_ID:currentRow.ID\n    }\n    formatData.push(newItem)\n  })\n}\n\nreturn formatData`,
        }
    },
    methods:{}
}
</script>