export const descriptionsSchema = {
  type: 'descriptions',
  icon: 'descriptions',
  formItemFlag: false,
  options: {
    name: '',
    title: 'descriptions',
    descriptionsList: '',
    size:"mini",
    columnNumber:3,
    seriesItems: [
      { label: 'label1',displayType:"text",columnNumber:1,showAsTag:false,tagSize:"small", key: 'label1',iconUrl:'', fieldName: '', disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
    ],
  }
}


export const iconInfoSchema = {
  type: 'iconInfo',
  icon: 'iconInfo',
  formItemFlag: false,
  options: {
    name: '',
    label: 'iconInfo',
    iconInfo:true,
    // 同环比信息项配置本月废品量(万吨)
    iconInfoItems: [
       { title:"标题1",iconUrl:'el-icon-present',iconColor:'#3799FF',iconSize:60,totalField:'123',upOrDownField:'16.5%', label: '同比', fieldName: 'iconInfo1', disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
   ],
  }
}
export const colorBlockInfoSchema = {
  type: 'colorBlockInfo',
  icon: 'colorBlockInfo',
  publicAttribute:["value"],// 对外公布的属性字段
  formItemFlag: false,
  options: {
    name: '',
    label: 'colorBlockInfo',
    customClass: '',
    height:0,
    padding:15,
    colorBlockInfo:true,
    // 同环比信息项配置本月废品量(万吨)
    colorBlockInfoItems: [
       { title:"标题1",showBorder:false, blockWidth:60,borderType:'solid',bgColor:'#67C23A',fontColor:'#fff',totalField:'', fieldName: 'colorBlockInfo1', disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
   ],
  }
}

export const collapseDescSchema = {
  type: 'collapseDesc',
  icon: 'collapseDesc',
  publicAttribute:["value"],// 对外公布的属性字段
  formItemFlag: false,
  options: {
    name: '',
    label: 'collapseDesc',
    customClass: '',
    height:0,
    padding:15,
    collapseDesc:true,
  }
}

export const tabMenuSchema = {
  type: 'tabMenu',
  icon: 'tabMenu',
  formItemFlag: false,
  options: {
    name: '',
    label: 'tabMenu',
    customClass: '',
    height:0,
    padding:15,
    tabMenu:true,
    // 同环比信息项配置本月废品量(万吨)
    tabMenuItems: [
       { title:"标题1",name:"1", iconUrl:'el-icon-menu',iconColor:'#606266',iconSize:20,bgColor:'#fff', hoverColor:'lightsalmon',activeColor:"lightsalmon", fontColor:'#606266', fieldName:'tabMenu1', disabled:false, check: true, canRemove: false, otherParams: {}, actionParams: {} },
   ],
  }
}
// export const divContainerSchema = {
//   type: 'divContainer',
//   icon: 'divContainer',
//   formItemFlag: false,
//   options: {
//     name: '',
//     label: 'divContainer',
//     divContainer:true,// 是否显示自定义编辑熟悉
//   }
// }
// customTree 自定义树结构 默认配置
export const customTreeSchema = {
  type: 'customTree',
  icon: 'customTree',
  formItemFlag: false,
  publicAttribute:["value","node"],// 对外公布的属性字段
  options:{
    name: '',
    title: '自定义树',
    showTitle:true,// 是否显示标题
    showTopFilter:false,// 是否添加顶级过滤标题：如：全部类别
    showExtraItemEvent:false,//显示项额外事件按钮
    showExtraItemFilter:[],//项额外事件显示条件
    showFilterInput:false,// 是否显示查询
    topFilterText:"全部",//顶级过滤参数 默认值
    topFilterValue:0,//顶级过滤参数 默认值
    actionName:"",//执行API
    dataSetSelectedModel:"",// 多集合中选中集合
    dataSetData:[],// 多集合字段列表
    triggerCtrlNames:[],// 触发控件的名称（作用于什么控件）可同时触发多个控件{ label: '', value: "", check: true, canRemove: false }
    dataSetAllModel:{},// 多集合模型所有实体数据
    customTree: true,
    selectedType:"",
    eventList:[],
    treeRowField:"",
    dataSetAllModel:[],
    treeParentField:"",
    showTreeLabel:"",//显示标签字段
    filterTreeField:"",//过滤字段,主要用于触发控件时，需要传递过去过滤的字段值
  }
}

// echarts 默认配置
export const echartsSchema = {
  type: 'echarts',
  icon: 'echarts',
  formItemFlag: false,
  publicAttribute:["row"],
  options: {
    name: '',
    label: 'echarts',
    customClass: '',
    echarts: true,
    echartTitle: {
      text: '',
      subtext: '',
      left: 'center'
    },
    xAxisKey: "department",// x轴显示字段
    xAxisDirection: "horizontal",
    actionName:"",//执行API
    dataSetSelectedModel:"",// 多集合中选中集合
    dataSetData:[],// 多集合字段列表
    dataSetAllModel:{},// 多集合模型所有实体数据
    showTextIcon:false,
    textIconColor:'#7C260B',
    grid: {
      left: '3%',
      right: '3%',
      bottom: '12%',
      top:'15%',
      containLabel: true
    },
    tableColumns:[{
        field: "spending",
        title: "支出",
      },

      {
        field: "budget",
        title: "预算",
      },
      {
        field: "department",
        title: "部门",
      },
      {
        field: "date",
        title: "时间",
      },
      {
        field: "finalP",
        title: "其它数字",
      },
   ],
    series: [
      {
        "columnName": "spending",
        "seriesName": "支出",
        "show": true,
        "dataIndex": 1
      },
      {
        "columnName": "budget",
        "seriesName": "预算",
        "show": true,
        "dataIndex": 2
      }
    ],
    xConfig: {
      "axisName": "",//X轴名称
      "type": "category",
      axisLabel: { 
        interval: 'auto', // 0强制显示所有，不间隔不显示，如果为数字，间隔几个显示
        rotate: 0  // 刻度标签旋转的角度,在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。
      }
    },
    yConfig: {
      "axisName": "",//Y轴名称
      //"logBase": "",
      "type": "value",
      "formatter": {
        "value": "{value}"
      }
    },
    yAxisExList:[
      // {
      //  "yAxisIndex": 1
      //    remarkName:"备注",
      //   "axisName": "",//Y轴名称
      //   "type": "value",
      //   "labelformatValue":""
      //    "color":""
      //    "position":"right"
      //    "axisLineShow":true
      //    "offset":0
      // }
    ], // 其它Y轴
    chartConfig: {
      compType: "bar",
      comp: {
        type: "basic",
        roseType:true,
        showLabel: false, 
        showLabelValue:false,// 饼图 是否显示数值
        shape: "circle",
        smooth: false, 
        itemColor: {
            "value": "red"
        }
      },
      tooltip:{
        trigger:"item"
      }
    },
    legendConfig: {
      position: "none"
    },
    containerSize: {
      "height": 0, 
      "width": 'auto'
    }, 
    testChartsData:[
      {
        "date": "2021-09",
        'finalP':1.1,
        "department": "行政部",
        "spending": 9003,
        "budget": 1000
      },
      {
        "date": "2021-08",
        'finalP':1.1,
        "department": "财务部",
        "spending": 5033,
        "budget": 2000
      },
      {
        "date": "2021-07",
        'finalP':1.1,
        "department": "销售部",
        "spending": 6230,
        "budget": 3000
      },
      {
        "date": "2021-10",
        'finalP':1.1,
        "department": "行政部",
        "spending": 13032,
        "budget": 15000
      },
      {
        "date": "2021-10",
        'finalP':1.2,
        "department": "财务部",
        "spending": 2300,
        "budget": 5000
      },
      {
        "date": "2021-10",
        'finalP':1.2,
        "department": "销售部",
        "spending": 7323.5,
        "budget": 8000
      },
      {
        "date": "2021-11",
        'finalP':1.3,
        "department": "行政部",
        "spending": 13000,
        "budget": 16023
      },
      {
        "date": "2021-11",
        'finalP':1.3,
        "department": "财务部",
        "spending": 3569.5,
        "budget": 3000
      },
      {
        "date": "2021-11",
        'finalP':1.3,
        "department": "销售部",
        "spending": 10000,
        "budget": 9932
      },
      {
        "date": "2021-12",
        'finalP':1.5,
        "department": "行政部",
        "spending": 18033,
        "budget": 20000
      },
      {
        "date": "2021-12",
        'finalP':1.6,
        "department": "财务部",
        "spending": 4890,
        "budget": 4500
      },
      {
        "date": "2021-12",
        'finalP':1.9,
        "department": "销售部",
        "spending": 9322,
        "budget": 8000
      }
    ]
  }
}



// 窗口分割容器你
export const splitpanesSchema = {
  type: 'splitpanes',
  category: 'container',
  icon: 'splitpanes',
  // widgetList: [],
  panes: [
    {
      "type": "split-pane",
      "category": "container",
      "icon": "split-pane",
      "widgetList": [],
      "options": {
        "name": "pane1",
        "wtPercent": 20,
        "label": "pane 1",
        "hidden": false,
        "customClass": ""
      },
      "id": "split-pane-93187"
    },
    {
      "type": "split-pane",
      "category": "container",
      "icon": "split-pane",
      "widgetList": [],
      "options": {
        "name": "pane2",
        "wtPercent": 80,
        "label": "pane 2",
        "hidden": false,
        "customClass": ""
      },
      "id": "split-pane-94187"
    },
  ],
  options: {
    name: '',
    label: 'splitpanes',
    asMainContainer:false,// 是否作为主容器
    OffsetHeightNumber:0,// 作为主容器时，高度偏移量
    horizontal: false,
    firstSplitter: false,
    RightToLeft: false,
    dblClickSplitter: true,
    pushOtherPanesDefault: true,
    hidden: false,
    customClass: '',
    splitpanesSetting: true,
  }
}

// 高级表格
export const vxetableSchema = {
  type: 'vxetable',
  formItemFlag: false,
  icon: 'data-table',
  publicAttribute:["row","pageIndex","pageSize","totalRows","tableData","selectedRows","checkboxRecordsCIDS","checkboxRecords"],// 对外公布的属性字段
  options: {
    name: '',
    label: 'vxetable',
    OffsetHeight:0,// 高度偏移量，可正，可负
    triggerCtrlNames:[],// 触发控件的名称（作用于什么控件）可同时触发多个控件{ label: '', value: "", check: true, canRemove: false }
    vxetable: true,// 表格设置器
    dataSetSelectedModel:"",// 多集合模型
    dataSetData:[],// 多集合模型
    dataSetAllModel:{},// 多集合模型
    requstConfig: {
      postUrl: "",
      actionName: "API",// Page 主表：分页查询，Detail：从表：详情查询
      postParams: {},
    },
    // 表格树配置
    // showTreeNode:false,
    treeConfig: { transform: false, rowField: 'CID', parentField: 'CPARENT_ORG_ID' },
    layoutLevel: 1,
    // hidden: false,
    // rowSpacing: 8,
    tableHeight: '', //高度，百分比或像素,默认auto
    tableWidth: '100%', //宽度，百分比或像素宽度
    // customClass: '', //自定义css样式
    // stripe: true, // 是否斑马线
    showSeqColumn: true, // 是否显示行号列
    // showCheckBox: true, // 是否显示复选框列
    showPagination: true, // 是否分页
    smallPagination: false, //是否显示小型分页
    // showSummary: false, // 是否合计
    // border: true, // 是否带有纵向边框（拖拽）
    // tableSize: 'medium', // 表格大小 large大；medium中；small小，mini迷你
    columnsTreeNode: "",
    tableColumns: [{
      type: "seq",
      fixed: "left",
      title: "序号",
      width: 50,
      "iisShowList": 1,
      "iisShowEdit": 1,
    },

    {
      field: "field",
      title: "字段名",
      width: "120",
      "iisShowList": 1,
      "iisShowEdit": 1,
    },
    {
      field: "title",
      title: "中文描述",
      width: "100",
      "iisShowList": 1,
      "iisShowEdit": 1,
    },
    {
      field: "titleHelp",
      title: "标题帮助",
      width: "100",
      "iisShowList": 1,
      "iisShowEdit": 1,
      titlePrefix: {
        content: `标题帮助内容`,
        //useHTML:false
      }
    },
    {
      field: "fieldDefault",
      title: "默认值",
      width: "80",
      "iisShowList": 1,
      "iisShowEdit": 1,
    },
    {
      field: "iisRequired",
      title: "必填？",
      "iisShowList": 1,
      "iisShowEdit": 1,
      width: "75",
      align: "center",
      cellRender: {
        name: "vxeCheckbox",
      },
      titlePrefix: {
        content: `表单或表格数据中，是否必填`,
        //useHTML:false
      }
    },
    {
      field: "iisShowList",
      title: "列表显示？",
      width: "100",
      align: "center",
      "iisShowList": 1,
      "iisShowEdit": 1,
      cellRender: {
        name: "vxeCheckbox",
      },
      titlePrefix: {
        content: `列表页面中 字段是否显示`,
        //useHTML:false
      }
    },
    {
      field: "iisShowEdit",
      title: "编辑显示？",
      align: "center",
      width: "100",
      "iisShowList": 1,
      "iisShowEdit": 1,
      cellRender: {
        name: "vxeCheckbox",
      },
      titlePrefix: {
        content: `编辑页面中 字段是否显示`,
      }
    },

    {
      field: "width",
      title: "列表宽",
      width: "70",
      "iisShowList": 1,
      "iisShowEdit": 1,
      align: "right",
    },
    {
      field: "editWidth",
      title: "编辑宽",
      width: "70",
      "iisShowList": 1,
      "iisShowEdit": 1,
      align: "right",
    },
    {
      field: "labelWidth",
      title: "标题宽",
      width: "80",
      "iisShowList": 1,
      "iisShowEdit": 1,
      align: "right",
    },
     
    ],
    showOperationColumn: false, // 是否显示操作列
    operationColumn: [
      {
        fixed: 'left',
        slots: { default: 'operate' },
        title: '操作',
        headerAlign: 'center',
        align: 'center',
        width: 120,
      }
    ],
    // 操作按钮配置
    operationButtons: [
      { label: '新增', value: "iisAdd", check: false, canRemove: false, otherParams: {}, actionParams: {} },
      { label: '编辑', value: "iisEdit", check: true, canRemove: false, otherParams: {}, actionParams: {} },
      { label: '删除', value: "iisDelete", check: true, canRemove: false, otherParams: {}, actionParams: {} },
      // { label: '详情', value: "iisDetail", check: false, canRemove: false, otherParams: {}, actionParams: {} },
    ],
     // 表格底部按钮配置
     footerButtons: [
        { label: '新增',onBeforeSubmit: '', fieldName: 'iisAdd',value: "iisAdd", size:"small",type:"primary",icon:"vxe-icon-add",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '编辑',onBeforeSubmit: '',  fieldName: 'iisEdit',value: "iisEdit",size:"small",type:"success",icon:"vxe-icon-edit",disabled:false,  check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '删除',onBeforeSubmit: '',  fieldName: 'iisDel',value: "iisDel", size:"small",type:"danger",icon:"vxe-icon-delete",disabled:false,  check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '启用',onBeforeSubmit: '',  fieldName: 'iisEnable',value: "iisEnable", size:"small",type:"success",icon:"el-icon-unlock",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '禁用',onBeforeSubmit: '',  fieldName: 'iisDisabled',value: "iisDisabled",size:"small",type:"danger",icon:"el-icon-lock",disabled:false,  check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '导入',onBeforeSubmit: '',  fieldName: 'iisImport',value: "iisImport", size:"small",type:"primary",icon:"vxe-icon-upload",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '预览',onBeforeSubmit: '',  fieldName: 'iisView',value: "iisView", size:"small",type:"primary",icon:"vxe-icon-eye-fill",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '导出',onBeforeSubmit: '',  fieldName: 'iisExport',value: "iisExport", size:"small",type:"primary",icon:"vxe-icon-download",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
        { label: '刷新',onBeforeSubmit: '',  fieldName: 'iisRefresh',value: "iisRefresh", size:"small",type:"success",icon:"vxe-icon-refresh",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
    ],
    tabelConfigs: {
      rowId: 'CID',
      menuConfig: {}, // 右键菜单配置
      footerConfig: {
        leftToolShow: true, // 是否显示左侧工具栏列筛选
        rightToolShow: true, // 是否显示右侧工具栏分页
        pageConfig: { // 分页配置
          total: 0,
          currentPage: 1,
          pageSize: 10,
        }
      }
    },
    // dsEnabled: false, //是否使用数据源数据
    // dsName: '',  //数据源名称
    tableData: [
      {
        "field": "CDATETIME_CREATED",
        "fieldDefault": "",
        id: 24300,
        parentId: 10050,
        "pid": 652367510536197,
        "title": "创建时间",
        "iisRequired": 0,
        "iisShowList": 0,
        "iisShowEdit": 0,
        "iisShowForm": 0,
        "width": 100,
        "editWidth": 100,
        "labelWidth": 110,
        "titleHelp": "",
        "fieldOrder": 10,
        "editOrder": 10,
        "titleAlign": "",
        "align": "",
        "controlType": "输入框",
        "sourceKey": "",
        "matchField": "",
        "searchParams": "",
        "iisVirtual": 0,
        "iisQuery": 0,
        "iisHQuery": 0,
        "fixed": null,
        "iisSummary": 0,
        "iisReadOnly": 0,
        "iisMoney": 0,
        "iisSortable": 0,

      },
      {
        "field": "CUSER_CREATED",
        "fieldDefault": "",
        id: 24301,
        parentId: 10050,
        "pid": 652367510536197,
        "title": "创建人",
        "iisRequired": 0,
        "iisShowList": 0,
        "iisShowEdit": 0,
        "iisShowForm": 0,
        "width": 100,
        "editWidth": 100,
        "labelWidth": 110,
        "titleHelp": "",
        "fieldOrder": 10,
        "editOrder": 10,
        "titleAlign": "",
        "align": "",
        "controlType": "输入框",
        "sourceKey": "",
        "matchField": "",
        "searchParams": "",
        "iisVirtual": 0,
        "iisQuery": 0,
        "iisHQuery": 0,
        "fixed": null,
        "iisSummary": 0,
        "iisReadOnly": 0,
        "iisMoney": 0,
        "iisSortable": 0,

      },
      {
        "field": "CDATETIME_MODIFIED",
        "fieldDefault": "",
        id: 24302,
        parentId: 10050,
        "pid": 652367510536197,
        "title": "更新时间",
        "iisRequired": 0,
        "iisShowList": 0,
        "iisShowEdit": 0,
        "iisShowForm": 0,
        "width": 100,
        "editWidth": 100,
        "labelWidth": 110,
        "titleHelp": "",
        "fieldOrder": 10,
        "editOrder": 10,
        "titleAlign": "",
        "align": "",
        "controlType": "输入框",
        "sourceKey": "",
        "matchField": "",
        "searchParams": "",
        "iisVirtual": 0,
        "iisQuery": 0,
        "iisHQuery": 0,
        "fixed": null,
        "iisSummary": 0,
        "iisReadOnly": 0,
        "iisMoney": 0,
        "iisSortable": 0,

      },
      {
        "field": "CUSER_MODIFIED",
        "fieldDefault": "",
        id: 24303,
        parentId: 10050,
        "pid": 652367510536197,
        "title": "更新用户",
        "iisRequired": 0,
        "iisShowList": 0,
        "iisShowEdit": 0,
        "iisShowForm": 0,
        "width": 100,
        "editWidth": 100,
        "labelWidth": 110,
        "titleHelp": "",
        "fieldOrder": 10,
        "editOrder": 10,
        "titleAlign": "",
        "align": "",
        "controlType": "输入框",
        "sourceKey": "",
        "matchField": "",
        "searchParams": "",
        "iisVirtual": 0,
        "iisQuery": 0,
        "iisHQuery": 0,
        "fixed": null,
        "iisSummary": 0,
        "iisReadOnly": 0,
        "iisMoney": 0,
        "iisSortable": 0,

      },

    ],
    
  }
}
// 默认菜单 权限按钮
export const defaultmenubuttonSchema = {
  type: 'defaultmenubutton',
  icon: 'alert',
  formItemFlag: false,
  publicSubAttribute:[
    {key:"searchKeyInput",value:"value",text:"关键字"}
  ],// 动态的 对外公布的属性字段
  options: {
    name: '',
    cleanMarginTop10PX:false,// 非第一个容器
    triggerCtrlNames:[],// { label: '', value: "", check: true, canRemove: false }触发控件的名称（作用于什么控件）可同时触发多个控件
    // 参数标识，不做其它用途【暂时】
    defaultmenubutton: true,
    mainTableName:"mainTableRef",// 主表名称Ref
    // 权限按钮配置
    menuButtonOptionItems: [
      { label: '新增',onSubmit: '', fieldName: 'iisAdd',value: "iisAdd", size:"mini",type:"primary",icon:"vxe-icon-add",disabled:false, check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '编辑',onSubmit: '',  fieldName: 'iisEdit',value: "iisEdit",size:"mini",type:"success",icon:"vxe-icon-edit",disabled:false,  check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '删除',onSubmit: '',  fieldName: 'iisDel',value: "iisDel", size:"mini",type:"danger",icon:"vxe-icon-delete",disabled:false,  check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '启用',onSubmit: '',  fieldName: 'iisEnable',value: "iisEnable", size:"mini",type:"success",icon:"el-icon-unlock",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '禁用',onSubmit: '',  fieldName: 'iisDisabled',value: "iisDisabled",size:"mini",type:"danger",icon:"el-icon-lock",disabled:false,  check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '导入',onSubmit: '',  fieldName: 'iisImport',value: "iisImport", size:"mini",type:"primary",icon:"vxe-icon-upload",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '预览',onSubmit: '',  fieldName: 'iisView',value: "iisView", size:"mini",type:"primary",icon:"vxe-icon-eye-fill",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '导出',onSubmit: '',  fieldName: 'iisExport',value: "iisExport", size:"mini",type:"primary",icon:"vxe-icon-download",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '刷新',onSubmit: '',  fieldName: 'iisRefresh',value: "iisRefresh", size:"mini",type:"success",icon:"vxe-icon-refresh",disabled:false, check: false, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
    ],
    // 搜索输入框配置
    searchInputItems: [
      { label: '关键字', fieldName: 'searchKeyInput', ctrlWidth:"130",defaultValue:"",searchItemConfig:{}, controlType: 'input', placeholder: '关键字', disabled:false, check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
    ],
    // 搜索按钮配置
    searchBttonItems: [
      { label: '查询',fieldName: 'iisSearch',isLoadding:false, value: "iisSearch",size:"small",type:"primary",icon:"el-icon-search",disabled:true, check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] },
      { label: '重置',fieldName: 'iisReset',isLoadding:false,  value: "iisReset",size:"small",type:"primary",icon:"el-icon-refresh-right",disabled:true, check: true, canRemove: false, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] }
    ],
   
    // 成功或失败后执行的操作
    // afterSuccessOrErrorEvents:[
    //   // {label: '成功后1',type:'', value: "afterSuccess",check:true,canRemove:false,otherParams:{},actionParams:{}}
    // ]
  }

}

export const cardSchema = {
  type: 'card',
  category: 'container',
  icon: 'card',
  widgetList: [],
  options: {
    name: '',
    label: 'card',
    hidden: false,
    folded: false,
    showFold: false,
    cardWidth: '100%',
    shadow: 'never',
    customClass: '',
    cardSetting:"",
    cardEventlist:"",
  }
}

export const alertSchema = {
  type: 'alert',
  icon: 'alert',
  formItemFlag: false,
  options: {
    name: '',
    title: 'Good things are coming...',
    type: 'info',
    description: '',
    closable: true,
    closeText: '',
    center: true,
    showIcon: false,
    effect: 'light',
    hidden: false,
    onClose: '',
    customClass: '',
  }
}

// export const DIVContainerSchema = {
//   type: 'DIVContainer',
//   icon: 'DIVContainer',
//   formItemFlag: false,
//   options: {
//     name: '',
//     title: 'DIVContainer',
//   }
// }
