<template>
    <div>
        <template v-if="displayType=='date'">
            <!-- date -->
            {{ formatDate }}
        </template>
        <template v-else-if="displayType=='datetime'">
            <!-- datetime -->
            {{formatDateTime }}
        </template>
        <template v-else-if="displayType=='image'">
            <!-- image -->
            <!-- {{ mainItem[fieldName] }} -->
            <div class="flex">
                <div v-for="(item,index) in  mainItem[fieldName]" style="margin:5px;" >
                    <el-image 
                        :key="index"
                        style="width: 50px; height: 50px"
                        :src="item" 
                        :preview-src-list="mainItem[fieldName]">
                    </el-image>
            </div>
            </div>
           
        </template>
        <template v-else>
            <!-- text -->
          <span class="flex" v-text=" mainItem[fieldName]"></span>
        </template>
    </div>
</template>
<script>
//
import dayjs from 'dayjs'
export default {
    name:"descriptionsFieldNameItem",
    props:{
        // 子对象，字段配置信息
        subItem:{
            type:Object,
            default(){
                return {}
            }
        },
        // 显示字段
        fieldName:{
            type:String,
            default:""
        },
        // 显示类型：目前支持：text,image,date,datetime
        displayType:{
            type:String,
            default:""
        },
        // 主对象，数据对象
        mainItem:{
            type:Object,
            default(){
                return {}
            }
        }
    },
    data(){
        return {

        }
    },
    computed:{
        formatDate(){
            if(!!this.mainItem[this.fieldName]){
                return dayjs(this.mainItem[this.fieldName]).format('YYYY-MM-DD');
            }else{
                return ""
            }
           
        },
        formatDateTime(){
            if(!!this.mainItem[this.fieldName]){
                return dayjs(this.mainItem[this.fieldName]).format('YYYY-MM-DD HH:mm:ss');
            }else{
                return ""
            }
           
        },
    },
    methods:{

    }
}
</script>