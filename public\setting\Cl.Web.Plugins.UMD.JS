!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("VueWebpackUmd",[],t):"object"==typeof exports?exports.VueWebpackUmd=t():e.VueWebpackUmd=t()}(self,(()=>(()=>{"use strict";var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{Button:()=>i,Input:()=>s,default:()=>c,install:()=>p});var n=function(){var e=this,t=e.$createElement;return(e._self._c||t)("button",[e._v(e._s(e.label))])};function o(e,t,n,o,r,i,l,a){var s,u="function"==typeof e?e.options:e;if(t&&(u.render=t,u.staticRenderFns=n,u._compiled=!0),o&&(u.functional=!0),i&&(u._scopeId="data-v-"+i),l?(s=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(l)},u._ssrRegister=s):r&&(s=a?function(){r.call(this,(u.functional?this.parent:this).$root.$options.shadowRoot)}:r),s)if(u.functional){u._injectStyles=s;var p=u.render;u.render=function(e,t){return s.call(t),p(e,t)}}else{var c=u.beforeCreate;u.beforeCreate=c?[].concat(c,s):[s]}return{exports:e,options:u}}n._withStripped=!0;var r=o({props:{label:{type:String,default:"Click me"}}},n,[],!1,null,null,null);r.options.__file="src/components/Button/index.vue";const i=r.exports;var l=function(){var e=this,t=e.$createElement;return(e._self._c||t)("input",{directives:[{name:"model",rawName:"v-model",value:e.value,expression:"value"}],attrs:{placeholder:e.placeholder},domProps:{value:e.value},on:{input:function(t){t.target.composing||(e.value=t.target.value)}}})};l._withStripped=!0;var a=o({props:{placeholder:{type:String,default:"Enter text"},value:{type:String,default:""}}},l,[],!1,null,null,null);a.options.__file="src/components/Input/index.vue";const s=a.exports;var u={Button:i,Input:s},p=function(e){Object.keys(u).forEach((function(t){e.component(t,u[t])}))};"undefined"!=typeof window&&window.Vue&&p(window.Vue);const c={install:p};return t})()));