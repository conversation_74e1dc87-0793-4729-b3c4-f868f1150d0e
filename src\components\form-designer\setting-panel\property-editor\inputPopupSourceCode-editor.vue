<template>
  <div>
    <el-form-item label-width="0">
            <commonParamsOnChange contrlType="select" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label="标准弹框编码">
      <div class="flex">
        <el-input  type="text" v-model="optionModel.inputPopupSourceCode"></el-input>
        <el-button style="margin-left:5px" size="mini" @click="selectSourceCodeEvent()" type="text">选择</el-button>
      </div>
     
      <vxe-modal title="标准弹框选择" :position="{ top: 50 }" v-model="showPopupModal" width="1000" show-footer>
      <template #default>
        <vxe-grid @cell-dblclick="cellDblclickEvent" ref="inputPopupGridRef" v-bind="gridOptions">
          <template #CIS_PAGE_default="{ row }">
            <el-checkbox disabled true-label="Y" false-label="N" v-model="row.CIS_PAGE"></el-checkbox>
          </template>
          <template #CIS_MULTI_default="{ row }">
            <el-checkbox disabled true-label="Y" false-label="N" v-model="row.CIS_MULTI"></el-checkbox>
          </template>
          <template #CSTATE_default="{ row }">
            <el-switch
              active-value="A"
              inactive-value="D"
              v-model="row.CSTATE"
              disabled>
            </el-switch>
          </template>
          <template #top>
            <div style="margin-bottom:5px" class="flex">
              <el-input clearable @keyup.native="keyupEvent" size="mini" v-model="searchkey"
                    placeholder="请输入编码"></el-input>
                    <el-input clearable style="margin-left:10px" @keyup.native="keyupEvent" size="mini" v-model="searchkeyName"
                    placeholder="请输入名称"></el-input>
                    <el-button style="margin-left:10px" size="mini" :loading="loadingBtn" @click="searchEvent()" type="primary">查询</el-button>
            </div>
                
            </template>
          <template #pager>
            <vxe-pager v-show="showPager" size="mini" :current-page.sync="tablePage.currentPage"
              :page-size.sync="tablePage.pageSize" :total="tablePage.total" @page-change="handlePageChange">
            </vxe-pager>
          </template>
        </vxe-grid>
      </template>
      <template v-slot:footer>
        <el-button size="medium" @click="cancelBtn()">取消</el-button>
        <el-button size="medium" :loading="loadingBtn" @click="preSubmitEvent()" type="primary">确定</el-button>
      </template>
    </vxe-modal>
  </el-form-item>
  <!-- <el-form-item label="字段转换">
    <div class="flex">
        <el-input  type="text" v-model="optionModel.matchField"></el-input>
      </div>
  </el-form-item> -->
  </div>
 
</template>

<script>
import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
  import request from '@/libs/request'
  export default {
    name: "inputPopupSourceCode-editor",
    components:{commonParamsOnChange},
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data(){
      return {
        loadingBtn:false,
        searchkey:"",
        searchkeyName:"",
        showPopupModal:false,
        showPager: true,// 是否显示分页
        tablePage: {
          total: 0,
          currentPage: 1,
          pageSize: 10
        },
         //表格属性信息
        gridOptions: {
          border: true,
          loading:false,
          resizable: true,
          showOverflow: true,
          rowConfig:{
            isCurrent:true //当鼠标点击行时，是否要高亮当前行
          },
          height: 400,
          align: 'left',
          columns: [
            {
            type: "seq",
            fixed: "left",
            title: "序号",
            align: 'center',
            headerAlign: 'center',
            width: 60,
          },
       
          {
            field: "CPOPUP_CODE",
            title: "编码",
            headerAlign: 'center',
            align: 'left',
            width: 200,
            iisQuery: 1,
          },
         
          {
            field: "CPOPUP_NAME",
            title: "名称",
            align: 'left',
            width: 200,
            headerAlign: 'center',
            iisQuery: 1,
          },
          {
            field: "CIS_PAGE",
            title: "分页",
            width: 60,
            align: 'center',
            controlType:"statusColumnYN",
            headerAlign: 'center',
            slots: {
                    // 使用插槽模板渲染
                    default: 'CIS_PAGE_default',
                  }
          },
          {
            field: "CIS_MULTI",
            title: "多选",
            width: 60,
            controlType:"statusColumnYN",
            align: 'center',
            headerAlign: 'center',
            slots: {
                    // 使用插槽模板渲染
                    default: 'CIS_MULTI_default',
                  }
          },
          {
            field: "CRETURN_VALUE",
            title: "返回值字段",
            width: 100,
            align: 'left',
            headerAlign: 'center',
          },
          {
            field: "CRETURN_TEXT",
            title: "返回文本字段",
            align: 'left',
            headerAlign: 'center',
          },
          // {
          //   field: "CDATA_SOURCE_CODE",
          //   title: "数据源编码",
          //   align: 'left',
          //   headerAlign: 'center',
          // },
          // {
          //   field: "CDATA_SOURCE_NAME",
          //   title: "数据源名称",
          //   align: 'left',
          //   headerAlign: 'center',
          // },
          // {
          //   field: "CDATA_SOURCE_CONTENT",
          //   title: "数据源",
          //   headerAlign: 'center',
          // },
          {
            field: "CSTATE",
            title: "状态",
            align: 'left',
            width: 80,
            controlType:"statusColumnAD",
            headerAlign: 'center',
            align: 'center',
            slots: {
                    // 使用插槽模板渲染
                    default: 'CSTATE_default',
                  }
          },
          ],
          data: [
    
          ],
        }
      }
    },
    methods:{
      cellDblclickEvent(tableInfo){
        let row = tableInfo.row
        this.optionModel.inputPopupSourceCode = row["CPOPUP_CODE"]
        this.showPopupModal = false
      },
    
       // 查询表格数据
    async loadPopupTableData() {
      this.gridOptions.loading = true
      let _url = `api/SYS/Popup/GetAll`//`api/MD/DataSet/GetListByDataSetId` // 固定取值地址
      let params = {
          "search":{
              "CPOPUP_CODE":this.searchkey,
              "CPOPUP_NAME":this.searchkeyName
          },
          "PageIndex": this.tablePage.currentPage,
          "PageSize":this.tablePage.pageSize
      }
      // {
      //   condition:this.searchkey
      // }
      // params.Parameter["start"] = this.tablePage.currentPage
      // params.Parameter["length"] = this.tablePage.pageSize
      let dataList = []
      await request["post"](_url, params).then(res => {
        
        if ([4, 5].includes(res.Data)) {
          res.Datas = JSON.parse(res.Datas)
        }
        if (res && res.Datas) {
          dataList = res.Datas
        }
        if (this.showPager) {
            //  是否分页
            this.tablePage.total = res.TotalRows
        }
      });
      this.gridOptions.loading = false
      this.gridOptions.data = dataList
      //return dataList
    },
       // 分页触发事件
    handlePageChange(val) {
      this.tablePage.currentPage = val
      this.loadPopupTableData()
    },
      searchEvent(){
        this.loadPopupTableData()
      },
      // 显示 标准弹框
      selectSourceCodeEvent(){
        this.showPopupModal = true
        setTimeout(()=>{
          this.loadPopupTableData()
        },300)
      },
      cancelBtn(){
        this.showPopupModal = true
        this.gridOptions.loading = false
      },
        // 获取选择数据
      getSelectedData(){
        let _dataList =[]
        let inputPopupGridRef = this.$refs["inputPopupGridRef"]
        if(inputPopupGridRef){
            // 用于 row-config.isCurrent，获取高亮的当前行数据
            _dataList = [inputPopupGridRef.getCurrentRecord()]
          }
        return _dataList
      },
      preSubmitEvent() {
        let _self = this
        this.loadingBtn = true
        let postData = this.getSelectedData()
        if(postData && postData.length==0){
          this.$message.error("请先选择数据！！")
          _self.loadingBtn = false
          return
        }
        this.optionModel.inputPopupSourceCode = postData[0]["CPOPUP_CODE"]

        setTimeout(() => {
          _self.loadingBtn = false
          _self.showPopupModal = false
        }, 1000)
    }
    }
  }
</script>

<style scoped>

</style>
