<template>
    <div class="option-items-pane">

        <draggable tag="ul" :list="optionModel.xyDataZoom"
            v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
            <li v-for="(option, idx) in optionModel.xyDataZoom" :key="idx">
                <el-checkbox v-model="option.show">
                    <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.remark" size="mini"
                        style="width: 150px"></el-input>
                    <i class="iconfont icon-drag drag-option"></i>
                    <el-button circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
                        icon="el-icon-minus" class="col-delete-button"></el-button>
                </el-checkbox>
            </li>
        </draggable>



        <div>
            <el-button type="text" @click="addQueryOption">+添加Y轴</el-button>
        </div>

        <el-dialog v-dialog-drag title="编辑 区域缩放" :visible.sync="showEditOptionDialogFlag" v-if="showEditOptionDialogFlag"
            :show-close="true" class="small-padding-dialog" append-to-body :close-on-click-modal="false"
            :close-on-press-escape="false" :destroy-on-close="true">
            <el-form :model="editItemForm" :rules="editItemFormRules" ref="editItemForm" label-width="160px">
                <el-form-item label="备注" prop="remark">
                    <el-input style="width:409px" v-model="editItemForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="过滤轴">
                    <el-select v-model="editItemForm.AxisName">
                        <el-option label="X轴" value="xAxisIndex"></el-option>
                        <el-option label="Y轴" value="yAxisIndex"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="过滤轴下标">
                    <el-select v-model="editItemForm.AxisIndex">
                        <el-option label="0" :value="0"></el-option>
                        <el-option label="1" :value="1"></el-option>
                        <el-option label="2" :value="2"></el-option>
                        <el-option label="3" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="过滤模式">
                    <el-select v-model="editItemForm.filterMode">
                        <el-option label="filter" value="filter"></el-option>
                        <el-option label="empty" value="empty"></el-option>
                        <el-option label="weakFilter" value="weakFilter"></el-option>
                        <el-option label="none" value="none"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="过滤组件类型">
                    <el-select v-model="editItemForm.type">
                        <el-option label="slider" value="slider"></el-option>
                        <el-option label="inside" value="inside"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <span slot="label">dataZoom范围类型
                    <el-tooltip effect="light" content="默认 显示全部dataZoom，如果选择固定，则是不管数据多少，默认显示几组数据【适用于动态数据】；如果自定义则 自由安排范围【适用于固定数据】">
                        <i class="el-icon-info"></i></el-tooltip>
                    </span>
                    <el-select v-model="editItemForm.rangeType">
                        <el-option label="请选择" value=""></el-option>
                        <el-option label="固定" value="hardRange"></el-option>
                        <el-option label="自定义范围" value="customRange"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="editItemForm.rangeType=='hardRange'" label="固定显示组">
                    <el-input-number v-model="editItemForm.rangeTypeValue" :min="1"  label="偏移量start"></el-input-number>
                </el-form-item>
                <el-form-item v-show="editItemForm.rangeType=='customRange'" label="偏移量start">
                    <el-input-number v-model="editItemForm.start" :min="0" :max="100"  label="偏移量start"></el-input-number>
                </el-form-item>
                <el-form-item v-show="editItemForm.rangeType=='customRange'" label="偏移量end">
                    <el-input-number v-model="editItemForm.end" :min="0" :max="100"  label="偏移量end"></el-input-number>
                </el-form-item>
                  <el-form-item label-width="0">
                    <el-divider class="custom-divider">区域缩放偏移量</el-divider>
                    </el-form-item>
                    <el-form-item  label="偏移top"> 
                    <el-input type="text" v-model="editItemForm.top"></el-input>
                    </el-form-item>
                    <el-form-item  label="偏移right">
                    <el-input type="text" v-model="editItemForm.right"></el-input>
                    </el-form-item>
                    <el-form-item  label="偏移bottom">
                    <el-input type="text" v-model="editItemForm.bottom"></el-input>
                    </el-form-item>
                    <el-form-item  label="偏移left">
                    <el-input type="text" v-model="editItemForm.left"></el-input>
                    </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm')
                }}</el-button>
                <el-button size="large" type="" @click="showEditOptionDialogFlag = false">{{ i18nt('designer.hint.cancel')
                }}</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
const default_editItemForm = {
    remark: '新缩放item',
    AxisName:"xAxisIndex",
    filterMode:"filter",
    type:"slider",
    AxisIndex:0,
    rangeType:'hardRange',
    rangeTypeValue:8,
    start:0,
    end:100,
    show:true,
    top:"auto", right:"auto", bottom:"auto", left:"auto",
}

// import cloneDeep from "clone-deep" 
// import { defaultsDeep } from "lodash-es";
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n";

export default {
    name: "echartsYAxisItems", // 其它Y轴列表
    mixins: [i18n],
    components: {
        Draggable,
    },
    props: {
        designer: Object,
        selectedWidget: Object,
        globalDsv: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            predefineColors: [
                '#E3B76D',
                '#6794A7',
                '#014D64',
                '#01A2D9',
                '#C6D3DF',
                '#7AD2F6',
                '#7C260B',
                '#00887D',
                '#EE8F71',
                '#ADADAD',
                '#76C0C1',
                '#ff4500',
                '#ff8c00',
                '#ffd700',
                '#90ee90',
                '#00ced1',
                '#1e90ff',
                '#c71585',
                '#c7158577'

            ],
            showEditOptionDialogFlag: false,
            currentEditOption: {},// 当前编辑菜单按钮
            editItemForm: Object.assign({}, default_editItemForm),
            editItemFormRules: {
                remarkName: [
                    { required: true, message: '必填', trigger: 'blur' },
                ],
           
            }
        }
    },
    computed: {
        optionModel() {
            return this.selectedWidget.options
        },

    },
    methods: {


        // 提交修改按钮菜单属性
        submitEditMenu() {
           
            this.showEditOptionDialogFlag = false
        
            this.currentEditOption.remark = this.editItemForm.remark
            this.currentEditOption.AxisName = this.editItemForm.AxisName
            this.currentEditOption.type = this.editItemForm.type
            this.currentEditOption.filterMode = this.editItemForm.filterMode
            this.currentEditOption.AxisIndex = this.editItemForm.AxisIndex
            this.currentEditOption.rangeType = this.editItemForm.rangeType
            this.currentEditOption.rangeTypeValue = this.editItemForm.rangeTypeValue
        
            this.currentEditOption.start = this.editItemForm.start
            this.currentEditOption.end = this.editItemForm.end
            this.currentEditOption.show = this.editItemForm.show

            this.currentEditOption.top = this.editItemForm.top
            this.currentEditOption.right = this.editItemForm.right
            this.currentEditOption.bottom = this.editItemForm.bottom
            this.currentEditOption.left = this.editItemForm.left
        },
        // 弹框编辑菜单属性--- 初始化弹框属性参数
        showEditDialogEvent(option) {
 
            this.currentEditOption = option // 当前菜单属性
            this.showEditOptionDialogFlag = true

            this.editItemForm.remark = option.remark
            this.editItemForm.AxisName = option.AxisName
            this.editItemForm.type = option.type
            this.editItemForm.filterMode = option.filterMode
            this.editItemForm.AxisIndex = option.AxisIndex
            this.editItemForm.rangeType = option.rangeType
            this.editItemForm.rangeTypeValue = option.rangeTypeValue
           
            this.editItemForm.start =option.start
            this.editItemForm.end =option.end
            this.editItemForm.show =option.show

            this.editItemForm.top =option.top
            this.editItemForm.right =option.right
            this.editItemForm.bottom =option.bottom
            this.editItemForm.left =option.left
           
        },
        deleteOption(option, index) {
            this.selectedWidget.options.xyDataZoom.splice(index, 1)
        },
        // 添加
        addQueryOption() {
            if (!this.selectedWidget.options.hasOwnProperty("xyDataZoom")) {
                this.$set(this.selectedWidget.options, "xyDataZoom", [])
            }
            let newValue = this.selectedWidget.options.xyDataZoom.length + 1
            
            this.selectedWidget.options.xyDataZoom.push(
                { remark: '新缩放item' + newValue, rangeTypeValue:8,rangeType:'hardRange', AxisName: "xAxisIndex", type: 'slider', filterMode: 'filter', AxisIndex: 0, start: 0, end: 100, show:true,top:"auto", right:"auto", bottom:"auto", left:"auto", }
            )
        },




    }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
}

li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
}

.drag-option {
    cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
}

.dialog-footer .el-button {
    width: 100px;

}
</style>
