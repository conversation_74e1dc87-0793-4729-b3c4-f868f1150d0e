<template>
    <div>
      <el-form-item label-width="0">
            <commonParamsOnChange contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
        <commonSettingEventDialog contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonSettingEventDialog>
    </el-form-item>
    </div>
  </template>
  
  <script>
  import commonSettingEventDialog from '@/components/form-designer/setting-panel/commonSettingEventDialog.vue'
  import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
    import i18n from "@/utils/i18n"
    // import Draggable from 'vuedraggable'
    // import {deepClone} from "@/utils/util";
  
    export default {
      name: "cardEventlist-editor",
      componentName: 'PropertyEditor',
      mixins: [i18n],
      components: {
        // Draggable,
        commonSettingEventDialog,
        commonParamsOnChange,
      },
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
      data() {
        return {
       
        }
      },
      created() {
      
      },
      methods: {
       
  
      }
    }
  </script>

  <style lang="scss" scoped>
  
  
  </style>
  