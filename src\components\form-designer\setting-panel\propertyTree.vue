<template>
    <div>
      <!-- default-expand-all -->
        <el-tree ref="nodeTree" :data="nodeTreeData" node-key="id" :expand-on-click-node="false" highlight-current class="node-tree"
               icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
    </div>
</template>
<script>
/*
 * 属性组件树
 * add by andy
 * 用于快速查看当前表单、列表使用了那些控件和嵌套组件 
 * 2022-12-08
 */
import i18n from '@/utils/i18n'
import {
    // deepClone,
    // copyToClipboard,
    // generateId,
    // getQueryParam,
    // addWindowResizeHandler,
    traverseAllWidgets, 
  } from "@/utils/util"
export default {
    name:"propertyTree",
    mixins: [i18n],
    props: {
      designer: Object,
      globalDsv: {
        type: Object,
        default: () => ({})
      },
    },
    data(){
        return {
            nodeTreeData: [],
        }
    },
    computed:{
      containerDragEndState() {
        return this.$store.state.containerDragEndChange
      },
    
    },
    watch:{
      containerDragEndState: {
        handler(val) {
          console.log("====containerDragEndChange===== andy")
          // 刷新属性列表
          this.showNodeTreeDrawer()
        },
        deep:true
      },
      // 'designer.widgetList': {
      //   deep: true,
      //   handler(val) {
      //  // 刷新属性列表
      //     this.showNodeTreeDrawer()
      //   }
      // },
    },
    mounted(){
        //debugger
        this.showNodeTreeDrawer()
    },
    methods:{
      refreshNodeTree() {
        //debugger
            this.nodeTreeData =[]
            this.nodeTreeData.length = 0
            this.designer.widgetList.forEach(wItem => {
              this.buildTreeNodeOfWidget(wItem, this.nodeTreeData)
            })
            this.designer.nodeTreeData = this.nodeTreeData
      },
      buildTreeNodeOfWidget(widget, treeNode) {
        //debugger
        let curNode = {
          id: widget.id,
          label: widget.options.name || (widget.options.label || widget.type),
          text:widget.options.label || widget.type, // NEW ADD BY ANDY
          widget:widget, // NEW ADD BY ANDY
          //selectable: true,
        }
        // 固定属性 // NEW ADD BY ANDY
        if(!!widget.publicAttribute){
          curNode.publicAttribute = widget.publicAttribute
        }
        // 动态属性 // NEW ADD BY ANDY
        if(!!widget.publicSubAttribute){
          curNode.publicSubAttribute = widget.publicSubAttribute
        }
        treeNode.push(curNode)

        if (widget.category === undefined) {
          return
        }

        curNode.children = []
        if (widget.type === 'grid') {
          widget.cols.map(col => {
            let colNode = {
              id: col.id,
              label: col.options.name || widget.type,
              children: []
            }
            curNode.children.push(colNode)
            col.widgetList.map(wChild => {
              this.buildTreeNodeOfWidget(wChild, colNode.children)
            })
          })
        } else if (widget.type === 'table') {
          //TODO: 需要考虑合并单元格！！
          widget.rows.map(row => {
            let rowNode = {
              id: row.id,
              label: 'table-row',
              selectable: false,
              children: [],
            }
            curNode.children.push(rowNode)

            row.cols.map(cell => {
              if (!!cell.merged) {  //跳过合并单元格！！
                return
              }

              let rowChildren = rowNode.children
              let cellNode = {
                id: cell.id,
                label: 'table-cell',
                children: []
              }
              rowChildren.push(cellNode)

              cell.widgetList.map(wChild => {
                this.buildTreeNodeOfWidget(wChild, cellNode.children)
              })
            })
          })
        }  else if (widget.type === 'splitpanes') {
          widget.panes.map(pane => {
            let paneNode = {
              id: pane.id,
              label: pane.options.name || widget.type,
              selectable: false,
              children: []
            }
            curNode.children.push(paneNode)
            pane.widgetList.map(wChild => {
              this.buildTreeNodeOfWidget(wChild, paneNode.children)
            })
          })
        }
        else if (widget.type === 'tab') {
          widget.tabs.map(tab => {
            let tabNode = {
              id: tab.id,
              label: tab.options.name || widget.type,
              selectable: false,
              children: []
            }
            curNode.children.push(tabNode)
            tab.widgetList.map(wChild => {
              this.buildTreeNodeOfWidget(wChild, tabNode.children)
            })
          })
        } else if (widget.type === 'sub-form') {
          widget.widgetList.map(wChild => {
            this.buildTreeNodeOfWidget(wChild, curNode.children)
          })
        } else if (widget.category === 'container') {  //自定义容器
          widget.widgetList.map(wChild => {
            this.buildTreeNodeOfWidget(wChild, curNode.children)
          })
        }
      },
      showNodeTreeDrawer() {
       
       // this.showNodeTreeDrawerFlag = true
        this.$nextTick(() => {
          if (!!this.designer.selectedId) {  //同步当前选中组件到节点树！！！
            this.$refs.nodeTree.setCurrentKey(this.designer.selectedId)
          }
          this.refreshNodeTree()
        })
      },
      findWidgetById(wId) {
        let foundW = null
        traverseAllWidgets(this.designer.widgetList, (w) => {
          if (w.id === wId) {
            foundW = w
          }
        })

        return foundW
      },

      onNodeTreeClick(nodeData, node, nodeEl) {
        if ((nodeData.selectable !== undefined) && !nodeData.selectable) {
          this.$message.info(this.i18nt('designer.hint.currentNodeCannotBeSelected'))
        } else {
          const selectedId = nodeData.id
          const foundW = this.findWidgetById(selectedId)
          if (!!foundW) {
            this.designer.setSelected(foundW)
          }
        }
      },
    }
}
</script>
<style lang="scss" scoped>
.node-tree ::v-deep {
    .el-tree > .el-tree-node:after {
      border-top: none;
    }
    .el-tree-node {
      position: relative;
      padding-left: 12px;
    }

    .el-tree-node__content {
      padding-left: 0 !important;
    }

    .el-tree-node__expand-icon.is-leaf{
      display: none;
    }
    .el-tree-node__children {
      padding-left: 12px;
      overflow: visible !important; /* 加入此行让el-tree宽度自动撑开，超出宽度el-draw自动出现水平滚动条！ */
    }

    .el-tree-node :last-child:before {
      height: 38px;
    }

    .el-tree > .el-tree-node:before {
      border-left: none;
    }

    .el-tree > .el-tree-node:after {
      border-top: none;
    }

    .el-tree-node:before {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }

    .el-tree-node:after {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }

    .el-tree-node:before {
      border-left: 1px dashed #4386c6;
      bottom: 0px;
      height: 100%;
      top: -10px;
      width: 1px;
    }

    .el-tree-node:after {
      border-top: 1px dashed #4386c6;
      height: 20px;
      top: 12px;
      width: 16px;
    }

    .el-tree-node.is-current > .el-tree-node__content {
      background: #c2d6ea !important;
    }

    .el-tree-node__expand-icon {
      margin-left: -3px;
      padding: 6px 6px 6px 0px;
      font-size: 16px;
    }

  }
</style>