<template>
    <static-content-wrapper :designer="designer" :field="field" :design-state="designState"
                            :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                            :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div  class="flex justify-center">
        <div style="border-radius: 5px; margin-top:5px;margin-bottom:5px;padding: 5px;width:99%;overflow: hidden;" :style="[{border:!!field.options.showBorderStyle?'1px solid #EBEEF5':'', height:getContentBoxHeight()+`px`}]" class="flex items-center justify-evenly">
          
          <div   v-for="(item,index) in iconInfoItemsList" class="flex shrink">
            <div class="flex items-center justify-center" style="width:88px;">
              <div  v-if="index !=0 && !!field.options.showSplitStyle"  style=" height: 80%; width: 2px; border-left:2px solid #eee; border-right:2px solid #eee;margin-right:10px;"></div>
              <i :style="[{color:item.iconColor,'font-size': (!!item.iconSize?item.iconSize:60)+'px'}]" :class="[!!item.iconUrl ? item.iconUrl : 'el-icon-present']"></i>
            </div>
            <div style="margin-left:10px;" class="flex flex-col h-full">
                <div style="color:#262626" v-text="item.title"></div>
                <div style="font-size:16px;font-weight:bolder;color:#5E95FF" v-text="getDataByFieldName(item.totalField)"></div>
                <div style="color:#595959"><span v-text="item.label"></span><span v-text="getDataByFieldName(item.upOrDownField)"></span>
                  <template v-if="getDataByFieldName(item.upOrDownFlag)==0">
                    <i style="color:#EE3B3B;margin-left:5px;" class="vxe-icon-caret-down"></i>
                  </template>
                 <template v-else>
                    <i style="color:#3DBA84;margin-left:5px;" class="vxe-icon-caret-up"></i>
                 </template>
                </div>
            </div>
    
          </div>

     </div>
    </div>
   
    </static-content-wrapper>
  </template>
  
  <script>
  import { useFormatParams } from "@/hooks/useFormatParams"
    import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
    import emitter from '@/utils/emitter'
    import i18n from "@/utils/i18n"
    import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
    const XEUtils = require('xe-utils') // vxeTable 通用库
    export const commonFormat = {
        qffFormat(cellValue){
            return XEUtils.commafy(XEUtils.toString(cellValue), { spaceNumber: 3, separator: ',' })
        },
        wffFormat(cellValue){
            return XEUtils.commafy(XEUtils.toString(cellValue), { spaceNumber: 4, separator: ',' })
        },
        decimalDigitsFormat (cellValue, digits) {
            // 四舍五入金额，每隔3位逗号分隔，默认2位数
            return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
        }
  }
    export default {
      name: "iconInfo-widget",
      componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
      mixins: [emitter, fieldMixin, i18n],
      inject: ['getPageInstance', 'sourceVFormRenderState'],
      components: {
        StaticContentWrapper,
      },
      props: {
        field: Object,
        parentWidget: Object,
        parentList: Array,
        indexOfParentList: Number,
        designer: Object,
  
        designState: {
          type: Boolean,
          default: false
        },
        previewState: { //是否表单预览状态
        type: Boolean,
        default: false
      },
       // 控件来源父集 add by andy
       contentBoxHeight: {
        type: [Number, String],
        default: 0
      },
            // 控件来源父集 add by andy
        subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
          type: String,
          default: ''
        },
  
      },
      data(){
        return {
          dataObj:{},
          useFormatParamsFn: useFormatParams(this),
        }
      },
     computed:{
          pageInstance() {
                // 获取列表示例
                return this.getPageInstance()
        },
         iconInfoItemsList(){
          let _list = this.field.options.iconInfoItems.filter(item=>{
            //debugger
             if(!!item.check){
              return item
             }
          })
          return _list
         }
          
      },
     mounted(){
      let _self = this
      this.$nextTick(() => {
          setTimeout(()=>{
            _self.tryTillGetData()
          },300)
        })
     },
      
      created() {
        this.registerToRefList()
        this.initEventHandler()
      },
      beforeDestroy() {
        this.unregisterFromRefList()
      },
      methods: {
        getDataByFieldName(fieldName=''){
             if( this.dataObj && Object.keys( this.dataObj).length>0){
                if(this.dataObj.hasOwnProperty(fieldName)){
                  let formatValue = commonFormat.decimalDigitsFormat( this.dataObj[fieldName], 2)
                  return formatValue
                }else{
                  //let formatValue = commonFormat.decimalDigitsFormat( 123, 2)
                  return fieldName
                }
             } 
        },
      // 循环直到获取到数据
      async tryTillGetData() {
          // 干正事
          let _dataList= await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
          if(_dataList && _dataList.length>0){
            this.dataObj  = _dataList[0]
          }
        },
         // 获取内容高
      getContentBoxHeight() {
       // debugger
        let testHeight = 70
        if (this.designState) {
            // 预览状态下，固定高度分配
            return testHeight
        }else{
            return this.contentBoxHeight - 10
        }
      },
  
      }
    }
  </script>
  
  <style lang="scss" scoped>
   .test{
    color: #4E7BFA;
   }
  </style>
  