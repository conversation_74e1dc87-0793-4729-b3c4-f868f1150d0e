<template>
  <div class="option-items-pane">


    <draggable tag="ul" :list="optionModel.searchInputItems"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in optionModel.searchInputItems" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input :placeholder="option.placeholder" @click.native="showEditDialogEvent(option)" readonly
            v-model="option.label" size="mini" style="width: 200px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>



    <div>
      <el-button type="text" @click="addOption">+添加控件</el-button>
    </div>

    <el-dialog v-dialog-drag title="搜索框控件配置 编辑" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input style="width:330px" v-model="editMenuForm.label"></el-input>
        </el-form-item>
        <el-form-item label="唯一名称" prop="fieldName">
          <el-input style="width:330px" disabled v-model="editMenuForm.fieldName"></el-input>
        </el-form-item>
        <el-form-item label="占位符" prop="placeholder">
          <el-input style="width:330px" v-model="editMenuForm.placeholder"></el-input>
        </el-form-item>
        <el-form-item label="">
          <span slot="label">控件宽度
            <el-tooltip effect="light" content="查询条件过多时，可以适当调整宽度，比如结束时间设置‘-’，节省长度">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-input-number style="width:330px" v-model="editMenuForm.ctrlWidth" :min="0" label="控件宽度"></el-input-number>
        </el-form-item>
        <el-form-item label="控件默认值">
          <span slot="label">控件默认值
          <el-tooltip effect="light" content="如时间默认值 当前日期:curdate 当前日期-1月:subtract-1-month 当前日期+1天:add-1-day 当前日期+2年:add-2-year,具体时间点设置：subtract-1-month-08:30:03，时间范围默认格式：curdate#curdate，subtract-1-month#subtract-1-month，add-1-day#add-1-day，#作为分割符号
NEW:1.$startOf-month（当前月第一天）,2.$startOf-month-add-1-day（当前月加一天） 3.$endOf-month-subtract-1-day（当前月最后一天减一天）,4.$endOf-month（当前月最后一天），必须为$符号开头，加减数量可以自定义（add为加，subtract为减）
                                              ">
            <i class="el-icon-info"></i></el-tooltip></span>
          <el-input style="width:330px" v-model="editMenuForm.defaultValue"></el-input>
        </el-form-item>
        <el-form-item label-width="130px" label="">
          <span slot="label">改变时立刻触发
          <el-tooltip effect="light" content="查询条件，修改后是否立即执行查询，无需点击查询按钮">
            <i class="el-icon-info"></i></el-tooltip></span>
          <el-checkbox v-model="editMenuForm.execWhenChange"></el-checkbox>
        </el-form-item>
        <el-form-item label="" prop="controlType">
          <span slot="label">控件类型
          <el-tooltip effect="light" content="默认查询条件为输入框，当选择不同的控件类型时，需要不同的配置，如：下拉，弹框等">
            <i class="el-icon-info"></i></el-tooltip></span>
          <el-select @change="change_controlType" style="width:330px" v-model="editMenuForm.controlType"
            placeholder="请选择类型">
            <el-option v-for="(item, index) in searchCtrlType" :key="index" :label="item.label"
              :value="item.value"></el-option>

          </el-select>
        </el-form-item>

        <el-form-item v-show="isNeedConfigType" label="控件配置">
          <template v-if="!!editMenuForm.searchItemConfig && Object.keys(editMenuForm.searchItemConfig).length > 0">
            <el-button @click="showEditCtrlEvent(editMenuForm)" type="success">编辑配置</el-button>
          </template>
          <template v-else>

            <el-button @click="showEditCtrlEvent(editMenuForm)" type="primary">添加配置</el-button>
          </template>
        </el-form-item>


        <eventTypeByCtrlDialog contrlType="common" :editItemForm="editMenuForm" :designer="designer"
          :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>
        <!-- 动态参数 -->
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'"
          style="font-weight: bold;">参数
          <el-tooltip effect="light" content="并非所有事件动作，添加参数都有效。一般用于执行API（如果和原有API参数相同，则覆盖），或动作‘传参数给控件列表后查询’，其它有待开发">
                         <i class="el-icon-info"></i></el-tooltip>
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
            <el-form-item label="KEY" prop="key">
              <el-input placeholder="key" v-model="item.key"></el-input>
            </el-form-item>
            <el-form-item label="VALUE" prop="value">
              <el-input placeholder="value" v-model="item.value"></el-input>
            </el-form-item>

            <!-- v-show="actionParamsList.length>1" -->
            <div style="margin-bottom:20px;margin-left:5px"> <i @click="deleteParam(item, index)"
                class="el-icon-delete"></i></div>
          </div>
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
        </div>

        <div style="font-weight: bold;">成功或失败后事件处理
          <el-tooltip effect="light" content="一般指执行查询API后，需要执行的后续动作，如：刷新表格列表数据，或其它">
                         <i class="el-icon-info"></i></el-tooltip>
        </div>
        <el-form-item label-width="0">
          <!-- 注意：成功后或失败后事件处理-->
          <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
            :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
        }}</el-button>
      </div>
    </el-dialog>
    <el-dialog v-dialog-drag width="65%" :title="`控件配置- ${title}`" :visible.sync="showEditCtrlDialogFlag" v-if="showEditCtrlDialogFlag"
      :show-close="true" class="small-padding-dialog" append-to-body :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <!-- hello world && currentCtrlType != 'customMulSelect'-->
      <div v-if="currentCtrlType != 'customSelect' ">
        <el-form size="small" :inline="true" :model="searchItemConfig" label-width="80px">

          <el-form-item label-width="100px" label="选择加载API">
              <el-select @change="change_sourceApi(searchItemConfig, $event)"
                  v-model="searchItemConfig.actionName" style="width:193.33px" placeholder="请选择查询">
                  <el-option label="请选择" value=""></el-option>
                  <el-option :key="queryIndex + queryItem.value"
                      v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                      :value="queryItem.value"></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label-width="100px" v-show="!!searchItemConfig.actionName && currentCtrlType.includes('Select')" label="设为被动加载">
                        <el-switch v-model="searchItemConfig.isPassiveDataLoad"></el-switch>
          </el-form-item>
          <el-form-item v-show="!searchItemConfig.actionName"
            label-width="80px" label="数据源key">
            <el-input v-model="searchItemConfig.sourceKey"></el-input>
          </el-form-item>
          <el-form-item v-show="!searchItemConfig.actionName"
            label-width="80px" label="字段转换">
            <el-input v-model="searchItemConfig.matchField"></el-input>
          </el-form-item>
          <el-form-item v-show="!searchItemConfig.actionName"
            label-width="100px" label="过滤参数">
            <el-input v-model="searchItemConfig.searchParams"></el-input>
          </el-form-item>
          <el-form-item v-show="currentCtrlType == 'reportSelectTree'" label-width="90px" label="树结构子ID" prop="treeRowField">
            <el-input v-model="searchItemConfig.treeRowField"></el-input>
          </el-form-item>
          <el-form-item v-show="currentCtrlType == 'reportSelectTree'" label-width="90px" label="树结构父ID" prop="treeParentField">
            <el-input v-model="searchItemConfig.treeParentField"></el-input>
          </el-form-item>
          <el-form-item v-show="currentCtrlType == 'reportSelectTable'" label-width="80px" label="分页数" prop="CPAGE_SIZE">
            <el-input-number v-model="searchItemConfig.CPAGE_SIZE" :min="1"></el-input-number>
          </el-form-item>
          <el-form-item v-show="currentCtrlType == 'reportSelectTable'" label-width="80px" label="是否分页" prop="CIS_PAGE">
            <el-checkbox false-label="N" true-label="Y" v-model="searchItemConfig.CIS_PAGE"></el-checkbox>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="currentCtrlType == 'customSelect' || currentCtrlType == 'customMulSelect'">
        <div class="flex">
          <el-button size="mini" @click="ctrlSearchParams('add')" type="primary" icon="vxe-icon-add">&nbsp;新加</el-button>
          <!-- <el-button size="mini" @click="ctrlSearchParams('remove')" type="danger"
            icon="vxe-icon-delete">&nbsp;删除</el-button> -->
        </div>
        <div style="height:5px"></div>
      </div>
      <el-table border :data="tableData" style="width: 100%" max-height="300">
        <!-- <el-table-column
          type="selection"
          width="55">
        </el-table-column> -->
        <el-table-column type="index" fixed :index="indexMethod">
        </el-table-column>
        <el-table-column fixed prop="field" label="字段名" header-align="center" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.field"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="字段描述" header-align="center" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.title"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="iisShowList" label="是否显示" header-align="center" align="center" width="120">
          <template slot-scope="scope">
            <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.iisShowList"> </el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="isSelectTextField" label="下拉Text" header-align="center" align="center" width="80">
          <template slot-scope="scope">
            <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.isSelectTextField"> </el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="isSelectKeyField" label="下拉Key" header-align="center" align="center" width="80">
          <template slot-scope="scope">
            <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.isSelectKeyField"> </el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="width" label="列宽" header-align="center" align="center" width="130">
          <template slot-scope="scope">

            <el-input-number style="width:100px" :controls="false" v-model="scope.row.width" controls-position="right"
              :min="0"></el-input-number>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" header-align="center" align="center" width="120">
          <template slot-scope="scope">
            <el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text" size="small">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- hello world -->
      <div slot="footer" class="dialog-footer">
        <div class="flex justify-between">
          <div></div>
          <div> <el-button size="large" type="danger" @click="resetConfig()">清空配置</el-button></div>
          <div>
            <el-button size="large" type="primary" @click="submitSearchItem()">{{ i18nt('designer.hint.confirm')
            }}</el-button>
            <el-button size="large" type="" @click="showEditCtrlDialogFlag = false">{{ i18nt('designer.hint.cancel')
            }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-dialog-drag title="设置查询参数映射" :visible.sync="showParamsMapBox" v-if="showParamsMapBox" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <!-- hello world -->
      <div style="margin-bottom:10px" class="flex">
        <div><el-button :disabled="searchItemConfig.CDATASET_ID == -1" @click="reloadingParamsEvent()"
            :load="isBtnReloading" size="mini" type="primary">重新加载</el-button>
          <span v-show="currentCtrlType == 'reportSelectTable'" style="margin-left:10px;"> 默认搜索框字段值可设置为：searchKey</span>
        </div>

      </div>
      <el-table border :data="gridSetparamsMap.data" style="width: 100%" max-height="300">
        <el-table-column fixed prop="pfield" label="名称" header-align="center" width="180">
          <!-- <template slot-scope="scope">
              <el-input v-model="scope.row.pfield"></el-input>
            </template> -->
        </el-table-column>
        <el-table-column prop="ptitle" label="标题" header-align="center" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.ptitle"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="mfield" label="字段/值" header-align="center" align="center" width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.mfield"></el-input>
          </template>
        </el-table-column>

        <!-- <el-table-column fixed="right" label="操作" header-align="center" align="center" width="120">
          <template slot-scope="scope">
            <el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text" size="small">
              移除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <!-- hello world -->
      <div slot="footer" class="dialog-footer">
        <div class="flex justify-between">
          <div></div>
          <div></div>
          <div>
            <el-button size="large" type="primary" @click="submitParamsMapBox()">{{ i18nt('designer.hint.confirm')
            }}</el-button>
            <el-button size="large" type="" @click="showParamsMapBox = false">{{ i18nt('designer.hint.cancel')
            }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
import {
  searchCtrlType,

} from "@/enum/enumData";
const default_searchItemConfig = {
  CDATASOURCE_ID: -1,// 数源ID CID
  CPID: null,// 关联主表Id,后台自动关联
  CDATASET_ID: -1,// 数据集ID
  CIS_PAGE: "N",// 是否分页
  CPAGE_SIZE: 10,// 分页数
  sourceKey: "",// 数据源key
  matchField: "",// 字段转换
  treeRowField:"", // 树结构子ID
  treeParentField:"", // 树结构父ID
  searchParams: "",// 过滤参数
  CDATAS: [],//  表格数据>>配置参数JSON,存储时，需要转换为字符串
  CMAP_PARAMETER: [],// 查询参数>>JSON字符串，查询查询映射列表KEY:VALUE ,存储时，需要转换为字符串
}
const default_editMenuForm = {
  label: "",// 标签
  fieldName: "",// 唯一名称
  placeholder: "",// 占位符
  controlType: "",
  ctrlWidth: "130",
  execWhenChange: false,// 改变时，是否立刻触发查询
  defaultValue: "",
  searchItemConfig: {
    CDATASOURCE_ID: -1,// 数源ID CID
    CPID: null,// 关联主表Id,后台自动关联
    CDATASET_ID: -1,// 数据集ID
    CIS_PAGE: "N",// 是否分页
    CPAGE_SIZE: 10,// 分页数
    sourceKey: "",// 数据源key
    matchField: "",// 字段转换
    treeRowField:"", // 树结构子ID
    treeParentField:"", // 树结构父ID
    searchParams: "",// 过滤参数
    CDATAS: [],//  表格数据>>
    CMAP_PARAMETER: [],// 查询参数>>
  },
  otherParams: {
    hidden: false, // 隐藏
    disabled: false,// 激活
    actionType: "-1",// 动作
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
    // query:"",
    // hash:""
  },
}
import afterSuccessOrErrorSetting from '@/components/form-designer/setting-panel/afterSuccessOrErrorSetting'
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import CodeEditor from '@/components/code-editor/index'
import i18n from "@/utils/i18n";
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
import {
  generateId,
} from "@/utils/util"
export default {
  name: "searchItemsSetting",
  mixins: [i18n],
  components: {
    eventTypeByCtrlDialog,
    Draggable,
    //CodeEditor: () => import('@/components/code-editor/index'),
    CodeEditor,
    afterSuccessOrErrorSetting,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
  },
  data() {
    return {
      isBtnReloading: false,
      showParamsMapBox: false,
      useFormatParamsFn: useFormatParams(this),
      needConfigType: ["reportSelectTree","reportSelect", "reportSelectTable", "customSelect","customMulSelect", "standardFrame", "reportMulSelect"],
      tableData: [], // 控件配置表格字段信息
      //map_tableData:[],// 参数映射表格数据
      loading_tableData: false,// 表格数据是否加载中...
      dataSourceList: [],// 数据源列表
      dataSetList: [],// 数据集合列表 
      dataModelList: [],// 模型实体数据
      currentCtrlType: "text",// 当前控件类型
      showEditCtrlDialogFlag: false,// 控件配置 弹框 是否显示
      searchCtrlType: searchCtrlType,// 导入后使用
      showEditMenuDialogFlag: false,
      optionLines: '',
      currentEditOption: {},// 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm), // 搜索输入框配置
      searchItemConfig: Object.assign({}, default_searchItemConfig),// 搜索输入框配置 中的 控件类型配置
      actionParamsList: [{ key: "", value: "" }],
      editMenuFormRules: {
        placeholder: [
          { required: true, message: '请输入标签名称', trigger: 'blur' },
        ],
        fieldName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
        ],
      },
      // 查询参数映射  { label: 'left', value: 'left' },
      gridSetparamsMap: {
        data: []
      },
    }
  },
  computed: {
    // 验证 是否配置信息
    isNeedConfigType() {
      this.currentCtrlType = this.editMenuForm.controlType
      let needConfig = this.needConfigType.includes(this.currentCtrlType)
      return needConfig
    },
    optionModel() {
      return this.selectedWidget.options
    },

  },
  watch: {
    'selectedWidget.options': {
      deep: true,
      handler(val) {
        //console.log('888888', 'Options change!')
      }
    },
    showEditCtrlDialogFlag(n, o) {
      if (n) {

      } else {
        // 重置清空缓存
        this.$nextTick(() => {
          this.cleanCache()
        })

      }
    }
  },
  mounted() {

  },
  methods: {
     // 数据源API 改变
     async change_sourceApi(formItem, actionName) {
            this.tableData = []
            let dataSetId = this.useFormatParamsFn.getVFormDataSetID(this.searchItemConfig.actionName);
            // debugger
            if (dataSetId != -1 && !!this.searchItemConfig.actionName) {
                this.loading_tableData = true
                let _modelList = await this.getDataModelList(dataSetId)
                this.dataModelList = this.formatDataModel(_modelList)
                this.tableData = cloneDeep(this.dataModelList)
                this.loading_tableData = false
            } else {
                this.dataModelList = []
            }
        },
    // 重新加载参数映射列表
    async reloadingParamsEvent() {
      this.isBtnReloading = true
      let datasetId = this.searchItemConfig.CDATASET_ID
      // 首次添加
      let _resDataList = await this.loadSearchParamsMap(datasetId)
      let paramsMapDataList = this.formatParamsMapData(_resDataList)
      this.gridSetparamsMap.data = paramsMapDataList
      this.isBtnReloading = false
    },
    // 打开参数映射弹框//设置查询参数映射
    async setSearchParamsMap() {
      //debugger
      let paramsMapDataList = []
      let datasetId = this.searchItemConfig.CDATASET_ID
      let dataSearchMapData = cloneDeep(this.searchItemConfig.CMAP_PARAMETER)  // JSON字符串，查询查询映射列表KEY:VALUE ,存储时，需要转换为字符串
      if (!!dataSearchMapData && dataSearchMapData.length > 0) {
        // 编辑时
        paramsMapDataList = dataSearchMapData
      } else {
        // 首次添加
        let _resDataList = await this.loadSearchParamsMap(datasetId)
        paramsMapDataList = this.formatParamsMapData(_resDataList)
      }
      // 赋值表格
      this.gridSetparamsMap.data = paramsMapDataList
      // 打开参数映射弹框
      this.showParamsMapBox = true
    },
    // 格式化查询参数映射
    formatParamsMapData(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            pfield: oldItem.key,
            ptitle: oldItem.desc,
          }

          newDataList.push(newItem)
        })
      }

      return newDataList
    },
    // 加载 参数 列表(查询参数映射列表)
    async loadSearchParamsMap(datasetId) {
      if (datasetId == -1) {
        return
      }
      let params = {
        Id: datasetId
      }
      let ParameterMap = ""
      let _url = "api/MD/DataSet/GetInputParameterByDataSetId"
      await request['post'](_url, params).then(res => {
        // debugger
        if (res && res.Datas && res.Datas.length > 0) {
          ParameterMap = res.Datas
        }
      })
      return ParameterMap
    },
    // 提交查询参数映射
    submitParamsMapBox() {
      //debugger
      this.searchItemConfig.CMAP_PARAMETER = this.gridSetparamsMap.data//this.map_tableData // 参数映射表格数据
      this.showParamsMapBox = false
    },
    // 自定义本地控件时，添加触发事件
    ctrlSearchParams(type = 'add') {
      switch (type) {
        // 新增一行
        case 'add':
          let newItem = {
            field: 'newOption',
            title: '新子项',
            iisShowList: 0,
            isSelectTextField: 0,
            isSelectKeyField: 0,
            width: 200,
            fieldOrder: 100,
          }
          this.tableData.push(newItem)
          break;
        default:
          break;
      }
    },
    // 重置清空缓存
    cleanCache() {
      this.tableData = [] // 控件配置表格字段信息
      //this.map_tableData=[]// 参数映射表格数据
      this.gridSetparamsMap.data = []
      this.dataSourceList = []// 数据源列表
      this.dataSetList = []// 数据集合列表 
      this.dataModelList = []// 模型实体数据

    },
    // 清空控件类型配置信息
    resetConfig() {
      this.$confirm('此操作将删除控件配置数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData = [] // 控件配置表格字段信息
        //this.map_tableData=[]// 参数映射表格数据
        this.gridSetparamsMap.data = [] // 参数映射表格数据
        this.dataSetList = []// 数据集合列表 
        this.dataModelList = []// 模型实体数据
        this.searchItemConfig = Object.assign({}, default_searchItemConfig)// 搜索输入框配置 中的 控件类型配置
      }).catch(() => {

      });
    },
    // 提交 控件类型 配置信息
    submitSearchItem() {
      // debugger
      this.searchItemConfig.CDATAS = this.tableData // 控件配置表格字段信息
      this.searchItemConfig.CMAP_PARAMETER = this.gridSetparamsMap.data//this.map_tableData // 参数映射表格数据
      if (this.searchItemConfig.CDATASOURCE_ID != -1) {
        this.editMenuForm.searchItemConfig = cloneDeep(this.searchItemConfig)
      } else {
        this.editMenuForm.searchItemConfig = {}
      }

      this.showEditCtrlDialogFlag = false
    },
    // 控件配置表格 >>移除表格数据
    deleteRow(index, rows) {
      rows.splice(index, 1);
    },
    // 数据源数据选择改变，触发数据集合改变
    async change_dataSource(formItem, sourceId) {
      if (sourceId != -1) {
        this.dataSetList = await this.getDataSetList(sourceId)
      } else {
        this.dataSetList = []
      }
      this.resetDataSetTableData(formItem, sourceId)
    },

    // 清空数据集表格数据
    resetDataSetTableData(formItem, sourceId) {
      // 重置
      this.tableData = []
      formItem.CDATASET_ID = -1
    },
    // 数据集数据选择改变，触发数据模型改变
    async change_dataSet(formItem, dataSetId) {
      //debugger
      if (dataSetId != -1) {
        this.loading_tableData = true
        let _modelList = await this.getDataModelList(dataSetId)
        this.dataModelList = this.formatDataModel(_modelList)
        this.tableData = cloneDeep(this.dataModelList)
        this.loading_tableData = false
      } else {
        this.dataModelList = []
      }

    },
    // 获取数据源数据
    async getDataModelList(datasetId) {
      let dataModelList = []
      let params = {
        condition: "",
        datasetId: datasetId
      }
      //let _url = "api/MD/DataSetModel/GetAll"
      //let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })
      return dataModelList
    },
    // 格式化模型数据
    formatDataModel(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        let innerField = [
          "CID",
          "CDATETIME_CREATED",
          "CUSER_CREATED",
          "CDATETIME_MODIFIED",
          "CUSER_MODIFIED",
          //"CSTATE",
          "CINSTANCE_ID",
          "CROWREMARK",
          "CENTERPRISE_CODE",
          "CORG_CODE",
        ]
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: oldItem.CCOLUMN_DESC,
            CID: oldItem.CID,
            titleHelp: "",
            iisShowList: 0,
            isSelectTextField: 0,
            isSelectKeyField: 0,
            width: 200,
            titleAlign: "center",
            fieldOrder: 100,
            align: "center",
            controlType: "text",
            groupTitle: "",
            iisSummary: 0,
            fieldRules: ""
          }
          // 内置字段不显示
          if (innerField.includes(newItem.field)) {
            newItem.iisShowList = 0
          }
          newDataList.push(newItem)
        })
      }

      return newDataList
    },
    indexMethod(index) {
      return index + 1;
    },
    // 控件类型改变
    change_controlType(val) {
      //debugger
      this.currentCtrlType = val
    },
    // 控件配置 弹框
    async showEditCtrlEvent(editMenuForm) {
      //let needConfigType = ["reportSelect", "reportSelectTable","customSelect"]
      if (this.needConfigType.includes(editMenuForm.controlType)) {
        this.showEditCtrlDialogFlag = true
      } else {
        this.$message({
          type: 'warning',
          message: '当前控件无需配置,请选择其它控件类型！ '
        });
        return
      }
      this.currentCtrlType = editMenuForm.controlType
      switch (this.currentCtrlType) {
        case "customRenderCtrl":
          this.title = "自定义渲染控件"
          break;
        case "reportMulSelect":
          this.title = "下拉框(多选)配置"
          break;
        case "reportSelect":
          this.title = "下拉框配置"
          break;
        case "reportSelectTree":
          this.title = "下拉树配置"
          break;
        case "reportSelectTable":
          this.title = "下拉框（表）配置"
          break;
        case "customSelect":
          this.title = "下拉框（自定义单选）配置"
          break;
          case "customMulSelect":
          this.title = "下拉框（自定义多选）配置"
          break;  
        case "standardFrame":
          this.title = "标准弹框配置"
          break;
        default:
          break;
      }

      this.$nextTick(() => {
        this.setEditDefaultVal()
      })
    },
    // 获取数据源数据
    async getDataSourceList() {
      if (this.dataSourceList.length > 0) {
        return
      }
      let params = {
        condition: "",
        typeId: 0
      }
      //let _url = "api/MD/DataSource/GetAll"
      //let _url = "api/MD/DataSource/GetByType"
      let _url = "/api/MD/DataSource/GetList"
      
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          // debugger CHOST
          this.dataSourceList = res.Datas
        }
      })
    },
    // 编辑时，设置默认值
    async setEditDefaultVal() {
      // debugger
      if (this.searchItemConfig.CDATASOURCE_ID != -1) {
        // 查询数据集
        this.dataSetList = await this.getDataSetList(this.searchItemConfig.CDATASOURCE_ID)
      }
      // 重置 赋值
      this.tableData = []
      //this.map_tableData=[]
      this.gridSetparamsMap.data = [] // 参数映射表格数据
      if (this.searchItemConfig.CDATAS && this.searchItemConfig.CDATAS.length > 0) {
        this.tableData = this.searchItemConfig.CDATAS // 控件配置表格字段信息
      }
      if (this.searchItemConfig.CMAP_PARAMETER && this.searchItemConfig.CMAP_PARAMETER.length > 0) {
        this.gridSetparamsMap.data = this.searchItemConfig.CMAP_PARAMETER// 参数映射表格数据
      }

    },
    // 获取数据集合
    async getDataSetList(sourceId) {
      let dataSetList = []
      let params = {
        //typeId:0,
        condition: `{CID:${sourceId}}`
      }
      let _url = "api/MD/DataSet/GetAll"
      //let _url = "api/MD/DataSet/GetAllDataSet"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          // debugger
          dataSetList = res.Datas
        }
      })
      return dataSetList
    },
    // 切换动作时，重置动作的参数配置
    actionChangeEvent(params) {
      // 初始化 重置
      this.editMenuForm.actionParams = {
        //actionName:""
      }
    },
    // 设置组合后的动态参数
    setQueryList() {
      let queryParams = {}
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach(item => {
          if (!!item.key) {
            queryParams[item.key] = item.value
          }
        })
      }
      //debugger
      return queryParams
    },
    // 获取组合后的动态参数
    getQueryList() {
      let queryParamsList = []
      //debugger
      if (this.editMenuForm.actionParams && this.editMenuForm.actionParams.query && Object.keys(this.editMenuForm.actionParams.query).length > 0) {

        for (const [key, value] of Object.entries(this.editMenuForm.actionParams.query)) {
          if (!!key) {
            let newItem = { key, value }
            queryParamsList.push(newItem)
          }
        }
      }
      return queryParamsList
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editMenuForm.label
          this.currentEditOption.placeholder = this.editMenuForm.placeholder
          this.currentEditOption.controlType = this.editMenuForm.controlType
          this.currentEditOption.fieldName = this.editMenuForm.fieldName
          // NEW
          this.currentEditOption.ctrlWidth = this.editMenuForm.ctrlWidth
          this.currentEditOption.execWhenChange = this.editMenuForm.execWhenChange

          this.currentEditOption.defaultValue = this.editMenuForm.defaultValue
          this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
          // 控件类型详情配置信息 CMAP_PARAMETER
          //debugger
          if (this.editMenuForm.searchItemConfig.CDATASOURCE_ID != -1) {
            this.currentEditOption.searchItemConfig = cloneDeep(this.editMenuForm.searchItemConfig)
          } else {
            this.currentEditOption.searchItemConfig = {}
          }

          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setQueryList()

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑属性--- 初始化弹框属性参数
    showEditDialogEvent(option) {
      // debugger
      this.currentEditOption = option // 当前属性
      this.showEditMenuDialogFlag = true
      this.editMenuForm.label = option.label
      this.editMenuForm.placeholder = option.placeholder
      this.editMenuForm.controlType = option.controlType
      this.editMenuForm.fieldName = option.fieldName
      //   NEW
      this.editMenuForm.ctrlWidth = option.ctrlWidth
      this.editMenuForm.execWhenChange = option.execWhenChange

      this.editMenuForm.defaultValue = option.defaultValue
      this.actionParamsList = [{ key: "", value: "" }] // 默认
      this.editMenuForm.otherParams = cloneDeep(option.otherParams)
      this.editMenuForm.actionParams = cloneDeep(option.actionParams)
      // 控件类型配置信息 //   NEW
      this.editMenuForm.searchItemConfig = cloneDeep(option.searchItemConfig)
      // 
      this.searchItemConfig = Object.assign({}, this.editMenuForm.searchItemConfig)
      //debugger
      if (this.editMenuForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getQueryList()
      }
      // debugger
      let tt = this.editMenuForm.otherParams.actionType

    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        // debugger
        let fieldName = option.fieldName // searchKey100433
        this.optionModel.searchInputItems.splice(index, 1)
        // 避免拖动后，删除不正确
        let delIndex = this.selectedWidget.publicSubAttribute.findIndex(item => {
          return item.key == fieldName
        })
        this.selectedWidget.publicSubAttribute.splice(delIndex, 1)
      }

    },
    // 添加按钮
    addOption() {
      let newValue = this.optionModel.searchInputItems.length + 1
      let fieldNameRandom = "searchKey" + generateId()
      this.optionModel.searchInputItems.push(
        { label: '', fieldName: fieldNameRandom, controlType: 'text', execWhenChange: false, ctrlWidth: "130", defaultValue: "", searchItemConfig: {}, placeholder: '', value: "", disabled: false, check: false, canRemove: true, otherParams: {}, actionParams: {}, afterSuccessOrErrorEvents: [] }
      )
      // 添加对外暴露属性控件
      this.selectedWidget.publicSubAttribute.push(
        { value: 'value', key: fieldNameRandom }
      )
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1
      this.actionParamsList.push(
        { key: '', value: "" }
      )
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1)
    },


  }
}
</script>
  
<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}
</style>
  