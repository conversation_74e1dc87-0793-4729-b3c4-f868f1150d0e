<template>
    <vxe-grid 
     row-id="id"
     ref="originEditTalbe"
     :data="gridOptions.data"
     :columns="gridOptions.columns"
     :border="gridOptions.border"
     :size="gridOptions.size"
     :stripe="gridOptions.stripe"
     :resizable="gridOptions.resizable"
     :show-overflow="gridOptions.showOverflow"
     :show-footer-overflow="gridOptions.showFooterOverflow"
     :highlight-current-row="gridOptions.highlightCurrentRow"
     :checkbox-config="gridOptions.checkboxConfigLocal"
     :highlight-hover-row="gridOptions.highlightHoverRow"
     :filter-config="gridOptions.filterConfig"
     :toolbar-config="gridOptions.toolbarConfig"
     :loading="gridOptions.loading"
     :params="gridOptions.params"
     :keyboard-config="{ isArrow: true }"
     :print-config="{}"
     :import-config="{}"
     :export-config="{}"
     v-bind="$attrs"
     v-on="$listeners">
      <!-- 重写 bottom 并且默认值分页控件，可以重写-->
       <template v-if="isShowPager" v-slot:bottom>
           <template v-if="$slots.bottom">
              <!-- 自定义插槽 -->
              <slot name="bottom"></slot>
           </template>
           <template v-else>
             <!-- 默认显示 分页控件 -->
              <nvxePage></nvxePage>
           </template>
       </template>
   </vxe-grid>
</template>
<script>
import nvxePage from "@/components/override/vxe-table/nvxe-page.vue"
export default {
   name:'nvxe-grid',
   components:{nvxePage},
   props:{
     // 是否显示 分页,默认 显示bu分页，特殊情况自定义
     isShowPager:{
        type:Boolean,
        default:false
     },
     // 重写>> 表格列头,默认为空
     columns:{
       type:Array,
       default(){
         return []
       }
     },
     //重写>> 表格数据,默认为空
     data:{
       type:Array,
       default(){
         return []
       }
     },
   },
   data(){
       return {
            // 表格默认配置属性
             gridOptions: {
               size:"small",
               border: true,
               stripe:true,
               resizable: true,
               showOverflow: true,
               showFooterOverflow:true,
               highlightCurrentRow:true,
               highlightHoverRow:true,
               checkboxConfigLocal:{
                highlight: true,
                range: false,
                strict:true,
               },
               filterConfig:{},
               loading:false,
               params:{},
               height: 300,
               align: 'left',
               columns: [],
               data: []
             }
           }
   },
   
   mounted(){
     this.initColumns()
     this.initData()
   },
   methods:{
     // 初始化基础数据和其它设置等
     initData(){
       let _self = this
       if(_self.data && _self.data.length>0){
         _self.gridOptions.data = _self.data
       }
     },
     // 初始化列表信息和其它配置
     initColumns(){
       let _self = this
       if(_self.columns && _self.columns.length>0){
         _self.gridOptions.columns = _self.columns
       }
     }
   }
}
</script>
<style lang="scss" scoped>
// 表格排序图标位置调整
::v-deep .vxe-table .vxe-cell--sort-vertical-layout{
  height: 1.2em !important
}
</style>