<template>
    <div>
      <el-form-item  label="显示边框">
          <el-switch v-model="optionModel.showBorderStyle"></el-switch>
      </el-form-item>
      <el-form-item  label="显示分割栏">
          <el-switch v-model="optionModel.showSplitStyle"></el-switch>
      </el-form-item>
    </div>
  </template>
  
  <script>
    import i18n from "@/utils/i18n"

    export default {
      name: "divContainer-editor",
      mixins: [i18n],
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
    }
  </script>
  
  <style scoped>
 
  </style>
  