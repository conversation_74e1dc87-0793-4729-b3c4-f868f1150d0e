<template>
    <div>
        <el-form-item label-width="120px" v-show="contrlType =='common'" label="" >
            <span slot="label">隐藏逻辑
          <el-tooltip effect="light" content="自定义JS代码，逻辑判断此按钮是否隐藏">
            <i class="el-icon-info"></i></el-tooltip></span>
                <el-button @click="customJsEventCodeOption('onBeforeHiding')" size="mini" type="text">
                    <span :style="[{ color: (!!editItemForm.otherParams.onBeforeHiding) ? `green` : `` }]">{{ (!!editItemForm.otherParams.onBeforeHiding)?
                      `编辑隐藏逻辑` : `添加隐藏逻辑` }}</span>
                  </el-button>
        </el-form-item>
        <el-form-item label-width="120px" v-show="contrlType =='common'" label="" >
            <span slot="label">禁用逻辑
          <el-tooltip effect="light" content="自定义JS代码，逻辑判断此按钮是否禁用">
            <i class="el-icon-info"></i></el-tooltip></span>
                <el-button @click="customJsEventCodeOption('onBeforeDisabled')" size="mini" type="text">
                    <span :style="[{ color: (!!editItemForm.otherParams.onBeforeDisabled) ? `green` : `` }]">{{ (!!editItemForm.otherParams.onBeforeDisabled)?
                      `编辑禁用逻辑` : `添加禁用逻辑` }}</span>
                  </el-button>
        </el-form-item>
        <el-form-item label-width="130px" v-show="contrlType =='common' && editItemForm.otherParams.eventType=='click' && editItemForm.otherParams.actionType =='api' && showOnBeforeSubmit" label="" >
            <span slot="label">提交API前逻辑
          <el-tooltip effect="light" content="自定义JS代码，提交API接口前拦截判断">
            <i class="el-icon-info"></i></el-tooltip></span>
                <el-button @click="customJsEventCodeOption('onBeforeSubmit')" size="mini" type="text">
                    <span :style="[{ color: (!!editItemForm.otherParams.onBeforeSubmit) ? `green` : `` }]">{{ (!!editItemForm.otherParams.onBeforeSubmit)?
                      `编辑提交API前逻辑` : `添加提交API前逻辑` }}</span>
                  </el-button>
        </el-form-item>
        <el-form-item label="事件" prop="eventType">
            <el-select @change="eventTypeChange" v-model="editItemForm.otherParams.eventType" placeholder="请选择事件">
                <el-option label="未选择" value=""></el-option>
                <el-option :key="index" v-for="(item,index) in eventTypeList" :label="item.label" :value="item.value"></el-option>
            </el-select>
        </el-form-item>
        <!-- <el-form-item label="事件标签" prop="size">
          <el-select  v-model="editItemForm.value" placeholder="请选择标签">
            <el-option label="请选择" value=""></el-option>
            <el-option v-for="(item,index) in actionLabelList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="动作" prop="actionType">
            <el-select @change="actionChangeEvent" v-model="editItemForm.otherParams.actionType" placeholder="请选择动作">
                <el-option label="未选择" value="-1"></el-option>
                <!-- <el-option label="执行查询" value="api"></el-option>
                <el-option label="弹框应用" value="popup"></el-option>
                <el-option label="控制组件" value="ctrlComp"></el-option> -->
                <el-option :key="actionTypeIndex"
                        v-for="(actionTypeItem, actionTypeIndex) in actionTypeList" :label="actionTypeItem.label"
                        :value="actionTypeItem.value"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType =='logicCode'" label="自定义逻辑" >
                <el-button @click="customJsEventCodeOption()" size="mini" type="text">
                    <span :style="[{ color: (!!editItemForm.otherParams[editItemForm.otherParams.eventType]) ? `green` : `` }]">{{ (!!editItemForm.otherParams[editItemForm.otherParams.eventType])?
                      `编辑自定义逻辑` : `添加自定义逻辑` }}</span>
                  </el-button>
        </el-form-item>
        <el-form-item v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'api'" label="集合类型" prop="excuteEventType">
            <span slot="label">集合类型
            <el-tooltip effect="light" content="添加，修改，删除时候必须要选择操作集，其它查询类，默认:结果集">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
            <el-select  v-model="editItemForm.otherParams.excuteEventType" placeholder="请选择">
                <el-option label="未选择" value="-1"></el-option>
                <el-option label="结果集" value="GetListByDataSetId"></el-option>
                <el-option label="操作集" value="ExecuteByDataSetId"></el-option>
            </el-select>
        </el-form-item>
        <!-- 请选择查询 -->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'api'">
            <el-form-item v-show="editItemForm.otherParams.actionType == 'api'" label="选择" prop="actionParams">
                <el-select v-model="editItemForm.actionParams.actionName" placeholder="请选择">
                    <el-option label="请选择" value=""></el-option>
                    <el-option :key="queryIndex + queryItem.value"
                        v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                        :value="queryItem.value"></el-option>
                </el-select>
            </el-form-item>
        </div>
        <!-- 打开新页面 -->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'openNewPage'">
            <el-form-item v-show="editItemForm.otherParams.actionType == 'openNewPage'" label="页面地址" prop="actionParams">
                <el-input placeholder="新打开页面地址" v-model="editItemForm.actionParams.newPageUrl"></el-input>
            </el-form-item>
        </div>
        <!-- 请选择弹框 -->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'popup'">
            <el-form-item v-show="editItemForm.otherParams.actionType == 'popup'" label="选择弹框" prop="actionParams">
                <el-select v-model="editItemForm.actionParams.actionName" placeholder="请选择弹框">
                    <el-option label="请选择" value=""></el-option>
                    <el-option :key="popupIndex + popupItem.value"
                        v-for="(popupItem, popupIndex) in designer.formConfig.popupList" :label="popupItem.label"
                        :value="popupItem.value"></el-option>
                </el-select>
            </el-form-item>
        </div>
        <!-- 控制组件-->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'ctrlComp'">
            <el-form-item v-show="editItemForm.otherParams.actionType == 'ctrlComp'" label="">
                <span slot="label">选择组件
                    <el-tooltip effect="light" content="选择组件后，可以选择其可以执行的组件方法;目前支持表格，分栏容器（收缩/展开）【分栏容器：只支持除了第一个左边第一栏，其它收缩，展开】，其它有待开发">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input disabled readonly v-model="editItemForm.actionParams.actionName" style="width: 203px"></el-input>
                <el-button style="margin-left:5px" type="text" @click="showCtrlListDialogEvent()">+选择</el-button>
            </el-form-item>
            <el-form-item v-show="editItemForm.otherParams.actionType == 'ctrlComp'" label="选择方法">
                <el-select v-model="editItemForm.actionParams.actionFunction" placeholder="请选择方法">
                    <el-option label="请选择" value=""></el-option>
                    <!-- <el-option label="添加行" value="insertRowFn"></el-option>
                    <el-option label="删除行" value="removeRowFn"></el-option> -->
                    <el-option :key="index" v-for="(item, index) in actionFunctionList" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-show="editItemForm.actionParams.actionFunction == 'removeRowFn'" label="">
                <span slot="label">执行前操作
                    <el-tooltip effect="light" content="删除记录前的提示信息，限制！">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-select @change="onBeforeActionTypeChange" v-model="editItemForm.actionParams.onBeforeActionFunctionType" placeholder="请选择">
                    <el-option label="请选择" value=""></el-option>
                    <el-option label="至少保留一条记录！" value="1"></el-option>
                    <el-option label="提示删除确认信息！" value="2"></el-option>
                </el-select>
            </el-form-item>
        </div>
        
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'formEditBox'">
            <el-form-item label-width="130px" label="">
                <span slot="label">表单配置编码
                    <el-tooltip effect="light" content="表单配置编码，一般是CID">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.formEditBoxCode" style="width: 203px"></el-input>
                <!-- <el-button @click="showStandarBox=true" type="primary" icon="el-icon-plus"></el-button> -->
            </el-form-item>
            <el-form-item label-width="130px" label="">
                <span slot="label">表单配置标题
                    <el-tooltip effect="light" content="表单配置标题">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.formEditBoxTitle" style="width: 203px"></el-input>
               
            </el-form-item>
        </div>
         <!-- 标准弹框-->
         <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'standardBulletBox'">
            <el-form-item label-width="130px" label="">
                <span slot="label">标准弹框编码
                    <el-tooltip effect="light" content="标准弹框编码，列表复制标准弹框编码">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.standardBulletBoxCode" style="width: 203px"></el-input>
                <el-button @click="showStandarBox=true" type="primary" icon="el-icon-plus"></el-button>
            </el-form-item>
            <el-form-item label-width="130px" label="">
                <span slot="label">标准弹框标题
                    <el-tooltip effect="light" content="标准弹框标题，默认标准弹框">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.standardBulletBoxTitle" style="width: 203px"></el-input>
               
            </el-form-item>
            <el-form-item label-width="130px" label="">
                <span slot="label">标准弹框子ID
                    <el-tooltip effect="light" content="标准弹框树结构，子ID">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.standardBulletBoxRowField" style="width: 203px"></el-input>
               
            </el-form-item>
            <el-form-item label-width="130px" label="">
                <span slot="label">标准弹框父ID
                    <el-tooltip effect="light" content="标准弹框树结构，父ID">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-input  v-model="editItemForm.actionParams.standardBulletBoxParentField" style="width: 203px"></el-input>
               
            </el-form-item>
            <el-form-item label-width="130px" label="">
                <span slot="label">回调执行API
                    <el-tooltip effect="light" content="标准弹框选择数据后，确认执行API，参数固定">
                    <i class="el-icon-info"></i></el-tooltip>
                </span>
                <el-select @change="change_standardBulletBoxActionName" v-model="editItemForm.actionParams.standardBulletBoxActionName" style="width:193.33px" placeholder="请选择">
                            <el-option label="请选择" value=""></el-option>
                            <el-option :key="queryIndex + queryItem.value"
                                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                                :value="queryItem.value"></el-option>
               </el-select>
            </el-form-item>
        </div>
        
        <!-- 控件列表 弹框-->
        <el-dialog title="控件列表 选择" :visible.sync="showFlag" v-if="showFlag" v-dialog-drag append-to-body :show-close="true"
            custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false" :close-on-press-escape="false"
            :destroy-on-close="true">
            <div>
                <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id"
                    :expand-on-click-node="false" highlight-current class="node-tree" icon-class="el-icon-arrow-right"
                    @node-click="onNodeTreeClick"></el-tree>
            </div>
            <div slot="footer" class="dialog-footer">

                <el-button type="primary" size="large" @click="submitNodeEvent()">
                    确定</el-button><el-button size="large" @click="showFlag = false">
                    取消</el-button>
            </div>
        </el-dialog>
        <!-- 标准弹框 弹框-->
        <el-dialog title="标准弹框 双击选择" :visible.sync="showStandarBox" v-if="showStandarBox" v-dialog-drag append-to-body :show-close="true"
                    custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false" :close-on-press-escape="false"
                  :destroy-on-close="true">
              <!-- 表格列表 -->
               <div style="margin-bottom: 10px;" class="flex">
                        <div class="flex justify-center items-center" style="width: 50px;">编码</div><el-input style="width: 200px;" v-model="searchConfig.code" placeholder="请输入编码"></el-input>
                        <div  class="flex justify-center items-center" style="width: 50px;">名称</div><el-input style="width: 200px;" v-model="searchConfig.name" placeholder="请输入名称"></el-input>
                        <el-button size="mini" @click="loadStandarBoxData(true)" type="primary">搜索</el-button>
                        <el-button size="mini" @click="resetData()" type="">重置</el-button>
               </div>
                  <vxe-grid 
                   @cell-dblclick="cellDblclick"
                   :pagerConfig="{
                        pageSize: 10,
                        pageSizes: [5,10,15, 20, 50, 100],
                        currentPage:pageConfig.currentPage,
                        pageSize:pageConfig.pageSize,
                        total:pageConfig.total,
                    }" 
                    @page-change="pageChangeGrid"
                    :rowConfig="{
                        isHover: true
                    }" showOverflow border stripe ref="standarBoxGirdRef" :loading="gridOptions.loading" height="250px"  :columns="gridOptions.columns"  @currentChange="currentChangeEvent">
                    
                </vxe-grid>
            <div slot="footer" class="dialog-footer">
                <!-- <el-button type="primary" size="large" @click="submitStandarBox()">
                    确定</el-button> -->
                    <el-button size="large" @click="showStandarBox = false">
                    取消</el-button>
            </div>
        </el-dialog>
      
        <commonEventDialog :functionName="functionName" :jsCodeTitle="jsCodeTitle" :eventCodeConfig="currentEditSettingBtnOption" @submitEvent="submitCommonEventDialog" ref="commonEventDialogRef"></commonEventDialog>
    </div>
</template>
<script>
const { actionType } = require('@/enum/enumData')
import request from '@/libs/request'
import commonEventDialog from "@/components/form-designer/setting-panel/commonEventDialog.vue"
import cloneDeep from "clone-deep"
export default {
    name: "eventTypeByCtrlDialog",// 根据控件类型名称，展示不同的事件类别和行为
    components:{commonEventDialog},
    props: {
        designer: Object,
        selectedWidget: Object,
        contrlType: {
            type: String,
            default: "common"
        },// 控件类型名称
        editItemForm: Object,
        showOnBeforeSubmit:{
            type:Boolean,
            default:false,
        },
    },
    data() {
        return {
           
            actionLabelList:actionType,
            searchConfig:{
                name:'',
                code:''
            },
            gridOptions:{
                loading:false,
                columns: [
          {
            type: "seq",
            fixed: "left",
            title: "序号",
            align: 'center',
            headerAlign: 'center',
            width: 60,
          },
          // 重写覆盖 模板默认设置
        //   {
        //     fixed: 'right',
        //     slots: { default: 'operate' },
        //     title: '操作',
        //     headerAlign: 'center',
        //     align: 'center',
        //     width: 200,
        //   },

          {
            field: "CPOPUP_CODE",
            title: "编码",
            headerAlign: 'center',
            align: 'left',
            width: 200,
            iisQuery: 1,
          },

          {
            field: "CPOPUP_NAME",
            title: "名称",
            align: 'left',
            width: 200,
            headerAlign: 'center',
            iisQuery: 1,
          },
        //   {
        //     field: "CIS_PAGER",
        //     title: "分页",
        //     align: 'center',
        //     width: 80,
        //     controlType: "statusColumnYN",
        //     headerAlign: 'center',
        //   },
        //   {
        //     field: "CIS_MULTI",
        //     title: "多选",
        //     width: 80,
        //     controlType: "statusColumnYN",
        //     align: 'center',
        //     headerAlign: 'center',
        //   },
          {
            field: "CRETURN_VALUE",
            title: "返回值字段",
            align: 'left',
            headerAlign: 'center',
          },
          {
            field: "CRETURN_TEXT",
            title: "返回文本字段",
            align: 'left',
            headerAlign: 'center',
          },
        
            //   {
            //     field: "CSTATE",
            //     title: "状态",
            //     align: 'left',
            //     width: 80,
            //     controlType: "statusColumnAD",
            //     headerAlign: 'center',
            //     align: 'center',
            //   },
        ],
                data:[]
            },
            pageConfig: { // 分页配置
                total: 0,
                currentPage: 1,
                pageSize: 10,
            },
            showStandarBox:false, //
            jsCodeTitle:"",
            functionName:"",// 隐藏逻辑
            currentEditSettingBtnOption:{},// 当前操作按钮配置
            eventTypeList: [],// 事件列表，更加不同来源，控制显示事件类别
            nodeCtrlValue: "",// 节点选中值
            showFlag: false,// 显示弹框
            actionFunctionList: [],
            functionList: {
                // 需要在指定的控件中 添加暴露的事件
                vxeTable: [
                    { label: "添加行", value: "insertRowFn" },
                   // { label: "添加指定行", value: "insertRowByCurrentFn" }, vxeTable 树结构 本身不支持 插入
                    { label: "删除行", value: "removeRowFn" },
                    { label: "刷新表", value: "searchTableDataFn" },
                    { label: "刷新表-选中原来", value: "searchTableDataAndSelectdOldData" },
                    { label: "导出-高级", value: "exportTableDataFn" }
                ],
                splitpanes:[
                      { label: "收缩/展开【右】", value: "toggleFn" },
                      { label: "收缩/展开【左】", value: "toggleFnLeft" },
                ],
                echarts:[
                      { label: "全屏显示/取消全屏", value: "toggleFullScreenFn" },
                      { label: "导出全部数据", value: "exportAllDataFn" },
                      { label: "导出选中数据", value: "exportSelectedDataFn" },
                ],
                card:[
                      { label: "全屏显示/取消全屏", value: "toggleFullScreenFn" },
                ]
            },
            // 默认动作 列表
            defaultActionTypeList:[
                    { label: "执行查询", value: "api" },
                    { label: "弹框应用", value: "popup" },
                    { label: "标准弹框【左树右表】", value: "standardBulletBox" },
                    { label: "新页面打开", value: "openNewPage" },
                    // { label: "表单配置【指定ID】", value: "formEditBox"},
                    { label: "控制组件", value: "ctrlComp" },
                    { label: "自定义逻辑", value: "logicCode" },
                    { label: "传参数给控件列表后查询", value: "postParamsToCtrl" },
            ],
            // 事件对应动作列表
            actionTypeList:[]
        }
    },
    computed:{
        // hasOnBeforeSubmitStr(){
        //    // debugger
        //     let onBeforeSubmitStr = !!this.editItemForm.onBeforeSubmit
        //     return onBeforeSubmitStr
        // }
    },
    watch: {
        // hasOnBeforeSubmitStr:{
        //     handle(n,o){
        //         debugger
        //     },
        //     deep:true
        // },
        showFlag(n, o) {
            if (!!n) {

            } else {
                // 退出控件列表选中时
                this.actionFunctionList = []
            }
            this.$nextTick(() => {
                this.handleNodeCtrlChange(this.editItemForm.actionParams.actionName)
            })
        },
        nodeCtrlValue(n, o) {
            if (!!n) {
                // 控件列表选中发生改变时候，触发可用方法列表
                // this.handleNodeCtrlChange(n)
            }
        },
        showStandarBox(n,o){
            if(n){
                this.loadStandarBoxData()
            }
        }
    },
    mounted() {
        this.handleNodeCtrlChange(this.editItemForm.actionParams.actionName)
        this.initEventTypeList()
    },
    methods: {
        
        pageChangeGrid(pageInfo){
           this.pageConfig.currentPage =pageInfo.currentPage
           this.pageConfig.pageSize=pageInfo.pageSize
           this.loadStandarBoxData()
        },
        cellDblclick(tableInfo){
            //debugger
            let _row = tableInfo.row
  
            this.editItemForm.actionParams.standardBulletBoxCode = _row.CPOPUP_CODE
            this.editItemForm.actionParams.standardBulletBoxTitle = _row.CPOPUP_NAME
            //CCOL 和 CCOL_PARENT
            this.editItemForm.actionParams.standardBulletBoxRowField = _row.CCOL
            this.editItemForm.actionParams.standardBulletBoxParentField = _row.CCOL_PARENT
            this.showStandarBox = false
        },
        resetData(){
            this.searchConfig={
                    name:'',
                    code:''
                }
             this.loadStandarBoxData(true)
        },
        loadStandarBoxData(resetPage=false){
            if(resetPage){
                this.pageConfig.currentPage =1
            }
            this.gridOptions.loading = true
            let _url = `api/SYS/Popup/GetAll`
            let params ={
                PageIndex:this.pageConfig.currentPage,
                PageSize:this.pageConfig.pageSize,
                search:{
                    CPOPUP_CODE:this.searchConfig.code,
                    CPOPUP_NAME:this.searchConfig.name       
                }
            }
            request["post"](_url, params).then(res=>{
               this.gridOptions.loading = false
               let _standarBoxGirdRef = this.$refs["standarBoxGirdRef"]
               if(_standarBoxGirdRef){
                    _standarBoxGirdRef.loadData(res.Datas)
               }
               this.pageConfig.total = res.TotalRows
            })
            setTimeout(() => {
                this.gridOptions.loading = false
            }, 3000);
        },
        // 提交标准弹框
        submitStandarBox(){

        },
        currentChangeEvent(){

        },
        change_standardBulletBoxActionName(_value){
            let dataList = this.designer.formConfig.queryList
            this.editItemForm.actionParams.standardBulletBoxParentsID=this.selectedWidget.id
             if(dataList && dataList.length>0){
                dataList.forEach(item=>{
                    if(item.value==_value){
                       this.editItemForm.actionParams.standardBulletBoxDataSetID=item.actionParams.CDATASET_ID
                    }
                })
             }
        },
        // 事件改变
        eventTypeChange(val){
            this.actionTypeList = []
            if(!!val){
                this.getActionTypeList(val)
            }
          
        },
        getActionTypeList(eventType){
            switch (this.contrlType) {
                        case "vxetableEventList":
                            this.actionTypeList = this.defaultActionTypeList
                            break;
                        case "common":
                            this.actionTypeList = this.defaultActionTypeList
                            break;
                        default:
                          this.actionTypeList = this.defaultActionTypeList
                            break;
                    }
        },
        // 控制控件 执行前操作 类型
        onBeforeActionTypeChange(val){
            //debugger
              /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
              this.$set(this.editItemForm.actionParams, "onBeforeActionFunctionType", val)
        },
        // 打开弹框 自定逻辑代码
        customJsEventCodeOption(type="",root = false){
           
            if(!!root){
                this.currentEditSettingBtnOption = this.editItemForm; // 当前菜单按钮属性
            }else{
                 // onBeforeCurrentChange
                 this.currentEditSettingBtnOption = this.editItemForm.otherParams; // 当前菜单按钮属性
            }
            if(!!type){
                this.functionName =type
            }else{
                  // 根据当前控件类型切换，事件名称
                this.functionName = this.editItemForm.otherParams.eventType
            }
            this.jsCodeTitle =this.editItemForm.label+"."+this.functionName+"(paramsData)"

            let commonEventDialogRef = this.$refs["commonEventDialogRef"]
            if(commonEventDialogRef){
                commonEventDialogRef.showDialog = true
            }
        },
          // 提交
        submitCommonEventDialog(params){
           // debugger
            let code = params.code
            this.editItemForm[this.functionName] = code
            /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            this.$set(this.currentEditSettingBtnOption, this.functionName, code)
        },
        // 根据不同的控件类型 获取不同的事件列表
        initEventTypeList() {
            this.eventTypeList = []
            switch (this.contrlType) {
                case "vxetableEventList":
                    this.eventTypeList = [{ label: "当前行改变前", value: "onBeforeCurrentChange" }]
                    break;
                case "common":
                    this.eventTypeList = [{ label: "当单击时", value: "click" }]
                    break;
                case "defaultmenubuttonSearch":
                    this.eventTypeList = [{ label: "当值发生改变时", value: "change" }]
                    break;    
                case "afterSuccessOrError":
                    this.eventTypeList = [{ label: "成功后", value: "success" }, ]//{ label: "失败后", value: "error" }
                    break;
                default:
                    break;
            }
            // 初始化动作列表
            if(!!this.editItemForm.otherParams.eventType){
                this.getActionTypeList(this.editItemForm.otherParams.eventType)
            }
        },
        // 根据控件类型变化，改变可用方法列表
        handleNodeCtrlChange(ctrlName = "") {
            // debugger
            // this.editItemForm.actionParams.actionFunction=""
            this.actionFunctionList = []
            if (ctrlName.includes('vxetable')) {
                this.actionFunctionList = this.functionList.vxeTable
            }
            if (ctrlName.includes('splitpanes')) {
                this.actionFunctionList = this.functionList.splitpanes
            }
            if (ctrlName.includes('echarts')) {
                this.actionFunctionList = this.functionList.echarts
            }
            if (ctrlName.includes('card')) {
                this.actionFunctionList = this.functionList.card
            }
        },
        // 切换动作时，重置动作的参数配置
        actionChangeEvent(params) {
            // 初始化 重置
            this.editItemForm.actionParams = {}
        },

        // 获取数据源控件列表
        getNodeTreeData() {
            let copyData = cloneDeep(this.designer.nodeTreeData)
            let dataList = this.setPublicAttribute(copyData)
            return dataList
        },
       // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData) {
      let dataList = []
      for (let i = 0; i < nodeTreeData.length; i++) {
        let item = nodeTreeData[i]
        if (item.hasOwnProperty("children")) {
          this.setPublicAttribute(item.children)
          dataList.push(item)
        } else {
          let randomNum = Math.floor(Math.random() * 100000000 + 1)
          // 优先子集对外开放属性
          if (item.hasOwnProperty("publicSubAttribute")) {
            //debugger
            item.children = []

            if (item.publicSubAttribute && item.publicSubAttribute.length > 0) {
              item.publicSubAttribute.forEach((subItem, index) => {

                let valueItem = {
                  label: subItem.value,
                  id: subItem.value + "-" + randomNum + index
                }
                let keyItem = {
                  label: subItem.key,
                  id: subItem.key + "-" + randomNum + index,
                  children: [valueItem]
                }
                //debugger
                item.children.push(keyItem)
              })
              // debugger
              dataList.push(item)
            }

          } else {
            // 对外开放属性
            if (item.hasOwnProperty("publicAttribute")) {
              item.children = []
              if (item.publicAttribute && item.publicAttribute.length > 0) {
                item.publicAttribute.forEach((subItem, index) => {
                  let kidItem = {
                    label: subItem,
                    id: subItem + "-" + randomNum
                  }
                  item.children.push(kidItem)
                })
                dataList.push(item)
              }
            } else {
              dataList.push(item)
            }
          }
        }

      }
      return dataList

    },
        // 打开控件列表 弹框
        showCtrlListDialogEvent() {
            this.showFlag = true
        },
        // 提交控件列表
        submitNodeEvent() {
            this.editItemForm.actionParams.actionName = this.nodeCtrlValue
            this.showFlag = false

        },
        onNodeTreeClick(params){
            this.nodeCtrlValue =""
            let hasChildren = params.hasOwnProperty("children")
            let ctrlValue = params.label
            if(!hasChildren){
                this.nodeCtrlValue = ctrlValue
            }else{
               
                    if(ctrlValue.includes('splitpanes')){
                    this.nodeCtrlValue = ctrlValue
                    }else if(ctrlValue.includes('card')){
                        this.nodeCtrlValue = ctrlValue
                    }
                    else if(ctrlValue.includes('tab') && ctrlValue.length>5){
                        this.nodeCtrlValue = ctrlValue
                    }
                    else{
                        this.$message.warning('此节点不可选！')
                    }
            }
        },
        // 点击控件列表 回调事件
        // onNodeTreeClick(params) {
        //     //debugger
        //     this.nodeCtrlValue = ""
        //     let hasChildren = params.hasOwnProperty("children")
        //     let ctrlValue = params.label
        //     if (!hasChildren) {
        //         this.nodeCtrlValue = ctrlValue
        //     } else {
        //         if(ctrlValue.includes('splitpanes')){
        //             this.nodeCtrlValue = ctrlValue
        //         }else if(ctrlValue.includes('card')){
        //             this.nodeCtrlValue = ctrlValue
        //         }
        //         else{
        //             this.$message.warning('此节点不可选！')
        //         }
             
        //     }
        // },
        //  // 参数值设置 弹框 树节点 点击事件
        //  onNodeTreeClick(params) {
        //    // debugger
        //     this.nodeCtrlValue =""
        //     let hasChildren = params.hasOwnProperty("children")
        //     let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
        //     let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
        //     let parentNodeLabel = parentsNode.data.label
        //     let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
        //         ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params)
        //     if(!hasChildren){
        //     this.nodeCtrlValue = ctrlValue
        //         // this.curSetParamsItem.value = ctrlValue
        //     }else{
        //         this.$message.warning('此节点不可选！')
        //     }
        // },
         // 获取第三层 控件名称
    tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params) {
      // debugger
      let newCtrlVal = ctrlValue
      let layoutList = ["defaultmenubutton"]//|| parentsNode.parent.data.label.includes('card'),"card"
      let level3NodeType = ""
      try {
        level3NodeType = parentsNode.parent.data.text
        //  if(layoutList.includes(level3NodeType)){
        if (layoutList.includes(level3NodeType)) {
          newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
        }
      } catch (error) {
        level3NodeType = ""
      }
      return newCtrlVal
    },
    }
}
</script>