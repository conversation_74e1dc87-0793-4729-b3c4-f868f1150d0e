<template>
    <div>
        <!-- @focus="focusEvent" 
         suffix-icon="el-icon-plus"
           -->
        <el-input
            placeholder=""
            clearable
            v-bind="$attrs"
            v-on="$listeners"
          
            >
            <el-button @click="focusEvent" slot="append" icon="el-icon-plus"></el-button>
        </el-input>
        <nvxeModal  ref="popTableRenderRef" @confirmEvent="submitEvent()" :title="label+title"  v-model="showPopTableRender">
              <vxe-grid 
              
                @cell-dblclick="cellDblclickEvent"
                :size="gridDefaultOptions.size"
                :border="gridDefaultOptions.border" 
                :loading="gridDefaultOptions.loading"
                :stripe="gridDefaultOptions.stripe" 
                :resizable="gridDefaultOptions.resizable"
                :show-overflow="gridDefaultOptions.showOverflow" 
                :show-footer-overflow="gridDefaultOptions.showFooterOverflow"
                :highlight-current-row="gridDefaultOptions.highlightCurrentRow"
                :show-header-overflow="gridDefaultOptions.showHeaderOverflow"
                :highlight-hover-row="gridDefaultOptions.highlightHoverRow" 
                 height="500px"
                  :pager-config="pagerConfig"
                  :columns="gridDefaultOptions.columns" 
                  :data="gridDefaultOptions.data"
                  @page-change="pageChangeEvent"
                  >
                  <template #top>
                     <!-- 合并关键字搜索 -->
                     <!-- {{ inpuSearchKeyObj }} -->
                    <div v-if="Object.keys(inpuSearchKeyObj).length>0"  style="margin-bottom:5px" class="flex">
                        <template v-for="(item, index) in Object.keys(inpuSearchKeyObj)">
                            <el-input :key="index"  clearable  size="mini"
                                 v-model.trim="inpuSearchKeyObj[item].value"
                                :placeholder="inpuSearchKeyObj[item].title"></el-input>
                        </template>
                        <!-- <el-input  clearable @keyup.enter="searchEvent()" size="mini"
                                v-model="searchkey"
                                :placeholder="inputSearchFieldTitle +` 关键字`"></el-input> -->
                        <el-button style="margin-left:10px" size="mini" :loading="loadingBtn" @click="searchEvent()"
                            type="primary">查询</el-button>
                    </div>
                </template>
             </vxe-grid>
             <template #footer>
                <div></div>
             </template>
        </nvxeModal>
    </div>
</template>
<script>
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'popTableRender',// 弹框表格数据
    props:{
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
      
      // 字段标题
      label:{
            type:String,
          default:""
        },
        // 是否可用
        disabled:{
            type:Boolean,
            default:false,
        }  
    }, 
    data(){
        return {
            inpuSearchKeyObj:{},// 配置的搜索关键字，对象，可以配置多个关键字搜索
            inputSearchField:"", // 配置的搜索关键字只支持一个
            inputSearchFieldTitle:"", // 配置的搜索关键字只支持一个,字典描述
            searchkey:"",// 搜索关键字
              // 表格默认配置属性
            gridDefaultOptions: {
                size: "mini",
                border: true,
                stripe: true,
                resizable: true,
                showOverflow: true,
                showFooterOverflow: true,
                showHeaderOverflow:true,
                highlightCurrentRow: true,
                highlightHoverRow: true,
                // filterConfig: {},
                 loading: false,
                // params: {},
                // height: 0,
                // align: "left",
                columns: [],
                data: [],
            },
            config:config,
            pagerConfig:{
                currentPage:1,
                pageSize:10,
                total:0,
            },
            title:" (双击选中)",
            loading:false,
            showPopTableRender:false,// 是否显示弹框
          
        }
    },
    methods:{
        searchEvent(){
            this.loadData(true)
        },
        // 聚焦输入框
        focusEvent(params){
            this.showPopTableRender = true
            this.$nextTick(()=>{
                this.loadData()
            })
        },
        // 确认
        submitEvent(){
          
        },
        //  双击 选中单元格 触发事件
        cellDblclickEvent(params){
            //debugger
            let row = params.row
            let matchField= this.paramsItem.matchField //转换字段 CTABLE_NAME|CDISPLAY_NAME
         
            let postParams = {
                row,
                matchField
            }
            this.$emit("changeEvent",postParams)
            setTimeout(()=>{
                this.showPopTableRender = false
            },100)

        },
        // 格式化 转换字段列表
        formatMatchField(){

        },
        // 翻页时间触发
        pageChangeEvent(pageInfo){
            this.pagerConfig.currentPage = pageInfo.currentPage
            this.$nextTick(()=>{
                this.loadData()
            })
        },
        // 处理列头显示问题
        handleColumns(columns=[]){
            if(columns && columns.length>0){
                columns = columns.filter(item=>{
                    if(item.iisShowList && !!item.title){
                        return item
                    }
                })
            }
            return columns
        },
         // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
       loadData(isSearch=false){
           if(!!isSearch){
                this.pagerConfig.currentPage =1
           }
            let _self = this
            this.gridDefaultOptions.loading = true
            let sourceKey = this.paramsItem.sourceKey
            //debugger
            let searchParams = this.paramsItem.searchParams

            let _url=this.config.moduleSub+"PopSource"
            let params = {
                   "PageIndex":this.pagerConfig.currentPage,
                   "PageSize":this.pagerConfig.pageSize,
                   SourceType:"Table", //Dict Table Tree
                   Params:{
                    sourceKey,// 表名
                    filter:{} // 字段过滤参数
                   }
                };
               
                if(!!searchParams){
                    //debugger
                    _self.inputSearchField =""
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                       let searchParamsArr = searchParams.split(',')
                       searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                            // 固定值参数
                            if(!paramItem[1].includes('@')){
                                params.Params.filter[paramItem[0]] =paramItem[1]
                            }
                             // 动态 配置多个查询参数
                            if(paramItem[1].includes('@')){
                              if(!_self.inpuSearchKeyObj[paramItem[0]]?.value){
                                _self.$set(_self.inpuSearchKeyObj,paramItem[0],{
                                    key:paramItem[0],
                                    value:"",
                                   
                                })
                              }
                              
                                params.Params.filter[paramItem[0]] =   _self.inpuSearchKeyObj[paramItem[0]].value

                            //    _self.inputSearchField = paramItem[0]
                            //     params.Params.filter[paramItem[0]] =_self.searchkey
                            }
                       })
                      //  console.log("filter+json:",params.Params.filter)
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                          // 固定值参数
                        if(!paramItem[1].includes('@')){
                            params.Params.filter[paramItem[0]] =paramItem[1]
                        }
                        // 动态 配置多个查询参数
                        if(paramItem[1].includes('@')){
                           
                            if(!_self.inpuSearchKeyObj[paramItem[0]]?.value){
                                _self.$set(_self.inpuSearchKeyObj,paramItem[0],{
                                    key:paramItem[0],
                                    value:"",
                                   
                                })
                            }
                            params.Params.filter[paramItem[0]] = _self.inpuSearchKeyObj[paramItem[0]].value
                            // _self.inputSearchField = paramItem[0]
                            // params.Params.filter[paramItem[0]] =_self.searchkey 
                        }
                      
                       
                    }
                }
            request['post'](_url, params).then(res=>{
              
                if(res.Success){
                    let resData = res.Datas
                    this.gridDefaultOptions.loading = false
                    this.gridDefaultOptions.columns = this.handleColumns(resData.TableDetial)
                    let searchKeyList = Object.keys(this.inpuSearchKeyObj)
                     //  动态参数 title 值 绑定
                    this.gridDefaultOptions.columns.forEach(item=>{
                        searchKeyList.forEach(titlekey=>{
                            if(item.field == titlekey){
                                this.inpuSearchKeyObj[titlekey].title = item.title
                            }
                        })
                        // if(item.field == _self.inputSearchField){
                        //     _self.inputSearchFieldTitle = item.title
                        // }
                    })
                    this.gridDefaultOptions.data = resData.TableData
                    this.pagerConfig.total = res.TotalRows
                }

            })
       },
    }
}
</script>
<style lang="scss" scoped>

</style>