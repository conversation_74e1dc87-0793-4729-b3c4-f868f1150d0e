<template>
    <div class="selectTableRender">
        
          <el-select 
            ref="elSelectRef"
            @change="changeEvent"
            v-bind="$attrs"
            v-model="currentItem"
            v-on="$listeners"
            :loading="loading"
            @focus="loadData()"
            placeholder="请选择">
             <el-option  v-for="item in selectDataList"  :key="item.value" :label="item.label" :value="item.value">
            </el-option>
         </el-select>
    </div>
</template>
<script>
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'selectTableRender',
    components:{},
    props:{
     // 编辑状态是否变更>>仅用于表格talbe中 
    // updateFlag:{
    //     type: [String,<PERSON>olean],
    //     default: "",
    // },    
       // 是否立即加载数据
    //  isLoadNow:{
    //      type:Boolean,
    //      default:false,
    //  },    
    //    // 每次点击都查询
    //  reSearch:{
    //      type:Boolean,
    //      default:false,
    //  },  
    //   // 表格信息>>dataFrom=talbe 才有效
    //   tableInfo:{
    //       type:Object,
    //       default(){
    //           return {}
    //       } 
    //   },
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
       // 当前字段:value
     currentValue:{
         type:[String,Number],
         default:""
     },
      // 当前字段:Text
     currentValueText:{
        type:[String,Number],
         default:""
     },
     // 是否可用
     disabled:{
         type:Boolean,
         default:false,
     }  
    }, 
    data(){
        return {
            config:config,
            //value1: [],
            loading: false,
            prefixIcon:"", // 头部图标,用于是否加载中 显示
            currentItem:'',// 当前选择项
            currentItemText:'',// 当前选择项描述
            selectDataList:[],// 当前列表数据
        }
    },
    watch:{
        currentValue(n,o){
            //debugger
            if(!!n){
               this.currentItem = n
            }else{
                // 清空数据
                 this.currentItem =''
                 this.currentItemText =''
            }
        },
         currentValueText(n,o){
             //debugger
            if(!!n){
               this.currentItemText = n
            }else{
                // 清空数据
                this.currentItemText =''
            }
        }
        
    },
   
    mounted(){
        //首次赋值
      this.$nextTick(()=>{
        // 设置默认值
          if(!!this.currentValueText){
            let newItem ={
                        value: this.currentValue,
                        label:this.currentValueText
                    }
            this.selectDataList.push(newItem)
            this.currentItem = this.currentValue
          }
      })
    },
    methods:{
        // 格式化数据成下拉框所需的
        formatData(dataList=[]){
           // debugger
            let newData =[] 
            let matchField = this.paramsItem.matchField
           // let matchField = "bpArtId,bpArtCode"
            let keyValueArr = matchField.split(',')
            //debugger
            if(dataList && dataList.length>0){
                dataList.forEach(item=>{
                    let newItem ={
                        value:item[keyValueArr[0]],
                        label:item[keyValueArr[1]]
                    }
                    newData.push(newItem)
                })
            }
          return newData
        },
       // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
       loadData(){
       // debugger
            if(this.selectDataList.length >1){
                return
            }
            this.loading = true
            let sourceKey = this.paramsItem.sourceKey
            //debugger
            let searchParams = this.paramsItem.searchParams

            let _url=this.config.virModule+"Data/ComboSource"
            let params = {
                   SourceType:"Table", //Dict Table Tree
                   Params:{
                    sourceKey,// 表名
                    filter:{} // 字段过滤参数
                   }
                };
                if(!!searchParams){
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                       let searchParamsArr = searchParams.split(',')
                       searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                          params.Params.filter[paramItem[0]] =paramItem[1]
                       })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    }
                }
            request['post'](_url, params).then(res=>{
                //debugger
                if(res.Success){
                    let resData = res.Datas
                    this.loading = false
                    this.selectDataList = this.formatData(resData)
                }
            })
       },
       // 选择改变回调事件
       changeEvent(val){
           let _self = this
           this.$nextTick(()=>{
                let params ={
                    value:val,
                    text:_self.$refs["elSelectRef"].selectedLabel
                } 
                _self.$emit("changeEvent",params)
           })
       }
    }
}
</script>


<style lang="scss">

</style>