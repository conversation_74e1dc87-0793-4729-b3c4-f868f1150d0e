/**
 * @desc  表格列表页面通用类
 *
 * @params 参数
 * 
 * <AUTHOR>
 *
 * @created 2022/10/10
 */
import dayjs from 'dayjs'
 import config from '@/config'
 import emitterFn from '@/libs/mitt'
 import request from '@/libs/request'
 const {actionType} = require('@/enum/enumData')
 import { useFormatParams } from "@/hooks/useFormatParams"
import { useElementSize } from "@vueuse/core";//useUrlSearchParams
export default {
    name:"mixinList",
 
    data() {
        return {
          editContentName:"editContentBoxRef",
          useFormatParamsFn: useFormatParams(this),
          activatedFlag:true,// 是否当前激活页面,默认为false,使用KEEP-ALIVE时候，必须设置为false，否则为TRUE
          config:config,
          request:request,
          originColumns:{},// 原始的列头信息
           // 主表查询配置
           requstConfig:{
            postUrl: "",
            actionName: "",
            postParams: {},
          },
          actionType:actionType,
          splitpanesOptions:{
            pane1H:0,// 分栏1高度，需要在 渲染后 initData() 重新赋值
            pane2H:0,// 分栏2高度
          },
          contentOptions:{
            height: 0, // 主体内容 contentBoxRef 高 
            width: 0,  // 宽
          },
         
          tableLoading:false,// 表格是否加载数据中...
          urlSearchParams:{},// 路由参数对象
          searchColumnsName:"",// 默认为空            
          gridOptions:{
              dataSearchOptions:{},//数据来自表单tableHeader,主要配置分页，排序字段等信息
              columns:[], // 表格需要的列头
              data:[],//表格数据
          },
          paramsFromList:{
            layoutLevel:1, //层级级别 1:主表，2:从表，3:孙表
            responseData:{}, // 原始查询出来的数据
            subRow:{},// 当前从表选中行
            mainRow:{},// 当前主表选中行
            subTableInfo:{}// 当前从表配置信息
            
          },
          currentMainRow:null,// 当前主表选中信息
          searchDetailParams:{
            rowId:null,// 当前选中行ID
            resetPage:true,// 是否重置翻页，默认重置
            randomNumber:"",// 随机码
          },
        };
      },
      provide() { 
        // 主要解决深层次的组件嵌套，祖先组件向子孙组件之间传值。
        return {
            //  返回当前页面整体实例
              getPageInstance: () => this,
          } 
      },
      computed: {
        //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
        actionType_state() {
          return this.$store.state.actionType;
        },
        // 普通查询参数
        commonSearch() {
          return this.$store.state.commonSearch;
        },
      },
      watch:{
        "$store.state.removeCachekey":{
          handler(n, o) {
            console.log("=mixins=removeCachekey_state====移除缓存关键词===========：" + n)
              if (this.activatedFlag) {
                console.log("activatedFlag=mixins=removeCachekey_state====移除缓存关键词===========：" + n)
                if(!!n){
                  this.pruneCacheEntry(n)
                }
              }
          },
          deep: true,
          immediate: false
        },
        // 添加/修改/删除 按钮触发事件
        actionType_state:{
            handler(n,o){
              console.log("===action change===",n);
              if(this.activatedFlag){
                
                this.actionTypeChange(n)
              }
            },
             deep:true,
             immediate:false
        },
        // 普通查询参数 变更 重新查询
        commonSearch:{
          handler(n,o){
             if(this.activatedFlag){
                this.commonSearchChange(n)
             }
            console.log("===commonSearchChange===");
          },
           deep:true,
           immediate:false
      },
      },
      mounted() {
        //debugger
        // 初始化数据配置
        this.initData()
      },
    methods:{
        // 移除缓存
    pruneCacheEntry(key) {
      //debugger
      try {
        const { cache, keys } = this.$vnode.parent.componentInstance;
        console.log('pruneCacheEntry cache: ', cache)
        console.log('pruneCacheEntry keys: ', keys)
        let componentKey = this.$vnode.key ??
        this.$vnode.componentOptions.Ctor.cid + (this.$vnode.componentOptions.tag ? `::${this.$vnode.componentOptions.tag}` : "");
        if(!!cache[key]){
          cache[key].componentInstance.$destroy()
          cache[key] = null
          delete cache[key]
          let index = keys.indexOf(key);
          if (index > -1) {
            keys.splice(index, 1);
          }
          console.warn("===成功手动移除缓存=key===")
          this.$store.commit("set_removeCachekey","")
        }
      
      } catch (error) {
        this.$store.commit("set_removeCachekey","")
        console.error("===pruneCacheEntry=移除缓存失败！！！====")
      }

    },
        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
        // 储存 当前页面原始表格列头信息，route.name 作为KEY
        setPageOriginColumns(originColumns={}){
          //console.log("====setPageOriginColumns===length="+Object.keys(originColumns).length);
          this.$store.commit("set_routerName", this.$route.name);
          this.$store.commit("set_pageOriginColumns", originColumns);
        },
        // 储存 动态页面原始表格列头信息 dynamic
        setPageOriginColumnsViewdetail(originColumns={}){
            //let urlParams = useUrlSearchParams() 
            let params ={
              key:this.getRequest("formName"),//urlParams.formName,
              value: originColumns
            }
            this.$store.commit("set_pageOriginColumns_viewdetail", params);
        },
        // 普通查询
        commonSearchChange(e){
          let params ={
              resetPage:true, // 是否重置翻页
              search:e.value, //普通查询 commonSearch
            // searchPro:{} // 高级搜索
          }
          // debugger
          this.refreshMain(params)
        },
          // 获取随机码
        getRandomNum(){
          return Math.floor(Math.random() * 10000 + 1);
        },
        refreshMain(searchParams={}){
          //debugger
          this.searchDetailParams.resetPage = true // 是否需重置从表分页
            // 刷新主表 VFORM 内置方法 widgetRefList
          if(!!!this.$refs['preForm']){
            console.warn("==this.$refs['preForm']== 对象为空")
            return
          }
          let mainTableRef = this.$refs['preForm'].widgetRefList["mainTableRef"].$refs["mainTableRef"]
          if(mainTableRef){
            //debugger
            mainTableRef.searchTableData(searchParams)
          }
        },
        // 初始化数据配置
        initData(){
            // 监听获取获取 contentRef 高 宽
            if(this.$refs["contentBoxRef"]){
              const { width, height } = useElementSize(this.$refs["contentBoxRef"]);
              this.contentOptions.width = width;
              this.contentOptions.height = height;
              // 初始化分栏 默认高度 
              setTimeout(()=>{
                this.splitpanesOptions.pane1H = this.splitpanesOptions.pane2H = this.contentOptions.height /2
              },100)
            }
        },
       // 拖拽分栏触发事件,重新分配高度
        resizedEvent(e){
          if(e && e.length>0){
            this.splitpanesOptions.pane1H = Number(e[0].size).toFixed(2)/100 * this.contentOptions.height
            this.splitpanesOptions.pane2H = Number(e[1].size).toFixed(2)/100 * this.contentOptions.height
          }
        },
         // 从表操作
        sub1ActionBtn(row,type='iisAdd',tableInfo){
          //debugger
          this.paramsFromList.layoutLevel =2 //  退出时 需要 重置
          this.paramsFromList.subRow = row // 从表当前选中行
          this.paramsFromList.subTableInfo=tableInfo // 当前从表配置信息tableHeader,tableDetail
          this.paramsFromList.mainRow = this.currentMainRow
          this.$store.commit("set_actionType",type)
          
        },
         // 判断使用普通弹框还是抽屉式弹框（仅用于子应用）
       isDrawerOrModal() {
          // debugger
          let popupParams = this.$store.state.popupParams;
          let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
          let popupItem = this.useFormatParamsFn.getPopupItemByUrl(selfActionName)
          if (!!popupItem) {
             //debugger
             if(popupItem.otherParams.actionType =='drawer'){
               this.editContentName="editContentBoxDrawerRef"
             }else{
               this.editContentName="editContentBoxRef"
             }
          }
      },
        // 添加/修改/删除/ 触发事件
        actionTypeChange(n){
         // debugger
          let _self = this
            try {
               this.editContentName="editContentBoxRef"
               this.isDrawerOrModal()
           } catch (error) {
             this.editContentName="editContentBoxRef"
           }
           if(n){
            let btnType = n.value
            switch (btnType) {
                 // 弹框
                 //case this.actionType.iisDetail:
                 case this.actionType.iisPopup:
                  _self.popupAction(n) 
                  break;
                  // 刷新
                case this.actionType.iisRefresh:
                    _self.refreshAction() 
                    break;
                // 添加
                case this.actionType.iisAdd:
                    _self.addAction() 
                    break;
                 // 编辑   
                case this.actionType.iisEdit:
                   _self.editAction()   
                        break;
                // 删除        
                case this.actionType.iisDel:
                    _self.beformDeleteAction()   
                  
                     break; 
                  // jsonData_import 导入  
                  case "jsonData_import_Refresh":
                    _self.jsonData_import_RefreshFn()
                     break;  
                       // jsonData_export 导出    
                case "jsonData_export":
                // alert("jsonData_export")
                  _self.jsonData_exportFn()
                   break;  
                   case "tongbu":
                    // alert("jsonData_export")
                      _self.tongbuData()
                       break;                
                // 动态表单预览          
                case _self.actionType.iisView:
                  
                  _self.$router.push({path:'/viewDetail'})
                    break;
             
                default:
                    break;
            }
           }
        },
        //  json导入后刷新 主页面
        jsonData_import_RefreshFn(){
          let mainTableRef = this.$refs["mainTableRef"]
          if(mainTableRef){
            mainTableRef.searchTableData()
          }
        },
        //  json导出
        jsonData_exportFn(){
          let _self = this
          let _checkboxRecords =[]
          let mainTableRef = this.$refs["mainTableRef"]
          if(mainTableRef){
            let list = mainTableRef.getCheckboxRecords()
            if(list?.length>0){
              list.forEach(item=>{
                _checkboxRecords.push(item.CID)
              })
              
            }
            
          }
          let _url = `/api/MD/VisualFormDesigner/Export`
          if(_checkboxRecords.length>0){
            let params = _checkboxRecords
            request['post'](_url, params).then(res => {
              this.$message({
                message: '导出中，请稍后!!',
                type: 'success'
              });
              //debugger
              let link = document.createElement("a");
              let blob = new Blob([JSON.stringify(res.Datas)], { type: "data:text/plain;charset=utf-8" });
              let downloadUrl = URL.createObjectURL(blob);
              link.href = downloadUrl;
              //对下载的文件命名
              let fileName = "可视化配置" + dayjs().format('YYYYMMDDHHmmss') 
              fileName = `${fileName}.json`;
              link.download = fileName;//"接口工具.json"; //fileName;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              setTimeout(()=>{
                _self.jsonData_import_RefreshFn()
              })
            })
          }else{
            this.$message({
              message: '请勾选需要导出的数据！',
              type: 'warning'
            });
          }
         
        },
        //同步
        tongbuData(){
          let _self = this
          let _checkboxRecords =[]
          let mainTableRef = this.$refs["mainTableRef"]
          if(mainTableRef){
            let list = mainTableRef.getCheckboxRecords()
            if(list?.length>0){
              list.forEach(item=>{
                _checkboxRecords.push(item.CID)
              })
              
            }
            
          }
          let _url = `/api/md/VisualFormDesigner/PushData`
          if(_checkboxRecords.length>0){
            let params = _checkboxRecords
            request['post'](_url, params).then(res => {
              console.log(res);
              if(res.Success){
                this.$message({
                  message: res.Content,
                  type: 'success'
                });
              }else{
                this.$message({
                  message: res.Content,
                  type: 'error'
                });
              }
            })
          }else{
            this.$message({
              message: '请勾选需要导出的数据！',
              type: 'warning'
            });
          }
         
        },
           // 当前主表选中行
        currentRow_get() {
          let routerName = this.$route.name
          return this.$store.state.currentRow.value[routerName];
        },
           // 获取 动态表单选中行
        currentRowViewdetail_get() {
           // let urlParams = useUrlSearchParams() 
            let formName = this.getRequest("formName") //urlParams.formName
            return this.$store.state.currentRow_viewdetail.value[formName];
        },
        // 复制功能
        async copyToClipboard(textToCopy) {
          // navigator clipboard 需要https等安全上下文
          if (navigator.clipboard && window.isSecureContext) {
              // navigator clipboard 向剪贴板写文本
              return await navigator.clipboard.writeText(textToCopy);
          } else {
              // 创建text area
              let textArea = document.createElement("textarea");
              textArea.value = textToCopy;
              // 使text area不在viewport，同时设置不可见
              textArea.style.position = "absolute";
              textArea.style.opacity = 0;
              textArea.style.left = "-999999px";
              textArea.style.top = "-999999px";
              document.body.appendChild(textArea);
              textArea.focus();
              textArea.select();
              return new Promise((res, rej) => {
                  // 执行复制命令并移除文本框
                  document.execCommand('copy') ? res() : rej();
                  textArea.remove();
              });
          }
      },
        // 删除前 提示
        beformDeleteAction(){
          let formName = this.getRequest("formName")
          this.$confirm('此操作将永久删除该选择数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if(!!formName){
                // 确定删除 后续操作 
               this.deleteAction()
            }else{
              this.deleteDesignAction()
            }
            

          }).catch(() => {
            // 取消删除 
            // this.$message({
            //   type: 'info',
            //   message: '已取消删除'
            // });          
          });
        },
        deleteDesignAction() {

          let _url = `api/MD/VisualFormDesigner/Delete`
          let row = this.currentRow_get()
          let params = {
            "CID": row.CID
          }
          request['post'](_url, params).then(res => {
            this.$message({
              message: res.Content,//'执行成功！',
              type: 'success'
            });
            setTimeout(() => {
              // 刷新数据
              // let mainTableRef = this.$refs["mainTableRef"]
              // debugger
              // if (mainTableRef) {
              //   mainTableRef.searchTableData()
              // }
              emitterFn.emit("deleteDesign", {})
            }, 1000)
          })
        },
        // 获取待删除数据
        getDeletedData(){
          let responseData = this.paramsFromList.responseData
          let TableHeader = responseData.Datas.MainTable.TableHeader
          let currentViewDetailRow = this.currentRowViewdetail_get()
           // 层级级别 1:主表，2:从表，3:孙表
          if(this.paramsFromList.layoutLevel==2){
              // 是否从表，默认主表
              TableHeader =  this.paramsFromList.subTableInfo.TableHeader
              currentViewDetailRow =this.paramsFromList.subRow
          }
          let submitData ={
              TableDetail:[currentViewDetailRow],
              TableHeader,
          }
          return submitData
        },
        // 删除 回调事件
        deleteAction(){
          //debugger
          let submitData = this.getDeletedData()
          let _self = this
          let _url=this.config.virModule+"Data/Delete"
          let params = submitData
          request['post'](_url, params).then(res=>{
            this.$message({
                  message: res.Content,//'执行成功！',
                  type: 'success'
                });
            setTimeout(()=>{
              // 刷新数据
             if(_self.paramsFromList.layoutLevel ==1){
                _self.refreshMain()
              }else{
                _self.refreshSub()
              }

            },1000)
          })
        },
        // 刷新数据
        refreshAction(){
            let params ={
              resetPage:true, // 是否重置翻页
              // search:e.value, //普通查询 commonSearch
              // searchPro:{} // 高级搜索
            }
            let currentWidgetRefName = this.$store.state.runCurrentWidget;
            let selfControlParams = this.$store.state.selfControlParams;
              // 刷新主表 VFORM 内置方法 widgetRefList
            let currentWidgetRef = this.$refs['preForm'].widgetRefList[currentWidgetRefName].$refs[currentWidgetRefName]
            if(currentWidgetRef){
              // debugger
              currentWidgetRef.searchTableData(selfControlParams)
              // 执行完毕后，清除临时的参数
              this.$store.commit("set_selfControlParams",{})
            }
        },
        popupAction(params){
          //this.$message.error('暂未配置功能,请联系管理员！+popupAction');
          let editContentBoxObj = this.$refs[this.editContentName]
          if(editContentBoxObj){
              editContentBoxObj.showEditBox = true
              editContentBoxObj.refreshKey = params.key
          }
        },
        // 添加 回调事件 
        addAction(){
            let editContentBoxObj = this.$refs[this.editContentName]
            if(editContentBoxObj){
                editContentBoxObj.showEditBox = true
            }
        },
        editAction(){
          //debugger
           let editContentBoxObj = this.$refs[this.editContentName]
            if(editContentBoxObj){
                editContentBoxObj.showEditBox = true
            }
        },
      // 刷新从表
      refreshSub(){
        this.searchDetailParams.resetPage = false  // 是否需重置从表分页
        this.searchDetailParams.randomNumber =this.getRandomNum()// 随机码 改变 触发更新页面数据
      },  
         // 提交数据成功，刷新数据
      submitSuccess(params){
        // debugger
        // 提交成功后 是刷新从表还是主表
        if(params.layoutLevel ==1){
          this.refreshMain()
        }else{
          this.refreshSub()
        }
        
      },
    },
    // keep-alive 生命周期
    activated(){
      //console.log("lit page activated");
      this.activatedFlag = true;
        //fix 跳转路由后，编辑弹框总是没有关闭
        // let editContentBoxObj = this.$refs[this.editContentName]
        // if(editContentBoxObj){
        //     // 关闭弹框
        //     editContentBoxObj.showEditBox = false
        // }
    },
    deactivated(){
      this.activatedFlag = false;
      // let editContentBoxObj = this.$refs[this.editContentName]
      //   if(editContentBoxObj){
      //       // 关闭弹框
      //       editContentBoxObj.showEditBox = false
      //   }
    }
}