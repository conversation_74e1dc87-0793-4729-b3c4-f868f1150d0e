import axios from './api.request'
import 'xe-utils' // vxeTable 通用库
import VXETable from 'vxe-table'
import {Loading} from 'element-ui'; 
// import JSONbig from 'json-bigint'
export default {
  get: function (url, data, params) {
    let _self = this
    return new Promise((resolve, reject) => {
      axios.request({
        url: url,
        method: 'get',
        params: params || {},
        data: data || {}
      }).then(res => {
        _self.requestSuccessHandler(res, resolve, reject)
      }).catch(err => {
        _self.requestErrorHandler(err, resolve, reject)
      })
    })
  },
  post: function (url, data, params) {
    let _self = this
    return new Promise((resolve, reject) => {
      axios.request({
        url: url,
        method: 'post',
        params: params || {},
        data: data || {}
      }).then(res => {
        _self.requestSuccessHandler(res, resolve, reject)
      }).catch(err => {
        _self.requestErrorHandler(err, resolve, reject)
      })
    })
  },
  postBlob: function (url, data, params) {
    let _self = this
    return new Promise((resolve, reject) => {
      axios.request({
        responseType: 'blob',// 文件 PDF EXCEL 下载
        url: url,
        method: 'post',
        params: params || {},
        data: data || {}
      }).then(res => {
        //debugger
        if(res.data.size>200){
          resolve(res.data)
        }else{
          this.showErrMessage("找不到记录,请检查记录是否存在!")
        }
       
      }).catch(err => {
        //debugger
        resolve(res.data)
      })
    })
  },
  update: function (url, data, params) {
   // debugger
    let _self = this
    this.openFullScreen(true)
    return new Promise((resolve, reject) => {
      axios.request({
        url: url,
        method: 'post',
        params: params || {},
        data: data || {}
      }).then(res => {
       // debugger
          _self.openFullScreen(false)
         _self.requestSuccessHandlerByUpdate(res, resolve, reject)
      }).catch(err => {
       // debugger
         _self.openFullScreen(false)
        _self.requestErrorHandler(err, resolve, reject)
      })
    })
  },
  requestSuccessHandlerByUpdate (res, resolve, reject) {
   // debugger
    if (!res) {
      reject(false)
      return
    }
   // debugger
    if (res.data.success) { // 返回数据成功
      // resolve(res.data.result)
      resolve(res.data.data)
      let successfulExecution =  "操作成功"
      VXETable.modal.message({message:successfulExecution, status:'success' })
    } else {
      //debugger
      let requestException =  "请求业务异常，请联系系统管理员-001"
      let msg = res.data.message.content == null || res.data.message.content == '' ? requestException : res.data.message.content
      this.showErrMessage(msg)
      reject(msg)
    }
  },
  requestSuccessHandler (res, resolve, reject) {
    //debugger
    if (!res) {
      reject(false)
      return
    }
    if (res.data.success) { // 返回数据成功
     // resolve(res.data.result)
     resolve(res.data.data)
    } else {
      let requestException =  "请求业务异常，请联系系统管理员"
      let msg = res.data.message.content == null || res.data.message.content == '' ? requestException: res.data.message.content
      this.showErrMessage(msg)
      reject(msg)
    }
  },
 
  showErrMessage(msg){
       clearTimeout(this.showMessageTimer)
       this.showMessageTimer = setTimeout(() => {
           VXETable.modal.message({message:msg, status:'error' })
       }, 300);
  },
  requestErrorHandler (err, resolve, reject) {
    if (!!err) {
      this.showErrMessage(err)
      reject(err)
    }
  },
  openFullScreen(showOrNot=false) {
    let dataLoading = "数据加载中,请稍等..."
    const loadingObj = Loading.service({
      lock: showOrNot,
      text: dataLoading,
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    if(!showOrNot){
        loadingObj.close();
    }
  },
}
