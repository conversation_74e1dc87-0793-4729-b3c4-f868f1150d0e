<template>
    <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
        :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
        :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
        <div :publicAttribute="publicAttribute" :style="[{ height: getSettingHeight() }]" :id="field.options.name"
            :ref="field.options.name">
            <div class="flex h-full justify-center items-center">
                <div v-show="!showLoadingText">
                    <!-- 加载中... -->
                    <el-skeleton style="width: 240px">
                        <template slot="template">
                            <el-skeleton-item variant="image" style="width: 240px; height: 240px;" />
                            <div style="padding: 14px;">
                                <el-skeleton-item variant="p" style="width: 50%" />
                                <div style="display: flex; align-items: center; justify-items: space-between;">
                                    <el-skeleton-item variant="text" style="margin-right: 16px;" />
                                    <el-skeleton-item variant="text" style="width: 30%;" />
                                </div>
                            </div>
                        </template>
                    </el-skeleton>
                </div>
                <div v-show="showLoadingText">
                    加载中...
                </div>
            </div>

        </div>
        <vxe-modal :title="modalTitle" v-model="showModal">
            <template #default>
                <div>
                    {{ modalContent }}
                </div>

            </template>
        </vxe-modal>
    </static-content-wrapper>
</template>

<script>
import dayjs from 'dayjs'
import { useFullscreen, useMouseInElement } from '@vueuse/core'
const XEUtils = require('xe-utils') // vxeTable 通用库
// import * as $echarts from "echarts";
import { groupBy } from "lodash-es";
import tdTheme from "./theme.json"; // 自定义主题样式
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import emitterFn from '@/libs/mitt'
import i18n from "@/utils/i18n"

import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import {
    useDebounceFn
} from '@vueuse/core'
import cloneDeep from 'clone-deep';
export default {
    name: "echarts-widget",
    componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    // 注入列表页面整体实体 
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    props: {
        field: Object,
        parentWidget: Object,
        parentList: Array,
        indexOfParentList: Number,
        designer: Object,

        contentBoxHeight: {
            type: [Number, String],
            default: 0
        },
        // 控件来源父集 add by andy
        sourceVFormRender: {
            type: String,
            default: ""
        },
        previewState: { //是否表单预览状态
            type: Boolean,
            default: false
        },
        designState: {
            type: Boolean,
            default: false
        },

        subFormRowIndex: {
            /* 子表单组件行索引，从0开始计数 */
            type: Number,
            default: -1
        },
        subFormColIndex: {
            /* 子表单组件列索引，从0开始计数 */
            type: Number,
            default: -1
        },
        subFormRowId: {
            /* 子表单组件行Id，唯一id且不可变 */
            type: String,
            default: ''
        },

    },
    components: {
        StaticContentWrapper,
    },
    data() {
        return {
            useMouseInElementObj: {
                isOutside: true
            },
            dataZoomChangeObj: {},
            isMouseover: false,
            publicAttribute: {
                row: null, // 当前选中行
            },// 对外暴露属性值
            selectedDataIndex: null,
            originColor: "",// 原始颜色
            currentSeries: null,
            showModal: false,// 是否显示弹框
            modalTitle: "",// 弹框标题
            modalContent: "",// 弹框内容
            currentSelectedData: [],// 当前选中列表
            useHandleVFormEventFn: useHandleVFormEvent(this),
            fullscreenObj: null,// 当前全屏设置对象
            tdTheme: Object.assign({}, tdTheme),
            showLoadingText: false,
            loading_data: false,
            useFormatParamsFn: useFormatParams(this),
            maxLoadTime: 20,
            echartData: [],
            preViewData: [],
            fullIconPath: "path://M145.066667 85.333333h153.6c25.6 0 42.666667-17.066667 42.666666-42.666666S324.266667 0 298.666667 0H34.133333C25.6 0 17.066667 8.533333 8.533333 17.066667 0 25.6 0 34.133333 0 42.666667v256c0 25.6 17.066667 42.666667 42.666667 42.666666s42.666667-17.066667 42.666666-42.666666V145.066667l230.4 230.4c17.066667 17.066667 42.666667 17.066667 59.733334 0 17.066667-17.066667 17.066667-42.666667 0-59.733334L145.066667 85.333333z m170.**********.2L162.**********.133333l-76.8 76.8V725.333333C85.**********.733333 68.**********.666667 42.**********.666667s-42.666667 17.066667-42.666667 42.666666v256c0 25.6 17.066667 42.666667 42.666667 42.666667h256c25.6 0 42.666667-17.066667 42.666666-42.666667s-17.066667-42.666667-42.666666-42.666666H145.066667l76.8-76.8 153.6-153.6c17.066667-17.066667 17.066667-42.666667 0-59.733334-17.066667-17.066667-42.666667-17.066667-59.733334 0z m665.6 34.133334c-25.6 0-42.666667 17.066667-42.666666 42.666666v153.6l-76.8-76.8-153.6-153.6c-17.066667-17.066667-42.666667-17.066667-59.733334 0-17.066667 17.066667-17.066667 42.666667 0 59.733334l153.6 153.6 76.8 76.8H725.333333c-25.6 0-42.666667 17.066667-42.666666 42.666666s17.066667 42.666667 42.666666 42.666667h256c25.6 0 42.666667-17.066667 42.666667-42.666667v-256c0-25.6-17.066667-42.666667-42.666667-42.666666z m0-682.666667h-256c-25.6 0-42.666667 17.066667-42.666666 42.666667s17.066667 42.666667 42.666666 42.666666h153.6l-76.8 76.8-153.6 153.6c-17.066667 17.066667-17.066667 42.666667 0 59.733334 17.066667 17.066667 42.666667 17.066667 59.733334 0l153.6-153.6 76.8-76.8v153.6c0 25.6 17.066667 42.666667 42.666666 42.666666s42.666667-17.066667 42.666667-42.666666v-256c0-25.6-17.066667-42.666667-42.666667-42.666667z",
            unFullIconPath: "path://M298.666667 631.466667H226.133333v-81.066667h217.6v204.8h-85.333333v-68.266667l-128 128L170.666667 759.466667l128-128z m422.4 0l128 128-59.733334 59.733333-128-128v68.266667h-85.333333V554.666667h217.6v81.066666h-72.533333zM298.666667 341.333333L187.733333 230.4 243.2 170.666667l115.2 115.2V217.6h85.333333v204.8H226.133333V341.333333H298.666667z m430.933333 0h64v81.066667h-217.6V217.6h85.333333v72.533333L780.8 170.666667l59.733333 59.733333L729.6 341.333333z"

        }
    },
    computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
        // 图表配置
        echartsConfig() {
            let options = this.field.options
            return options
        },
        // 面板切换隐藏
        formTemplatePanelSize() {
            return this.$store.state.formTemplatePanel
        },
        // echartDataListVuex(){
        //     let data = this.$store.state.mainTableDataList
        //     return data
        // }
    },
    watch: {
        // 监控配置信息
        echartsConfig: {
            handler(n, o) {
                // console.log("====field.options.chartConfig====change!")
                this.debouncedFn_reloadOption()
            },
            deep: true
        },
        // 监控面板切换隐藏
        formTemplatePanelSize: {
            handler(n, o) {
                this.debouncedFn_reloadOption()
            },
            deep: true
        },
        // 监控鼠标是否进入了图表内部，默认再外部isOutside为真
        "useMouseInElementObj.isOutside"(n, o) {
            this.handlerEcharMouseoverEvent(n)

        },

    },
    created() {
        this.registerToRefList()
        this.initEventHandler()
    },
    beforeDestroy() {
        this.unregisterFromRefList()
        //小贴士：在容器节点被销毁时，总是应调用 echartsInstance.dispose 以销毁实例释放资源，避免内存泄漏。
        if (this.echartsInstance) {
            this.echartsInstance.dispose()
        }

    },
    beforeMount() {
        //  移除监听 触发监听 emitterFn.emit("sumTableRowData", state)
        emitterFn.off("afterLoadTableData")
    },
    mounted() {
        let _self = this
        // document.onkeydown = function(event){
        // 		if(event.code =='Escape'){
        //             _self.debouncedFn_reloadOption()
        //         }
        // }
        let element = this.$refs[this.field.options.name];


        // 是否全屏显示对象
        this.fullscreenObj = useFullscreen(element)
        // const { x, y, isOutside } = useMouseInElement(target)
        this.useMouseInElementObj = useMouseInElement(element)
        this.echartData = this.field.options.testChartsData

        this.$nextTick(() => {
            // 是否被动加载数据
            if (!!!this.field.options.isPassiveDataLoad) {
                this.tryTillGetData()
            }
            // const fullscreenElement = this.$refs.fullscreenElement;
            element.addEventListener('fullscreenchange', (e) => {
                // 监控指定元素 是否全屏 发生改变
                console.log("===element=======fullscreenchange=================")
                if (_self.fullscreenObj.isFullscreen) {
                    _self.debouncedFn_reloadOption()
                }

            });
        })
        //监听页面的 window.onresize 事件获取浏览器大小改变的事件，然后调用 echartsInstance.resize 改变图表的大小。
        window.onresize = function () {
            if (_self.echartsInstance) {
                _self.echartsInstance.resize();
            }
        };
        this.debouncedFn_reloadOption = useDebounceFn((params) => {
            this.reloadOption(params)
        }, 300)
        // 被动数据源 且 共享数据源控件不为空
        // debugger
        if (!!this.field.options.isPassiveDataLoad && !!this.field.options.shareDataTableName) {
            // 注册监听 
            emitterFn.on("afterLoadTableData", (params) => {
                //debugger
                if (_self.field.options.shareDataTableName == params.refName) {
                    // debugger 
                    _self.showLoadingText = true
                    setTimeout(() => {
                        _self.showLoadingText = false
                        _self.afterLoadTableData_emitterFn(params)
                    }, 1000)
                }
            })
        }
    },
    destroyed() {
        // 移除监听 
        emitterFn.off("afterLoadTableData")
    },
    methods: {
        // 对外调用 导出选中数据
        exportSelectedDataFn() {
            //debugger
            if (this.currentSelectedData && this.currentSelectedData.length == 0) {
                this.$message({
                    message: '请选中数据后再导出！',
                    type: "warning",
                });
            } else {
                this.exportCSV(this.currentSelectedData)
            }
        },
        // 对外调用 导出全部数据
        exportAllDataFn() {
            //debugger
            if (this.echartData && this.echartData.length > 0) {
                this.exportCSV(this.echartData)
            }
        },
        // 对外调用 全屏显示/取消全屏
        toggleFullScreenFn() {
            console.log("=echarts==toggleFn==对外调用全屏显示/取消全屏=======")
            this.setFullScreenToolBox()
        },
        // 随机数代码
        randomString(len) {
            len = len || 32;
            let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            let maxPos = $chars.length;
            let pwd = '';
            for (let i = 0; i < len; i++) {
                pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return pwd;
        },
        // 导出CSV文件
        exportCSV(jsonData, fileName) {
            if (!jsonData || jsonData.length == 0) {
                return;
            }
            if (!fileName) {
                let exportCSVName = "报表数据" + dayjs().format('YYYYMMDDHHmmss') + this.randomString(10)
                fileName = `${exportCSVName}.csv`;
            }
            let one = jsonData[0];
            let csvText = "";
            for (let key in one) {
                csvText += key + ","
            }
            csvText = this.trim(csvText, ",") + "\n";
            //增加\t为了不让表格显示科学计数法或者其他格式
            for (let i = 0; i < jsonData.length; i++) {
                let row = "";
                for (let item in jsonData[i]) {
                    row += `${jsonData[i][item] + '\t'},`;
                }
                csvText += this.trim(row, ",") + '\n';
            }
            //encodeURIComponent解决中文乱码
            let uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(csvText);
            //通过创建a标签实现
            let link = document.createElement("a");
            link.href = uri;

            //   let link = document.createElement("a");
            //   let blob = new Blob([JSON.stringify(res.Datas)], { type: "data:text/plain;charset=utf-8" });
            //   let downloadUrl = URL.createObjectURL(blob);
            //   link.href = downloadUrl;
            //对下载的文件命名
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        // 过滤空格
        trim(str, char) {
            if (char) {
                str = str.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '');
            }
            return str.replace(/^\s+|\s+$/g, '');
        },

        // 表格数据源加载完毕后，触发此
        async afterLoadTableData_emitterFn(params) {
            console.log("=======表格数据源加载完毕后，触发图表=========afterLoadTableData_emitterFn====")
            this.loading_data = true
            //this.echartData = []
            let _dataKey = params.refName
            let _tableData = this.getDataFromCacheByKey(_dataKey)
            if (_tableData && _tableData.length > 0) {
                this.echartData = _tableData //await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
            } else {
                this.echartData = [] // 即使数据为空 也需要重新加载渲染
            }

            // 开始加载图表
            this.debouncedFn_reloadOption()
            this.loading_data = false
        },
        // 获取共享数据源数据 BY key
        getDataFromCacheByKey(cacheKey) {
            // debugger
            let ctrlShareData = []
            try {
                ctrlShareData = this.$store.state.ctrlShareData.value[cacheKey] || []
            } catch (error) {
                ctrlShareData = []
            }
            return ctrlShareData
        },
        // 对外暴露事件，重新加载
        async reSearchData(paramsOptions = {}) {
            //debugger
            this.loading_data = true
            if (!!this.field.options.actionName) {
                this.echartData = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
            }

            // 开始加载图表
            this.debouncedFn_reloadOption()
            this.loading_data = false
            // this.loadData()
        },
        // 循环直到获取到数据
        async tryTillGetData() {
            //debugger
            let _self = this
            let dataObj = this.$refs[_self.field.options.name] //this.$refs["echartsDom"]  // 
            if (!!dataObj) {
                // 干正事
                if (!!this.field.options.actionName) {
                    this.echartData = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
                }

                //debugger
                // 开始加载图表
                this.loadData()
                this.maxLoadTime = 20
            } else {
                if (_self.maxLoadTime > 0) {
                    setTimeout(async () => {
                        _self.maxLoadTime = _self.maxLoadTime - 1
                        await this.tryTillGetData()
                    }, 1000)
                }
            }
        },

        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
        // 从缓存中获取数据
        getDataFromVuexCache() {
            //debugger
            let resData = []
            let formName = this.getRequest("formName")
            if (!formName) {
                formName = 666666 // 测试时使用
            }
            // 当前页面返回的数据集合缓存
            let datasetData = this.$store.state.mainTableDataList
            if (!datasetData) {
                return
            }
            // 当前页面缓存数据，过滤后的数据
            let currentDatalist = datasetData.value[formName]
            if (!currentDatalist) {
                return
            }
            // 从集合列表中返回指定集合数据
            let dataSetSelectedModel = this.field.options.dataSetSelectedModel
            if (!!dataSetSelectedModel) {
                resData = currentDatalist[dataSetSelectedModel]
            } else {
                resData = currentDatalist
            }
            // 非设计模式适
            this.echartData = resData
            return resData
        },
        // 计算高度
        getSettingHeight() {
            // debugger
            let _self = this
            let size = 0
            let contentBoxHeight = this.contentBoxHeight
            if (typeof (contentBoxHeight) == 'string') {
                contentBoxHeight = contentBoxHeight.replace("px", '')
            }
            if (contentBoxHeight == 0 && (!!this.designState || this.previewState)) {
                // 设计模式适，直接返回默认值
                return `300px`
            }
            let optionHeight = Number(this.field.options.containerSize.height)
            if (!!optionHeight) {
                if (optionHeight > 1) {
                    size = `${optionHeight}px`
                } else {
                    // 小于1时， 百分百占用高度
                    let maxHeight = (contentBoxHeight * optionHeight) + 'px'
                    size = maxHeight
                }

            } else {
                // 默认
                if (contentBoxHeight > 0) {
                    size = parseInt(contentBoxHeight) + 'px'
                    // return `222px`
                } else {
                    size = ``
                }
            }
            if (!this.designState && !this.previewState) {
                setTimeout(() => {
                    if (_self.echartsInstance) {
                        _self.echartsInstance.resize();
                    }

                }, 300)
            }
            return size

        },
        // 加载图表配置信息
        loadData() {
            let _self = this
            this.$nextTick(() => {
                //debugger
                let chartDom = _self.$refs[_self.field.options.name];
                if (!!_self.field.options.backgroundColor) {
                    this.tdTheme.backgroundColor = _self.field.options.backgroundColor
                }
                // 开发环境使用$echarts 正式环境 this.$echarts
                  const _echarts = process.env.NODE_ENV === 'development' ? $echarts : _self.$echarts; 
                  let myChart = _echarts.init(chartDom, this.tdTheme);
                 // let myChart = _self.$echarts.init(chartDom, this.tdTheme);
            // ====== 修正：定义 dataAxis、dataLength、zoomSize ======

            let _option = this.getOptions();
            _option && myChart.setOption(_option);
            let zoomSize = 6;
            let dataAxis = [];
            // 优先从 xAxis.data 获取
            if (_option.xAxis && _option.xAxis.data) {
                dataAxis = _option.xAxis.data;
            } else if (_option.dataset && _option.dataset.source && Array.isArray(_option.dataset.source)) {
                // 尝试从 dataset 提取
                let xKey = _self.field.options.xAxisKey;
                dataAxis = _option.dataset.source.map(item => item[xKey]);
            }
            let dataLength = dataAxis.length;
                // 处理点击事件 缩放柱图
                myChart.on('click', function (params) {
                     // 判断是否为柱状图
                    if (params.seriesType === 'bar' && dataAxis.length > 0) {
                        let startValue = dataAxis[Math.max(params.dataIndex - Math.floor(zoomSize / 2), 0)];
                        let endValue = dataAxis[Math.min(params.dataIndex + Math.floor(zoomSize / 2), dataLength - 1)];
                        myChart.dispatchAction({
                            type: 'dataZoom',
                            startValue: startValue,
                            endValue: endValue
                        });
                    }
                    _self.handlerEcharClickEvent(params)

                    // params 包含如下属性字段：{
                    // $vars: (3) ["seriesName", "name", "value"]
                    // borderColor: undefined
                    // color: "#014d64"
                    // componentIndex: 1
                    // componentSubType: "bar"
                    // componentType: "series"
                    // data: {分组: "6", 分隔: 1.6, 频数: 8, 拟合曲线（总体）: 2.081932565, 拟合曲线（组内）: 2.021320374, …}
                    // dataIndex: 5
                    // dataType: undefined
                    // dimensionNames: (4) ["分组", "拟合曲线（总体）", "频数", "拟合曲线（组内）"]
                    // encode: {x: Array(1), y: Array(1)}
                    // event: {type: "click", event: PointerEvent, target: Rect, topTarget: Rect, cancelBubble: false, …}
                    // name: "6"
                    // seriesId: "频数0"
                    // seriesIndex: 1
                    // seriesName: "频数"
                    // seriesType: "bar"
                    // type: "click"
                    // value: {分组: "6", 分隔: 1.6, 频数: 8, 拟合曲线（总体）: 2.081932565, 拟合曲线（组内）: 2.021320374, …}
                    //alert('点击的数值是：' + params.value)
                    //  }
                    // _self.triggerCtrlNames(params)
                })

                myChart.on('datazoom', function (params) {
                    // console.log("======datazoom change=======")
                    // console.log(params);
                    _self.dataZoomChangeObj = params
                    // console.log("======datazoom change=======")
                });
                _self.echartsInstance = myChart
                let option;

                option = this.getOptions()
                //debugger
                // console.log("==========option=========")
                // console.log(option)
                // console.log(JSON.stringify(option))
                // console.log("==========option=========")
                option && myChart.setOption(option);
            });
        },
        // 处理图形点击事件回调
        handlerEcharClickEvent(params) {
            // debugger
            if (this.originColor == '') {
                this.originColor = params.color
            }
            let currentSeriesList = this.field.options.series.filter(item => {
                if (item.seriesName == params.seriesName) {
                    return item
                }
            })
            if (currentSeriesList && currentSeriesList.length > 0) {
                this.currentSeries = currentSeriesList[0]
                if (!!this.currentSeries.clickEventType) {
                    switch (this.currentSeries.clickEventType) {
                        case "showModalHightLightFn":
                            this.showModalHightLightFn()
                            break;
                        case "showHightLight":
                            // debugger
                            if (this.selectedDataIndex != null && this.selectedDataIndex == params.dataIndex) {
                                // 再次点击  取消选中 高亮
                                this.selectedDataIndex = null
                                this.currentSelectedData = []
                                this.publicAttribute.row = null
                                let tempData = {
                                    key: this.$route.fullPath,
                                    value: {}
                                }
                                this.$store.commit("set_ctrlData", tempData)
                                this.triggerCtrlNames({})
                            } else {
                                // 首次点击 选中 高亮
                                this.selectedDataIndex = params.dataIndex
                                this.currentSelectedData = [params.value]
                                // debugger
                                this.publicAttribute.row = params.value
                                // 保存临时变量数据，只能在相同页面的控件传值
                                let tempData = {
                                    key: this.$route.fullPath,
                                    value: params
                                }
                                this.$store.commit("set_ctrlData", tempData)
                                console.log(" x 轴当前点击name：" + params.name)
                                this.triggerCtrlNames(params)
                            }
                            break;

                        default:
                            break;
                    }
                }

            }
        },
        // 处理鼠标悬停事件触发
        handlerEcharMouseoverEvent(n) {

            if (!!this.field.options.enableDataZoom && this.field.options.hasOwnProperty("xyDataZoom") && this.field.options.xyDataZoom.length > 0) {
                // 拿到当前图表配置   
                let curOption = this.getOptions()
                // 克隆复制dataZoom
                let xyDataZoom_cloneData = cloneDeep(curOption.dataZoom)
                // 修改克隆数据dataZoom
                xyDataZoom_cloneData = xyDataZoom_cloneData.map(item => {
                    if (item.type == "slider") {
                        // 修改为显示
                        item.show = !this.useMouseInElementObj?.isOutside
                        if (this.dataZoomChangeObj && Object.keys(this.dataZoomChangeObj).length > 0) {
                            // 修复数据 改变后，再次触发 数据回滚原始状态了，此处保留上次移动的状态
                            item.start = this.dataZoomChangeObj.start
                            item.end = this.dataZoomChangeObj.end
                        }
                    }
                    return item
                })
                // 覆盖当前图表dataZoom修改，并且重新加载
                let customOption = {
                    dataZoom: xyDataZoom_cloneData
                }
                curOption = Object.assign({}, curOption, customOption)
                //局部刷新 加载
                this.echartsInstance.setOption(curOption);

            }
        },
        // 显示弹框并高亮功能
        showModalHightLightFn() {
            this.modalContent = this.currentSeries.modalContent
            this.modalTitle = this.currentSeries.modalTitle
            this.showModal = this.currentSeries.show
        },
        // 触发控件列表
        triggerCtrlNames(postParams) {
            if (!!this.field.options.triggerCtrlNames) {
                let params = {
                    formCtrlName: "echart",
                    postParams: postParams,
                    triggerCtrlNames: this.field.options.triggerCtrlNames, //重点： 触发控件名称
                }
                this.useHandleVFormEventFn.reSearchData(params);
            }
        },
        // 设置全屏
        setFullScreenToolBox(option) {
            // debugger
            if (!this.fullscreenObj.isFullscreen) {
                this.fullscreenObj.enter()
            } else {
                this.fullscreenObj.exit()
            }
            this.debouncedFn_reloadOption()
        },
        // 获取图表配置信息
        getOptions() {
            let type = this.field.options.chartConfig.compType

            let option = {}
            switch (type) {
                // 趋势图
                case 'trend':
                    option = this.getOption_Trend()
                    break;
                // 柱状图
                case 'bar':
                    option = this.getOption_Bar()
                    break;
                // 混合图
                case 'mix':
                    option = this.getOption_Mix()
                    break;
                // 折线图    
                case 'line':
                    option = this.getOption_line()
                    break;
                // 散点图
                case 'scatter':
                    option = this.getOption_scatter()
                    break;
                // 饼图
                case 'pie':
                    option = this.getOption_Pie()
                    break;
                // 柱状时间状态图
                case 'barTimeStatus':
                    option = this.getOption_barTimeStatus()
                    break;
                default:
                    break;
            }
            return option
        },
        // 时间状态图 数据格式化
        barTimeStatusDataformat(dataList, types) {
            //debugger
            let _self = this
            let data = []
            for (let i = 0; i < dataList.length; i++) {
                let dataItem = dataList[i]
                let xItem = _self.field.options.xAxisKey // X轴配置字段
                let yItem = _self.field.options.series[0]
                //let typeItem = types.filter(a=>a.name == dataItem[xItem])[0];
                let startDay = dayjs(dataItem[_self.field.options.startDay]).format("YYYY-MM-DD HH:mm:ss")
                let endDay = dayjs(dataItem[_self.field.options.endDay]).format("YYYY-MM-DD HH:mm:ss")
                //debugger
                let duration = dataItem[yItem.columnName]
                data.push({
                    name: "",//typeItem.name,
                    //value: [0, baseTime, (baseTime += duration), duration,startDay,endDay],
                    value: [0, new Date(dataItem[_self.field.options.startDay]), new Date(dataItem[_self.field.options.endDay]), duration, startDay, endDay],
                    itemStyle: {
                        // normal: {
                        //     color: typeItem.color
                        // }
                    }
                });
            }
            return data
        },
        // 获取JSON设置格式-(柱状图表)
        getOption_barTimeStatus() {
            let _self = this
            let _dataset = {
                // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
                // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
                dimensions: [],
                source: _self.echartData,
            }
            // 获取数据集
            let dataset = this.getDataset(_dataset)
            // debugger
            let newGroupByDataObj = groupBy(dataset.source, dataset.dimensions[0])

            let types = [
                { name: 'JS Heap', color: '#7b9ce1' },
                { name: 'Documents', color: '#bd6d6c' },
                { name: 'Nodes', color: '#75d874' },
                { name: 'Listeners', color: '#e0bc78' },
                { name: 'GPU Memory', color: '#dc77dc' },
                { name: 'GPU', color: '#72b362' }
            ];
            if (newGroupByDataObj && Object.keys(newGroupByDataObj).length > 0) {
                let keysList = Object.keys(newGroupByDataObj)
                keysList.forEach((item, index) => {
                    types[index].name = item
                })
            } else {
                return
            }

            let data = [];
            // let startTime = +new Date(); 
            let firstItemDate = dataset.source[0][_self.field.options.startDay]
            let startTime = +new Date(firstItemDate)
            //debugger
            let categories = ['categoryA'];
            let baseTime = startTime;
            for (let i = 0; i < dataset.source.length; i++) {
                let dataItem = dataset.source[i]
                let xItem = dataset.dimensions[0]
                let yItem = _self.field.options.series[0]
                let typeItem = types.filter(a => a.name == dataItem[xItem])[0];
                let startDay = dayjs(dataItem[_self.field.options.startDay]).format("YYYY-MM-DD HH:mm:ss")
                let endDay = dayjs(dataItem[_self.field.options.endDay]).format("YYYY-MM-DD HH:mm:ss")
                //debugger
                let duration = dataItem[yItem.columnName]
                data.push({
                    name: typeItem.name,
                    //value: [0, baseTime, (baseTime += duration), duration,startDay,endDay],
                    value: [0, new Date(dataItem[_self.field.options.startDay]), new Date(dataItem[_self.field.options.endDay]), duration, startDay, endDay],
                    itemStyle: {
                        normal: {
                            color: typeItem.color
                        }
                    }
                });
            }
            let option = {
                title: this.getTitle(),
                tooltip: {
                    trigger: _self.field.options.chartConfig.tooltip.trigger,
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE,
                    // appendToBody: true,
                    // renderMode: 'html',
                    //  confine: true,    //是否将 tooltip 框限制在图表的区域内。
                    // extraCssText:"z-index:999999999"
                },
                grid: _self.field.options.grid,
                legend: {
                    show: true,
                    bottom: 0
                },
                xAxis: {
                    min: startTime,
                    max: (startTime + 60 * 60 * 1000 * 24),
                    // type: 'time',
                    splitNumber: 0,
                    maxInterval: 3600 * 1000,
                    scale: true,
                    axisLabel: {
                        interval: 1,
                        rotate: 50,
                        align: "center",
                        margin: 18,
                        formatter: function (val) {
                            //return Math.max(0, val - startTime) + ' ms';
                            return (new Date(val)).getHours() + ":00";
                        }
                    },

                    name: _self.field.options.xConfig.axisName,


                },
                yAxis: {
                    //type: 'value',
                    data: categories,
                    show: false,
                },
                series: [
                    // {
                    //     type: 'custom',
                    //     name:"1111",
                    //     renderItem: this.renderItem,
                    //     itemStyle: {
                    //         opacity: 1
                    //     },
                    //     dimensions: ['11', '22', '33', "44",'开始时间', '结束时间'],
                    //     encode: {
                    //         x: [1, 2],
                    //         y: 0,
                    //         tooltip: [4, 5],

                    //     },
                    //     data: data
                    // },

                ]
            }
            // 序列配置
            option.series = this.getOptionSeries_barTimeStatus(data, newGroupByDataObj, types)
            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend
            }
            return option
        },
        renderItem(params, api) {
            let categoryIndex = api.value(0);
            let start = api.coord([api.value(1), categoryIndex]);
            let end = api.coord([api.value(2), categoryIndex]);
            let height = api.size([0, 1])[1] * 0.6;
             // 开发环境使用$echarts 正式环境 this.$echarts
            const _echarts = process.env.NODE_ENV === 'development' ? $echarts : _self.$echarts; 
            let rectShape = _echarts.graphic.clipRectByRect(
                {
                    x: start[0],
                    y: start[1] - height / 2,
                    width: end[0] - start[0],
                    height: height
                },
                {
                    x: params.coordSys.x,
                    y: params.coordSys.y,
                    width: params.coordSys.width,
                    height: params.coordSys.height
                }
            );
            return (
                rectShape && {
                    type: 'rect',
                    transition: ['shape'],
                    shape: rectShape,
                    style: api.style()
                }
            );
        },

        // 获取JSON设置格式-(饼图图表)
        getOption_Pie() {
            let _self = this
            let option = {
                title: this.getTitle(),
                tooltip: {
                    trigger: _self.field.options.chartConfig.tooltip.trigger,
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE,
                },
                grid: _self.field.options.grid,
                dataset: {
                    // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
                    // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
                    dimensions: ["department", "spending", "budget", "date"],
                    source: _self.echartData
                },
                series: []
            };
            // 获取数据集
            option.dataset = this.getDataset(option.dataset)
            // 序列配置
            option.series = this.getOptionSeries_Pie()
            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend
            }
            return option
        },
        // 获取JSON设置格式-(柱状图表)
        getOption_Bar() {
            let _self = this
            let option = {
                title: this.getTitle(),
                tooltip: {
                    trigger: _self.field.options.chartConfig.tooltip.trigger,
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE,
                },
                grid: _self.field.options.grid,
                  // 添加 dataZoom 组件配置
                dataZoom: [
                    {
                        type: 'inside', // 内置型数据区域缩放组件
                        xAxisIndex: 0,  // x轴
                        start: 0,      // 数据窗口范围的起始百分比
                        end: 100,      // 数据窗口范围的结束百分比
                        zoomOnMouseWheel: true  // 触发方式为滚轮
                    }
                ],
                dataset: {
                    // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
                    // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
                    dimensions: ["department", "spending", "budget", "date"],
                    source: _self.echartData,
                },
                xAxis: {
                    name: _self.field.options.xConfig.axisName,
                    type: _self.field.options.xConfig.type,
                    axisLabel: {
                        interval: _self.field.options.xConfig.axisLabel.interval,
                        rotate: _self.field.options.xConfig.axisLabel.rotate
                    },
                    splitLine: {
                        show: !!_self.field.options.xConfig.splitLineShow ? true : false // 是否显示分隔线。默认 数值轴显示，
                    },
                    axisLine: {
                        show: !!_self.field.options.xConfig.hideAxisLineShow ? false : true // 是否显示坐标轴轴线
                    },
                    axisTick: {
                        inside: !!_self.field.options.xConfig.axisTickInside ? true : false, // 坐标轴刻度是否朝内，默认朝外
                        show: !!_self.field.options.xConfig.axisTickShow ? true : false //是否显示坐标轴刻度
                    },
                    // 坐标轴刻度最小值
                    min: function (value) {
                        if (_self.field.options.xConfig.hasOwnProperty("min") && _self.field.options.xConfig.min != 0 && value.min > _self.field.options.xConfig.min) {
                            return _self.field.options.xConfig.min
                        } else {
                            return value.min;
                        }

                    },
                    // 坐标轴刻度最大值
                    max: function (value) {
                        if (_self.field.options.xConfig.hasOwnProperty("max") && _self.field.options.xConfig.max != 0 && value.max < _self.field.options.xConfig.max) {
                            return _self.field.options.xConfig.max
                        } else {
                            return value.max;
                        }
                    }
                },
                yAxis: {
                    name: _self.field.options.yConfig.axisName,
                    type: _self.field.options.yConfig.type,
                    axisLabel: {
                        // Y轴格式化 {value} °C
                        formatter: !!_self.field.options.yConfig.formatter.value ? _self.field.options.yConfig.formatter.value : '{value}'
                    },
                    splitLine: {
                        show: !!_self.field.options.yConfig.hideSplitLineShow ? false : true // 是否显示分隔线。默认数值轴显示，类目轴不显示
                    },
                    axisLine: {
                        show: (!!_self.field.options.yConfig.axisLineShow ? true : false), // 是否显示坐标轴轴线
                        lineStyle: {
                            color: !!_self.field.options.yConfig.axisLineStyleColor ? _self.field.options.yConfig.axisLineStyleColor : ""
                        }
                    },
                    axisTick: {
                        inside: !!_self.field.options.yConfig.axisTickInside ? true : false, // 坐标轴刻度是否朝内，默认朝外
                        show: !!_self.field.options.yConfig.axisTickShow ? true : false //是否显示坐标轴刻度
                    },
                    // Y坐标轴刻度最小值
                    min: function (value) {
                        if (_self.field.options.yConfig.hasOwnProperty("min") && _self.field.options.yConfig.min != 0 && value.min > _self.field.options.yConfig.min) {
                            return _self.field.options.yConfig.min
                        } else {
                            return value.min;
                        }

                    },
                    // Y坐标轴刻度最大值
                    max: function (value) {
                        if (_self.field.options.yConfig.hasOwnProperty("max") && _self.field.options.yConfig.max != 0 && value.max < _self.field.options.yConfig.max) {
                            return _self.field.options.yConfig.max
                        } else {
                            return value.max;
                        }
                    }
                },
                series: []
            };
            // 获取数据集
            option.dataset = this.getDataset(option.dataset)
            // X轴方向:垂直，默认为水平方向
            if (_self.field.options.xAxisDirection == 'verticle') {
                option.xAxis.type = _self.field.options.yConfig.type
                // 反转数据
                option.yAxis.type = _self.field.options.xConfig.type
            }

            // 序列配置
            option.series = this.getOptionSeries()
            // 动态添加参数配置
            option.series.map(item => {
                let type = _self.field.options.chartConfig.comp.type
                switch (type) {
                    // 基础柱状图
                    case 'basic':
                        item.stack = false
                        break;
                    // 堆叠柱状图
                    case 'total':
                        item.stack = true
                        break;
                    default:

                        break;
                }
                return item
            })
            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend
            }
   
            return option
        },

        // 获取JSON设置格式-(折线图表)
        getOption_Trend(smooth = false) {
            let _self = this
            let option = {
                title: this.getTitle(),
                tooltip: {
                    trigger: 'item',
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE,
                    transitionDuration: 0
                },
                grid: _self.field.options.grid,
                xAxis: {
                    name: "",// X轴名称
                    type: 'category',
                    // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                    name: "",// Y轴名称
                    type: 'value',
                    axisLabel: {
                        formatter: !!_self.field.options.yConfig.formatter.value ? _self.field.options.yConfig.formatter.value : '{value}',
                        interval: _self.field.options.xConfig.axisLabel.interval,// 0强制显示所有，不间隔不显示，如果为数字，间隔几个显示
                        rotate: _self.field.options.xConfig.axisLabel.rotate // 刻度标签旋转的角度,在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。
                    }
                },
                series: []
            };
            // X轴分组分类
            option.xAxis.data = this.getOptionXAxisData_trend()
            // 序列配置
            option.series = this.getOptionSeries_trend(option)
            // 动态添加参数配置
            option.series.map(item => {
                let type = _self.field.options.chartConfig.comp.type
                switch (type) {
                    // 区域面积图
                    case 'areaStyle':
                        item.areaStyle = {}
                        break;
                    // 堆叠折线图
                    case 'stack':
                        item.stack = 'total'
                        break;
                    default:
                        // 基础折线图
                        // delete操作符移除对象指定属性，删除成功返回true，否则返回false。
                        // 删除的属性不存在，delete不会起任何作用 ，但是仍会返回true。
                        delete item.areaStyle
                        delete item.stack
                        break;
                }
                return item
            })

            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend  // 位置控制
                //debugger
                option.legend.data = this.getOptionLegendData_trend() //['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
            }
            return option
        },
        getOptionLegendData_trend() {
            let defaultSeries = []
            let dataObj = groupBy(this.echartData, this.field.options.trendGroupByField)
            for (const [key, val] of Object.entries(dataObj)) {
                defaultSeries.push(key)
            }
            return defaultSeries
        },
        // 获取趋势图 X轴 数据列表 也就是图例列表
        getOptionXAxisData_trend() {
            let defaultSeries = []
            let dataObj = groupBy(this.echartData, this.field.options.xAxisKey)
            for (const [key, val] of Object.entries(dataObj)) {
                defaultSeries.push(key)
            }
            return defaultSeries
        },
        //num请以字符串形式传入
        commafy(num, { spaceNumber = 3, separator = ',' }) {
            if (!num) {
                return ''
            }
            num = num.split(".");  // 分隔小数点
            let arr = num[0].split("").reverse();  // 转换成字符数组并且倒序排列
            let res = [];
            for (let i = 0, len = arr.length; i < len; i++) {
                if (i % spaceNumber === 0 && i !== 0) {
                    res.push(separator);   // 添加分隔符
                }
                res.push(arr[i]);
            }
            res.reverse(); // 再次倒序成为正确的顺序
            if (num[1]) {  // 如果有小数的话添加小数部分
                res = res.join("").concat("." + num[1]);
            } else {
                res = res.join("");
            }
            return res;
        },
        // 混合图形 格式化提示框 数据格式
        mixTooltipFormatter(params, type = 'array') {
            // debugger
            //console.log("--x轴类目对应的参数数组--", params); // 比如当鼠标hover到孙悟空同学这一列的时候，这个params数组存放的每一项数据分别是语数外三门成绩的具体信息
            let valueFormatSuffix = ""// 值后缀修饰符
            let _formatterHtml = `<div class='flex flex-col'><div>${params[0].axisValue}</div>`
            if (type == 'object') {
                _formatterHtml = `<div class='flex flex-col'><div>${params[0].name}</div>`
            }
            let itemList = ``
            // 数组
            params.forEach((item, index) => {
                // 千分符 XEUtils.commafy
                let dataValue = this.commafy(XEUtils.toString(Number(item.data[item.seriesName])), { spaceNumber: 3, separator: ',' })
                try {
                    let optionList = this.field.options.series.filter(optionItem => {
                        return item.seriesName == optionItem.seriesName
                    })
                    if (optionList.length > 0) {
                        if (!!optionList[0]?.chartConfig?.comp?.formatTooltipSuffix) {
                            valueFormatSuffix = optionList[0]?.chartConfig?.comp?.formatTooltipSuffix
                        }
                    }
                } catch (error) {

                }
                dataValue = dataValue + valueFormatSuffix
                if (!itemList) {

                    itemList = ` <div class='flex justify-between'><div>${item.marker} ${item.seriesName}</div><div>&nbsp;&nbsp;</div><div><b>${dataValue}</b></div></div> `
                } else {
                    itemList = itemList + `<div class='flex justify-between'><div>${item.marker} ${item.seriesName}</div><div>&nbsp;&nbsp;</div><div><b>${dataValue}</b></div></div>`
                }
            })
            _formatterHtml = _formatterHtml + itemList
            _formatterHtml = _formatterHtml + `</div>`
            return _formatterHtml
        },
        // 获取JSON设置格式-(柱状图表)
        getOption_Mix() {
            let _self = this
            let option = {
                title: this.getTitle(),
                // dataZoom: [
                //     {
                //         id: 'dataZoomX',
                //         type: 'slider',
                //         xAxisIndex: [0],
                //         filterMode: 'filter', // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
                //         start: 30,
                //         end: 70
                //     },   
                // ],
                tooltip: {
                    trigger: _self.field.options.chartConfig.tooltip.trigger,
                    transitionDuration: 0,
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE
                    //confine: true,    //是否将 tooltip 框限制在图表的区域内。
                    formatter: function (params) {
                        if (Array.isArray(params)) {
                            return _self.mixTooltipFormatter(params, 'array')
                        } else {
                            return _self.mixTooltipFormatter([params], 'object')
                        }
                    },
                    axisPointer: {
                        type: 'none' // 去除指示线。
                    }
                },
                // 工具栏
                toolbox: {
                    show: true,
                    feature: {
                        myFullscreen: {
                            show: _self.fullscreenObj.isFullscreen,// 只有全屏时，才显示关闭按钮
                            title: _self.fullscreenObj.isFullscreen ? '取消全屏' : '全屏查看',
                            icon: _self.fullscreenObj.isFullscreen ? _self.unFullIconPath : _self.fullIconPath,
                            onclick: function () {
                                _self.setFullScreenToolBox(option)
                            }
                        }
                    }
                },
                grid: _self.field.options.grid,
                dataset: {
                    // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
                    // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
                    dimensions: ["department", "spending", "budget", "date"],
                    source: _self.echartData,
                },
                xAxis: {
                    name: _self.field.options.xConfig.axisName,
                    type: _self.field.options.xConfig.type,
                    boundaryGap: !_self.field.options.xConfig.boundaryGap,
                    axisLabel: {
                        show: !!_self.field.options.xConfig.hideAxisLabelShow ? false : true,
                        interval: _self.field.options.xConfig.axisLabel.interval,
                        rotate: _self.field.options.xConfig.axisLabel.rotate
                    },
                    splitLine: {
                        show: !!_self.field.options.xConfig.splitLineShow ? true : false // 是否显示分隔线。默认 数值轴显示，
                    },
                    axisLine: {
                        show: !!_self.field.options.xConfig.hideAxisLineShow ? false : true // 是否显示坐标轴轴线
                    },
                    axisTick: {
                        inside: !!_self.field.options.xConfig.axisTickInside ? true : false, // 坐标轴刻度是否朝内，默认朝外
                        show: !!_self.field.options.xConfig.axisTickShow ? true : false //是否显示坐标轴刻度
                    },
                    // 坐标轴刻度最小值
                    min: function (value) {
                        if (_self.field.options.xConfig.hasOwnProperty("min") && _self.field.options.xConfig.min != null) {
                            return _self.field.options.xConfig.min
                        } else {
                            return value.min;
                        }

                    },
                    // 坐标轴刻度最大值
                    max: function (value) {

                        if (_self.field.options.xConfig.hasOwnProperty("max") && _self.field.options.xConfig.max != null && value.max < _self.field.options.xConfig.max) {
                            return _self.field.options.xConfig.max
                        } else {
                            return value.max;
                        }
                    }
                },
                yAxis: [
                    {
                        name: _self.field.options.yConfig.axisName,
                        type: _self.field.options.yConfig.type,
                        axisLabel: {
                            show: !!_self.field.options.yConfig.hideAxisLabelShow ? false : true,
                            // Y轴格式化 {value} °C
                            formatter: !!_self.field.options.yConfig.formatter.value ? _self.field.options.yConfig.formatter.value : '{value}'
                        },
                        splitLine: {
                            show: !!_self.field.options.yConfig.hideSplitLineShow ? false : true // 是否显示分隔线。默认数值轴显示，类目轴不显示
                        },
                        axisLine: {
                            show: (!!_self.field.options.yConfig.axisLineShow ? true : false), // 是否显示坐标轴轴线
                            lineStyle: {
                                color: !!_self.field.options.yConfig.axisLineStyleColor ? _self.field.options.yConfig.axisLineStyleColor : ""
                            }
                        },
                        axisTick: {
                            inside: !!_self.field.options.yConfig.axisTickInside ? true : false, // 坐标轴刻度是否朝内，默认朝外
                            show: !!_self.field.options.yConfig.axisTickShow ? true : false //是否显示坐标轴刻度
                        },
                        // Y坐标轴刻度最小值
                        min: function (value) {
                            //return 0 && _self.field.options.yConfig.min != 0 && value.min > _self.field.options.yConfig.min
                            if (_self.field.options.yConfig.hasOwnProperty("min") && _self.field.options.yConfig.min != null) {
                                return _self.field.options.yConfig.min
                            } else {
                                return value.min;
                            }

                        },
                        // Y坐标轴刻度最大值
                        max: function (value) {
                            if (_self.field.options.yConfig.hasOwnProperty("max") && _self.field.options.yConfig.max != null && value.max < _self.field.options.yConfig.max) {
                                return _self.field.options.yConfig.max
                            } else {
                                return value.max;
                            }
                        }
                    },

                ],
                series: []
            };
            // Y轴设置保留几位小数位
            if (_self.field.options.yConfig.valueToFixed && _self.field.options.yConfig.valueToFixed != 0) {
                option.yAxis[0].axisLabel.formatter = function (value, index) {
                    let _formatterValueStr = _self.field.options.yConfig.formatter.value.replace("{value}", "")
                    return value.toFixed(_self.field.options.yConfig.valueToFixed) + _formatterValueStr
                }
            }
            // 多条Y轴
            if (_self.field.options.yAxisExList && _self.field.options.yAxisExList.length > 0) {
                let formatYList = this.formatYAxisExList()
                //debugger
                option.yAxis = option.yAxis.concat(formatYList)

            }
            // 序列配置
            option.series = this.getOptionSeries_Mix(option)

            // 获取数据集
            option.dataset = this.getDataset(option.dataset)

            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend
            }

            // 是否 开启区域缩放 
            if (!!_self.field.options.enableDataZoom) {
                option.dataZoom = []
                if (_self.field.options.hasOwnProperty("xyDataZoom")) {
                    _self.field.options.xyDataZoom.forEach(item => {
                        let newItem = {
                            type: item.type,
                            show: false,
                            top: item.top,
                            right: item.right,
                            bottom: item.bottom,
                            left: item.left,
                            handleSize: 32, // 两边的按钮大小

                        }
                        // 固定显示几组数据 适应于动态数据
                        if (item.rangeType && item.rangeType == 'hardRange' && item.rangeTypeValue > 0) {
                            newItem.startValue = option.series.length - item.rangeTypeValue //显示几组
                            newItem.endValue = option.series.length - 1

                        }
                        // 自定义显示范围 适应于固定数组
                        if (item.rangeType && item.rangeType == 'customRange') {
                            newItem.start = item.start
                            newItem.end = item.end
                        }
                        newItem[item.AxisName] = [item.AxisIndex]   // ==> yAxisIndex: [0] or xAxisIndex: [0],
                        option.dataZoom.push(newItem)
                    })
                }
            }
            // debugger
            return option
        },
        // 格式化Y轴数据
        formatYAxisExList() {
            let newYlist = []
            let dataList = this.field.options.yAxisExList
            dataList.forEach(oldItem => {
                let newItem = {
                    name: oldItem.axisName,
                    type: oldItem.type,
                    axisLabel: {
                        show: !!oldItem.hideAxisLabelShow ? false : true,
                        // Y轴格式化 {value} °C
                        formatter: !!oldItem.labelformatValue ? oldItem.labelformatValue : '{value}'
                    },
                    splitLine: {
                        show: !!oldItem.hideSplitLineShow ? false : true // 是否显示分隔线。默认数值轴显示，类目轴不显示
                    },
                    axisLine: {
                        show: (!!oldItem.axisLineShow ? true : false), // 是否显示坐标轴轴线
                        lineStyle: {
                            color: !!oldItem.axisLineStyleColor ? oldItem.axisLineStyleColor : ""
                        }
                    },
                    axisTick: {
                        inside: !!oldItem.axisTickInside ? true : false, // 坐标轴刻度是否朝内，默认朝外
                        show: !!oldItem.axisTickShow ? true : false //是否显示坐标轴刻度
                    }
                }
                // if(!!oldItem.axisLineShow){
                //     newYlist.push(newItem)
                // }
                newYlist.push(newItem)
            })
            return newYlist
        },
        // 获取最新数据集
        getDataset(dataSet = []) {
            // debugger
            let newDataSet = dataSet
            let dimensions = []
            let dataSeries = this.field.options.series
            dataSeries.forEach(item => {
                // 是否隐藏图例
                // if(!!!item.chartConfig.comp.hideLegend){

                // }
                dimensions.push(item.columnName)
            })
            // xAxisKey: "department",// x轴显示字段 放到第一个
            let firstField = this.field.options.xAxisKey
            if (!!firstField) {
                dimensions.unshift(firstField)
            }
            newDataSet.dimensions = dimensions
            newDataSet.source = this.echartData
            return newDataSet
        },
        // 获取混合序列数据配置信息
        getOptionSeries_Mix(dataOptions) {
            // debugger
            let defaultSeries = []
            let _self = this
            let dataSeries = this.field.options.series
            let totalItems = dataSeries.filter(item => {
                if (item.show) {
                    return item
                }
            }).length
            if (dataSeries && dataSeries.length > 0) {
                dataSeries.forEach((item, index) => {
                    // 是否为目标线
                    if (!!item.isTargetLine) {
                        // 直接修改类型为目标线
                        item.chartConfig.compType = 'line'
                    }
                    if (!!!item.chartConfig.comp.yAxisIndex) {
                        item.chartConfig.comp.yAxisIndex = 0 // 强制设置默认为0
                    }
                    // 配置刚好 使用柱图，不去设置其它时，可能出现没有chartConfig 配置
                    if (!!!item.hasOwnProperty("chartConfig")) {
                        item.chartConfig = {
                            compType: "bar",
                            comp: {
                                yAxisIndex: 0,
                                type: "basic",

                            },
                            tooltip: {
                                trigger: "item"
                            }
                        }
                    }

                    if (item.hasOwnProperty("chartConfig")) {
                        // 共用参数设置
                        let newItem = {
                            name: "",
                            // 图表类型
                            type: item.chartConfig.compType,
                            yAxisIndex: (!!item.chartConfig.comp.yAxisIndex && (_self.field.options.yAxisExList && _self.field.options.yAxisExList.length > 0)) ? item.chartConfig.comp.yAxisIndex : 0 // Y轴的下标
                        }
                        //debugger
                        switch (item.chartConfig.compType) {
                            case "bar":
                                let _barConfig = {
                                    // 是否显示背景
                                    showBackground: false,
                                    // 是否堆叠
                                    stack: false,
                                    //显示标签
                                    label: {
                                        show: !!item.chartConfig.comp.showLabel,
                                        position: 'top',
                                        //formatter:"{@[1]}",//(!!item.chartConfig.comp.formatLabelStr ? item.chartConfig.comp.formatLabelStr : "{@[1]}"),
                                    },
                                    itemStyle: {
                                        //color: item.chartConfig.comp.barColor.value
                                        color: function (params) {

                                            //  debugger
                                            // 点击后 高亮
                                            if (_self.selectedDataIndex == params.dataIndex) {
                                                // 自定义高亮颜色
                                                if (item.chartConfig.comp?.barLightColor?.value) {
                                                    return item.chartConfig.comp.barLightColor.value
                                                } else {
                                                    // 默认高亮颜色
                                                    return '#FFD700'
                                                }

                                            } else {
                                                if (!!item?.chartConfig?.comp?.barColor?.value) {
                                                    // console.log("====color:function(params){===")
                                                    return item.chartConfig.comp.barColor.value
                                                } else {
                                                    // 默认系统颜色
                                                    return params.color
                                                }
                                            }
                                        }
                                    },

                                    markLine: {
                                        symbol: "none",
                                        data: [
                                            // {
                                            //     xAxis: '行政部',
                                            //     symbol: "none",
                                            //     label:{
                                            //         color: "red",
                                            //         formatter: '1111'
                                            //     },
                                            //     lineStyle: {
                                            //         color: "red",
                                            //         type:'solid',
                                            //     }
                                            // }
                                        ]
                                    }
                                }
                                newItem = Object.assign({}, _barConfig, newItem)
                                if (item.markLineData && item.markLineData.length > 0) {
                                    item.markLineData.forEach(markLineItem => {
                                        let newMarkItem = {
                                            xAxis: (markLineItem.valueType == 'number' ? Number(markLineItem.xAxis) : markLineItem.xAxis),
                                            //y: "90%",
                                            symbol: "none",
                                            label: {
                                                color: markLineItem.color,
                                                formatter: markLineItem.formatLabelStr
                                            },
                                            lineStyle: {
                                                width: 2,
                                                //dashOffset: 120,
                                                color: markLineItem.color,
                                                type: markLineItem.type,
                                            }
                                        }
                                        if (!!markLineItem.xAxis) {
                                            newItem.markLine.data.push(newMarkItem)
                                        }

                                    })

                                }
                                let _subBarType = item.chartConfig.comp.type
                                switch (_subBarType) {
                                    // 基础柱状图
                                    case 'basic':
                                        newItem.stack = false
                                        break;
                                    // 堆叠柱状图
                                    case 'total':
                                        newItem.stack = true
                                        break;
                                    default:

                                        break;
                                }
                                break;
                            case "line":
                                let _lineConfig = {
                                    smooth: !!item.chartConfig.comp.smooth,
                                    label: {
                                        show: !!item.chartConfig.comp.showLabel,
                                        position: 'top',
                                        //formatter:(!!item.chartConfig.comp.formatLabelStr ? item.chartConfig.comp.formatLabelStr : "{@[1]}"),
                                        formatter: function (params) {
                                            let labelValCtrlData = item.labelValCtrlData
                                            if (labelValCtrlData && labelValCtrlData.length > 0) {
                                                // 条件判断 显示标签颜色 conditionType value color
                                                let showColor = ''
                                                // return '{error|' + params.data[params.seriesName] +item.chartConfig.comp.formatLabelStr+ '}';
                                                for (let index = 0; index < labelValCtrlData.length; index++) {
                                                    const element = labelValCtrlData[index];
                                                    let currentVal = Number(params.data[params.seriesName])
                                                    let compareVal = Number(element.value)
                                                    switch (element.conditionType) {
                                                        case "GreaterThan": //大于
                                                            if (currentVal > compareVal) {
                                                                showColor = element.color
                                                            }
                                                            break;
                                                        case "GreaterThanOrEqual": //大于等于
                                                            if (currentVal >= compareVal) {
                                                                showColor = element.color
                                                            }
                                                            break;
                                                        case "Equal": //等于
                                                            if (currentVal == compareVal) {
                                                                showColor = element.color
                                                            }
                                                            break;
                                                        case "LessThan": //小于
                                                            if (currentVal < compareVal) {
                                                                showColor = element.color
                                                            }
                                                            break;
                                                        case "LessThanOrEqual": //小于等于
                                                            if (currentVal <= compareVal) {
                                                                showColor = element.color
                                                            }
                                                            break;
                                                        default:
                                                            showColor = ""
                                                            break;
                                                    }
                                                }

                                                if (!!item.chartConfig.comp.formatLabelStr) {
                                                    // 值+ 修饰符
                                                    if (!!showColor) {
                                                        return `{${showColor}|` + params.data[params.seriesName] + item.chartConfig.comp.formatLabelStr + '}';
                                                    } else {
                                                        return params.data[params.seriesName] + item.chartConfig.comp.formatLabelStr
                                                    }

                                                } else {
                                                    // 默认
                                                    if (!!showColor) {
                                                        return `{${showColor}|` + params.data[params.seriesName] + '}';
                                                    } else {
                                                        return params.data[params.seriesName]
                                                    }

                                                }

                                            } else {
                                                if (!!item.chartConfig.comp.formatLabelStr) {
                                                    // 值+ 修饰符
                                                    return params.data[params.seriesName] + item.chartConfig.comp.formatLabelStr
                                                } else {
                                                    // 默认
                                                    return params.data[params.seriesName]
                                                }
                                            }
                                        },
                                        rich: {
                                            normal: {
                                                color: '#67C23A' // 绿色
                                            },
                                            error: {
                                                color: '#F56C6C' // 红色
                                            },
                                            warning: {
                                                color: '#E6A23C' // 橙色
                                            },
                                            info: {
                                                color: '#409EFF' // 蓝色
                                            }
                                        }
                                    },
                                    itemStyle: {
                                        opacity: 1,
                                        //borderColor: 'red'
                                    },
                                    lineStyle: {
                                        width: (!!item.targetLineWidth ? item.targetLineWidth : 2),
                                        type: !!item.chartConfig.compTypeLineType ? item.chartConfig.compTypeLineType : 'solid'
                                    },
                                    markLine: {
                                        symbol: "none",
                                        data: [
                                            // {
                                            //     xAxis: '行政部',
                                            //     symbol: "none",
                                            //     label:{
                                            //         color: "red",
                                            //         formatter: '1111'
                                            //     },
                                            //     lineStyle: {
                                            //         color: "red",
                                            //         type:'solid',
                                            //     }
                                            // }
                                        ]
                                    }
                                }
                                newItem = Object.assign({}, _lineConfig, newItem)
                                if (item.markLineData && item.markLineData.length > 0) {
                                    item.markLineData.forEach(markLineItem => {
                                        let newMarkItem = {
                                            xAxis: (markLineItem.valueType == 'number' ? Number(markLineItem.xAxis) : markLineItem.xAxis),
                                            //y: "90%",
                                            symbol: "none",
                                            label: {
                                                color: markLineItem.color,
                                                formatter: markLineItem.formatLabelStr
                                            },
                                            lineStyle: {
                                                width: 2,
                                                //dashOffset: 120,
                                                color: markLineItem.color,
                                                type: markLineItem.type,
                                            }
                                        }
                                        if (!!markLineItem.xAxis) {
                                            newItem.markLine.data.push(newMarkItem)
                                        }

                                    })

                                }
                                try {
                                    // 可能为空 报错
                                    if (!!item.chartConfig.comp.itemColor.value && !!item.chartConfig.comp.lineColor.value) {
                                        newItem.itemStyle.color = item.chartConfig.comp.itemColor.value


                                    }
                                    if (!!item.chartConfig.comp.itemColor.value && !!item.chartConfig.comp.lineColor.value) {
                                        newItem.lineStyle.color = item.chartConfig.comp.lineColor.value

                                    }


                                    // 隐藏折线点
                                    if (!!item.chartConfig.comp.hideItemNode) {
                                        newItem.itemStyle.opacity = 0
                                    } else {
                                        newItem.itemStyle.opacity = 1
                                    }

                                } catch (error) {

                                }

                                let _subLineType = item.chartConfig.comp.type
                                switch (_subLineType) {
                                    // 区域面积图
                                    case 'areaStyle':
                                        newItem.areaStyle = {}
                                        break;
                                    // 堆叠折线图
                                    case 'stack':
                                        newItem.stack = 'total'
                                        break;
                                    default:
                                        // 基础折线图
                                        // delete操作符移除对象指定属性，删除成功返回true，否则返回false。
                                        // 删除的属性不存在，delete不会起任何作用 ，但是仍会返回true。
                                        delete newItem.areaStyle
                                        delete newItem.stack
                                        break;
                                }
                                // 目标线
                                let _targetlineConfig = {
                                    name: "",
                                    type: "line",
                                    lineStyle: {
                                        width: !!item.targetLineWidth ? item.targetLineWidth : 2,
                                        type: !!item.targetLineType ? item.targetLineType : 'dashed',
                                        color: !!item.targetLineColor ? item.targetLineColor : '#E3B76D',
                                    },
                                    itemStyle: {
                                        opacity: 0
                                    }

                                }
                                if (!!item.isTargetLine) {
                                    // 目标线 逻辑处理
                                    newItem = _targetlineConfig
                                    this.echartData = this.echartData.map(dataItem => {
                                        dataItem[item.columnName] = Number(item.targetLineValue)
                                        return dataItem
                                    })
                                }

                                break;


                            case "pie":
                                let _pieConfig = {
                                    label: {
                                        // 是否显示内容
                                        show: !!item.chartConfig.comp.showLabel,
                                    },
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                                newItem = Object.assign({}, _pieConfig, newItem)
                                // 是否显示数值
                                if (!!item.chartConfig.comp.showLabelValue) {
                                    newItem.label.formatter = `{b}:({@[${index + 1}]})`
                                }
                                // newItem.roseType = true // 默认值
                                if (item.chartConfig.comp.roseType == 'area') {
                                    newItem.roseType = 'area'
                                }
                                // 重点关注
                                newItem.encode = {
                                    itemName: _self.field.options.xAxisKey,    // X轴名称
                                    value: item.columnName // 当前系列名称
                                }
                                // 动态计算偏移量
                                if (totalItems == 1) {
                                    if (!!item.chartConfig.comp.roseType) {
                                        newItem.radius = ['40%', ((100 / totalItems) - 30 + '%')]
                                    } else {
                                        newItem.radius = ((100 / totalItems) - 30 + '%')
                                    }

                                } else {
                                    if (!!item.chartConfig.comp.roseType) {
                                        newItem.radius = ['40%', ((100 / totalItems) + '%')]
                                    } else {
                                        newItem.radius = ((100 / totalItems) + '%')
                                    }

                                }
                                newItem.bottom = '10%'
                                newItem.center = ['50%', '50%']
                                newItem.left = (index) * (100 / totalItems) + '%'
                                newItem.right = [100 - (index + 1) * (100 / totalItems)] + '%'
                                break;
                            default:

                                break;
                        }
                        if (item.show) {
                            newItem.name = item.seriesName
                            defaultSeries.push(newItem)
                        }
                    }

                })
            }
            return defaultSeries
        },
        mixLabelFormatter() {

        },
        // 获取序列数据配置信息
        getOptionSeries() {
            let defaultSeries = []
            let _self = this
            let dataSeries = this.field.options.series
            if (dataSeries && dataSeries.length > 0) {
                dataSeries.forEach(item => {
                    let newItem = {
                        name: "",
                        // 图表类型
                        type: 'bar',
                        // 是否显示背景
                        showBackground: false,
                        // 是否堆叠
                        stack: false,
                        //显示标签
                        label: {
                            show: _self.field.options.chartConfig.comp.showLabel,
                            position: 'top'
                        },
                        // 背景颜色
                        backgroundStyle: {
                            color: 'rgba(180, 180, 180, 0.2)'
                        },
                        // radius: '50%',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                    if (item.show) {
                        newItem.name = item.seriesName
                        defaultSeries.push(newItem)
                    }
                })
            }
            return defaultSeries
        },
        // 获取序列数据配置信息
        getOptionSeries_barTimeStatus(dataList, typeList, types) {
            //debugger
            let defaultSeries = []
            let _self = this
            let dataSeries = Object.keys(typeList)
            if (dataSeries && dataSeries.length > 0) {
                dataSeries.forEach(itemKey => {
                    let newItem = {
                        type: 'custom',
                        renderItem: this.renderItem,
                        itemStyle: {
                            opacity: 1
                        },
                        dimensions: ['', '', '', "", '开始时间', '结束时间'],
                        encode: {
                            x: [1, 2],
                            y: 0,
                            tooltip: [4, 5],

                        },
                        data: _self.barTimeStatusDataformat(typeList[itemKey], types)
                    }
                    newItem.name = itemKey // 显示图例名称
                    defaultSeries.push(newItem)
                })
            }
            // debugger
            return defaultSeries
        },
        // 获取序列数据配置信息
        getOptionSeries_Pie() {
            let defaultSeries = []
            let _self = this
            let dataSeries = this.field.options.series
            let totalItems = dataSeries.filter(item => {
                if (item.show) {
                    return item
                }
            }).length
            if (dataSeries && dataSeries.length > 0) {
                dataSeries.forEach((item, index) => {

                    let newItem = {
                        name: "",
                        // 图表类型
                        type: _self.field.options.chartConfig.compType,
                        // roseType:_self.field.options.chartConfig.comp.roseType,
                        label: {
                            // 是否显示内容
                            show: _self.field.options.chartConfig.comp.showLabel,
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                    // 是否显示数值
                    if (_self.field.options.chartConfig.comp.showLabelValue) {
                        newItem.label.formatter = `{b}:({@[${index + 1}]})`
                    }
                    if (item.show) {
                        newItem.name = item.seriesName
                        defaultSeries.push(newItem)
                    }
                    if (_self.field.options.chartConfig.comp.roseType == 'area') {
                        newItem.roseType = 'area'
                    }
                    // 重点关注
                    newItem.encode = {
                        itemName: _self.field.options.xAxisKey,    // X轴名称
                        value: item.columnName // 当前系列名称
                    }
                    // debugger
                    // 动态计算偏移量
                    if (totalItems == 1) {
                        if (!!_self.field.options.chartConfig.comp.roseType) {
                            newItem.radius = ['40%', ((100 / totalItems) - 30 + '%')]
                        } else {
                            newItem.radius = ((100 / totalItems) - 30 + '%')
                        }

                    } else {
                        if (!!_self.field.options.chartConfig.comp.roseType) {
                            newItem.radius = ['40%', ((100 / totalItems) + '%')]
                        } else {
                            newItem.radius = ((100 / totalItems) + '%')
                        }

                    }
                    newItem.bottom = '10%'
                    newItem.center = ['50%', '50%']
                    newItem.left = (index) * (100 / totalItems) + '%'
                    newItem.right = [100 - (index + 1) * (100 / totalItems)] + '%'
                })
            }
            return defaultSeries
        },

        // 获取图例位置
        getLegendConfigPosition() {
            let legend = {
                //   orient: 'vertical',
                //   left: 'center',
                //   bottom:'10px'
                // padding: [
                //     5,  // 上
                //     10, // 右
                //     5,  // 下
                //     10, // 左
                // ]
            }
            let position = this.field.options.legendConfig.position
            switch (position) {
                case "bottom":
                    legend = {
                        left: 'center',
                        bottom: '10px'
                    }
                    break;
                case "bottomLeft":
                    legend = {
                        left: 'left',
                        bottom: '10px'
                    }
                    break;
                case "bottomRight":
                    legend = {
                        left: 'right',
                        bottom: '10px'
                    }
                    break;
                case "top":
                    legend = {
                        left: 'center',
                        top: '10px'
                    }
                    break;
                case "topLeft":
                    legend = {
                        left: 'left',
                        top: '10px'
                    }
                    break;
                case "topRight":
                    legend = {
                        left: 'right',
                        top: '10px'
                    }
                    break;
                case "left":
                    legend = {
                        orient: 'vertical',
                        left: '10px',
                        top: 'middle'
                    }
                    break;
                case "right":
                    legend = {
                        orient: 'vertical',
                        right: '10px',
                        top: 'middle'
                    }
                    break;
                default:
                    legend = {}
                    break;
            }
            //legend.padding=[this.field.options.legendConfig.top,this.field.options.legendConfig.right,this.field.options.legendConfig.bottom,this.field.options.legendConfig.left]
            return legend
        },
        // 获取JSON设置格式-(折线图表)
        getOption_line(smooth = false) {
            let _self = this
            let option = {
                title: this.getTitle(),
                tooltip: {
                    trigger: 'item',
                    appendToBody: !_self.fullscreenObj?.isFullscreen,// 全屏时必须设置为FALSE,
                    transitionDuration: 0
                },
                grid: _self.field.options.grid,
                dataZoom: [
                    {
                        type: 'inside', // 内置型数据区域缩放组件
                        xAxisIndex: 0,  // x轴
                        start: 0,      // 数据窗口范围的起始百分比
                        end: 100,      // 数据窗口范围的结束百分比
                        zoomOnMouseWheel: true  // 触发方式为滚轮
                    }
                ],
                dataset: {},
                xAxis: {
                    name: "",// X轴名称
                    type: 'category',
                },
                yAxis: {
                    name: "",// Y轴名称
                    type: 'value',
                    axisLabel: {
                        // interval: 'auto', // 0强制显示所有，不间隔不显示，如果为数字，间隔几个显示
                        // rotate: 0  // 刻度标签旋转的角度,在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。
                        // Y轴格式化 {value} °C
                        formatter: !!_self.field.options.yConfig.formatter.value ? _self.field.options.yConfig.formatter.value : '{value}',
                        interval: _self.field.options.xConfig.axisLabel.interval,// 0强制显示所有，不间隔不显示，如果为数字，间隔几个显示
                        rotate: _self.field.options.xConfig.axisLabel.rotate // 刻度标签旋转的角度,在类目轴的类目标签显示不下的时候可以通过旋转防止标签之间重叠。
                    }
                },
                series: []
            };
            // 获取数据集
            option.dataset = this.getDataset(option.dataset)
            // 序列配置
            option.series = this.getOptionSeries_line(smooth)
            // 动态添加参数配置
            option.series.map(item => {
                let type = _self.field.options.chartConfig.comp.type
                switch (type) {
                    // 区域面积图
                    case 'areaStyle':
                        item.areaStyle = {}
                        break;
                    // 堆叠折线图
                    case 'stack':
                        item.stack = 'total'
                        break;
                    default:
                        // 基础折线图
                        // delete操作符移除对象指定属性，删除成功返回true，否则返回false。
                        // 删除的属性不存在，delete不会起任何作用 ，但是仍会返回true。
                        delete item.areaStyle
                        delete item.stack
                        break;
                }
                return item
            })

            // 图例位置
            let legend = this.getLegendConfigPosition()
            if (legend && Object.keys(legend).length > 0) {
                option.legend = legend
            }
            return option
        },
        // 获取趋势图各自的数据
        getTrendSeriesData(keyName, groupDataList,optionData) {
            let xAxisData = optionData.xAxis.data // 趋势图x轴 分组数据
           /// debugger
            let dataList = []
            if (groupDataList && groupDataList.length > 0 && xAxisData.length>0) {
                    // 如果数据长度和X中标签长度一致，则直接全部添加
                    if(groupDataList.length == xAxisData.length){
                        groupDataList.forEach(item => {
                            dataList.push(item[this.field.options.trendYAxisKey]) // trendYAxisKey Y轴字段(趋势图)
                        })
                    }else{
                        // 是否启用补齐,因为数据长度和X轴长度可能出现不一致，所以需要这个判断
                        // 否则遍历X轴数据，找到对应的数据
                        xAxisData.forEach(PItem=>{
                            let defaultxAxisData =null
                            let _tempDataList = groupDataList.filter(item=>{
                                if(item[this.field.options.xAxisKey]==PItem){
                                    return item
                                }
                            })
                            if(_tempDataList && _tempDataList.length>0){
                                defaultxAxisData =_tempDataList[0][this.field.options.trendYAxisKey]
                            }
                            dataList.push(defaultxAxisData)
                        })
                    }
              
            }

            return dataList
        },
        getOptionSeries_trend(optionData) {
            let defaultSeries = []
            let _self = this
            let dataSeries = groupBy(this.echartData, this.field.options.trendGroupByField)
            if (dataSeries) {
                for (const [key, val] of Object.entries(dataSeries)) {
                    let newItem = {
                        name: key,  // 显示图例名称
                        // 图表类型 
                        type: 'line',
                        smooth: true,
                        //显示标签
                        label: {
                            show: _self.field.options.chartConfig.comp.showLabel,
                            position: 'top'
                        },
                        data: this.getTrendSeriesData(key, val,optionData),
                    }

                    defaultSeries.push(newItem)
                }
            }
            //debugger 
            return defaultSeries
        },
        getOptionSeries_line(smooth) {
            let defaultSeries = []
            let _self = this

            let dataSeries = this.field.options.series
            if (dataSeries && dataSeries.length > 0) {
                dataSeries.forEach(item => {
                    let newItem = {
                        name: "",
                        // 图表类型
                        type: 'line',
                        smooth: !!_self.field.options.chartConfig.comp.smooth,
                        //显示标签
                        label: {
                            show: _self.field.options.chartConfig.comp.showLabel,
                            position: 'top'
                        },
                    }
                    if (item.show) {
                        newItem.name = item.seriesName // 显示图例名称
                        defaultSeries.push(newItem)
                    }
                })
            }
            return defaultSeries
        },

        // 获取JSON设置格式-(散点图表)
        getOption_scatter() {
            let option = {
                xAxis: {},
                yAxis: {},
                series: [{
                    symbolSize: 20,
                    data: [
                        [10.0, 8.04],
                        [8.07, 6.95],
                        [13.0, 7.58],
                        [9.05, 8.81],
                        [11.0, 8.33],
                        [14.0, 7.66],
                        [13.4, 6.81],
                        [10.0, 6.33],
                        [14.0, 8.96],
                        [12.5, 6.82],
                        [9.15, 7.2],
                        [11.5, 7.2],
                        [3.03, 4.23],
                        [12.2, 7.83],
                        [2.02, 4.47],
                        [1.05, 3.33],
                        [4.05, 4.96],
                        [6.03, 7.24],
                        [12.0, 6.26],
                        [12.0, 8.84],
                        [7.08, 5.82],
                        [5.02, 5.68]
                    ],
                    type: 'scatter'
                }]
            };
            return option;
        },
        // 获取标题配置

        getTitle() {
            let _titleOption = this.field.options.echartTitle
            let title = _titleOption
            if (!!this.field.options.showTextIcon) {
                title = {
                    text: !!_titleOption.text ? ['{tbg|}', `{pl|${_titleOption.text}}`].join('') : '',
                    subtext: _titleOption.subtext,
                    left: _titleOption.left,
                    textStyle: {
                        rich: {
                            "tbg": {
                                width: 26,
                                height: 8,
                                backgroundColor: this.field.options.textIconColor // "#7C260B"
                            },
                            pl: {
                                padding: [0, 0, 0, 15],
                                fontSize: 18,
                                fontWeight: 'bolder',
                            }
                        }
                    },
                }
            }


            return title
        },

        // 重新加载配置信息
        reloadOption(customOption = {}) {
            //debugger
            let _self = this
            let option = this.getOptions()
            if (customOption && Object.keys(customOption).length > 0) {
                // 覆盖已有属性 或 新添加的属性
                option = Object.assign({}, option, customOption)
            }
            if (option?.toolbox?.feature?.myFullscreen) {
                option.toolbox.feature.myFullscreen.title = _self.fullscreenObj.isFullscreen ? '取消全屏' : '全屏查看'
                option.toolbox.feature.myFullscreen.icon = _self.fullscreenObj.isFullscreen ? _self.unFullIconPath : _self.fullIconPath
            }
            if (_self.echartsInstance && _self.echartsInstance.clear) {
                try {
                     // 清空当前实例，会移除实例中所有的组件和图表。
                _self.echartsInstance.clear()
                // 重新加载 推荐通过setOption去修改部分配置
                _self.echartsInstance.setOption({}); // 防止刷新缓存依旧存在
                setTimeout(() => {
                    _self.echartsInstance.setOption(option);

                }, 10)
                //_self.echartsInstance.setOption(option);
                _self.echartsInstance.resize();
                } catch (error) {
                    
                }
               
            } else {
                let chartDom = _self.$refs[_self.field.options.name];
                 // 开发环境使用$echarts 正式环境 this.$echarts
                  const _echarts = process.env.NODE_ENV === 'development' ? $echarts : _self.$echarts; 
                  let myChart = _echarts.init(chartDom, this.tdTheme);
                //let myChart = _self.$echarts.init(chartDom, this.tdTheme);
                _self.echartsInstance = myChart
                option && myChart.setOption(option);


                _self.echartsInstance.resize();
            }
        }

    }
}
</script>

<style lang="scss" scoped>

</style>
