<template>
    <div class="option-items-pane">
  
  
      <draggable tag="ul" :list="optionModel.seriesItems"
        v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
        <li v-for="(option, idx) in optionModel.seriesItems" :key="idx">
          <el-checkbox v-model="option.check">
            <el-input  @click.native="showEditDialogEvent(option)" readonly
              v-model="option.label" size="mini" style="width: 200px"></el-input>
            <i class="iconfont icon-drag drag-option"></i>
            <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
              icon="el-icon-minus" class="col-delete-button"></el-button>
          </el-checkbox>
        </li>
      </draggable>
  
      <div>
        <el-button type="text" @click="addOption">+添加项</el-button>
      </div>
  
      <el-dialog title="描述列表项 编辑" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
        class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
        :destroy-on-close="true">
        <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
          <el-form-item label="标题" prop="label">
            <el-input style="width:330px" v-model="editMenuForm.label"></el-input>
          </el-form-item>
          <el-form-item label="唯一名称" prop="key">
            <el-input style="width:330px" disabled v-model="editMenuForm.key"></el-input>
          </el-form-item>
          <el-form-item  label="显示Tag标签">
              <el-switch v-model="editMenuForm.showAsTag"></el-switch>
          </el-form-item>
          <el-form-item v-show="!!editMenuForm.showAsTag" label="Tag状态列表">
            <el-button  type="text" @click="addTagOption()">+添加</el-button>
          </el-form-item>
          <el-form-item v-show="!!editMenuForm.showAsTag" label="">
          <div style="margin-left:30px;" class="flex flex-col">
              <div class="flex" :key="subIndex" v-for="(subItem,subIndex) in editMenuForm.tagList">
                <div class="flex justify-center items-center" style="width:90px;overflow:hidden;" v-text="subItem.tagName"></div>
                <el-input style="width:130px" type="text" v-model="subItem.tagValue"></el-input>
                <el-select  v-model="subItem.tagType" placeholder="请选择">
                    <el-option label="请选择" value=""></el-option>
                    <el-option label="蓝色" value="primary"></el-option>
                    <el-option label="绿色" value="success"></el-option>
                    <el-option label="灰色" value="info"></el-option>
                    <el-option label="橙色" value="warning"></el-option>
                    <el-option label="红色" value="danger"></el-option>
                </el-select>
                <el-button  circle plain size="mini" type="danger" 
               icon="el-icon-minus" class="col-delete-button" @click="removeTagOption(subItem,subIndex)"></el-button>
              </div>
          </div>
        </el-form-item>
          <el-form-item  label="占居列数">
            <el-input-number v-model="editMenuForm.columnNumber"  :min="1" :max="4" label="描述文字"></el-input-number>
        </el-form-item>
          <el-form-item label="图标ICON">
              <el-input style="width:330px" type="text" v-model="editMenuForm.iconUrl"></el-input>
              <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
          </el-form-item>
          <el-form-item  label="显示类型">
            <el-select  v-model="editMenuForm.displayType" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option label="文本" value="text"></el-option>
              <el-option label="图片" value="image"></el-option>
              <el-option label="日期" value="date"></el-option>
              <el-option label="日期时间" value="datetime"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item  label="值字段">
            <el-select  v-model="editMenuForm.fieldName" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in optionModel.dataSetAllModel"
                :label="fieldItem.title" :value="fieldItem.field"></el-option>
            </el-select>
        </el-form-item>
    
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
          <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
        </div>
      </el-dialog>
    
    </div>
  </template>
    
  <script>
  
  const default_editMenuForm = {
    label: "",// 标签
    fieldName: "",// 唯一名称
    key: "",// 
    iconUrl: "",// 
    showAsTag:false,
    tagList:[],
    columnNumber:1,//默认占据1列
    displayType:"",// 显示类型，用于格式化数据
    otherParams: { },
    actionParams: {},
  }
  import {generateId } from "@/utils/util"
  import Draggable from 'vuedraggable'
  import cloneDeep from "clone-deep"
  import i18n from "@/utils/i18n";
  export default {
    name: "descriptionsItemList",
    mixins: [i18n],
    components: {
      Draggable
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
      
        showEditMenuDialogFlag: false,
        currentEditOption: {},// 当前编辑菜单按钮
        editMenuForm: Object.assign({}, default_editMenuForm), // 搜索输入框配置
        
        editMenuFormRules: {
          fieldName: [
            { required: true, message: '请输入字段名称', trigger: 'blur' },
          ],
        },
     
      }
    },
    computed: {
    
      optionModel() {
        return this.selectedWidget.options
      },
  
    },
  
    methods: {
      showEditDialogEvent(option){
            this.currentEditOption = option // 当前菜单属性
            this.showEditMenuDialogFlag = true
          },
      // 提交修改按钮菜单属性
      submitEditMenu() {
        this.$refs["editMenuForm"].validate((valid) => {
          if (valid) {
            this.currentEditOption.label = this.editMenuForm.label
            this.currentEditOption.fieldName = this.editMenuForm.fieldName
             this.currentEditOption.key = this.editMenuForm.key
             this.currentEditOption.iconUrl = this.editMenuForm.iconUrl
             this.currentEditOption.displayType = this.editMenuForm.displayType
             this.currentEditOption.columnNumber = this.editMenuForm.columnNumber
             this.currentEditOption.showAsTag = this.editMenuForm.showAsTag
             this.currentEditOption.tagList = this.editMenuForm.tagList
            // this.currentEditOption.upOrDownFlag = this.editMenuForm.upOrDownFlag
            
            this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
            this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
           
  
            this.showEditMenuDialogFlag = false
          } else {
            console.log('error submit!!');
            return false;
          }
        });
  
      },
      // 弹框编辑属性--- 初始化弹框属性参数
      showEditDialogEvent(option) {
        this.currentEditOption = option // 当前属性
        this.showEditMenuDialogFlag = true
  
        this.editMenuForm.label = option.label
        this.editMenuForm.fieldName = option.fieldName
         this.editMenuForm.key = option.key
         this.editMenuForm.iconUrl = option.iconUrl
        this.editMenuForm.displayType = option.displayType
        this.editMenuForm.columnNumber = option.columnNumber
         this.editMenuForm.showAsTag = option.showAsTag
        this.editMenuForm.tagList = option.tagList
        // this.editMenuForm.upOrDownFlag = option.upOrDownFlag
        
        this.editMenuForm.otherParams = cloneDeep(option.otherParams)
        this.editMenuForm.actionParams = cloneDeep(option.actionParams)
      
        this.searchItemConfig = Object.assign({}, this.editMenuForm.searchItemConfig)
     
  
  
      },
      deleteOption(option, index) {
        // 是否可以移除
        if (!!option.canRemove) {
          this.optionModel.seriesItems.splice(index, 1)
        }
  
      },
      // 添加按钮
      addOption() {
        if(!this.optionModel.hasOwnProperty("seriesItems")){
              this.$set(this.optionModel, "seriesItems", [])
        }
        let newValue = this.optionModel.seriesItems.length + 1+"-"+generateId()
        this.optionModel.seriesItems.push(
          { label: 'label'+newValue,columnNumber:1,displayType:"text",showAsTag:false,tagSize:"small",key: 'label'+newValue,fieldName: '',iconUrl:"", disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
        )

      },
    // 添加tag按钮
    addTagOption() {
        if(!this.editMenuForm.hasOwnProperty("tagList") || !!!this.editMenuForm.tagList){
              this.$set(this.editMenuForm, "tagList", [])
        }
        let newValue = this.editMenuForm.tagList.length + 1+"-"+generateId()
        this.editMenuForm.tagList.push(
          { tagName: 'tag'+newValue,tagValue:'',tagType:"primary", },
        )

      },
      removeTagOption(subItem,subIndex) {
        this.editMenuForm.tagList.splice(subIndex, 1)
      },
    }
  }
  </script>
    
  <style lang="scss" scoped>
  .option-items-pane ul {
    // list-style: none;
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
  }
  
  li.ghost {
    // list-style: none;
    background: #fff;
    border: 2px dotted $--color-primary;
  }
  
  .drag-option {
    cursor: move;
  }
  
  .small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
  }
  
  .dialog-footer .el-button {
    width: 100px;
  
  }
  </style>
    