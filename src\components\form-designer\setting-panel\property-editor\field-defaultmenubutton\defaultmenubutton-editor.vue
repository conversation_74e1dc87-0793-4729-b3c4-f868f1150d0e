<template>
  <div>
    <el-form-item label="">
      <span  slot="label">加载后立即触发
        <el-tooltip effect="light" content="当表格或图表设置为被动加载后，可以设置此属性，用来传递当前查询属性去查询数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.execAfterDone"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span  slot="label">清除距顶10px
        <el-tooltip effect="light" content="当此控件为非首个设计控件时，默认时距离顶部10PX，可以设置此属性，清空">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.cleanMarginTop10PX"></el-switch>
    </el-form-item>
    <el-form-item label="字体大小">
            <el-input-number  v-model="optionModel.fontSize" controls-position="right" ></el-input-number>
    </el-form-item>
    <el-form-item label="显示高级搜索">
      <el-switch v-model="optionModel.showSearchPro"></el-switch>
    </el-form-item>
    <el-form-item label="显示靠左">
      <el-switch v-model="optionModel.showMenuLeft"></el-switch>
    </el-form-item>
    <el-form-item  label="背景颜色">
        <el-color-picker
            v-model="optionModel.bgColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="字体颜色">
        <el-color-picker
            v-model="optionModel.textColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item v-show="!!optionModel.showSearchPro" label="配置高级搜索">
       <!-- 隐藏字段 optionModel.searchProSettingID -->
        <searchProList :optionModel="optionModel" :designer="designer" :selected-widget="selectedWidget"></searchProList>
        <searchProSetting :optionModel="optionModel" :designer="designer" :selected-widget="selectedWidget"></searchProSetting>
    </el-form-item>
    <el-form-item v-show="!!optionModel.showSearchPro" label="搜索后关闭">
       <el-switch v-model="optionModel.closeAfterSearchPro"></el-switch>
    </el-form-item>
    <el-form-item label-width="0">
            <commonParamsOnChange contrlType="defaultmenubutton" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">操作按钮配置
        <el-tooltip effect="light" content="一般的权限按钮功能配置，勾选后，点击弹框配置需要的功能API">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <!-- <el-form-item label="andyTest">
        <el-switch v-model="optionModel.andy"></el-switch>
     </el-form-item> -->
     <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <option-items-setting :designer="designer" :selected-widget="selectedWidget"></option-items-setting>
      </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">搜索框配置
        <el-tooltip effect="light" content="配置需要显示的查询条件控件，如：输入框，下拉框，日期框等">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <option-search-setting :designer="designer" :selected-widget="selectedWidget"></option-search-setting>
      </el-form-item>
      <el-form-item label-width="0">
      <el-divider class="custom-divider">其它按钮配置
        <el-tooltip effect="light" content="默认为查询和重置按钮功能，无需配置，但可以隐藏，或添加其它的功能的按钮">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
      <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <option-button-setting :designer="designer" :selected-widget="selectedWidget"></option-button-setting>
      </el-form-item>
     <el-form-item label-width="0">
      <el-divider class="custom-divider"></el-divider>
    </el-form-item>
   
  </div>
  
</template>
<script>
  import i18n from "@/utils/i18n"
  import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
  import OptionItemsSetting from "./defaultmenubutton-option-setting.vue"
  import OptionSearchSetting from "./defaultmenubutton-search.vue"
  import searchProSetting from "./defaultmenubutton-searchPro.vue"
  import searchProList from "./defaultmenubutton-searchProList.vue"
  import OptionButtonSetting from "./defaultmenubutton-button.vue"
  export default {
    name: "defaultmenubutton-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    components: {
      commonParamsOnChange,
      searchProSetting,
      searchProList,
      OptionItemsSetting,
      OptionSearchSetting,
      OptionButtonSetting,
    },
    data(){
      return{
        predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
      }
    }
  }
</script>

<style scoped>

</style>