<template>
    <div class="selectRender">
         <!-- @focus="loadData()" -->
          <el-select 
            ref="elSelectRef"
            @change="changeEvent"
            v-bind="$attrs"
            v-model="currentItem"
            v-on="$listeners"
            :loading="loading"
            placeholder="请选择">
             <!-- <el-option  v-for="item in selectDataList"  :key="item.value" :label="item.label" :value="item.value">
            </el-option> -->
            <el-option  v-for="(item,index) in selectDataList"  :key="item.value+index" :label="item.label" :value="item.value">
            </el-option>
         </el-select>
    </div>
</template>
<script>
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'selectRender',
    components:{},
    props:{
     // 编辑状态是否变更>>仅用于表格talbe中 
    // updateFlag:{
    //     type: [String,Boolean],
    //     default: "",
    // },    
       // 是否立即加载数据
    //  isLoadNow:{
    //      type:Boolean,
    //      default:false,
    //  },    
    //    // 每次点击都查询
    //  reSearch:{
    //      type:<PERSON><PERSON><PERSON>,
    //      default:false,
    //  },  
    //   // 表格信息>>dataFrom=talbe 才有效
    //   tableInfo:{
    //       type:Object,
    //       default(){
    //           return {}
    //       } 
    //   },
      // 数据来信:表单form,表格talbe, 搜索：search
      dataFrom:{
          type:String,
          default:"form"
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
    // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
    dataSearchOptions: {
        type: Object,
        default() {
            return {};
        },
    },
       // 当前字段:value
     currentValue:{
         type:[String,Number],
         default:""
     },
      // 当前字段:Text
     currentValueText:{
        type:[String,Number],
         default:""
     },
     // 是否可用
     disabled:{
         type:Boolean,
         default:false,
     }  
    }, 
    data(){
        return {
            config:config,
            loading: false,
            prefixIcon:"", // 头部图标,用于是否加载中 显示
            currentItem:'',// 当前选择项
            currentItemText:'',// 当前选择项描述
            selectDataList:[],// 当前列表数据
        }
    },
    computed:{ 
        dataSourceID(){
            let _id =""
            try {
                
            } catch (error) {
                _id =""
            }
            return _id
        }
    },
    watch:{
        currentValue(n,o){
            this.$nextTick(()=>{
                this.loadData()
            })
        },
        // currentValue(n,o){
        //     //debugger
        //     if(!!n){
        //        this.currentItem = n
        //     }else{
        //         // 清空数据
        //          this.currentItem =''
        //          this.currentItemText =''
        //     }
        // },
        //  currentValueText(n,o){
        //     //debugger
        //     if(!!n){
        //        this.currentItemText = n
        //     }else{
        //         // 清空数据
        //         this.currentItemText =''
        //     }
        // }
        
    },
    mounted(){
      //首次赋值
    //   this.$nextTick(()=>{
    //     // 设置默认值
    //       if(!!this.currentValueText){
    //         let newItem ={
    //                     value: this.currentValue,
    //                     label:this.currentValueText
    //                 }
    //         this.selectDataList.push(newItem)
    //         this.currentItem = this.currentValue
    //       }
    //   })
      this.$nextTick(()=>{
        this.loadData()
      })
    },
    methods:{
        // 格式化数据成下拉框所需的
        formatData(dataList=[]){
            let newData =[] 
            if(dataList && dataList.length>0){
                dataList.forEach(item=>{
                    let newItem ={
                        value:item.Key,
                        label:item.Value
                    }
                    newData.push(newItem)
                })
            }
          return newData
        },
       // 加载数据 e.g::::控件选择 下拉框（字典），sourcekey:BD_ITEM_CONTROL_TYPE
       loadData(){
            if(this.selectDataList.length >1){
                return
            }
           this.loading = true
            let sourceKey = this.paramsItem.sourceKey
            let searchParams = this.paramsItem.searchParams
            let _url=this.config.moduleSub+"ComboSource"
            let params = {
                SourceType:"Dict", //Dict Table Tree
                SourceID:this.dataSourceID,
                Params:{
                    sourceKey,
                    filter:{}
                 }
                };
                if(!!searchParams){
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                    let searchParamsArr = searchParams.split(',')
                    searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    }
                }
            let _self = this
            request['post'](_url, params).then(res=>{
                if(res.Success){
                    let resData = res.Datas
                    this.loading = false
                    this.selectDataList = this.formatData(resData)
                    _self.$nextTick(()=>{
                        if(!!_self.currentValue){
                            _self.currentItem =_self.currentValue
                        }
                     })
                }
            })
       },
       // 选择改变回调事件
       changeEvent(val){
           let _self = this
           this.$nextTick(()=>{
                let params ={
                    value:val,
                    text:_self.$refs["elSelectRef"].selectedLabel
                } 
                _self.$emit("changeEvent",params)
           })
       }
    }
}
</script>


<style lang="scss">

</style>