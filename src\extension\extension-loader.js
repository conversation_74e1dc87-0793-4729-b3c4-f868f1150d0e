import Vue from 'vue'

import {
  addContainerWidgetSchema,
  addCustomWidgetSchema,
  addAppWidgetSchema,
} from '@/components/form-designer/widget-panel/widgetsConfig'
import * as PERegister from '@/components/form-designer/setting-panel/propertyRegister'
import * as PEFactory from '@/components/form-designer/setting-panel/property-editor-factory'

import {cardSchema} from "@/extension/samples/extension-schema"
import CardWidget from '@/extension/samples/card/card-widget'
import CardItem from '@/extension/samples/card/card-item'

import {registerCWGenerator} from '@/utils/sfc-generator' // 注册容器组件的代码生成器
import {registerFWGenerator} from '@/utils/sfc-generator' // 注册字段组件的代码生成器


import {alertSchema} from "@/extension/samples/extension-schema"
import AlertWidget from '@/extension/samples/alert/alert-widget'
// import {DIVContainerSchema} from "@/extension/samples/extension-schema"
// import DIVContainerWidget from '@/extension/samples/DIVContainer/DIVContainer-widget'
// add by andy
import {defaultmenubuttonSchema} from "@/extension/samples/extension-schema"
import defaultmenubuttonWidget from '@/extension/samples/defaultmenubutton/defaultmenubutton-widget'
// add by andy
import {vxetableSchema} from "@/extension/samples/extension-schema"
import vxetableWidget from '@/extension/samples/vxetable/vxetable-widget'
// add by andy
import {splitpanesSchema} from "@/extension/samples/extension-schema"
import splitpanesWidget from '@/extension/samples/splitpanes/splitpanes-widget'
import splitpanesItem from '@/extension/samples/splitpanes/splitpanes-item'

// add by andy 
import {echartsSchema} from "@/extension/samples/extension-schema"
import echartsWidget from '@/extension/samples/echarts/echarts-widget'

// add by andy 20230511
import {iconInfoSchema} from "@/extension/samples/extension-schema"
import iconInfoWidget from '@/extension/samples/iconInfo/iconInfo-widget'

// add by andy 20240511
import {colorBlockInfoSchema} from "@/extension/samples/extension-schema"
import colorBlockInfoWidget from '@/extension/samples/colorBlockInfo/colorBlockInfo-widget'

// add by andy 20240704
import {collapseDescSchema} from "@/extension/samples/extension-schema"
import collapseDescWidget from '@/extension/samples/collapseDesc/collapseDesc-widget'

// add by andy 20240531
import {tabMenuSchema} from "@/extension/samples/extension-schema"
import tabMenuWidget from '@/extension/samples/tabMenu/tabMenu-widget'
// add by andy 2023-0207
import {customTreeSchema} from "@/extension/samples/extension-schema"
import customTreeWidget from '@/extension/samples/customTree/customTree-widget'
// add by andy 2023-0529
import {descriptionsSchema} from "@/extension/samples/extension-schema"
import descriptionsWidget from '@/extension/samples/descriptions/descriptions-widget'

import {alertTemplateGenerator} from '@/extension/samples/extension-sfc-generator'
import {cardTemplateGenerator} from '@/extension/samples/extension-sfc-generator'
import {defaultmenubuttonTemplateGenerator} from '@/extension/samples/extension-sfc-generator'
// add by andy 20240523
// import {divContainerSchema} from "@/extension/samples/extension-schema"
// import divContainerWidget from '@/extension/samples/divContainer/divContainer-widget'
// import divContainerItem from '@/extension/samples/divContainer/divContainer-item'

// import {splitpanesSchema} from "@/extension/samples/extension-schema"
// import splitpanesWidget from '@/extension/samples/splitpanes/splitpanes-widget'
// import splitpanesItem from '@/extension/samples/splitpanes/splitpanes-item'
////////////////////////手机APP控件列表注册///////////////////////////////////////

////////////////////////手机APP控件列表注册///////////////////////////////////////


export const loadExtension = function () {

////////////////////////手机APP控件列表注册///////////////////////////////////////

////////////////////////手机APP控件列表注册///////////////////////////////////////
  /**
   * 加载容器组件步骤：
   * 1. 加载组件Json Schema;
   * 2. 全局注册容器组件，容器组件有两种状态——设计期和运行期，故需要注册两个组件；
   * 3. 全局注册属性编辑器组件（基本属性、高级属性、事件属性）；
   * 4. 注册容器组件的代码生成器；
   * 5. 加载完毕。
   */
  addContainerWidgetSchema(cardSchema)  //加载组件Json Schema
  /* -------------------------------------------------- */
  Vue.component(CardWidget.name, CardWidget)  //注册设计期的容器组件
  Vue.component(CardItem.name, CardItem)  //注册运行期的容器组件
  /* -------------------------------------------------- */
  PERegister.registerCPEditor('card-folded', 'card-folded-editor',
      PEFactory.createBooleanEditor('folded', 'extension.setting.cardFolded'))

  PERegister.registerCPEditor('card-showFold', 'card-showFold-editor',
      PEFactory.createBooleanEditor('showFold', 'extension.setting.cardShowFold'))

  PERegister.registerCPEditor('card-cardWidth', 'card-cardWidth-editor',
      PEFactory.createInputTextEditor('cardWidth', 'extension.setting.cardWidth'))

  let shadowOptions = [
    {label: 'never', value: 'never'},
    {label: 'hover', value: 'hover'},
    {label: 'always', value: 'always'},
  ]
  PERegister.registerCPEditor('card-shadow', 'card-shadow-editor',
      PEFactory.createSelectEditor('shadow', 'extension.setting.cardShadow',
          {optionItems: shadowOptions}))
  /* -------------------------------------------------- */
  registerCWGenerator('card', cardTemplateGenerator)  //注册容器组件的代码生成器
  /* -------------------------------------------------- */
  /* 容器组件加载完毕 end */

  /**
   * 加载字段组件步骤：
   * 1. 加载组件Json Schema;
   * 2. 全局注册字段组件，字段组件设计期和运行期共用，故需要仅需注册一个组件；
   * 3. 全局注册属性编辑器组件（基本属性、高级属性、事件属性）；
   * 4. 注册字段组件的代码生成器；
   * 5. 加载完毕。
   */
  addCustomWidgetSchema(alertSchema)  //加载组件Json Schema
  /* -------------------------------------------------- */
  Vue.component(AlertWidget.name, AlertWidget)  //注册组件

  /* -------------------------------------------------- */
  PERegister.registerCPEditor('alert-title', 'alert-title-editor',
  PEFactory.createInputTextEditor('title', 'extension.setting.alertTitle'))

  let typeOptions = [
    {label: 'success', value: 'success'},
    {label: 'warning', value: 'warning'},
    {label: 'info', value: 'info'},
    {label: 'error', value: 'error'},
  ]
  // PERegister.registerCPEditor('alert-type', 'alert-type-editor',
  //     PEFactory.createSelectEditor('type', 'extension.setting.alertType',
  //         {optionItems: typeOptions}))
  /* type属性映射已存在，无须再注册，故只需注册属性编辑器即可！！ */
  Vue.component('alert-type-editor',
      PEFactory.createSelectEditor('type', 'extension.setting.alertType',
          {optionItems: typeOptions}))

  PERegister.registerCPEditor('alert-description', 'alert-description-editor',
      PEFactory.createInputTextEditor('description', 'extension.setting.description'))

  PERegister.registerCPEditor('alert-closable', 'alert-closable-editor',
      PEFactory.createBooleanEditor('closable', 'extension.setting.closable'))

  PERegister.registerCPEditor('alert-closeText', 'alert-closeText-editor',
      PEFactory.createInputTextEditor('closeText', 'extension.setting.closeText'))

  PERegister.registerCPEditor('alert-center', 'alert-center-editor',
      PEFactory.createBooleanEditor('center', 'extension.setting.center'))

  PERegister.registerCPEditor('alert-showIcon', 'alert-showIcon-editor',
      PEFactory.createBooleanEditor('showIcon', 'extension.setting.showIcon'))

  let effectOptions = [
    {label: 'light', value: 'light'},
    {label: 'dark', value: 'dark'},
  ]
  PERegister.registerCPEditor('alert-effect', 'alert-effect-editor',
      PEFactory.createRadioButtonGroupEditor('effect', 'extension.setting.effect',
          {optionItems: effectOptions}))

  PERegister.registerEPEditor('alert-onClose', 'alert-onClose-editor',
      PEFactory.createEventHandlerEditor('onClose', []))
  /* -------------------------------------------------- */
  registerFWGenerator('alert', alertTemplateGenerator)  //注册字段组件的代码生成器
  /* -------------------------------------------------- */
  /* 字段组件加载完毕 end */

  //-----------------------------------NEW  BY ANDY--------------------------------------------
  addCustomWidgetSchema(vxetableSchema)  //加载组件Json Schema
  /* -------------------------------------------------- */
  Vue.component(vxetableWidget.name, vxetableWidget)  //注册组件
  /* -------------------------------------------------- */
  addCustomWidgetSchema(splitpanesSchema)  //加载组件Json Schema
  /* -------------------------------------------------- */
  Vue.component(splitpanesWidget.name, splitpanesWidget)  //注册组件
  Vue.component(splitpanesItem.name, splitpanesItem)  //注册运行期的容器组件
    /* -------------------------------------------------- */
  addCustomWidgetSchema(echartsSchema)  //加载组件Json Schema  
  Vue.component(echartsWidget.name, echartsWidget)  //注册组件
  /* -------------------------------------------------- */
  addCustomWidgetSchema(iconInfoSchema)  //加载组件Json Schema  
  Vue.component(iconInfoWidget.name, iconInfoWidget)  //注册组件
  /* -------------------------------------------------- */
    /* -------------------------------------------------- */
    addCustomWidgetSchema(colorBlockInfoSchema)  //加载组件Json Schema  
    Vue.component(colorBlockInfoWidget.name, colorBlockInfoWidget)  //注册组件
    /* -------------------------------------------------- */
      /* -------------------------------------------------- */
      addCustomWidgetSchema(collapseDescSchema)  //加载组件Json Schema  
      Vue.component(collapseDescWidget.name, collapseDescWidget)  //注册组件
      /* -------------------------------------------------- */
     /* -------------------------------------------------- */
     addCustomWidgetSchema(tabMenuSchema)  //加载组件Json Schema  
     Vue.component(tabMenuWidget.name, tabMenuWidget)  //注册组件
     /* -------------------------------------------------- */
    /* -------------------------------------------------- */
    // addCustomWidgetSchema(divContainerSchema)  //加载组件Json Schema  
    // Vue.component(divContainerWidget.name, divContainerWidget)  //注册组件
    // Vue.component(divContainerItem.name, divContainerItem)  //注册运行期的容器组件
    /* -------------------------------------------------- */
    /* -------------------------------------------------- */
    addCustomWidgetSchema(customTreeSchema)  //加载组件Json Schema  
    Vue.component(customTreeWidget.name, customTreeWidget)  //注册组件
  /* -------------------------------------------------- */
      /* -------------------------------------------------- */
      addCustomWidgetSchema(descriptionsSchema)  //加载组件Json Schema  
      Vue.component(descriptionsWidget.name, descriptionsWidget)  //注册组件
    /* -------------------------------------------------- */
//   addCustomWidgetSchema(standardFrameSchema)  //加载组件Json Schema  
//   Vue.component(standardFrameWidget.name, standardFrameWidget)  //注册组件
/* -------------------------------------------------- */
  /**
   * 加载字段组件步骤：
   * 1. 加载组件Json Schema;
   * 2. 全局注册字段组件，字段组件设计期和运行期共用，故需要仅需注册一个组件；
   * 3. 全局注册属性编辑器组件（基本属性、高级属性、事件属性）；
   * 4. 注册字段组件的代码生成器；
   * 5. 加载完毕。
   */
   addCustomWidgetSchema(defaultmenubuttonSchema)  //加载组件Json Schema
   /* -------------------------------------------------- */
   Vue.component(defaultmenubuttonWidget.name, defaultmenubuttonWidget)  //注册组件
   /* -------------------------------------------------- */
   PERegister.registerCPEditor('defaultmenubutton-title', 'alert-title-editor',
   PEFactory.createInputTextEditor('title', 'extension.setting.alertTitle'))
 
   let typeOptions_MEW = [
     {label: 'success', value: 'success'},
     {label: 'warning', value: 'warning'},
     {label: 'info', value: 'info'},
     {label: 'error', value: 'error'},
   ]

   /* type属性映射已存在，无须再注册，故只需注册属性编辑器即可！！ */
   Vue.component('defaultmenubutton-type-editor',
       PEFactory.createSelectEditor('type', 'extension.setting.alertType',
           {optionItems: typeOptions_MEW}))
  // 注册常见属性对应的属性编辑器
   PERegister.registerCPEditor('defaultmenubutton-description', 'alert-description-editor',
       PEFactory.createInputTextEditor('description', 'extension.setting.description'))
 
   PERegister.registerCPEditor('defaultmenubutton-closable', 'alert-closable-editor',
       PEFactory.createBooleanEditor('closable', 'extension.setting.closable'))
 
   PERegister.registerCPEditor('defaultmenubutton-closeText', 'alert-closeText-editor',
       PEFactory.createInputTextEditor('closeText', 'extension.setting.closeText'))
 
   PERegister.registerCPEditor('defaultmenubutton-center', 'alert-center-editor',
       PEFactory.createBooleanEditor('center', 'extension.setting.center'))
 
   PERegister.registerCPEditor('defaultmenubutton-showIcon', 'alert-showIcon-editor',
       PEFactory.createBooleanEditor('showIcon', 'extension.setting.showIcon'))
 
   let effectOptions_NEW = [
     {label: 'light', value: 'light'},
     {label: 'dark', value: 'dark'},
   ]
   PERegister.registerCPEditor('defaultmenubutton-effect', 'alert-effect-editor',
       PEFactory.createRadioButtonGroupEditor('effect', 'extension.setting.effect',
           {optionItems: effectOptions_NEW}))
 
   PERegister.registerEPEditor('defaultmenubutton-onClose', 'alert-onClose-editor',
       PEFactory.createEventHandlerEditor('onClose', []))
   /* -------------------------------------------------- */
  registerFWGenerator('defaultmenubutton', defaultmenubuttonTemplateGenerator)  //注册字段组件的代码生成器
   /* -------------------------------------------------- */
   /* 字段组件加载完毕 end */
}
