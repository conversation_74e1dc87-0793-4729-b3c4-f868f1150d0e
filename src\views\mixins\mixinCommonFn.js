// 通用方法
export default {
  name:'mixinCommonFn',
  data(){
    return {}
  },
  methods:{
      // 当前主表选中行
      currentRow_get() {
        let routerName = this.$route.name
        return this.$store.state.currentRow.value[routerName];
      },
        // 当前主表选中行>>查询子表详情
      currentRowDetail_get() {
        let routerName = this.$route.name
        return this.$store.state.currentRowDetail.value[routerName];
      },
      getRequest(name) {
        let urlStr = window.location.search
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        let r = urlStr.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        };
        return null;
    }
  }
}