<template>
  <div>
     <div v-show="!showSingleRowWithDiv">
    <el-form-item label-width="0">
      <el-divider class="custom-divider">数据源设置
        <el-tooltip effect="light" content="选择加载API,主要是选择查询列表中的定义好API，在预览或发布页面时，用来加载数据列表；获取数据模型，点击预加载后，在后续的需要使用模型的地方，方便直接快速选择对应的字段">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item label="选择加载API">
      <el-select @change="dataSourceChangeEvent" v-model="optionModel.actionName" placeholder="请选择查询">
        <el-option value="">请选择</el-option>
        <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
          :label="queryItem.label" :value="queryItem.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="获取数据模型">
      <el-button :loading="loadingTableSubmit" @click="loadTableData()" type="success">重新加载</el-button>
    </el-form-item>
    <!-- <el-form-item label="匹配集合">
      <span slot="label">匹配集合
        <el-tooltip effect="light" content="主要用于返回多集合时使用，单集合无需选择">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select @change="changeDataSet" v-model="optionModel.dataSetSelectedModel">
        <el-option label="请选择" value=""></el-option>
        <el-option :key="index + 1" v-for="(item, index) in optionModel.dataSetData" :label="item" :value="item"></el-option>
      </el-select>
    </el-form-item> -->
     </div>
     <div v-show="showSingleRowWithDiv">
       <div class="flex justify-center items-center">
             <div style="margin-right: 5px;">选择加载API
              <el-tooltip effect="light" content="注意：此处选择加载API后，会把模型对应的查询参数保存起来，如果后期修改了查询列表中对应的API，此处不会同步，需要重新触发获取后保存。">
          <i class="el-icon-info"></i></el-tooltip>
            </div>
             <div style="margin-right: 5px;">
              <el-select @change="dataSourceChangeEvent" v-model="optionModel.actionName" placeholder="请选择查询">
              <el-option value="">请选择</el-option>
              <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
                :label="queryItem.label" :value="queryItem.value"></el-option>
              </el-select>
            </div>
            <div style="margin-right: 5px;">获取数据模型</div>
            <el-button :loading="loadingTableSubmit" @click="loadTableData()" type="success">预加载</el-button>
       </div>
     </div>
  </div>

</template>

<script>
import request from '@/libs/request'
import i18n from "@/utils/i18n"
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
  name: "selectDataSourceApi", // 选择数据源
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    contrlType:{ // 控件类型
      type:String,
      default:""
    },
    showSingleRowWithDiv:{ //  单行展示 DIV
      type:Boolean,
      default:false
    },
    dataSetAllModel:{ // 是否保存模型到集合
      type:Boolean,
      default:true
    } 
    
  },
  data() {
    return {
      actionName: "",
      loadingTableSubmit: false,
      useFormatParamsFn: useFormatParams(this),
    }
  },
  methods: {
    dataSourceChangeEvent(val){
     // debugger
       if(this.contrlType =='vxetable'){
            // 用于图表中   optionModel.requstConfig.postUrl
          this.$set(this.optionModel.requstConfig,"postUrl", val)
       }
       this.$emit("dataSourceChangeEvent",val)
      setTimeout(()=>{
        this.loadTableData()
      },300)
    },
    // 多集合 数据集改变
    changeDataSet(val) {
      //this.optionModel.tableColumns = this.optionModel.dataSetAllModel[val]
    
      this.$emit("changeDataSet",val)
    },
    // 获取数据源数据
    async getDataModelList(datasetId,actionName) {
      if (!datasetId) {
        return []
      }
      let dataModelList = []
      // 获取固定参数值，这里无法获取控件的值，因为不是实际运作阶段
      let queryItem = this.useFormatParamsFn.getQueryItemByUrl(actionName)
      let condition = await this.useFormatParamsFn.getCommonParamsValue(queryItem);
      //debugger
      let params = {
        //condition: condition,
        datasetId: datasetId
      }
      //let _url = "api/MD/DataSetModel/GetAll"
      //let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })

      return dataModelList
    },

    // 格式化实体字段
    formatModelFields(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: !!oldItem.CCOLUMN_DESC ? oldItem.CCOLUMN_DESC : oldItem.CCOLUMN_NAME,
            CID: oldItem.CID,
            fieldDefault: oldItem.CCOLUMN_DEFAULT,
          }
          newDataList.push(newItem)
        })
      }
      return newDataList

    },
    async loadTableData() {
      // debugger
      // 列头查询 preUrl 路径固定不变
      let actionName = this.optionModel.actionName
      let datasetId = this.useFormatParamsFn.getVFormDataSetID(actionName);
    
      let dataModel = await this.getDataModelList(datasetId,actionName)
      // debugger
      let newDataModel = this.formatModelFields(dataModel)
      //debugger
      if(this.dataSetAllModel){
          /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
          this.$set(this.optionModel,"dataSetAllModel", newDataModel)
      }
       if(this.contrlType =='echarts'){
            // 用于图表中
          this.$set(this.optionModel,"tableColumns", newDataModel)
       }
       this.$emit("afterLoadDataSetAllModel",newDataModel)
    }
  }
}
</script>

<style lang="scss" scoped></style>
