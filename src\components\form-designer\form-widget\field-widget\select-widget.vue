<template>
  <form-item-wrapper  :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
                     <!-- ref="fieldEditor"==>>:ref="field.options.name" -->
                     <el-select :publicAttribute="publicAttribute"  v-model="fieldModel_Computed" v-show="!isReadMode" class="customform full-width-input "
               :disabled="field.options.disabled"
               :size="field.options.size"
               :ref="field.options.name"
               :clearable="field.options.clearable"
               :filterable="field.options.filterable"
               :allow-create="field.options.allowCreate"
               :default-first-option="allowDefaultFirstOption"
               :automatic-dropdown="field.options.automaticDropdown"
               :multiple="field.options.multiple" :multiple-limit="field.options.multipleLimit"
               :placeholder="field.options.placeholder || i18nt('render.hint.selectPlaceholder')"
               :remote="field.options.remote" :remote-method="remoteQuery"
               @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent"
               @change="handleChangeEvent">
      <el-option  :style="[{ display: !!field.options.dataTreeEnabled ? `none` : `display` }]" v-for="item in field.options.optionItems" :key="item.value" :label="item.label"
                 :value="item.value+''" :disabled="item.disabled">
      </el-option>
        <el-tree
            v-if="!!field.options.dataTreeEnabled"
             class="main-select-el-tree"
             ref="elTreeRef"
             :data="treeDataList"
             node-key="CID"
             highlight-current
             :props="defaultProps"
             @node-click="handleNodeClick"
             :current-node-key="currentNodeKeyValue"
             :expand-on-click-node="false"
             default-expand-all
           >
             <span class="custom-tree-node" slot-scope="{ node, data }">
               <i class=""></i> {{ data[defaultProps.label] }}</span
             >
           </el-tree>
    </el-select>
    <template v-if="isReadMode">
      <span class="readonly-mode-field">{{optionLabel}}</span>
    </template>
  </form-item-wrapper>
</template>

<script>
  import FormItemWrapper from './form-item-wrapper'
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
 // this.publicAttribute.value = node[this.field.options.filterTreeField] // 赋值 对外开发属性值
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

  export default {
    name: "select-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
      contentBoxHeight: {
        type: [Number, String],
        default: 0
      },
        // 控件来源父集 add by andy
      sourceVFormRender:{
        type: String,
        default: ""
      },
    },
    components: {
      FormItemWrapper,
    },
    inject: ['refList', 'globalOptionData', 'globalModel','getPageInstance','sourceVFormRenderState'],
    data() {
     // let _self = this
      return {
        publicAttribute:{
          value:"",
        },// 对外开发属性值
        currentNodeKeyValue: "",//当前选中的节点
        // 配置选项
        defaultProps: {
                children: "children",
                label: "label",
            },
        treeDataList:[],// 当前树列表数据
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        fieldModel_Computed:null,
        treeSelectedKey:null,
        rules: [],
      }
    },
    computed: {
   
      pageInstance() {
        // 获取列表示例
        return this.getPageInstance()
      },
      allowDefaultFirstOption() {
        return (!!this.field.options.filterable && !!this.field.options.allowCreate)
      },

    },
    watch:{
      fieldModel(n,o){
        if(n || n=='0' || n==0){
          this.convertValueToString()
        }else{
          //// 是否强制转换值为字符串
          if(!!this.field.options.convertToString){
            this.fieldModel_Computed=""
          }
         
        }
      }
    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },
    
    created() {
      //debugger
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initOptionItems()
      this.initFieldModel()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
     //debugger
          // 是否强制转换值为字符串
      this.convertValueToString()
    },

    beforeDestroy() {
      // debugger
      this.unregisterFromRefList()
    },

    methods: {
       // 是否强制转换值为字符串
      convertValueToString(){
        this.$nextTick(()=>{
        if(!!this.field.options.convertToString){
          this.fieldModel_Computed =  this.fieldModel+""
        }else{
          this.fieldModel_Computed =  this.fieldModel
        }
     })
      },
       //  setCurrentKey	//通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
       setTreeCurrentKey(){
        this.$nextTick(()=>{
           let elTreeRef = this.$refs["elTreeRef"]
           // debugger
            if(elTreeRef && !!this.fieldModel){
              elTreeRef.setCurrentKey(this.fieldModel)
            }
        })
      
       }
    }
  }
</script>

<style lang="scss" scoped>
  @import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

  .full-width-input {
    width: 100% !important;
  }
  .main-select-el-tree {
    .custom-tree-node {
      font-size: 14px;
      //background-color: transparent;
    }
  }
  ::v-deep .customform {
    .el-tag--info {
        width: auto;
        height: 24px;
        color: #1989FA;
        border-radius: 2px;
        border: 1px solid #1989FA;
        background: rgba(65, 158, 251, 0.10);
        .el-tag__close {
          font-size: 16px;
          color: #1989FA;
          background: none;
          &:hover {
            color: #1989FA;
            background: none;
          }
        }
      }
  }
</style>