export default {
  extension: {
    widgetLabel: {
      card: '卡片',
      alert: '提示',
      defaultmenubutton: '默认菜单',
      vxetable: '高级表格',
     // DIVContainer: 'DIV容器',
      splitpanes: '分栏容器',
      echarts: '图表',
      customTree: '自定义树',
      standardFrame: '标准弹框',
      iconInfo: '同环比信息图',
      colorBlockInfo: '颜色块信息图',
      collapseDesc: '分组描述',
      tabMenu: 'tab菜单',
      //divContainer: 'DIV容器',
      inputScan: '扫描输入框',
      descriptions: '描述列表',
    },

    setting: {
      cardFolded: '是否收起',
      cardShowFold: '显示折叠按钮',
      cardWidth: '卡片宽度',
      cardShadow: '显示阴影',

      alertTitle: '标题',
      alertType: '类型',
      description: '辅助性文字',
      closable: '是否可关闭',
      closeText: '关闭按钮文字',
      center: '文字居中',
      showIcon: '显示图标',
      effect: '显示效果',

    },

  }
}
