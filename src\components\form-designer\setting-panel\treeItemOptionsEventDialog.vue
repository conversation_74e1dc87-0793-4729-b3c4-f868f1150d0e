<template>
    <div class="commonSettingEventDialog">
        <el-form-item label-width="0">
            <!-- 交互事件 -->
            <el-divider class="custom-divider">{{title}}</el-divider>
        </el-form-item>
        <el-form-item label-width="0">
            <div class="flex justify-between">
                <div style="font-weight: bold;font-size: 13px;">事件</div>
                <div></div>
                <div> <el-button style="height:32px;" @click="actionBtn('iisAdd')" type="text" icon="vxe-icon-add"
                        size="mini">&nbsp;添加</el-button></div>
            </div>
        </el-form-item>
        <el-form-item label-width="0">
            <draggable tag="ul" :list="optionModel.itemEventList"
                v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
                <li v-for="(option, idx) in optionModel.itemEventList" :key="idx">
                    <el-checkbox v-model="option.check">
                        <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.label" size="mini"
                            style="width: 150px"></el-input>
                        <i class="iconfont icon-drag drag-option"></i>
                        <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger"
                            @click="deleteOption(option, idx)" icon="el-icon-minus" class="col-delete-button"></el-button>
                    </el-checkbox>
                </li>
            </draggable>
        </el-form-item>
        <el-dialog :title="`${title}编辑`" :visible.sync="showDialog" v-if="showDialog" :show-close="true"
            class="small-padding-dialog" v-dialog-drag append-to-body :close-on-click-modal="false"
            :close-on-press-escape="false" :destroy-on-close="true">
            <el-form :model="editEventItemForm" :rules="editEventItemFormRules" ref="editEventItemForm" label-width="100px">
                <el-form-item label="标签" prop="label">
                    <el-input v-model="editEventItemForm.label"></el-input>
                </el-form-item>
                <el-form-item label="唯一编码" prop="value">
                    <el-input v-model="editEventItemForm.value"></el-input>
                </el-form-item>
                <el-form-item  label="图标icon" prop="iconClass">
                    <el-input v-model="editEventItemForm.iconClass"></el-input>
                  
                </el-form-item>
                <el-form-item label="动作" prop="actionType">
                    <el-select @change="actionChangeEvent" v-model="editEventItemForm.otherParams.actionType"
                        placeholder="请选择动作">
                        <el-option label="未选择" value="-1"></el-option>
                        <el-option label="执行查询" value="api"></el-option>
                        <el-option label="弹框应用" value="popup"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 请选择查询 -->
                <div v-if="editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType == 'api'">
                    <el-form-item v-show="editEventItemForm.otherParams.actionType == 'api'" label="--选择查询"
                        prop="actionName">
                        <el-select v-model="editEventItemForm.actionParams.actionName" placeholder="请选择查询">
                            <el-option label="请选择" value=""></el-option>
                            <el-option :key="queryIndex + queryItem.value"
                                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                                :value="queryItem.value"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <!-- 请选择弹框 -->
                <div v-if="editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType == 'popup'">
                    <el-form-item v-show="editEventItemForm.otherParams.actionType == 'popup'" label="--选择弹框"
                        prop="actionName">
                        <el-select v-model="editEventItemForm.actionParams.actionName" placeholder="请选择弹框">
                            <el-option label="请选择" value=""></el-option>
                            <el-option :key="popupIndex + popupItem.value"
                                v-for="(popupItem, popupIndex) in designer.formConfig.popupList" :label="popupItem.label"
                                :value="popupItem.value"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <!-- 动态参数 -->
                <div v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType != '-1'"
                    style="font-weight: bold;">参数</div>
                <div
                    v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType != '-1'">
                    <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
                        <el-form-item label="KEY" prop="key">
                            <el-input placeholder="key" v-model="item.key"></el-input>
                        </el-form-item>

                        <el-form-item label="VALUE" prop="value">
                            <el-input placeholder="value" v-model="item.value"></el-input>
                        </el-form-item>
                        <!-- <div style="margin-bottom:20px;margin-left:5px"> <i @click="deleteParam(item, index)"
                                class="el-icon-delete"></i></div> -->
                                <div  style="margin-bottom:20px;margin-left:5px">  
                                    <!-- <el-checkbox style="margin-left:5px;" v-model="item.execNow">立刻执行</el-checkbox> -->
                                    
                                    <i style="margin-left:5px;" @click="deleteParam(item,index)"  class="el-icon-delete"></i>
                 
                </div>
                    </div>
                </div>
                <div
                    v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType != '-1'">
                    <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
                </div>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="large" @click="showDialog = false">
                    取消</el-button>
                <el-button size="large" type="primary" @click="saveCommonEventHandler">
                    确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
  import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n"
// import CodeEditor from '@/components/code-editor/index'
// import { deepClone, generateId } from "@/utils/util"
const default_editEventItemForm = {
    label: "",// 标签
    value:"",// 唯一编码
    iconClass:"vxe-icon-add",// 图标名称
    otherParams: {
        // hidden: false, // 隐藏
        // disabled: false,// 激活
        actionType: "-1",// 动作
        // condition: "", // 条件
        // debounceOrThrottleTime: 1, // 防抖、节流 时间
    },
    actionParams: {
        eventType: "",// 外部传入的参数contrlType>> 决定事件 类型列表
        contrlType: "",// 外部传入的参数 【vxetable,customTree,echarts,splitpanes,defaultmenubutton】
        actionName: "",
        // query:"",
        // hash:""
    },
}
export default {
    name: "treeItemOptionsEventDialog",
    mixins: [i18n],
    components: {
        //CodeEditor,
        Draggable
    },
    props: {
        title:{
            type:String,
            default:"项额外事件"
        },
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
        contrlType: String,// 控件类型名称
    },
    data() {
        return {
            currentEditOption: null,
            showDialog: false,
            actionParamsList: [],//{ key: "", valueType: "1", value: "" }
            editEventItemForm: Object.assign({}, default_editEventItemForm),
            editEventItemFormRules: {
                label: [
                    { required: true, message: '请输入标签名称', trigger: 'blur' },
                ],
                value: [
                    { required: true, message: '请输入唯一编码', trigger: 'blur' },
                ],
                iconClass: [
                    { required: true, message: '请输入图标样式', trigger: 'blur' },
                ],
            }
        }
    },
    computed: {
        formTemplateList() {
            return this.$store.state.formTemplateList
        }
    },
    // "queryList":[{"label":"组织查询","value":"query1","check":false,"canRemove":true,"otherParams":{"actionType":"api"},"actionParams":{"actionName":"api/DEMO/VXETableDemo/Data/Page","query":{"Pid":"5946218519199749"}}}]
    methods: {
        // 添加参数
        addParamsEvent() {
            let newValue = this.actionParamsList.length + 1
            this.actionParamsList.push(
                { key: "", execNow:false, value: "" }
            )
        },
        // 移除参数
        deleteParam(item, index) {
            this.actionParamsList.splice(index, 1)
        },

        showEditDialogEvent(option) {
            this.currentEditOption = option // 当前菜单属性
            this.showDialog = true

            this.editEventItemForm.label = option.label
            this.editEventItemForm.value = option.value
            this.editEventItemForm.iconClass = option.iconClass
            this.actionParamsList = [] // 默认,重置
            this.editEventItemForm.otherParams = cloneDeep(option.otherParams)
            this.editEventItemForm.actionParams = cloneDeep(option.actionParams)
            if(this.editEventItemForm.actionParams.hasOwnProperty("query")){
                this.actionParamsList= this.getQueryList()
            }
        },
         // 获取组合后的动态参数
         getQueryList(){
         // debugger
          let queryParamsList =[]
            //debugger {key:"",execNow:false,value:""}
            if(this.editEventItemForm.actionParams && this.editEventItemForm.actionParams.query && Object.keys(this.editEventItemForm.actionParams.query).length>0){
                //debugger
                for (const [key,value] of Object.entries(this.editEventItemForm.actionParams.query)) {
                    if(!!key){
                        // let newItem ={key,value} 多参数解析
                        queryParamsList.push(value)
                    }
                }
            }
            return queryParamsList
        },
        deleteOption(option, index) {
            // 是否可以移除
            if (!!option.canRemove) {
                this.optionModel.itemEventList.splice(index, 1)
            }
        },
        // 添加新的事件交互
        actionBtn(type = 'iisAdd') {
            switch (type) {
                case "iisAdd":
                    this.iisAdd()
                    break;

                default:
                    break;
            }
        },
        iisAdd() {
            if(!!!this.optionModel.hasOwnProperty("itemEventList")){
                this.$set(this.optionModel, "itemEventList", [])
            }
            let newValue = this.optionModel.itemEventList.length + 1
            this.optionModel.itemEventList.push(
                { label: '新项事件' + newValue, value: "event" + newValue,iconClass: "vxe-icon-add", check: false, canRemove: true, otherParams: {}, actionParams: {} }
            )
        },
         // 设置组合后的动态参数
         setQueryList(){
            let queryParams ={}
            if(this.actionParamsList && this.actionParamsList.length>0){
                this.actionParamsList.forEach(item=>{
                if(!!item.key){
                    queryParams[item.key] = item // 多参数
                }
                })
            }
            //debugger
            return queryParams
        },
        saveCommonEventHandler() {
            //debugger
      
            /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            // this.$set(this.formConfig, this.curEventName, this.commonEventHandlerCode)
            let _self = this
        this.$refs["editEventItemForm"].validate((valid) => {
            if (valid) {
                _self.currentEditOption.label = _self.editEventItemForm.label
                _self.currentEditOption.value = _self.editEventItemForm.value
                _self.currentEditOption.iconClass = _self.editEventItemForm.iconClass
                _self.currentEditOption.otherParams = cloneDeep(_self.editEventItemForm.otherParams) 
                _self.currentEditOption.actionParams = cloneDeep(_self.editEventItemForm.actionParams) 
                // 动态添加参数列表
                _self.currentEditOption.actionParams["query"] = _self.setQueryList()
                
                _self.showDialog = false
            } else {
                console.log('saveCommonEventHandler error submit!!');
                return false;
            }
            });
        },
    }
}
</script>
<style lang="scss">
.commonSettingEventDialog ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
}

li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
}

.drag-option {
    cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
}

.dialog-footer .el-button {
    width: 100px;

}</style>