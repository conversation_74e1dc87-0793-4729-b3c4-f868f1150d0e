// import Vue from 'vue'
import VXETable, { RenderOptions } from 'vxe-table'

import Vue from 'vue'
// 自定义 逻辑渲染控件
import customRenderCtrl from '@/components/override/vxe-table/custom/customRenderCtrl'
Vue.component('customRenderCtrl', customRenderCtrl)

// ICON图文
import IconTextColumn from '@/components/override/vxe-table/custom/IconTextColumn'
Vue.component('IconTextColumn', IconTextColumn)

// 状态栏 控件
import statusColumnAD from '@/components/override/vxe-table/custom/statusColumnAD'
Vue.component('statusColumnAD', statusColumnAD)

// 自定义 列表 资料弹框查看
import popupTextarea from '@/components/override/vxe-table/custom/popupTextarea'
Vue.component('popupTextarea', popupTextarea)

VXETable.renderer.add('IconTextColumn', {
  // 默认显示模板 v-model={row[column.field]}
  renderDefault (h, renderOpts, params) {
    const { row, column } = params
    // const { events } = renderOpts
    // let rowVal = row[column.field]
    return [<IconTextColumn renderType="defaultRender" rowData={row} currentValue={row[column.field]}   field = {renderOpts.options.field} fieldRules= {renderOpts.options.fieldRules} itemOptions={renderOpts.options}></IconTextColumn>]
  }
})


// 创建一个 单元格 disabled checkbox 渲染
VXETable.renderer.add('vxeCheckbox', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      // (不可编辑 勾选框)
      return [
         <vxe-checkbox checked-value={1} unchecked-value={0} value={row[column.field]} ></vxe-checkbox>
      ]
   
    }
  })

  VXETable.renderer.add('statusColumn', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      let optionDisabled = false
      if(renderOpts.options && !!renderOpts.options.disabled){
          optionDisabled = true
      }
      let rowVal = row[column.field ]
      return [<statusColumnAD disabled={optionDisabled} rowData={row} currentValue={row[column.field]}   field = {renderOpts.options.field}  itemOptions={renderOpts.options} ></statusColumnAD>]
      // return [<el-switch active-value='A' inactive-value='D' v-model={rowVal}></el-switch>]
    }
  })

  VXETable.renderer.add('statusColumnYN', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      //debugger
      let rowVal = row[column.field ]
      return [<el-switch active-value='Y' inactive-value='N' v-model={rowVal}></el-switch>]

    }
  })
  VXETable.renderer.add('statusColumnTF', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      //debugger
      let rowVal = row[column.field ]
      return [<el-switch  v-model={rowVal}></el-switch>]

    }
  })
  VXETable.renderer.add('statusColumn10', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      //debugger
      let rowVal = row[column.field ]
      return [<el-switch active-value={1}inactive-value={0} v-model={rowVal}></el-switch>]

    }
  })

  VXETable.renderer.add('customSelect', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
      const { row, column } = params
      const { events } = renderOpts
      //debugger
      let rowVal = row[column.field]
      return [<customSelect renderType="defaultRender" defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} itemOptions={renderOpts.options}></customSelect>]
    }
  })

  VXETable.renderer.add('reportSelect', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
       const { row, column } = params
 
      let optionDisabled = false
      if(renderOpts.options && !!renderOpts.options.disabled){
          optionDisabled = true
      }
      return [
        <reportSelect renderType="defaultRender" optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportSelect>
      ]
    }
  })

  VXETable.renderer.add('customRenderCtrl', {
    // 默认显示模板
    renderDefault (h, renderOpts, params) {
       const { row, column } = params
 
      let optionDisabled = false
      if(renderOpts.options && !!renderOpts.options.disabled){
          optionDisabled = true
      }
      return [
        <customRenderCtrl renderType="defaultRender" rowData={row}  defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></customRenderCtrl>
      ]
    }
  })

  VXETable.renderer.add('popupTextarea', {
    renderDefault (h, renderOpts, params) {
      //debugger
      const { row, column } = params
      const { events } = renderOpts
     // debugger
      let rowVal = row[column.field ]
      return [<popupTextarea row={row} $tableInfo={params} field={column.field} fieldTitle={column.title} fieldOptions={renderOpts.attrs.item} currentValue={rowVal}></popupTextarea>]
    }
  })
  