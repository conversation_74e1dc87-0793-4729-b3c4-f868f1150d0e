<template>
  <container-item-wrapper :widget="widget">
    <!-- {{ contentBoxHeight }}style="border:1px solid red;" -->
    <div :key="widget.id"  class="splitpanes-container" :style="[{ 'margin-top': (!!widget.options.asMainContainer?10:0) + 'px'}]"  v-show="!widget.options.hidden">
      <splitpanes class="default-theme" :ref="widget.id" :class="[customClass]" :horizontal="widget.options.horizontal"
        :push-other-panes="widget.options.pushOtherPanesDefault" :dbl-click-splitter="widget.options.dblClickSplitter"
        :rtl="widget.options.RightToLeft" :id="`splitpanes_${ widget.id}`" :first-splitter="widget.options.firstSplitter" @resize="debouncedFn_paneSize"

        :style="[{ height: getSettingHeight(),'margin-top': (widget.options.containerMarginTop) + 'px','margin-top': (widget.options.containerMarginTop) + 'px','margin-right': (widget.options.containerMarginRight) + 'px','margin-bottom': (widget.options.containerMarginBottm) + 'px','margin-left': (widget.options.containerMarginLeft) + 'px' }]">
        
        <pane style="padding: 2px;background-color: #fff;" :size="pane.options.wtPercent" v-for="(pane, index) in widget.panes" :key="index">
          <template v-if="index==0">
            <div v-html="displayNoneStyle"></div>
          </template>
          <div v-html="bgColorStyle"></div>
          <div v-html="btnColorStyle"></div>
          <div :label="pane.options.label" :name="pane.options.name" style="height:100%;">
            <template v-for="(subWidget, swIdx) in pane.widgetList">
              <template v-if="'container' === subWidget.category">
                <component :sourceVFormRender="sourceVFormRender" :formConfig="formConfig" :contentBoxHeight="getContentBoxHeight(pane)"
                  :is="getComponentByContainer(subWidget)" :widget="subWidget" :key="swIdx" :parent-list="pane.widgetList"
                  :index-of-parent-list="swIdx" :parent-widget="widget" :sub-form-row-id="subFormRowId"
                  :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex">
                  <!-- 递归传递插槽！！！ -->
                  <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                    <slot :name="slot" v-bind="scope" />
                  </template>
                </component>
              </template>
              <template v-else>
                <component :sourceVFormRender="sourceVFormRender" :formConfig="formConfig" :contentBoxHeight="getContentBoxHeight(pane)"
                  :is="subWidget.type + '-widget'" :field="subWidget" :key="swIdx" :parent-list="pane.widgetList"
                  :index-of-parent-list="swIdx" :parent-widget="widget" :sub-form-row-id="subFormRowId"
                  :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex">
                  <!-- 递归传递插槽！！！ -->
                  <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                    <slot :name="slot" v-bind="scope" />
                  </template>
                </component>
              </template>
            </template>
          </div>
        </pane>
        
      </splitpanes>
    </div>

  </container-item-wrapper>
</template>

<script>
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import cloneDeep from "clone-deep"
import refMixin from "@/components/form-render/refMixin"
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper'
import containerItemMixin from "@/components/form-render/container-item/containerItemMixin"
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
import { useDebounceFn } from "@vueuse/core";
export default {
  name: "splitpanes-item",
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    formConfig: Object, // add by andy 202301
    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    contentBoxHeight: {
        type: [Number, String],
        default: 0
    },
              // 控件来源父集 add by andy
   sourceVFormRender:{
        type: String,
        default: ""
      },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    designState: {
      type: Boolean,
      default: false
    },
  },
  inject: ['refList', 'sfRefList', 'globalModel','sourceVFormRenderState'], 
  data() {
    return {
      bgColorStyle:"",// 背景颜色
      btnColorStyle:"",// 拖拽按钮颜色
      displayNoneStyle:"",
      debouncedFn_paneSize: Function,
      activepaneName: '',
      isToggle:false,
      beforeTogglePanes:[],
    }
  },
  computed: {
    visiblepanes() {
      return this.widget.panes.filter(tp => {
        return !tp.options.hidden
      })
    },
    paneTotalHeight(){
   
      let tableHeight = Number(this.widget.options.settingHeight)
      if(tableHeight>0){
        console.log("=====paneTotalHeight====")
        return tableHeight
      }
      
       // asMainContainer作为主容器时,需要距离顶部10PX
      let height = parseInt(this.contentBoxHeight)
      let _OffsetHeightNumber =Number(!!this.widget.options.OffsetHeightNumber?this.widget.options.OffsetHeightNumber:0)
      if(!!this.widget.options.asMainContainer){
        height = this.contentBoxHeight + 52 // 高偏移量（作为主容器时使用，也就是操作按钮区域的高度）
        //console.log("========高偏移量（作为主容器时使用，也就是操作按钮区域的高度）=="+height);
      }
      height = (height+_OffsetHeightNumber)
      return height
    }

  },
  created() {
    this.initRefList()
  },
  mounted() {
    this.setBgColor()
    this.setBtnColor()
    this.debouncedFn_paneSize = useDebounceFn((params) => {
      this.paneSizeEvent(params)
      
    }, 300)
    this.setHideDragItemStyle()
    this.setDefaultHideType()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
    setDefaultHideType(){
      let type = this.widget.options.defaultHideType
      //debugger
      switch (type) {
        case "left":
            this.toggleFnLeft()
          break;
          case "right":
          this.toggleFn()
          break;
        default:
          break;
      }
    },
    // 隐藏右边 对外调用展开、收缩功能
    toggleFn(){
      if(this.beforeTogglePanes && this.beforeTogglePanes.length==0){
        // 复制
        this.beforeTogglePanes = cloneDeep(this.widget.panes) 
      }
      if(!!this.isToggle){
        // 回滚 初始状态
        this.widget.panes =  cloneDeep(this.beforeTogglePanes) 
      }else{
        // 设置第一个为100，其它为0
         let panes = this.widget.panes
         panes = panes.map((item, index) => {
          if(index==0){ 
             item.options.wtPercent = 100
          }else{
             item.options.wtPercent = 0
          }
          return item
        })
      }
      this.isToggle = !this.isToggle // 取反
    },
    // 隐藏左边 对外调用展开、收缩功能
    toggleFnLeft(){
      if(this.beforeTogglePanes && this.beforeTogglePanes.length==0){
        // 复制
        this.beforeTogglePanes = cloneDeep(this.widget.panes) 
      }
      if(!!this.isToggle){
        // 回滚 初始状态
        this.widget.panes =  cloneDeep(this.beforeTogglePanes) 
      }else{
        // 设置第一个为100，其它为0
         let panes = this.widget.panes
         panes = panes.map((item, index) => {
          if(index==1){
             item.options.wtPercent = 100
          }else{
             item.options.wtPercent = 0
          }
          return item
        })
      }
      this.isToggle = !this.isToggle // 取反
    },
    // 设置是否隐藏拖拽 按钮
    setHideDragItemStyle(){
      let panes = this.widget.panes
      this.displayNoneStyle =""
      if(!!this.widget.options.hideDragItem && panes.length>0){//
        this.displayNoneStyle =`<style type="text/css">#splitpanes_${this.widget.id} .splitpanes__splitter {display:none;} </style>`
      }else{
        // 
        this.displayNoneStyle =`<style type="text/css">#splitpanes_${this.widget.id} .splitpanes__splitter {display:block;} </style>`
      }
    },
    // 设置拖拽条背景样式颜色
    setBgColor(){
      if(!!this.widget.options.bgColor){
        let bgColor= this.widget.options.bgColor
        this.bgColorStyle =`<style type="text/css">
      .splitpanes.default-theme .splitpanes__pane {
          background-color: ${bgColor};
      }
      .splitpanes.default-theme .splitpanes__splitter {
        background-color: ${bgColor};
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        position: relative;
        -ms-flex-negative: 0;
        flex-shrink: 0
    }
      .default-theme.splitpanes--vertical>.splitpanes__splitter,
      .default-theme .splitpanes--vertical>.splitpanes__splitter {
          width: 7px;
          border-left: 1px solid ${bgColor};
          margin-left: -1px
      }
      .default-theme.splitpanes--horizontal>.splitpanes__splitter,
      .default-theme .splitpanes--horizontal>.splitpanes__splitter {
          height: 7px;
          border-top: 1px solid ${bgColor};
          margin-top: -1px
      }
      </style>`
      }
     
    },
    setBtnColor(){
          let btnColor= this.widget.options.btnColor
          if(!!btnColor){
            this.btnColorStyle =`<style type="text/css">
            .splitpanes.default-theme .splitpanes__splitter:before,
            .splitpanes.default-theme .splitpanes__splitter:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  background-color: ${btnColor};
                  -webkit-transition: background-color .3s;
                  -o-transition: background-color .3s;
                  transition: background-color .3s
              }

              .splitpanes.default-theme .splitpanes__splitter:hover:before,
              .splitpanes.default-theme .splitpanes__splitter:hover:after {
                  background-color: ${btnColor};
              }
          </style>`
          }
        
        },
    paneSizeEvent(params) {
      let _self = this
      this.$nextTick(() => {
        let panes = _self.widget.panes
        panes = panes.map((item, index) => {
          let curPercent = Number((params[index].size).toFixed(2))
          // console.log("========paneSizeEvent=curPercents=="+curPercent);
          item.options.wtPercent = curPercent
          return item
        })
      })


    },
    getSettingHeight() {
     // debugger
     // console.log("==splitpanes-item======getSettingHeight=change=");
      if (this.previewState) {
         if(!!this.widget.options.asMainContainer){
               return `600px`
              }else{
                return `300px`
              }
      }
      // 设置了固定高度
      let tableHeight = Number(this.widget.options.settingHeight)
      if (!!tableHeight) {
        if (tableHeight > 1) {
          return `${tableHeight}px`
        } else {
          // 小于1时， 百分百占用高度
          let maxHeight = (this.paneTotalHeight * tableHeight) + 'px'
          return maxHeight
        }

      } else {
        // 默认
        if (this.paneTotalHeight > 0) {
          //console.log("==paneTotalHeight="+this.paneTotalHeight);
       
          return parseInt(this.paneTotalHeight) + 'px'
        } else {
          return ``
        }
      }
    },
    // 获取sub 内容高
    getContentBoxHeight(pane) {
     // debugger
      let tableHeight = Number(this.widget.options.settingHeight)
      if (tableHeight > 1) {
          return `${tableHeight}px`
      }
      let testHeight = 300
      if(!!this.widget.options.asMainContainer){
            testHeight = 600
          }else{
            testHeight = 300
          }
      if (this.previewState) {
        // 预览状态下，固定高度分配
        if (!!this.widget.options.horizontal) {
          let size = pane.options.wtPercent
          let height = size / 100 * testHeight
          return height
        } else {
          return testHeight
        }
      }
      if (!!pane) {
        // 是否上下布局 
        if (!!this.widget.options.horizontal) {
          let size = pane.options.wtPercent
          //console.log("======上下布局 size=:"+size);
          let height = parseInt( size / 100 * (this.paneTotalHeight))
          //console.log("==splitpanes-item==getContentBoxHeight====上下布局 ="+height);
          return height
        } else {
         // console.log("==splitpanes-item==getContentBoxHeight====左右布局 ="+this.paneTotalHeight);
          return (this.paneTotalHeight)
        }
      }

    }


  },
}
</script>

<style lang="scss" scoped></style>
