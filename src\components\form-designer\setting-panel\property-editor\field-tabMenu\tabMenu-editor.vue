<template>
    <div>
      <el-form-item label-width="0">
      <commonParamsOnChange contrlType="tabMenu" :designer="designer" :selectedWidget="selectedWidget"
          :optionModel="optionModel"></commonParamsOnChange>
      </el-form-item>
        <el-form-item label="激活颜色">
             <el-color-picker  
           :predefine="predefineColors" v-model="optionModel.activeColor"></el-color-picker>
        </el-form-item>
        <el-form-item label="悬浮颜色">
             <el-color-picker  
           :predefine="predefineColors" v-model="optionModel.hoverColor"></el-color-picker>
        </el-form-item>
        <el-form-item label-width="0">
      <el-divider class="custom-divider">其它信息项</el-divider>
    </el-form-item>
    <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <tabMenuItemList :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></tabMenuItemList>
      </el-form-item>
    </div>
  </template>
  
  <script>
  import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
    import i18n from "@/utils/i18n"
    import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
    import tabMenuItemList from './tabMenuItemList.vue'
    export default {
      name: "tabMenu-editor",
      mixins: [i18n],
      components:{selectDataSourceApi,tabMenuItemList,commonParamsOnChange},
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
      data(){
        return {
          predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
          '#303133',
          '#DCDFE6',
          // '#3799FF',
          // 'rgba(255, 69, 0, 0.68)',
          // 'rgb(255, 120, 0)',
          // 'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577'
        ],
        }
      }
    }
  </script>
  
  <style scoped>

  </style>
  