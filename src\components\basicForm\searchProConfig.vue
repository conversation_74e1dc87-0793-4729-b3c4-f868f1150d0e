<template>
    <div class="flex flex-col">
        <el-input type="textarea" v-model="currentItem" :rows="6" disabled style="width: 530px;" v-bind="$attrs" v-on="$listeners">
        </el-input>
        <el-button @click="searchProSettingFn()" type="text">
            <span :style="[{ color: (!!$attrs.value) ? `green` : `` }]">{{ (!!$attrs.value)
                ?
                `编辑高级搜索条件JSON` : `添加高级搜索条件JSON` }}</span>
        </el-button>


        <vxe-modal title="配置高级搜索" width="80%" :position="{ top: 50 }" 
            v-model="showEditDialogFlag" show-footer>
            <div class="flex justify-between">
                <div style="margin-bottom: 10px;" class="flex justify-center">
                    <el-button size="small" style="margin-right: 10px;" type="primary" @click="addGroupClick"><i
                            class="vxe-icon-add"></i>&nbsp;新增(条件组)</el-button>
                    <el-button size="small" style="margin-right: 10px;" type="success" @click="addConditionClick"><i
                            class="vxe-icon-add"></i>&nbsp;新增条件</el-button>
                  
                   
                </div>
                <dataSourceAndSet @dataSetChange="dataSourceChangeEvent"></dataSourceAndSet>
            </div>
            <div class="tableTemplateClass">
                <vxe-table :loading="tableDataLoading" ref="vxeTableRef" :edit-rules="validRules" keep-source row-id="id"
                    @current-change="currentChange" max-height="400px" resizable show-overflow border size="mini" row-key
                    :highlight-current-row="true" :row-config="{ isHover: true, isCurrent: true }"
                    :edit-config="{ trigger: 'click', mode: 'cell' }"
                    :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', expandAll: true }"
                    :data="tableData" :scroll-y="{ enabled: false }">
                    <vxe-column field="field" title="参数字段" width="250" tree-node header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <span v-if="row.parentId == -1">+</span>
                                <el-input size="mini" :style="[{ width: (row.parentId == -1 ? '82%' : '86%') }]" v-model="row.field"
                                    placeholder="参数字段" />
                                <el-dropdown size="mini" style="cursor:pointer;">
                                    <span>
                                        <i class="el-icon-arrow-down el-icon--right"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item @click.native="change_dropdownItem(item, row, index)" :key="index"
                                            v-for="(item, index) in fieldDropdownItems">{{ item.field }}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </template>
                            <template v-else>
                                <span>条件组</span>
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="title" title="字段描述" header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-input size="mini" v-model="row.title" placeholder="字段描述" />
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="tableName" title="表名称" header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-input size="mini" v-model="row.tableName" placeholder="表名称" />
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="controlType" title="控件类型" width="150" header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-select size="mini" v-model="row.controlType" placeholder="请选择">
                                    <el-option label="文本" value="text" />
                                    <el-option label="数字" value="number" />
                                    <el-option label="下拉框" value="reportSelect" />
                                    <el-option label="下拉框(多选)" value="reportMulSelect" />
                                    <el-option label="下拉框(表)" value="reportSelectTable" />
                                    <el-option label="下拉框(自定义单选)" value="customSelect" />
                                    <el-option label="下拉框(自定义多选)" value="customMulSelect" />
                                    <el-option label="日期选择" value="date" />
                                    <el-option label="日期时间" value="datetime" />
                                    <el-option label="日期范围选择" value="daterange" />
                                    <el-option label="日期时间范围" value="datetimerange" />
                                </el-select>
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="controlOptions" title="控件配置" width="90" header-align="center" align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-button size="mini" @click="showSearchProConfigEditFn(row)" type="text">
                                    <span :style="[{ color: !!row.controlOptions ? `green` : `` }]">{{ !!row.controlOptions
                                        ? `编辑配置` :
                                        `添加配置`
                                    }}</span>
                                </el-button>
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="comparator" title="比较符" width="125" header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-select size="mini" @change="change_comparator(row, $event)" v-model="row.comparator"
                                    placeholder="请选择">
                                    <el-option v-for="(item, index) in comparatorOptions" :key="index" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="defaultValue" title="默认值"
                        :title-help="{ message: '设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year),时间范围add-1-day#add-10-day,使用#分割' }"
                        header-align="center">
                        <template #default="{ row }">
                            <template v-if="row.parentId != 0">
                                <el-input size="mini" v-model="row.defaultValue" placeholder="默认值" />
                            </template>
                        </template>
                    </vxe-column>
                    <vxe-column field="relationSign" title="关系" width="90" header-align="center">
                        <template #default="{ row }">

                            <el-select size="mini" @change="change_relationSign(row, $event)" v-model="row.relationSign"
                                placeholder="请选择">
                                <el-option label="并" :value="0" />
                                <el-option label="或" :value="1" />
                                <el-option label="无" :value="-1" />
                            </el-select>
                        </template>
                    </vxe-column>
                    <vxe-column title="操作" width="80" header-align="center" align="center" show-overflow>
                        <template #default="{ row }">
                            <vxe-button size="mini" style="color:red;" type="text" icon="vxe-icon-delete"
                                @click="removeRow(row)">删除</vxe-button>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>
            <template v-slot:footer>
                <el-button size="medium" @click="submitEditDialog()" type="primary">确定</el-button>
                <el-button size="medium" @click="showEditDialogFlag = false">取消</el-button>
            </template>
        </vxe-modal>
        <vxe-modal title="控件配置" width="70%" :position="{ top: 80 }" v-model="showSearchProConfigEdit" show-footer>
            <div class="flex justify-between">
                
                <dataSourceAndSet @dataSetChange="dataSetChange" :configItem="configFormItem"></dataSourceAndSet>

            </div>
           
            <div class="tableTemplateClass">
                <vxe-table :loading="loading_tableData" ref="searchProConfigEditTableRef" row-id="id" max-height="400px"
                    resizable show-overflow border size="mini" row-key :highlight-current-row="true"
                    :row-config="{ isHover: true, isCurrent: true }" :edit-config="{ trigger: 'click', mode: 'cell' }"
                    :data="searchProConfigEditTableData" :scroll-y="{ enabled: false }">

                    <vxe-column field="field" title="字段名" header-align="center" align="center">
                        <template #default="{ row }">

                            <vxe-input v-model="row.field" placeholder="字段名"></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column field="title" title="字段描述" header-align="center" align="center">
                        <template #default="{ row }">
                            <vxe-input v-model="row.title" placeholder="字段描述"></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column field="iisShowList" width="80" title="是否显示" header-align="center" align="center">
                        <template #default="{ row }">
                            <el-checkbox size="mini" v-model="row.iisShowList"></el-checkbox>
                        </template>
                    </vxe-column>
                    <vxe-column field="isSelectTextField" width="80" title="下拉Text" header-align="center" align="center">
                        <template #default="{ row }">
                            <el-checkbox size="mini" v-model="row.isSelectTextField"></el-checkbox>
                        </template>
                    </vxe-column>
                    <vxe-column field="isSelectKeyField" width="80" title="下拉Key" header-align="center" align="center">
                        <template #default="{ row }">

                            <el-checkbox size="mini" v-model="row.isSelectKeyField"></el-checkbox>
                        </template>
                    </vxe-column>
                    <vxe-column field="width" title="列宽" header-align="center" align="center">
                        <template #default="{ row }">
                            <vxe-input :controls=false type="number" v-model="row.width"></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column field="fieldOrder" title="列表顺序" header-align="center" align="center">
                        <template #default="{ row }">
                            <vxe-input :controls=false type="number" v-model="row.fieldOrder"></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column title="操作" width="80" header-align="center" align="center" show-overflow>
                        <template #default="{ row }">
                            <vxe-button style="color:red;" type="text" icon="vxe-icon-delete"
                                @click="removeSearchProConfigRow(row)">删除</vxe-button>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>

            <template v-slot:footer>
                <div class="flex justify-between">
                    <div>
                        <el-button style="color:#ffffff" type="primary" size="medium" icon="el-icon-plus" plain round
                            @click="addNewField()">添加新字段</el-button>
                    </div>
                    <div>
                        <el-button size="medium" type="danger" @click="resetConfig()">清空配置</el-button>
                    </div>
                    <div>
                        <el-button size="medium" @click="showSearchProConfigEdit = false">取消</el-button>
                        <el-button size="medium" @click="submitSearchProConfigEdit()" type="primary">确定</el-button>
                    </div>
                </div>
            </template>
        </vxe-modal>
    </div>
</template>
<script>
 const cloneDeep = require("clone-deep");


import dataSourceAndSet from './dataSourceAndSet.vue'
const defaultConfigForm = {
    CDATASOURCE_ID: -1,// 数源ID CID
    CDATASET_ID: -1,// 数据集ID
    CIS_PAGE: "N",// 是否分页
    CPAGE_SIZE: 10,// 分页数
    sourceKey: "",// 数据源key
    matchField: "",// 字段转换
    searchParams: "",// 过滤参数
    CDATAS: [],//  表格数据>>配置参数JSON,存储时，需要转换为字符串
    loadDataConfig: {},
}

import dayjs from 'dayjs'
import { sqlServeSearchType } from "@/enum/enumData"
export default {
    name: 'searchProConfig',
    components: {dataSourceAndSet},
    props: {
       
        dataFrom: {
            type: String,
            default: "form"
        },

        // 当前字段表单
        formData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
        // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
        dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
        // 当前字段:value
        currentValue: {
            type: [String, Number, Array],
            default: ""
        },

        // 是否可用
        disabled: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            currentItem:"",
            optionModel:{
                actionName:"",//方法名称
                searchProSettingID:"", // 页面ID
            },
            designer:{
                formConfig:{
                    queryList:[]
                }
            },
            loadDataConfig: {},// 查询数据配置信息，来自查询列表，此处只能时固定值
            needConfigType: ["reportSelect", "reportSelectTable", "customSelect","customMulSelect", "standardFrame", "reportMulSelect", "customRenderCtrl"],
            currentEditSearchProConfigRow: null,// 当前编辑控件配置信息
            dataModelList: [],
            loading_tableData: false,
            searchProConfigEditTableData: [],
            currentCtrlType: "text",// 当前控件类型
            configFormItem: Object.assign({}, defaultConfigForm),
            configFormFules: {},// 配置信息验证规则
            showSearchProConfigEdit: false,// 控件配置 弹框
            fieldDropdownItems: [],// 参数字段模型列表
            editResDataObj: null,// 编辑时，对象
            tableDataLoading: false,
            submitBtnLoading: false,
            removeBtnLoading: false,
            currentSelectedRow: null,
            currentSelectedItem: null,
            rowCheck: null,
            showEditDialogFlag: false,
            comparatorOptions: sqlServeSearchType,
            tableData: [],
            validRules: {
                field: [
                    { required: true, message: '必填' },
                ]
            }
        }
    },
    watch:{
        currentValue(n, o) {

            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
            }
        },
        showSearchProConfigEdit(n,o){
            if(!n){
                // 关闭时，清空数据
                this.searchProConfigEditTableData =[]
                this.configFormItem = Object.assign({}, defaultConfigForm)
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.currentItem = this.currentValue
        })
    },
    methods: {
        // 比较符 改变对应的备份值，方便前端重置时使用
        change_comparator(row, $event) {
            row.comparatorBackup = $event
        },
        // 关系 改变对应的备份值，方便前端重置时使用
        change_relationSign(row, $event) {
            row.relationSignBackup = $event
        },
        // 添加新字段 控件配置表格信息
        async addNewField() {
            const $table = this.$refs["searchProConfigEditTableRef"]
            let randomIndex = Math.floor(Math.random() * 10000 + 1)
            let records = {
                field: "newField" + randomIndex,
                title: "新字段" + randomIndex,
                width: 200,
                fieldOrder: 100
            }
            await $table.insertAt(records, -1)
        },
        // 提交控件配置弹框
        submitSearchProConfigEdit() {
            this.configFormItem.CDATAS = this.getControlOptionsData()
            this.currentEditSearchProConfigRow.controlOptions = cloneDeep(this.configFormItem)
            this.showSearchProConfigEdit = false // 关闭当前弹框
        },
        // 获取控件类型配置表格信息
        getControlOptionsData() {
            let _tablefullData = []
            const $table = this.$refs["searchProConfigEditTableRef"]
            if ($table) {
                _tablefullData = $table.getTableData().visibleData //处理条件之后的全量表体数据
            }
            return _tablefullData
        },
        // 清空配置
        resetConfig() {
            this.$confirm('此操作将删除控件配置数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.currentEditSearchProConfigRow.controlOptions = null
                this.dataModelList = []
                this.searchProConfigEditTableData = []
                this.configFormItem = Object.assign({}, defaultConfigForm)
                setTimeout(() => {
                    this.showSearchProConfigEdit = false // 关闭当前弹框
                }, 100)
            }).catch(() => {

            });

        },
       
         // 数据源API 改变
        dataSetChange(params){
            let _self = this
            this.loading_tableData = true
            setTimeout(()=>{
                _self.dataModelList = params.dataModelList
                _self.configFormItem.CDATASET_ID = params.configFormItem.CDATASET_ID
                _self.configFormItem.CDATASOURCE_ID = params.configFormItem.CDATASOURCE_ID
                _self.dataModelList=_self.dataModelList.map(item=>{
                    if(!item.title){
                        item.title =  item.field
                    }
                    return item
                })
                _self.searchProConfigEditTableData = cloneDeep(_self.dataModelList)
                _self.loading_tableData = false
            },1000)
         
        },
        // // 获取控件API 配置参数
        // getCtrlLoadDataConfig(actionName) {
        //     let _config = ""
        //     let queryList = this.designer.formConfig.queryList
        //     let currentQueryList = queryList.filter(item => {
        //         if (item.value == actionName) {
        //             return item
        //         }
        //     })
        //     if (currentQueryList && currentQueryList.length > 0) {
        //         _config = currentQueryList[0]
        //     }
        //     return _config
        // },
        // 获取数据源数据
        async getDataModelList(datasetId) {
            let dataModelList = []
            let params = {
                condition: "",
                datasetId: datasetId
            }
            let _url = "/api/MD/DataSetModel/GetList"
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                    dataModelList = res.Datas
                }
            })
            return dataModelList
        },
        // 格式化模型数据
        formatDataModel(dataList) {
            let newDataList = []
            if (dataList && dataList.length > 0) {
                let innerField = [
                    "CID",
                    "CDATETIME_CREATED",
                    "CUSER_CREATED",
                    "CDATETIME_MODIFIED",
                    "CUSER_MODIFIED",
                    "CINSTANCE_ID",
                    "CROWREMARK",
                    "CENTERPRISE_CODE",
                    "CORG_CODE",
                ]
                dataList.forEach(oldItem => {
                    let newItem = {
                        field: oldItem.CCOLUMN_NAME,
                        title: oldItem.CCOLUMN_DESC,
                        CID: oldItem.CID,
                        titleHelp: "",
                        iisShowList: 0,
                        isSelectTextField: 0,
                        isSelectKeyField: 0,
                        width: 200,
                        titleAlign: "center",
                        fieldOrder: 100,
                        align: "center",
                        controlType: "text",
                        groupTitle: "",
                        iisSummary: 0,
                        fieldRules: ""
                    }
                    // 内置字段不显示
                    if (innerField.includes(newItem.field)) {
                        newItem.iisShowList = 0
                    }
                    newDataList.push(newItem)
                })
            }

            return newDataList
        },
        // 打开当前控件配置弹框
        showSearchProConfigEditFn(row) {
            if (!this.needConfigType.includes(row.controlType)) {
                this.$message({
                    type: 'warning',
                    message: '当前控件无需配置,请选择其它控件类型！ '
                });
                return
            }
            this.currentEditSearchProConfigRow = row
         
            try {
                if (row.controlOptions) {
                    this.configFormItem = cloneDeep(row.controlOptions)
                    if (this.configFormItem.CDATAS && this.configFormItem.CDATAS.length > 0) {
                        this.searchProConfigEditTableData = this.configFormItem.CDATAS
                    }

                }


            } catch (error) {

            }

            this.showSearchProConfigEdit = true
        },
        // 直接使用下拉获取对应字段的模型
        change_dropdownItem(item, row, index) {
            row.field = item.field
            row.title = (!!item.title?item.title:item.field)
        },
     
        // 数据源切换后，获取当前查询的配置信息
        dataSourceChangeEvent(params) {
             //debugger
            if (!params) {
                return
            }
            this.fieldDropdownItems = params.dataModelList
        },
        // 删除高级查询配置信息
        removeConditionClick() {
            this.$confirm('此操作将删除高级查询配置数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteData()
            }).catch(() => {

            });

        },
        async deleteData() {
            let _self = this
            this.removeBtnLoading = true
            let _url = `/api/SYSTEM/Common/Remove`

            let params = {
                "table": "TBL_SYS_ADVANCED_SEARCH", // 固定参数
                CID: this.optionModel.searchProSettingID // 页面ID
            }

            await request['post'](_url, params).then(res => {
                if (!!res.Success) {
                    this.removeBtnLoading = false
                    this.optionModel.searchProSettingID = null
                    this.tableData = []
                    this.$message({
                        message: !!res.Content ? res.Content : '执行成功',
                        type: 'success'
                    });
                    setTimeout(() => {
                        // 关闭高级搜索配置弹框
                        _self.showEditDialogFlag = false
                    }, 300)
                }
            })
        },
        // 查询高级查询配置信息
        async loadConditionData(defaultData_CCONDITION) {
            // debugger
            this.tableDataLoading = true
            let tableCCONDITION = JSON.parse(defaultData_CCONDITION)
            if (tableCCONDITION.tableData) {
                this.tableData = tableCCONDITION.tableData
            }
            if (tableCCONDITION.loadDataConfig) {
                this.loadDataConfig = tableCCONDITION.loadDataConfig
            }
            this.tableDataLoading = false
        },
        currentChange(tableInfo) {
            if (tableInfo.row.parentId == 0) {
                this.currentSelectedRow = tableInfo.row
            } else {
                this.currentSelectedRow = null
            }
            this.currentSelectedItem = tableInfo.row
        },
        // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year)
        setDateTimeDefaultVal(type, defaultVal) {
            let dValue = null
            let dateFormatStr = ""
            // 指定日期 格式化 格式
            switch (type) {
                case "date":
                    dateFormatStr = 'YYYY-MM-DD'
                    break;
                case "dateTime":
                    dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                    break;
                // case 'month':
                //     dateFormatStr = 'YYYY-MM'
                //     break    
                default:
                    dateFormatStr = ""
                    break
            }
            if (!!defaultVal) {
                if (defaultVal == 'curdate') {
                    // 当前日期
                    dValue = dayjs().format(dateFormatStr);
                } else if (defaultVal.includes('-')) {
                    // 指定日期加减
                    //dayjs().add(7, 'day').format('YYYY-MM-DD');
                    //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                    let daysetArray = defaultVal.split('-')
                    dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                } else {
                    //空日期
                    dValue = null
                }
            }

            return dValue
        },
        // 设置时间范围格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year)
        setDateTimeRangeDefaultVal(type, defaultVal) {
            const end = new Date();
            const start = new Date();

            try {

                let dValue = [start, end]
                let dateFormatStr = ""
                let defaultValList = defaultVal.split('#')
                // 指定日期 格式化 格式
                switch (type) {
                    case "daterange":
                        dateFormatStr = 'YYYY-MM-DD'
                        break;
                    case "datetimerange":
                        dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                        break;
                    default:
                        dateFormatStr = ""
                        break
                }
                // 开始时间 和 结束时间
                // curdate#curdate
                if (!!defaultValList[0]) {
                    if (defaultValList[0] == 'curdate') {
                        // 当前日期
                        dValue[0] = dayjs().format(dateFormatStr);
                    } else if (defaultValList[0].includes('-')) {
                        // 指定日期加减
                        //dayjs().add(7, 'day').format('YYYY-MM-DD');
                        //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                        let daysetArray = defaultValList[0].split('-')
                        dValue[0] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                    } else {
                        //空日期
                        dValue[0] = start
                    }
                }
                if (!!defaultValList[1]) {
                    if (defaultValList[1] == 'curdate') {
                        // 当前日期
                        dValue[1] = dayjs().format(dateFormatStr);
                    } else if (defaultValList[1].includes('-')) {
                        // 指定日期加减
                        //dayjs().add(7, 'day').format('YYYY-MM-DD');
                        //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                        let daysetArray = defaultValList[1].split('-')
                        dValue[1] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                    } else {
                        //空日期
                        dValue[1] = end
                    }
                }
                return dValue
            } catch (error) {
                return [start, end]
            }

        },
        // 添加条件
        async addConditionClick() {
            //debugger
            if (!!this.currentSelectedRow) {
                await this.insertRow(this.currentSelectedRow, 'bottom')
            } else {
                this.addCommonRow()
            }
        },
        async addCommonRow() {
            let randomparentId = Math.floor(Math.random() * 1000000 + 1)
            let randomID = 1000 + this.tableData.length + 1 + randomparentId
            let record = {
                id: randomID,
                parentId: -1,
                field: '',
                title: '',
                isCheck: true,
                controlType: 'text',
                defaultValue: '',
                tableName: '',
                comparator: 0,// Equal 等于
                relationSign: 0,
                comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                value: '',
                // dataFrom: 'local'
            }
            const $table = this.$refs["vxeTableRef"]
            //debugger
            if (this.currentSelectedItem && this.currentSelectedItem.parentId == -1) {
                const { row: newRow } = await $table.insertAt(record, this.currentSelectedItem)
                $table.setCurrentRow(newRow) // 插入子节点
            } else {
                const { row: newRow } = await $table.insertAt(record, -1)
                $table.setCurrentRow(newRow) // 插入子节点
            }

        },
        // 将 树状结构数据 转为 扁平数据
        toFlat(treeData = []) {
            let _self = this
            return treeData.reduce((result, node) => {
                if (node.children) {
                    result.push(..._self.toFlat(node.children));
                    // 如果有节点 添加前先移除
                    delete node.children
                }
                result.push(node);
                return result;
            }, []);
        },
        // 添加条件组
        async addGroupClick() {
            let randomparentId = Math.floor(Math.random() * 1000000 + 1)
            let randomID = 1000 + this.tableData.length + 1 + randomparentId
            let record = {
                id: randomID,
                parentId: 0,
                field: '',
                title: '',
                isCheck: true,
                controlType: 'text',
                defaultValue: '',
                tableName: '',
                comparator: 0,// Equal 等于
                relationSign: 0,
                comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                value: '',
                //dataFrom: 'local'
            }
            const $table = this.$refs["vxeTableRef"]
            // insertAt 如果 -1 则插入到目标节点底部
            //$table.insertAt(newRow,-1)
            const { row: newRow } = await $table.insertAt(record, -1)
            $table.setCurrentRow(newRow) // 插入子节点
            this.currentSelectedRow = newRow
            this.currentSelectedItem = newRow
        },
        // 打开高级搜索配置
        async searchProSettingFn() {
            this.showEditDialogFlag = true
            this.tableData = []
            this.editResDataObj = null
            if (!!this.currentItem) {
                await this.loadConditionData(this.currentItem)
                const $table = this.$refs["vxeTableRef"]
                if ($table) {
                    $table.setAllTreeExpand(true)
                }

            }
        },
        // 将 移除 树状结构数据 下的children节点数据
        removeTreeChildren(treeData = []) {
            let newTreeData = []
            treeData.forEach(item => {
                delete item.children
                newTreeData.push(item)
            })
            return newTreeData
        },
        getConditionTableDataJSON() {
            let _tablefullData = []
            const $table = this.$refs["vxeTableRef"]
            if ($table) {
                _tablefullData = $table.getTableData().visibleData //处理条件之后的全量表体数据
            }
            // debugger
            // 将 移除 树状结构数据 下的children节点数据
            _tablefullData = this.removeTreeChildren(_tablefullData)
            let CCONDITION = {
                tableData: _tablefullData,
                loadDataConfig: this.loadDataConfig,
                // 方便扩展其它字段
            }
            return CCONDITION
        },
        guid() {
            function S4() {
                return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
            }
            return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
        },
        async submitEditDialog() {
           // debugger
            let _self = this
            this.submitBtnLoading = true
            let defaultGUID = this.guid() //生成一个随机的GUID值来标识一个数据对象
            let CCONDITION  = this.getConditionTableDataJSON()
            this.currentItem = JSON.stringify(CCONDITION)
            let params ={
                    value:this.currentItem,
                    text:"方案JSON",
                } 
            this.$emit("changeEvent",params)
            this.showEditDialogFlag = false
        },
        async removeSearchProConfigRow(row) {
            const $table = this.$refs["searchProConfigEditTableRef"]
            await $table.remove(row)

        },
        async removeRow(row) {
            const $table = this.$refs["vxeTableRef"]
            // if(row.parentId==0){
            //     let _fullData = $table.getTableData().visibleData
            //     // 如果删除父类，需要删除对应的子类
            //     _fullData.forEach(item=>{
            //         if(item.parentId==rowData.id){
            //             $table.remove(item)
            //         }
            //     })
            // }
            await $table.remove(row)
            if (row.parentId == 0) {
                this.currentSelectedRow = null
                this.currentSelectedItem = null
            }

        },
        async insertRow(currRow, locat) {
            let randomID = 1000 + this.tableData.length + 1 + (Math.floor(Math.random() * 1000000 + 1))
            const $table = this.$refs["vxeTableRef"]
            // 如果 null 则插入到目标节点顶部
            // 如果 -1 则插入到目标节点底部
            // 如果 row 则有插入到效的目标节点该行的位置
            if (locat === 'current') {

                const record = {
                    id: randomID,
                    parentId: currRow.parentId,
                    field: '',
                    title: '',
                    isCheck: true,
                    controlType: 'text',
                    defaultValue: '',
                    tableName: '',
                    comparator: 0,// Equal 等于
                    relationSign: 0,
                    comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    value: '',
                    //dataFrom: 'local'
                }
                const { row: newRow } = await $table.insertAt(record, currRow)
                await $table.setCurrentRow(newRow) // 插入子节点
                this.currentSelectedItem = newRow
            } else if (locat === 'top') {

                const record = {
                    id: randomID,
                    parentId: currRow.id,
                    field: '',
                    title: '',
                    isCheck: true,
                    controlType: 'text',
                    defaultValue: '',
                    tableName: '',
                    comparator: 0,// Equal 等于
                    relationSign: 0,
                    comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    value: '',
                    //dataFrom: 'local'
                }
                const { row: newRow } = await $table.insert(record)
                await $table.setTreeExpand(currRow, true) // 将父节点展开
                await $table.setCurrentRow(newRow) // 插入子节点
                this.currentSelectedItem = newRow
            } else if (locat === 'bottom') {

                const record = {
                    id: randomID,
                    parentId: currRow.id,
                    field: '',
                    title: '',
                    isCheck: true,
                    controlType: 'text',
                    defaultValue: '',
                    tableName: '',
                    comparator: 0,// Equal 等于
                    relationSign: 0,
                    comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
                    value: '',
                    //dataFrom: 'local'
                }
                const { row: newRow } = await $table.insertAt(record, -1)
                await $table.setTreeExpand(currRow, true) // 将父节点展开
                await $table.setCurrentRow(newRow) // 插入子节点
                this.currentSelectedItem = newRow
            }
        }
        // 选择改变回调事件
        //    changeEvent(val){
        //        let _self = this
        //        this.$nextTick(()=>{
        //             let params ={
        //                 value:val,
        //                 text:_self.$refs["elSelectRef_selectTableBinary"].selectedLabel
        //             } 
        //              _self.$emit("changeEvent",params)
        //        })
        //    }
    }
}
</script>


<style lang="scss"></style>