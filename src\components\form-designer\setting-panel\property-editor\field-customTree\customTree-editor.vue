<template>
      <div> 
        <!-- <el-form-item label="选择类型">
          <el-select v-model="optionModel.selectedType">
            <el-option label="不可选" value="-1"></el-option>
            <el-option label="单选" value="0"></el-option>
            <el-option label="多选" value="1"></el-option>
            <el-option label="购选" value="2"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="">
        <span slot="label">设为被动加载
          <el-tooltip effect="light" content="默认 立即加载数据，设为被动加载后，数据需要通过其它方式触发加载，如 查询，点击等">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-switch v-model="optionModel.isPassiveDataLoad"></el-switch>
    </el-form-item>
        <el-form-item label="显示标题">
            <el-checkbox v-model="optionModel.showTitle"></el-checkbox>
        </el-form-item>
        <el-form-item label="显示顶级">
            <el-checkbox v-model="optionModel.showTopFilter"></el-checkbox>
        </el-form-item>
        <el-form-item label="显示查询">
            <el-checkbox v-model="optionModel.showFilterInput"></el-checkbox>
        </el-form-item>
        <el-form-item label="显示折叠按钮">
            <el-checkbox v-model="optionModel.showExpandFoldBtn"></el-checkbox>
        </el-form-item>
        <el-form-item label="显示项额外事件按钮">
            <el-checkbox v-model="optionModel.showExtraItemEvent"></el-checkbox>
        </el-form-item>
        <el-form-item v-if="!!optionModel.showExtraItemEvent" label="项额外事件显示条件">
          <el-select v-model="optionModel.showExtraItemFilter" placeholder="请选择">
            <el-option value="">全部</el-option>
              <el-option
                v-for="item in ExtraItem_Options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
        </el-form-item>
        <el-form-item v-show="!!optionModel.showTopFilter" label="顶级文本">
            <el-input v-model="optionModel.topFilterText" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item v-show="!!optionModel.showTopFilter" label="顶级默认值">
            <el-input v-model="optionModel.topFilterValue" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label-width="0">
            <selectDataSourceApi contrlType="customTree" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></selectDataSourceApi>
        </el-form-item>
        <el-form-item label-width="0">
            <commonParamsOnChange contrlType="customTree" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
        </el-form-item>
        
        <el-form-item label="树结构子ID">
          <span slot="label">树结构子ID
            <el-tooltip effect="light" content="需要先点击预加载后 方可获取可选数据">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-select  v-model="optionModel.treeRowField" placeholder="请选择查询">
            <el-option value="">请选择</el-option>
            <el-option
              :key="index"
              v-for="(item, index) in optionModel.dataSetAllModel"
              :label="item.title"
              :value="item.field"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="树结构父ID">
          <el-select  v-model="optionModel.treeParentField" placeholder="请选择查询">
            <el-option value="">请选择</el-option>
            <el-option
              :key="parentIndex"
              v-for="(parentItem, parentIndex) in optionModel.dataSetAllModel"
              :label="parentItem.title"
              :value="parentItem.field"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="显示标签字段">
          <el-select  v-model="optionModel.showTreeLabel" placeholder="请选择查询">
            <el-option value="">请选择</el-option>
            <el-option
              :key="modelIndex"
              v-for="(modelItem, modelIndex) in optionModel.dataSetAllModel"
              :label="modelItem.title"
              :value="modelItem.field"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过滤字段">
          <span slot="label">过滤字段
            <el-tooltip effect="light" content="主要用于触发控件时，需要传递过去过滤的字段值">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-select  v-model="optionModel.filterTreeField" placeholder="请选择查询">
            <el-option value="">请选择</el-option>
            <el-option
              :key="filterIndex"
              v-for="(filterItem, filterIndex) in optionModel.dataSetAllModel"
              :label="filterItem.title"
              :value="filterItem.field"
            ></el-option>
          </el-select>
        </el-form-item>
      
        <el-form-item v-if="!!optionModel.showExtraItemEvent" label-width="0">
            <treeItemOptionsEventDialog contrlType="customTree" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></treeItemOptionsEventDialog>
        </el-form-item>
        <!-- <el-form-item label-width="0">
            <commonSettingEventDialog contrlType="customTree" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonSettingEventDialog>
        </el-form-item> -->
      </div>
   
  </template>
  
  <script>
      import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
      import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
    import commonSettingEventDialog from '@/components/form-designer/setting-panel/commonSettingEventDialog.vue'
    import treeItemOptionsEventDialog from '@/components/form-designer/setting-panel/treeItemOptionsEventDialog.vue'
    import i18n from "@/utils/i18n"
    import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
    export default {
      name: "customTree-editor", // 必须与设置的名字相同，不然无效
      mixins: [i18n, propertyMixin],
      components:{commonSettingEventDialog,selectDataSourceApi,commonParamsOnChange,treeItemOptionsEventDialog},
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
      data(){
        return {
          ExtraItem_Options: [{
          value: '1',
          label: '仅显示无子项的'
        }, {
          value: '2',
          label: '仅显示有父项的'
        }, ],
        }
      }
    }
  </script>
  
  <style lang="scss" scoped>
  
  </style>
  