<template>
    <el-input-number @change="changeEvent" v-bind="$attrs" v-on="$listeners"></el-input-number>
</template>
<script>
import baseMixin from './minxin'
export default {
    name: "reportNumberInput",
    mixins:[baseMixin],
    props: {
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: String,// 当前查询字段名称
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
          //  value: "",// 当前选中的值的，对外暴露属性，仅供外部查询使用
        }
    },
    mounted(){
        // this.$nextTick(()=>{
        //     this.init()
        // })
        this.init()
    },
    methods: {
        // // 初始化，默认值等
        // init(){
        //     let defaultValue = this.paramsItem.defaultValue
        //     this.searchForm[this.fieldName] = defaultValue
        //     this.value = defaultValue
        // },
        // // 选择改变触发事件
        // changeEvent(val) {
        //     this.value = val
        // },
        // // 查询栏目 点击 重置清空数据，外部调用
        // clear() {
        //     if (!!this.fieldName && !!this.controlType && this.searchForm) {
        //         // 重置
        //         if (!!this.value && !!this.searchForm[this.fieldName]) {
        //             // this.value = null
        //             // this.searchForm[this.fieldName] = null
        //             this.init()
        //         }
        //     }
        // },

    }
}

</script>