<template>
    <div class="flex w-full justify-between  items-center">
        <div style="width: 30px;"><i style="font-size: 16px;" :style="{ color: displayIconColor }"
                :class="[!!displayIconText ? displayIconText : '']"></i></div>
        <div class="flex-1 flex justify-start items-center">{{ currentValue }}</div>
    </div>
</template>
<script>
// 根据条件渲染不同的ICON 和颜色，以及对应的文本描述
import { trim } from "lodash-es";
export default {
    name: "IconTextColumn",
    props: {
        // 当前字段 KEY
        field: {
            type: String,
            default: ""
        },
        fieldRules: {
            type: String,
            default: ""
        },
        // 当前字段值
        currentValue: {
            type: String,
            default: ""
        },
        // 当前表格行数据
        rowData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段配置信息
        itemOptions: {
            type: Object,
            default() {
                return {}
            }
        },

    },
    data() {
        return {
            displayIconText: "",
            displayIconColor: "",
            fieldRulesList: []
        }
    },

    mounted() {
        this.setIconText()
    },
    methods: {
        setIconText() {
            if (!!this.fieldRules) {
                try {
                    this.fieldRulesList = JSON.parse(this.fieldRules)
                    if (this.fieldRulesList && this.fieldRulesList.length > 0) {
                        let filterDataList = this.fieldRulesList.filter(item => {
                            if (item.type == 'IconValue') {
                                return item
                            }
                        })
                        if (filterDataList && filterDataList.length > 0) {
                            // 从条件中获取和当前行数据匹配的 图标&颜色 控制
                            filterDataList.forEach(item => {
                                // item.parentId: -1  独立条件
                                // item.parentId:  0  复合条件
                                if (item.parentId == -1) {
                                    if (this.calValue(item, this.rowData)) {
                                        this.displayIconColor = item.color
                                        this.displayIconText = item.IconValue
                                    }
                                }
                                else if (item.parentId == 0 && item.children.length > 0) {
                                    // debugger
                                    if (this.checkChildrenRules(item.children, this.rowData)) {
                                        this.displayIconColor = item.color
                                        this.displayIconText = item.IconValue
                                    }
                                }
                            })
                        }
                    }
                } catch (error) {

                }

            }
        },
        calValue(item, row) {
            let flag = false
            let fieldVal = Number(trim(row[item.field]))
            let compareVal = Number(trim(item.value))
            if (item.type != 'IconValue') {
                return false
            }
            if (item.condition == 'gt') {
                // 大于
                if (fieldVal > compareVal) {
                    flag = true
                }
            } else if (item.condition == 'gte') {
                if (fieldVal >= compareVal) {
                    flag = true
                }
            }
            else if (item.condition == 'lt') {
                // 小于
                if (fieldVal < compareVal) {
                    flag = true
                }
            } else if (item.condition == 'lte') {
                // 小于或等于
                if (fieldVal <= compareVal) {
                    flag = true
                }
            }
            else if (item.condition == 'eq') {
                // 等于 时 可以时字符串 比较
                //if (trim(row[item.field]) == trim(item.value)) {
                if (trim(row[item.field]) == trim(item.value)) {
                    flag = true
                }
            }
            else if (item.condition == 'neq') {
                // 不等于 时 可以时字符串 比较
                if (trim(row[item.field]) != trim(item.value)) {
                    flag = true
                }
            }
            else if (item.condition == 'in') {
                // 包含 时 可以是字符串 比较
                if (trim(row[item.field]).includes(trim(item.value))) {
                    flag = true
                }

            }
            return flag
        },
        checkChildrenRules(_childrenRules = [], row = {}) {
            let boolenList = []
            if (_childrenRules && _childrenRules.length > 0 && Object.keys(row).length > 0) {
                _childrenRules.forEach(item => {
                    let fieldVal = Number(trim(row[item.field]))
                    let compareVal = Number(trim(item.value))
                    if (item.condition == 'gt') {
                        // 大于
                        boolenList.push((fieldVal > compareVal) + "")
                    } else if (item.condition == 'gte') {
                        // 大于或等于
                        boolenList.push((fieldVal >= compareVal) + "")

                    }
                    else if (item.condition == 'lt') {
                        // 小于
                        boolenList.push((fieldVal < compareVal) + "")

                    } else if (item.condition == 'lte') {
                        // 小于或等于
                        boolenList.push((fieldVal <= compareVal) + "")

                    }
                    else if (item.condition == 'eq') {
                        // 等于 时 可以时字符串 比较  if (trim(trim(row[item.field])) == trim(item.value)) {
                        boolenList.push((trim(row[item.field]) == trim(item.value)) + "")

                    }
                    else if (item.condition == 'neq') {
                        // 不等于 时 可以时字符串 比较
                        boolenList.push((trim(row[item.field]) != trim(item.value)) + "")

                    }
                    else if (item.condition == 'in') {
                        // 包含 时 可以时字符串 比较
                        boolenList.push((trim(row[item.field]).includes(trim(item.value))) + "")

                    }
                })
            }
            // debugger
            let _flag = boolenList.includes('false') ? false : true
            return _flag
        },

    }
}
</script>