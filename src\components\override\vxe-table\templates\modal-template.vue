<template>
    <div>
         <vxe-modal 
            ref="csoftiModalRef" 
            v-bind="$attrs"
            v-on="$listeners">
            <!-- 重写插槽 default  v-model="showModal"-->
            <template v-slot:default>
                <template v-if="$slots.default">
                <slot name="default"></slot>
                </template>
            </template>
            <!-- 重写插槽 title -->
            <template v-slot:title>
                <template v-if="$slots.title">
                <slot name="title"></slot>
                </template>
            </template>
            <!-- 重写插槽 corner -->
            <template v-slot:corner>
                <template v-if="$slots.corner">
                <slot name="corner"></slot>
                </template>
            </template>
            <!-- 重写插槽 header -->
            <template v-slot:header>
                <template v-if="$slots.header">
                <slot name="header"></slot>
                </template>
            </template>
            <!-- 重写插槽 footer -->
            <template v-slot:footer>
                <template v-if="$slots.footer">
                <slot name="footer"></slot>
                </template>
            </template>
          
         </vxe-modal>
    </div>
</template>
<script>

export default {
    name:"modal-template",//统一弹框模板
    props:{
        // showModal:{
        //     type:Boolean,
        //     default:false
        // }
    },
    data(){
        return {
           
        }
    },
  
    mounted(){
         
    },  
    methods:{

    }
}
</script>
<style lang="scss" scoped>

</style>