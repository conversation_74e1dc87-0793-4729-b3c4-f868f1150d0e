

<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <div>hello world container...</div>

  </container-wrapper>
</template>

<script>
  import Draggable from 'vuedraggable'
  import i18n from "@/utils/i18n"
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
  import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
  import refMixinDesign from "@/components/form-designer/refMixinDesign"

  export default {
    name: "divContainer-widget",
    componentName: 'ContainerWidget',
    mixins: [i18n, containerMixin, refMixinDesign],
    inject: ['refList','sourceVFormRenderState'], 
    components: {
      ContainerWrapper,
      Draggable,

      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
      previewState: { //是否表单预览状态
        type: Boolean,
        default: false
      },
      contentBoxHeight:{
        type: Number,
        default: 0
      },
          // 控件来源父集 add by andy
   sourceVFormRender:{
      type: String,
      default: ""
    },
      designState: {
        type: Boolean,
        default: false
      },
    },
    data() {
      return {
      
      }
    },
    computed: {
      // selected() {
      //   return this.widget.id === this.designer.selectedId
      // },

      // customClass() {
      //   return this.widget.options.customClass || ''
      // },

    },
    watch: {
      //
    },
    created() {
      this.initRefList()
    },
    mounted() {
     
    },
    methods: {
    
    
    }
  }
</script>

<style lang="scss" scoped>
 

</style>
