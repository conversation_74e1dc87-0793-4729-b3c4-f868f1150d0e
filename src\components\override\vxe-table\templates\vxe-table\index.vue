<!--
 * @Author: hufoyang
 * @Date: 2022-10-10 14:15:16
 * @LastEditTime: 2022-11-10 08:55:18
 * @LastEditors: foyang
 * @Description: vxe-tabel
 * @FilePath: \my-project\src\components\vxe-table\index.vue
 * 版权声明
-->
<template>
  <div class="vxeTableCla">
    <vxe-grid 
      ref="csoftiTableRef" border round  :row-id="rowId"
      :menu-config="JSON.stringify(tabelConfigs.menuConfig) != '{}' ? tabelConfigs.menuConfig : gridOptions.menuConfig"
      @resizable-change="resizAbleChange"
      @header-cell-menu="headerCellContextMenuEvent"
      @menu-click="contextMenuClickEvent"
      v-bind="$attrs" v-on="$listeners"
    >
      <!-- @custom="toolbarCustomEvent" -->
      <!-- 重写插槽 empty -->
      <template v-slot:empty>
        <template v-if="$slots.empty">
          <slot name="empty"></slot>
        </template>
      </template>
   
      <!-- 重写插槽 form -->
      <template v-slot:form>
        <template v-if="$slots.form">
          <slot name="form"></slot>
        </template>
      </template>
      <!-- 重写插槽 toolbar -->
      <template v-slot:toolbar>
        <template v-if="$slots.toolbar">
          <slot name="toolbar"></slot>
        </template>
      </template>
      <!-- 重写插槽 top -->
      <template v-slot:top>
        <template v-if="$slots.top">
          <slot name="top"></slot>
        </template>
      </template>
      <!-- 重写插槽 pager -->
      <template v-slot:pager>
        <template v-if="$slots.pager">
          <slot name="pager"></slot>
        </template>
      </template>
      <template v-slot:operate="{ row }">
        <template v-if="$scopedSlots.operate">
        <!-- 自定义 操作 按钮 -->
        <slot :row="row" name="operate"></slot>
      </template>
    </template>
      <!-- 重写 bottom 并且默认值分页控件，可以重写-->
      <template v-if="tabelConfigs.footerConfig.leftToolShow||tabelConfigs.footerConfig.rightToolShow" v-slot:bottom>
        <template v-if="$slots.bottom">
          <!-- 自定义插槽 -->
          <slot name="bottom"></slot>
        </template>
        <template v-else>
          <div class="toolbarCla" v-if="!$slots.bottom">
              <FooterTools :footerConfig='tabelConfigs.footerConfig' @pageChanges='pageChange' />
          </div>
        </template>
      </template>
    </vxe-grid>
  </div>
</template>
<script>
import XEUtils from 'xe-utils'
import '../vxe-table/renderer/index.js'
import FooterTools from "./footerTools/index.vue"
import Sortable from './common/Sortable.min.js'
export default {
  /**
   * 组件说明: 
   *    内置有表格主体 <vxe-grid/>;
   *    底部工具栏 <FooterTools />;
   *    底部工具栏props: 
    // 数据配置
      tabelConfigs: {
        menuConfig: {}, // 右键菜单配置
        footerConfig: {
          leftToolShow:true, // 是否显示左侧工具栏列筛选
          rightToolShow: true, // 是否显示右侧工具栏分页
          pageConfig: { // 分页配置
            total: 0,
            currentPage: 1,
            pageSize: 10, 
          }
        }
      },
   * 内置暴露事件: 分页操作 pageChange({ type, currentPage, pageSize, $event}),
   * 示例用法: 
   * <vxeTable
        ref="vxeTable"
        :auto-resize="gridOptions.autoResize"
        :resizable="gridOptions.resizable"
        :row-config="gridOptions.rowConfig"
        :column-config="gridOptions.columnConfig"
        :print-config="{}"
        :show-overflow="true"
        :height="500"
        :tabelConfigs='tabelConfig'
        @pageChange='pageChangeFun'
      >
   * */ 
  name: 'VxeTable',
  components: { FooterTools },
  props: {
    // 表id
    rowId: {
      type: [String, Number],
      default: 'CID'
    },
    //底部工具栏配置 
    tabelConfigs: {
      type: Object,
      default() {
        return {
          menuConfig: {}, // 右键菜单配置
          footerConfig: {
            leftToolShow:true, // 是否显示左侧工具栏列筛选
            rightToolShow: true, // 是否显示右侧工具栏分页
            pageConfig: { // 分页配置
              total: 0,
              currentPage: 1,
              pageSize: 10
            }
          }
        }
      }
    },
    // 菜单项配置
    menuConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    // 表头右键
    headerCellMenu: {
      type: Function,
    },
    // 右键菜单点击
    menuClick: {
      type: Function
    },
    // 列宽拖动
    resizableChange: {
      type: Function,
    },
    // 表格底部区域控制
  },
  data() {
    return {
      isShow: false,
      filterList: [], // 搜索
      tableData: [], // 源数据
      tableColumns: [], // 行配置
      checkboxVal: [],
      lockColumns: [], 
      rowdata: [],
      rowItemVal: [],
      // 列显示隐藏
      pulldownData: {
        list: [],
        hash: 998,
      },
      gridOptions: {
        menuConfig: {
          header: {
            options: [
              [
                {
                  code: 'save',
                  name: '保存布局',
                  visible: true,
                  disabled: false
                },
                {
                  code: 'leftLock',
                  name: 'leftLock',
                  prefixIcon: 'vxe-icon-lock-fill',
                  visible: true,
                  disabled: false
                },
                {
                  code: 'rightLock',
                  name: 'rightLock',
                  prefixIcon: 'vxe-icon-lock-fill',
                  visible: true,
                  disabled: false
                },
                {
                  code: 'unLock',
                  name: 'UnLock',
                  prefixIcon: 'vxe-icon-unlock-fill',
                  visible: true,
                  disabled: false
                }
              ]
            ]
          },
          visibleMethod: this.visibleMethod
        },
      },
      timer: '',
      tableTitle: [],
      tranLeft: 0, 
      tranTop: 0, 
      muneShow: false,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.columnDrop();
  },
  mounted() {
  
    this.initDataFilter()
  },
  computed: {},
  methods: {
    pageChange(callback) { // callback: { type, currentPage, pageSize, $event}
      this.$emit('pageChange',callback)
    },
    getMousePos(event) {
			var e = event || window.event;
			var scrollX = document.documentElement.scrollLeft || document.body.scrollLeft;
			var scrollY = document.documentElement.scrollTop || document.body.scrollTop;
			var x = e.clientX + scrollX;
			var y = e.clientY + scrollY;
			return { "x": x, "y": y };
		},
    initDataFilter() {
      this.timer = setInterval(() => {
        if (document.readyState === 'complete') {
          // let t1 = this.$attrs
          // debugger
          /********** 开始编写需要处理的数据 ****************/ 
          this.tableData = this.$refs.csoftiTableRef.getTableData() // 源数据
          this.tableColumns = this.$refs.csoftiTableRef.getColumns(); // 行配置
          
          // 设置下拉容器列表数据
            this.pulldownData['list'] = this.tableColumns
            this.pulldownData['hash'] = Math.ceil(Math.random()*10000);
            this.tableTitle = this.removeEmptyArrayEle(this.pulldownData.list.map(i => i.field))
          //  搜索高亮, 获取表格的title

          /**************** End *********************/
          clearInterval(this.timer);
        }
      }, 1000);
    },
    // 过滤undefined值
    removeEmptyArrayEle(arr){
      for(var i = 0; i < arr.length; i++) {
        if(arr[i] == undefined || arr[i] == "undefined") {
          arr.splice(i,1);
          i = i - 1; // i - 1 ,因为空元素在数组下标 2 位置，删除空之后，后面的元素要向前补位，
                     // 这样才能真正去掉空元素,觉得这句可以删掉的连续为空试试，然后思考其中逻辑
        }
      }
      return arr;
    },
    // 搜索高亮
    searchEvent (val) {
      const filterName = XEUtils.toValueString(val).trim().toLowerCase()
      if (filterName) {
        const filterRE = new RegExp(filterName, 'gi')
        const searchProps = this.tableTitle
        const rest = this.tableData.fullData.filter(
          item => searchProps.some(key => XEUtils.toValueString(item[key]).toLowerCase().indexOf(filterName) > -1)
        )
        this.filterList = rest.map(row => {
          const item = Object.assign({}, row)
          searchProps.forEach(key => {
            item[key] = XEUtils.toValueString(item[key]).replace(filterRE, match => `<span class="keyword-lighten">${match}</span>`)
          })
          return item
        })
          this.$refs.csoftiTableRef.loadData(this.filterList)
      } else {
        this.$refs.csoftiTableRef.loadData(this.tableData.fullData)
      }
    },
    // 显示之前处理按钮的操作权限
      visibleMethod ({ options, column }) {
        let isShow = false;
        const leftDisabled = column && column.fixed && (column.fixed == "left");
        const rightDisabled = column && column.fixed && (column.fixed == "right");
        if(column.type == 'seq') {
            isShow = false
        }else {
          options.forEach(list => {
            this.$nextTick(() => {
              list.forEach(item => {
                if(!!leftDisabled) {
                  if (['leftLock'].includes(item.code)) {
                    item.disabled = true
                  }
                  if (['rightLock'].includes(item.code)) {
                    item.disabled = false
                  }
                  if (['unLock'].includes(item.code)) {
                    item.disabled = false
                  }
                }else if(!!rightDisabled) {
                  if (['leftLock'].includes(item.code)) {
                    item.disabled = false
                  }
                  if (['rightLock'].includes(item.code)) {
                    item.disabled = true
                  }
                  if (['unLock'].includes(item.code)) {
                    item.disabled = false
                  }
                }else {
                  item.disabled = false
                  if (['unLock'].includes(item.code)) {
                    item.disabled = true
                  }
                }
              })
            })
          })
          
          isShow = true

        }
        return isShow
    },
    // 表头右键
    headerCellContextMenuEvent(val) {
      this.$refs.csoftiTableRef.setCurrentColumn(val.column)
      if(this.typeFunction(this.headerCellMenu)){
        this.headerCellMenu(val)
      }else {
        // console.log(val, 'contextMenuClickEvent');
      }
    },
    // 右键菜单点击
    contextMenuClickEvent(row) {
      let params = {
        tableinfo: row,
        data: this.data,
        ref: this
      }
      if(this.typeFunction(this.menuClick)) {
        this.menuClick(params)
      }else {
        switch (row.menu.code) {
          case 'save':
            let tableColumns = this.$refs.csoftiTableRef.getColumns()
            window.sessionStorage.setItem('resizable',JSON.stringify(tableColumns))
            document.location.reload();
            break;
          case 'leftLock':
            this.columnsFilter(row,'left');
            break;
          case 'rightLock':
            this.columnsFilter(row,'right');
            break;
          case 'unLock': 
            this.columnsFilter(row, 'unLock');
            break;
        }
      }
    },
    // 保存布局api接口请求
    SaveLayout() {
      axios({
        method: "post",
        url: "http://8.135.117.94:6689/api/DEMO/VXETableDemo/page",
        data: {
          Condition: "",
          PageIndex: 1,
          PageSize: 10,
        },
      });
    },
    // 右键菜单事件执行
    columnsFilter(row, flag) {
      const $grid = this.$refs.csoftiTableRef;
      this.rowdata = $grid.getColumns(); let lockIndex = 0,unlockIndex=0;
      this.lockColumns = JSON.parse(JSON.stringify(this.rowdata))
      let lengths = this.rowdata.length;
      for(let i=0; i < lengths; i++) {
        if(this.rowdata[i].fixed != undefined && this.rowdata[i].field != undefined ) {
          if(this.rowdata[i].fixed == 'left') {
            lockIndex+=1
          }else if(this.rowdata[i].fixed == 'right') {
            unlockIndex+=1
          }
        }
      }
      switch(flag) {
        case 'left': 
          this.rowdata[row.columnIndex]['fixed'] = 'left';
          this.lockColumns.splice(lockIndex+1,0,this.rowdata[row.columnIndex]);
          console.log(this.lockColumns,'left');
        break;
        case 'right': 
          this.rowdata[row.columnIndex]['fixed'] = 'right';
          this.lockColumns.splice(this.rowdata.length,0,this.rowdata[row.columnIndex]); 
          console.log(this.lockColumns,'right');
        break;
        case 'unLock': this.unlockFilter(row,lockIndex);
        break;
      }
      
      // 查找旧值进行删除
      let finData  = this.lockColumns.findIndex(i => {
        if(i.field == row.column.field && (!i.fixed || i.fixed != flag)) {
          return i
        }
      })
      this.lockColumns.splice(finData, 1)
      console.log(this.lockColumns,'this.lockColumns');
      this.$nextTick(() => {
        $grid.reloadColumn(this.lockColumns)
      })

    },
    // 解除固定
    unlockFilter(row,lockIndex) {
      if(row.column.fixed) {
        if(row.column.fixed == 'left') {
          this.rowdata[row.columnIndex]['fixed'] = null;
          this.lockColumns.splice(lockIndex+1,0,this.rowdata[row.columnIndex]);
          this.$refs.csoftiTableRef.setCurrentColumn(this.rowdata[row.columnIndex])
        }
        if(row.column.fixed == 'right') {
          let setIndex = lockIndex > 0 ? lockIndex+1 : 1;
          this.$nextTick(() => {
            this.rowdata[row.columnIndex]['fixed'] = null;
            this.lockColumns.splice(setIndex,0,this.rowdata[row.columnIndex]);
            this.$refs.csoftiTableRef.setCurrentColumn(this.rowdata[row.columnIndex])
          })
        }
      }
    },
    // 列宽拖拽事件
    resizAbleChange(val) {
      if(this.typeFunction(this.resizableChange)) {
        this.resizableChange(val)
      }else {
        console.log(val,'odl', this.tableColumns[val.$columnIndex]);
        this.tableColumns[val.$columnIndex].width = val.resizeWidth
        this.$refs.csoftiTableRef.setCurrentColumn(this.tableColumns[val.$columnIndex])
        console.log(this.$refs.csoftiTableRef.getColumns(),'new');
      }
    },
    // 检验父组件传值是否为存在方法
    typeFunction(item) {
      let isFun = null;
      if(typeof item != 'undefined' && item instanceof Function){ 
        isFun = true
      }else {
        isFun = false
      }
      return isFun
    },
    columnDrop() {
      this.$nextTick(() => {
        const $table = this.$refs.csoftiTableRef
        let el = $table.$el.querySelector('.vxe-table--header tr')
        let ops = {
          handle: '.vxe-header--column',
          //停靠位置样式
          draggable: '.vxe-header--column',
          filter: '.col--fixed,.col--seq',
          animation: 500,
          direction: 'vertical',
          // filter: function (evt, item) {
          //   // console.log(item, 'item');
          //   // if (item.dataset.id == "2") return true;
          //   //   return false;
          // },
          onEnd: (evt) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = evt.item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[evt.newIndex]
            if (newColumn.fixed) {
              const oldThElem = wrapperElem.children[evt.oldIndex]
              // 错误的移动
              if (evt.newIndex > evt.oldIndex) {
                wrapperElem.insertBefore(targetThElem, oldThElem)
              } else {
                wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
              }
              VXETable.modal.alert({ content: '固定列不允许拖动，即将还原操作！', status: 'error' })
              return false
            }
            const oldColumnIndex = $table.getColumnIndex(tableColumn[evt.oldIndex])
           // 获取列索引 columnIndex > fullColumn
            const newColumnIndex = $table.getColumnIndex(tableColumn[evt.newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            $table.loadColumn(fullColumn)
          },
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              const oldThElem = wrapperElem.children[oldIndex]
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, oldThElem)
              } else {
                wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
              }
              // VXETable.modal.message({ content: '固定列不允许拖动，即将还原操作！', status: 'error' })
              return
            }
            // 获取列索引 columnIndex > fullColumn
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            $table.loadColumn(fullColumn);
            // //获取拖动后的排序
            console.log(fullColumn,'fullColumn');
            // var arr = sortable.toArray();
            // console.log(arr, 'arr');
          }
        }
        Sortable.create(el, ops);
      })
    }
  },
}
</script>
<style lang='scss'>
  .keyword-lighten {
    color: #000;
    background-color: #FFFF00;
  }
</style>
<style lang='scss' scoped>
.vxe-icon-arrow-down {
    cursor: pointer;
  }
  
  .titleCa {
    display: inline-block;
    margin-right: 8px;
  }
  .muneCla {
    .muneListCla {
      display: inline-block;
      width: 150px;
      height: 230px;
      overflow: auto;
  
    }

  }
  ::v-deep .vxe-table--header {
    tr {
      .vxe-header--column{
        cursor: move;
      }
    }
  }
.vxeTableCla {
  height: auto;

  .toolbarCla {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    line-height: 45px;
    background: #fff;
    box-sizing: border-box;
    flex: 1;
    .leftCla{
      // width: 20%;
      .exportCla {
        display: inline-block;
        width: 35px;
        height: 35px;
        line-height: 35px;
        border-radius: 50%;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #ebeef5;
        }
      }
    }
    .rightCla {
      // width: 80%;
    }
  }
}
</style>

