<template>
  <div class="option-items-pane">

    <draggable tag="ul" :list="designer.formConfig.queryList"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in designer.formConfig.queryList" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.label" size="mini"
            style="width: 150px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>



    <div>
      <el-button type="text" @click="addQueryOption">+添加查询</el-button>
    </div>

    <el-dialog width="1024px" title="编辑 数据源 " v-dialog-drag :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag"
      :show-close="true" class="small-padding-dialog" append-to-body custom-class="drag-dialog small-padding-dialog"
      :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <div slot="title">
        <div>
          数据源编辑
          <el-tooltip content="页面设计中所有使用到的数据来源于此配置API,集中配置，方便修改和重用" effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
      </div>
      <el-form :model="editItemForm" :rules="editItemFormRules" ref="editItemForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input v-model="editItemForm.label"></el-input>
        </el-form-item>
        <el-form-item label="唯一编码" prop="value">
          <el-input disabled v-model="editItemForm.value"></el-input>
        </el-form-item>
        <!-- <el-form-item label="隐藏" prop="hidden">
            <el-switch v-model="editItemForm.otherParams.hidden"></el-switch>
          </el-form-item>
          <el-form-item label="激活" prop="disabled">
            <el-switch v-model="editItemForm.otherParams.disabled"></el-switch>
          </el-form-item> -->
        <!-- <div style="font-weight: bold;">当单击时</div> -->
        <el-form-item label="动作" prop="actionType">
          <el-select @change="actionChangeEvent" v-model="editItemForm.otherParams.actionType" placeholder="请选择动作">
            <el-option label="未选择" value="-1"></el-option>
            <el-option label="执行查询" value="api"></el-option>
            <!-- <el-option label="弹框应用" value="popup"></el-option> -->
          </el-select>
        </el-form-item>
        <!-- 请选择查询 -->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'api'">
          <el-form-item v-show="editItemForm.otherParams.actionType == 'api'" label="--选择数据源" prop="actionParams">
            <!-- <el-select @change="changeLoadApi" v-model="editItemForm.actionParams.actionName" placeholder="请选择查询">
                     <el-option label="请选择" value=""></el-option>
                     <el-option label="项目人天数据查询" value="api/CO/Project/UserProjectWork/PersonAanalysis"></el-option>
                     <el-option label="人天表头查询" value="api/Test/PersonAnalysis"></el-option>
                     <el-option label="组织树查询" value="api/DEMO/VXETableDemo/Data/Page"></el-option>
                    <el-option label="用户查询" value="api/DEMO/VXETableDemo/Data/Page"></el-option>
                    <el-option label="弹框列头" value="api/DEMO/VXETableDemo/Detail"></el-option>
                </el-select>
                <el-button @click="showDataSourceialogFlag=true" size="mini" type="success">+选择</el-button> -->
            <el-select  @focus="loadDataSource" filterable @change="change_dataSource(editItemForm, $event)"
              style="width:193.33px" v-model="editItemForm.actionParams.CDATASOURCE_ID" placeholder="选择数据源">
              <el-option label="请选择" :value="-1"></el-option>
              <el-option :key="subIndex + item.CID" v-for="(item, subIndex) in dataSourceList" :label="item.CNAME"
                :value="item.CID"></el-option>
            </el-select>
            <el-select ref="datasetSelectRef" filterable @change="change_dataSet(editItemForm, $event)" style="width:193.33px"
              v-model="editItemForm.actionParams.CDATASET_ID" placeholder="选择查询接口">
              <el-option label="请选择" :value="-1"></el-option>
              <el-option :key="subIndex + item.CID" v-for="(item, subIndex) in editItemForm.dataSetList"
                :label="item.CNAME" :value="item.CID"></el-option>
            </el-select>
            <el-button @click="synchronizeParamsData()" :loading="isLoading_searchParamsRef" style="margin-left:5px;"
              type="primary">同步参数</el-button>
              <el-button :disabled="!editItemForm.actionParams.CDATASET_ID" @click="showDatasetIframeBoxFn(editItemForm.actionParams.CDATASET_ID)" :loading="isLoading_searchParamsRef" style="margin-left:5px;"
              type="success">编辑数据集</el-button>
          </el-form-item>
        </div>
        <!-- 请选择弹框 -->
        <div v-if="editItemForm.otherParams.actionType && editItemForm.otherParams.actionType == 'popup'">
          <el-form-item v-show="editItemForm.otherParams.actionType == 'popup'" label="--选择弹框" prop="actionParams">
            <el-select v-model="editItemForm.actionParams.actionName" placeholder="请选择弹框">
              <el-option label="请选择" value=""></el-option>
              <el-option label="弹框1" value="1"></el-option>
              <el-option label="弹框2" value="2"></el-option>
            </el-select>

          </el-form-item>
        </div>
        <!-- 动态参数 -->
        <div>
          <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'"
          style="font-weight: bold;">参数【固定参数:$formData,$UserInfo,$Timestamp,$fullPath,$searchPro,
          <el-tooltip content="` 固定值：直接填写即可
          $formData 提交表单的数据 放在双括号中使用,
          $fullPath 获取浏览器指定参数值，如：$fullPath.nodeId [节点ID]/$fullPath.formName,如果存在分隔符¤，可以使用$fullPath.formName[0]方式使用， 但需放在双括号中使用,
          $searchPro 高级查询参数， 放在双括号中使用,
          $UserInfo 用户信息，如$UserInfo.CID 放在双括号中使用,
          $Timestamp/$iframePostMessage随机数等内置参数必须放到{{}}中使用.
          弹框参数传值案例：【树结构：parent.customTree110924.value，表格行CID：parent.vxetable51263.row.CID】;
          如勾选必填参数为空时，则跳过不调用接口;
          时间范围取值：【第一个日期值：defaultmenubutton14151.searchKey107889.value[0],第二个日期值：defaultmenubutton14151.searchKey107889.value[1]】,
          默认菜单：控件类型>>下拉表格>>查询参数【searchKey,pageIndex,pageSize:如：defaultmenubutton110213.searchKeyInput.searchKey，需手动添加修改,注意大小写】  
         `"
           effect="light">
              <i class="el-icon-info"></i></el-tooltip>
        </div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">
          <div :key="index" v-for="(item, index) in actionParamsList" style="margin-bottom: 5px;"
            class="flex justify-center items-center">
            <div>必填
              <el-tooltip content="勾选必填参数则这个参数必须有值，否则为空时，不执行调用此接口 " effect="light"> <i class="el-icon-info"></i></el-tooltip>
            </div>
            <div style="margin:0 10px;" l-form-item label="必填" prop="requireParams">
              <el-switch size="mini" v-model="item.requireParams"></el-switch>
            </div>
            <div>KEY</div>
            <div style="margin:0 10px;" l-form-item label="KEY" prop="key">
              <el-input style="width: 150px;" clearable placeholder="key" v-model="item.key"></el-input>
            </div>
            <div>VALUE</div>
            <div style="margin:0 10px;" label="VALUE" prop="value">
              <el-input clearable style="width: 500px;" :placeholder="`${!!item.placeholder?item.placeholder:''}【固定值/+选择获取】`" v-model="item.value">
                <div slot="prepend"  style="margin-top:2px;" class="flex justify-center items-center">
                  <el-select @focus="loadData_itemValue(item,'vxetable')" @change="selectChange_itemValue(item,$event,'vxetable')" v-if="item.value.includes('vxetable')&& item.value.includes('row') && !item.value.includes('parent')" size="mini" style="width: 120px;overflow: hidden;"  placeholder="可选字段">
                    <el-option v-for="(fieldItem,fieldIndex) in optionsListItemValue" :key="fieldItem.field" :label="fieldItem.title" :value="fieldItem.field"></el-option>
                  </el-select>
                  <el-select @focus="loadData_itemValue(item,'defaultmenubutton')" @change="selectChange_itemValue(item,$event,'defaultmenubutton')" v-if="item.value.includes('defaultmenubutton')&& item.value.includes('value') " size="mini" style="width: 120px;overflow: hidden;"  placeholder="可选字段">
                    <el-option v-for="(fieldItem,fieldIndex) in optionsListItemValue" :key="fieldItem.field" :label="fieldItem.title" :value="fieldItem.field"></el-option>
                  </el-select>
                </div>
              </el-input>
            </div>
            <div>
              <el-button type="text" @click="setParams(item)">+选择</el-button>
              <el-tooltip content="选择控件与参数绑定值 " effect="light"> <i class="el-icon-info"></i></el-tooltip>
            </div>
            <div class="flex justify-center items-center" style="margin-left:5px">
               <!-- <el-checkbox style="margin-left:5px;"
                v-model="item.execNow"><span style="font-size: 12px;">必传参数</span> 
                <el-tooltip effect="light" content="如果为空，则查询不会执行">
          <i class="el-icon-info"></i></el-tooltip>
              </el-checkbox> -->
                <i style="margin-left:5px;cursor: pointer;" @click="deleteParam(item, index)"
                class="el-icon-delete"></i>

            </div>
          </div>
        </div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
          <el-tooltip content="根据需要可以添加自定义的参数，最终提交到API接口 " effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
        </div>
      
        <!-- <el-form-item label="执行条件" prop="condition">
            <el-input v-model="editItemForm.otherParams.condition"></el-input>
          </el-form-item>
          <el-form-item label="防抖/秒" prop="debounceOrThrottleTime">
            <el-input-number v-model="editItemForm.otherParams.debounceOrThrottleTime" controls-position="right" :min="1" :max="10"></el-input-number>
          </el-form-item> -->


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
        }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="参数控件值 选择3" :visible.sync="showDataSourceialogFlag" v-if="showDataSourceialogFlag" v-dialog-drag
      append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <div>
        <!-- <el-button type="text" @click="addQueryOption">展开全部</el-button> -->
         <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
          highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
      </div>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" size="large" @click="submitNodeEvent()">
          确定</el-button><el-button size="large"  @click="showDataSourceialogFlag = false">
          取消</el-button>
      </div>
    </el-dialog>
    <el-dialog append-to-body top="0%" title="数据源修改" :close-on-click-modal="false" :visible.sync="showDatasetIframeBox" height="100%" width="100%">
    <div class="datasetIframeBox">

      <iframe
        :src="datasetIframeURL"
        width="100%"
        height="100%"
        crossOrigin="anonymous"
        frameborder="0"
        sandbox="allow-same-origin allow-scripts allow-forms"
      >
      </iframe>

    </div>

    </el-dialog>
  </div>
</template>

<script>
const default_editItemForm = {
  label: "",// 标签
  dataSetList: [],
  otherParams: {
    hidden: false, // 隐藏
    disabled: false,// 激活
    actionType: "-1",// 动作
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
  },
}
// import { useDebounceFn } from "@vueuse/core";
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
//import CodeEditor from '@/components/code-editor/index'
import request from '@/libs/request'
import i18n from "@/utils/i18n";
import { deepClone, generateId } from "@/utils/util"
export default {
  name: "queryListTree",
  mixins: [i18n],
  components: {
    Draggable,
  },
  props: {
    designer: Object,
    // selectedWidget: Object,
    globalDsv: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      clickTime: 0, // 用于记录点击时间
      currentDataSetIndex:0,// 当前选中数据源索引
      optionsListItemValue:[],
      showDatasetIframeBox:false,
      datasetIframeURL:"",
      curSetParamsItem: null,// 当前参数设置 编辑 项值
      nodeCtrlValue:"",
      isLoading_searchParamsRef: false,
      dataSourceList: [],// 数据源列表
      currentDataSourceItem:null,// 当前选中数据源
      pagination: {
        pageNo: 1,
        limit: 10,
        sizes: 10,
        total: 100
      },
      showDataSourceialogFlag: false,//弹框树 是否打开
      tableDataSourceList: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }],
      dataSetGroup: {},
      showEditMenuDialogFlag: false,
      optionLines: '',
      currentEditOption: {},// 当前编辑菜单按钮
      editItemForm: Object.assign({}, default_editItemForm),
      actionParamsList: [],//{ key: "", execNow: false, requireParams:false, value: "" }
      editItemFormRules: {
        label: [
          { required: true, message: '请输入标签名称', trigger: 'blur' },
        ],
        value: [
          { required: true, message: '请输入唯一编码', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
   
  },
  watch: {

    showEditMenuDialogFlag(n, o) {
      if (n) {
        this.$nextTick(() => {
          setTimeout(async () => {
            this.loadDataSource()
            //debugger
            if (!!this.editItemForm.actionParams.CDATASOURCE_ID && !this.dataSetGroup.hasOwnProperty(this.editItemForm.actionParams.CDATASOURCE_ID)) {
              this.editItemForm.dataSetList = await this.getDataSetList(this.editItemForm.actionParams.CDATASOURCE_ID)
              this.dataSetGroup[this.editItemForm.actionParams.CDATASOURCE_ID] = cloneDeep(this.editItemForm.dataSetList)
            } else {
              // 直接读取缓存数据
              this.editItemForm.dataSetList = this.dataSetGroup[this.editItemForm.actionParams.CDATASOURCE_ID]
            }
          }, 300)
        })
      } else {
        // 清除缓存
        // this.dataSetGroup={}
      }
    }
  },
  mounted() {
    //this.loadCurrentDataSourceItem()
  },
  methods: {
    loadData_itemValue(item,type='vxetable'){
      switch (type) {
        case 'vxetable':
        try{
        this.optionsListItemValue =[]
        let _itemData =null
        if(this?.designer?.widgetList?.length>0){
          _itemData =this.designer.widgetList.find(subItem=>{
               if(item.value.includes(subItem.id)){
                return subItem
               }                                                                     
          })
          if(_itemData){
            if(_itemData?.options?.dataSetAllModel?.length>0){
              this.optionsListItemValue =_itemData.options.dataSetAllModel
            }
          }
        }
      }catch(error){}
    
          break;
          case 'defaultmenubutton':
           // debugger
          try{
            this.optionsListItemValue =[]
            let _itemData =null
            if(this?.designer?.widgetList?.length>0){
              _itemData =this.designer.widgetList.find(subItem=>{
                  if(item.value.includes(subItem.id)){
                    return subItem
                  }                                                                     
              })
              if(_itemData){
                if(_itemData?.options?.searchInputItems?.length>0){
                  //let isDateRange= false
                  let _cItem= _itemData.options.searchInputItems.find(citem=>{
                    if(item.value.includes(citem.fieldName)){
                        return citem
                      } 
                  })
                  if(_cItem && _cItem.controlType.includes('range')){
                      this.optionsListItemValue =[
                        {title:'开始时间',field:'0'},
                        {title:'结束时间',field:'1'}
                      ]
                  }
                
                }
              }
            }
          }catch(error){}
          break;
        default:
          break;
      }
     
      
    },
    selectChange_itemValue(item,val,type='vxetable'){
     switch (type) {
      case 'vxetable':
           let tableName = item.value.replace('{{','').replace("}}",'').split('.')[0]
            item.value = `{{${tableName}.row.${val}}}`
        break;
        case 'defaultmenubutton':
            let itemName = item.value.replace('{{','').replace("}}",'').split('.')
            item.value = `{{${itemName[0]}.${itemName[1]}.value[${val}]}}`
        break;
      default:
        break;
     }
   
    },
    getNodeTreeData(){
       let copyData = cloneDeep(this.designer.nodeTreeData)
       let dataList = this.setPublicAttribute(copyData)
       return dataList
    },
    // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData){
      let dataList =[]
      for(let i=0;i<nodeTreeData.length;i++){
          let item = nodeTreeData[i]
          if(item.hasOwnProperty("children")){
              this.setPublicAttribute(item.children)
              dataList.push(item)
          }else{
            let randomNum = Math.floor(Math.random() * 100000000 + 1)
            // 优先子集对外开放属性
            if(item.hasOwnProperty("publicSubAttribute")){
              //debugger
               item.children = []
             
               if(item.publicSubAttribute && item.publicSubAttribute.length>0){
                  item.publicSubAttribute.forEach((subItem,index)=>{
                     
                       let valueItem = {
                          label:subItem.value,
                          id:subItem.value+"-"+randomNum+index
                       }
                       let keyItem = {
                          label:subItem.key,
                          id:subItem.key+"-"+randomNum+index,
                          children:[valueItem]
                       }
                       //debugger
                       item.children.push(keyItem)
                  })
                 // debugger
                  dataList.push(item)
               }

            }else{
              // 对外开放属性
              if(item.hasOwnProperty("publicAttribute")){
               item.children = []
               if(item.publicAttribute && item.publicAttribute.length>0){
                  item.publicAttribute.forEach((subItem,index)=>{
                       let kidItem = {
                          label:subItem,
                          id:subItem+"-"+randomNum
                       }
                       item.children.push(kidItem)
                  })
                  dataList.push(item)
               }
              }else{
                  dataList.push(item)
              }
            }
          }
        
      }
      return dataList
    
    },
   
    // 提交参数设置
    submitNodeEvent() {
      this.curSetParamsItem.value = this.nodeCtrlValue
      this.showDataSourceialogFlag = false
    },
    // 参数值设置 弹框 树节点 点击事件
    onNodeTreeClick(params) {
        let _canSelect = false
        this.nodeCtrlValue =""
        let hasChildren = params.hasOwnProperty("children")
        let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
        let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
        let parentNodeLabel = parentsNode.data.label
        let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
            ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params)
        if(!hasChildren){
          _canSelect = true
          this.nodeCtrlValue = ctrlValue
        // this.curSetParamsItem.value = ctrlValue
        }else{
          _canSelect = false
          this.$message.warning('此节点不可选！')
        }
        const now = new Date().getTime();
        if (now - this.clickTime < 500) { // 双击时间间隔设置为500毫秒
          console.log("双击时间间隔设置为500毫秒")
          if(_canSelect){
            this.submitNodeEvent() // 双击处理函数
          }
          
        }
      this.clickTime = now; // 更新点击时间
    },
    // 获取第三层 控件名称
    tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params){
      //debugger
        let newCtrlVal = ctrlValue
        let layoutList =["defaultmenubutton"] //|| parentsNode.parent.data.label.includes('card'),"card"
        let level3NodeType=""
        try {
           level3NodeType = parentsNode.parent.data.text
           if(layoutList.includes(level3NodeType)){
            newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
           }
        } catch (error) {
           level3NodeType=""
        }
        return newCtrlVal
    },
    // 弹框选择 参数 控件 来源
    setParams(item) {
      this.curSetParamsItem = item
      this.showDataSourceialogFlag = true
    },
    // 数据集数据选择改变，触发数据模型改变
    async change_dataSet(editItemForm, dataSetId) {
      await this.synchronizeParamsData()
      this.$nextTick(()=>{
                let selectedLabel = this.$refs["datasetSelectRef"].selectedLabel 
                // 执行DATASET方便
                editItemForm.label = selectedLabel
              
                if(editItemForm.dataSetList[this.currentDataSetIndex].CDATASOURCE_TYPE_CODE=="WEBAPI"){
                  editItemForm.actionParams.CDATASET_CONTENT = editItemForm.dataSetList[this.currentDataSetIndex].CDATASET_CONTENT // /API/abc/ccc
                  editItemForm.actionParams.CDATASOURCE_TYPE_CODE = editItemForm.dataSetList[this.currentDataSetIndex].CDATASOURCE_TYPE_CODE //"WEBAPI"
                  editItemForm.actionParams.CHOST_CPORT ='' // 默认为空，运行时加载  //this.currentDataSourceItem.CHOST + ":" + this.currentDataSourceItem.CPORT // "http://***********:8080"
                    /// 4:GET
                    /// 5:POST
                  editItemForm.actionParams.CDATASET_TYPE = editItemForm.dataSetList[this.currentDataSetIndex].CDATASET_TYPE== 4?"GET":"POST"
                }
                this.reloadingParamsEvent()
            })

    },
    showDatasetIframeBoxFn(datasetId){
      this.showDatasetIframeBox=true
      if(process.env.NODE_ENV == 'development'){
           this.datasetIframeURL= `http://**************:19719/subapp/datasource/publicData?id=${datasetId}`//&dataSourceId=${ids}
        }else{
          this.datasetIframeURL=window.location.origin+ "/subapp/datasource/publicData?id="+datasetId
        }
    },
    // 同步参数
    async synchronizeParamsData(){
      //debugger
      this.isLoading_searchParamsRef = true
      let datasetId = this.editItemForm.actionParams.CDATASET_ID
      // 所有参数
      let _resDataList = await this.loadSearchParamsMap(datasetId)
      // 已有参数
      if(_resDataList){
        _resDataList.forEach(pItem =>{
        let paramsIndex = this.actionParamsList.findIndex(sItem =>{
            return sItem.key == pItem.key
        })
        if(paramsIndex==-1){
          let newItem = {
            key: pItem.key,
            requireParams:false,
            execNow: false,
            value: !!pItem.value?pItem.value:"",
            placeholder:!!pItem.desc?pItem.desc:""
          }

          this.actionParamsList.push(newItem)
        }
      })
      }
    
      // let tesst = this.currentDataSourceItem
      // let test2 = this.editItemForm.dataSetList
      let CDATASET_CONFIG = null
      let isLoacalAPI = false
     this.currentDataSetIndex =0 // 选中的数据集下标数字
      this.editItemForm.dataSetList.forEach((_dataItem,index)=>{
        if(_dataItem.CID == datasetId){
         // debugger
         this.currentDataSetIndex = index
        }
      })

      try {
        CDATASET_CONFIG =JSON.parse(this.editItemForm.dataSetList[this.currentDataSetIndex].CDATASET_CONFIG)
        // 是否需要本地解析
        isLoacalAPI = CDATASET_CONFIG.IsParse
      } catch (error) {
        isLoacalAPI = false
      }
      this.editItemForm.actionParams.CDATASOURCE_TYPE_CODE ='' // 重置
      if(this.editItemForm.dataSetList[this.currentDataSetIndex].CDATASOURCE_TYPE_CODE=="WEBAPI" && isLoacalAPI){
        //debugger
        this.editItemForm.actionParams.CDATASET_CONTENT = this.editItemForm.dataSetList[this.currentDataSetIndex].CDATASET_CONTENT // /API/abc/ccc
        //debugger
        this.editItemForm.actionParams.CDATASOURCE_TYPE_CODE = this.editItemForm.dataSetList[this.currentDataSetIndex].CDATASOURCE_TYPE_CODE //"WEBAPI"
        this.editItemForm.actionParams.CHOST_CPORT = '' // 默认为空，运行时加载 //this.currentDataSourceItem.CHOST + ":" + this.currentDataSourceItem.CPORT // "http://***********:8080"
        /// 4:GET
        /// 5:POST
        this.editItemForm.actionParams.CDATASET_TYPE = this.editItemForm.dataSetList[this.currentDataSetIndex].CDATASET_TYPE== 4?"GET":"POST"
       }
      this.isLoading_searchParamsRef = false
    },
    // 重新加载参数映射列表
    async reloadingParamsEvent() {
      this.isLoading_searchParamsRef = true
      let datasetId = this.editItemForm.actionParams.CDATASET_ID
      // 首次添加
      let _resDataList = await this.loadSearchParamsMap(datasetId)
      // debugger
      this.formatParamsMapData(_resDataList)
      this.isLoading_searchParamsRef = false
    },
    // 格式化查询参数映射
    formatParamsMapData(dataList) {
      //debugger
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            key: oldItem.key,
            requireParams:false,
            execNow: false,
            value: !!oldItem.value?oldItem.value:"",
            placeholder:!!oldItem.desc?oldItem.desc:""
          }

          newDataList.push(newItem)
        })
      }
      this.actionParamsList = newDataList
    },
    // 加载 参数 列表(查询参数映射列表)
    async loadSearchParamsMap(datasetId) {
      let params = {
        Id: datasetId
      }
      let ParameterMap = ""
      let _url = "api/MD/DataSet/GetInputParameterByDataSetId"
      await request['post'](_url, params).then(res => {
        // debugger
        if (res && res.Datas && res.Datas.length > 0) {
          ParameterMap = res.Datas
        }
      })
      return ParameterMap
    },
   async loadDataSource() {
      if (this.dataSourceList.length == 0) {
       await this.getDataSourceList()
        if(this.editItemForm.actionParams.CDATASOURCE_ID){
          this.currentDataSourceItem = this.dataSourceList.find(item => item.CID == this.editItemForm.actionParams.CDATASOURCE_ID)
        }
       
      }
    },
    // 获取数据源数据
    async getDataSourceList() {
      this.dataSourceList = []
      let params = {
        condition: "",
        typeId: 0
      }
      let _url = "/api/MD/DataSource/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          this.dataSourceList = res.Datas
        }
      })
    },
    // 获取数据集合
    async getDataSetList(sourceId) {
      let dataSetList = []
      let params = {
        //typeId:0,
        condition: `{CID:${sourceId}}`
      }
      let _url = "api/MD/DataSet/GetAll"
      //let _url = "api/MD/DataSet/GetAllDataSet"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataSetList = res.Datas
        }
      })
      return dataSetList
    },
    // 数据源数据选择改变，触发数据集合改变
    async change_dataSource(formItem, sourceId) {
      //debugger
      this.currentDataSourceItem = this.dataSourceList.find(item => item.CID == sourceId)
      //debugger
      if (sourceId != -1) {
        formItem.dataSetList = await this.getDataSetList(sourceId)
        //debugger
      } else {
        formItem.dataSetList = []
      }
      //  this.resetDataSetTableData(formItem, index, sourceId)
    },
    // 添加测试数据
    changeLoadApi(value) {
      this.actionParamsList = []

      if (value.includes('PersonAanalysis')) {
        // 添加默认参数
        let searchParams = {
          "UserID": 102510316327266,
          "StartTime": "2022-01-04",
          "EndTime": "2023-01-11",
          "CUSER_NAME": ""
        }
        for (const [key, val] of Object.entries(searchParams)) {
          this.actionParamsList.push(
            { key: key, valueType: "1", value: val }
          )
        }

      }
    },
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val);
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val);
    },
    // 切换动作时，重置动作的参数配置
    actionChangeEvent(params) {
      // 初始化 重置
      this.editItemForm.actionParams = {
        //actionName:""
      }
    },
    // 设置组合后的动态参数
    setQueryList() {
      let queryParams = {}
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach(item => {
          if (!!item.key) {
            queryParams[item.key] = item //  多参数解析
          }
        })
      }
      //debugger
      return queryParams
    },
    // 获取组合后的动态参数
    getQueryList() {
      // debugger
      let queryParamsList = []
      //debugger {key:"",execNow:false, requireParams:false,value:""}
      if (this.editItemForm.actionParams && this.editItemForm.actionParams.query && Object.keys(this.editItemForm.actionParams.query).length > 0) {
        //debugger
        for (const [key, value] of Object.entries(this.editItemForm.actionParams.query)) {
          if (!!key) {
            // let newItem ={key,value} 多参数解析
            queryParamsList.push(value)
          }
        }
      }
      return queryParamsList
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editItemForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editItemForm.label
          this.currentEditOption.value = this.editItemForm.value
          this.currentEditOption.otherParams = cloneDeep(this.editItemForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editItemForm.actionParams)
          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setQueryList()

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑菜单属性--- 初始化弹框属性参数
    showEditDialogEvent(option) {
      this.currentEditOption = option // 当前菜单属性
      this.showEditMenuDialogFlag = true
      this.editItemForm.label = option.label
      this.editItemForm.value = option.value
      this.actionParamsList = [] // 默认
      this.editItemForm.otherParams = cloneDeep(option.otherParams)
      this.editItemForm.actionParams = cloneDeep(option.actionParams)
      // debugger
      if (this.editItemForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getQueryList()
      }
      //debugger
      let tt = this.editItemForm.otherParams.actionType

    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.designer.formConfig.queryList.splice(index, 1)
      }

    },
    // 添加查询
    addQueryOption() {
      let newValue = this.designer.formConfig.queryList.length + 1+"-"+generateId()
      this.designer.formConfig.queryList.push(
        { label: '新查询' + newValue, value: "query" + newValue, check: false, canRemove: true, otherParams: {}, actionParams: {} }
      )
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1
      this.actionParamsList.push(
        { key: "", execNow: false, requireParams:false, value: "" }
      )
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1)
    },


  }
}
</script>
  
<style lang="scss" scoped>
.datasetIframeBox{
  height:calc(100vh - 120px)
}
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}

.node-tree ::v-deep {
  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 12px;
  }

  .el-tree-node__content {
    padding-left: 0 !important;
  }

  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 12px;
    overflow: visible !important;
    /* 加入此行让el-tree宽度自动撑开，超出宽度el-draw自动出现水平滚动条！ */
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree>.el-tree-node:before {
    border-left: none;
  }

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: "";
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: "";
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed #4386c6;
    bottom: 0px;
    height: 100%;
    top: -10px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed #4386c6;
    height: 20px;
    top: 12px;
    width: 16px;
  }

  .el-tree-node.is-current>.el-tree-node__content {
    background: #c2d6ea !important;
  }

  .el-tree-node__expand-icon {
    margin-left: -3px;
    padding: 6px 6px 6px 0px;
    font-size: 16px;
  }

}</style>
  