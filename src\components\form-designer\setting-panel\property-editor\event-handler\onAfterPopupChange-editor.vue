<template>
  <el-form-item label="onAfterPopupChange" label-width="150px">
    <el-button type="info" icon="el-icon-edit" plain round @click="editEventHandler('onAfterPopupChange', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
/**
 * 
 *  Add by Andy
 *  标准弹框 发生改变后 回调事件
 *  value 配置属性也就是返回字段KEY，返回文本VALUE
 * originData 原始选择行属性
 * otherOptions：其它自身属性，如:表单FORM,字段配置OPTIONS等.
 */
  import i18n from "@/utils/i18n"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "onAfterPopupChange-editor",
    mixins: [i18n, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        // 返回参数
        eventParams: ['value', 'originData','otherOptions'],
      }
    }
  }
</script>

<style scoped>

</style>
