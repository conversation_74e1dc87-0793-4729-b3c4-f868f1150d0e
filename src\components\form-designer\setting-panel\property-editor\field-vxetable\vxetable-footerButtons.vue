<template>
  <div class="option-items-pane">


    <draggable tag="ul" :list="optionModel.footerButtons"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in optionModel.footerButtons" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input @click.native="showEditMenuDialogEvent(option)" readonly v-model="option.label" size="mini"
            style="width: 160px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button
            circle
            plain
            size="mini"
            type="info"
            @click="customJsEventCodeOption(option, idx)"
            icon="el-icon-edit"
          ></el-button>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>

    <commonEventDialog :functionName="functionName" :jsCodeTitle="jsCodeTitle" :eventCodeConfig="currentEditSettingBtnOption" @submitEvent="submitCommonEventDialog" ref="commonEventDialogRef"></commonEventDialog>

    <div>
      <el-button type="text" @click="addOption">+添加按钮</el-button>
    </div>

    <el-dialog title="编辑 表格底部操作按钮" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input v-model="editMenuForm.label"></el-input>
        </el-form-item>
        <el-form-item label="图标icon" prop="iconClass">
          <el-input v-model="editMenuForm.iconClass"></el-input>
          <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
        </el-form-item>
        <el-form-item label="显示类型">
            <el-select v-model="editMenuForm.type">
                <el-option label="default" value=""></el-option>
                <el-option label="primary" value="primary"></el-option>
                <el-option label="success" value="success"></el-option>
                <el-option label="warning" value="warning"></el-option>
                <el-option label="danger" value="danger"></el-option>
                <el-option label="info" value="info"></el-option>
                <el-option label="text" value="text"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="按钮大小" prop="size">
          <el-select v-model="editMenuForm.size" placeholder="请选择大小">
            <el-option label="大medium" value="medium"></el-option>
            <el-option label="中small" value="small"></el-option>
            <el-option label="小mini" value="mini"></el-option>
          </el-select>
        </el-form-item>

        <eventTypeByCtrlDialog contrlType="common" :editItemForm="editMenuForm" :designer="designer"
          :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>

        <!-- 动态参数 -->
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'"
          style="font-weight: bold;">参数</div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
            <el-form-item label="KEY" prop="key">
              <el-input  placeholder="key" v-model="item.key"></el-input>
            </el-form-item>
            <el-form-item label="VALUE" prop="value">

              <el-input placeholder="value" v-model="item.value"></el-input>
            </el-form-item>
            <div style="margin-bottom:20px;margin-left:5px"> <i @click="deleteParam(item, index)"
                class="el-icon-delete"></i></div>
          </div>
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType != '-1'">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
        </div>
        <div style="font-weight: bold;">提交后 成功或失败后事件处理
          <el-tooltip effect="light" content="一般指执行查询API后，需要执行的后续动作，如：刷新表格列表数据，或其它">
                         <i class="el-icon-info"></i></el-tooltip>
        </div>
        <el-form-item label-width="0">
          <!-- 注意：成功后或失败后事件处理-->
          <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
            :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type=""
          @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
const default_editMenuForm = {
  label: "",// 标签
  iconClass: "",// 图表ICON
  size: "medium",
  type:"primary",
  otherParams: {
    hidden: false, // 隐藏
    disabled: false,// 激活
    actionType: "-1",// 动作
    eventType: "click",// 事件类型
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
  },
}
import commonEventDialog from "@/components/form-designer/setting-panel/commonEventDialog.vue"
import afterSuccessOrErrorSetting from '@/components/form-designer/setting-panel/afterSuccessOrErrorSetting'
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import CodeEditor from '@/components/code-editor/index'
import i18n from "@/utils/i18n";
import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
export default {
  name: "vxetable-footerButtons",
  mixins: [i18n],
  components: {
    Draggable,
    CodeEditor,
    eventTypeByCtrlDialog,
    afterSuccessOrErrorSetting,
    commonEventDialog,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    //contrlType: String,// 控件类型名称
  },
  data() {
    return {
      jsCodeTitle:"",
      functionName:"onBeforeSubmit",
      currentEditSettingBtnOption:{},// 当前操作按钮配置
      showEditMenuDialogFlag: false,
      optionLines: '',
      currentEditOption: {},// 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm),
      actionParamsList: [],
      editMenuFormRules: {
        label: [
          //{ required: true, message: '请输入标签名称', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
    optionModel() {
      return this.selectedWidget.options
    },

  },
  watch: {
    'selectedWidget.options': {
      deep: true,
      handler(val) {
        //console.log('888888', 'Options change!')
      }
    },
  },
  methods: {
     // 提交
     submitCommonEventDialog(params){
       let code = params.code
      /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
       this.$set(this.currentEditSettingBtnOption, this.functionName, code)
    },
     // 自定义代码功能
     customJsEventCodeOption(option, idx){
      //debugger
      // 自定义代码 弹框 方法标题+参数名称
       this.jsCodeTitle = option.label+"."+option.value+"."+this.functionName+"(paramsData)"
      this.currentEditSettingBtnOption = option; // 当前菜单按钮属性
      let commonEventDialogRef = this.$refs["commonEventDialogRef"]
      if(commonEventDialogRef){
        commonEventDialogRef.showDialog = true
      }
    },
    // 设置组合后的动态参数
    setQueryList() {
      let queryParams = {}
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach(item => {
          if (!!item.key) {
            queryParams[item.key] = item.value
          }
        })
      }
      //debugger
      return queryParams
    },
    // 获取组合后的动态参数
    getQueryList() {
      let queryParamsList = []
      //debugger
      if (this.editMenuForm.actionParams && this.editMenuForm.actionParams.query && Object.keys(this.editMenuForm.actionParams.query).length > 0) {

        for (const [key, value] of Object.entries(this.editMenuForm.actionParams.query)) {
          if (!!key) {
            let newItem = { key, value }
            queryParamsList.push(newItem)
          }
        }
      }
      return queryParamsList
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editMenuForm.label
          this.currentEditOption.iconClass = this.editMenuForm.iconClass
          this.currentEditOption.size = this.editMenuForm.size
          this.currentEditOption.type = this.editMenuForm.type
          this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setQueryList()

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑菜单属性--- 初始化弹框属性参数
    showEditMenuDialogEvent(option) {
      this.currentEditOption = option // 当前菜单属性
      this.showEditMenuDialogFlag = true
      this.editMenuForm.label = option.label
      this.editMenuForm.iconClass = option.iconClass
      this.editMenuForm.size = option.size
      this.editMenuForm.type = option.type
      this.actionParamsList = [] // 默认
      this.editMenuForm.otherParams = cloneDeep(option.otherParams)
      this.editMenuForm.actionParams = cloneDeep(option.actionParams)
      //debugger
      if (this.editMenuForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getQueryList()
      }
      //debugger
      let tt = this.editMenuForm.otherParams.actionType

    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.optionModel.footerButtons.splice(index, 1)
      }

    },
    // 添加按钮
    addOption() {
      if(!this.optionModel.hasOwnProperty("footerButtons")){
            this.$set(this.optionModel, "footerButtons", [])
      }
      let newValue = this.optionModel.footerButtons.length + 1
      this.optionModel.footerButtons.push(
        { label: '新按钮', value: "", check: false, canRemove: true, otherParams: {}, actionParams: {} }
      )
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1
      this.actionParamsList.push(
        { key: '', value: "" }
      )
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1)
    },


  }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}</style>
