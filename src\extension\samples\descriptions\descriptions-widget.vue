<template>
    <static-content-wrapper :designer="designer" :field="field" :design-state="designState"
                            :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                            :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
                <div :ref="field.options.name">
                  <el-skeleton :loading="loading_container" animated>
                    <div :key="mainIndex" v-for="(mainItem,mainIndex) in dataList"  style="margin-bottom:5px;border-bottom:2px dotted #409EFF">
                          <el-descriptions  :title="!!field.options.showTitle?field.options.title:''" :size="field.options.size" :direction="!!field.options.isVertical?'vertical':'horizontal'" :column="field.options.columnNumber" :border="!!field.options.showBorderStyle">
                           <template v-for="(subItem,subIndex) in field.options.seriesItems">
                              <el-descriptions-item v-if="!!subItem.check" :span="Number(subItem.columnNumber)" :key="subIndex"  :label="subItem.label">
                                <template slot="label">
                                    <i v-if="!!subItem.iconUrl" :class="subItem.iconUrl"></i>
                                    {{subItem.label}}
                                  </template>
                                <template v-if="!!subItem.showAsTag">
                                    <template v-if="subItem.tagList.length>0">
                                      <el-tag :type="getTagType(mainItem[subItem.fieldName],subItem.tagList)" size="small">
                                         <descriptionsFieldNameItem :mainItem="mainItem" :fieldName="subItem.fieldName" :displayType="subItem.displayType" :subItem="subItem"></descriptionsFieldNameItem>
                                      </el-tag>
                                    </template>
                                    <template v-else>
                                      <el-tag  size="small">
                                        <!-- 值{{mainItem[subItem.fieldName]}} -->
                                        <descriptionsFieldNameItem :mainItem="mainItem" :fieldName="subItem.fieldName" :displayType="subItem.displayType" :subItem="subItem"></descriptionsFieldNameItem>
                                      </el-tag>
                                    </template>
                                 

                                </template>
                                <template v-else>
                                  <!-- {{subItem.fieldName}} -->
                                  <descriptionsFieldNameItem :mainItem="mainItem" :fieldName="subItem.fieldName" :displayType="subItem.displayType" :subItem="subItem"></descriptionsFieldNameItem>
                                </template>
                              </el-descriptions-item>
                           </template>
                          

                        </el-descriptions>
                      </div>
                  </el-skeleton>
              </div>
    </static-content-wrapper>
  </template>
  
  <script>
  import descriptionsFieldNameItem from './descriptionsFieldNameItem.vue'
    import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
    import emitter from '@/utils/emitter'
    import i18n from "@/utils/i18n"
    import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
    import { useFormatParams } from "@/hooks/useFormatParams"
    export default {
      name: "descriptions-widget",
      componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
      mixins: [emitter, fieldMixin, i18n],
       // 注入列表页面整体实体 
      inject: ['getPageInstance', 'sourceVFormRenderState'],
      props: {
        field: Object,
        parentWidget: Object,
        parentList: Array,
        indexOfParentList: Number,
        designer: Object,
  
        designState: {
          type: Boolean,
          default: false
        },
  
        subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
          type: String,
          default: ''
        },
  
      },
      data(){
        return{
          dataList:[{}],
          loading_container:false,
          maxLoadTime: 10,
          useFormatParamsFn: useFormatParams(this),
        }
      },
      computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
     
    },
      components: {
        StaticContentWrapper,descriptionsFieldNameItem
      },
      created() {
        this.registerToRefList()
        this.initEventHandler()
      },
      mounted(){
        let _self = this
        this.$nextTick(() => {
            // 是否被动加载数据
            if (!!!this.field.options.isPassiveDataLoad) {
              _self.tryTillGetData()
            }

        })
      },
      beforeDestroy() {
        this.unregisterFromRefList()
      },
      methods: {
        getTagType(typeText='',dataList){
         // debugger
          let _type='primary'
          if(!!typeText){
            if(dataList && dataList.length>0){
              let _selectItem = dataList.filter(item=>{
                if(item.tagValue ==typeText){
                  return item
                }
              })
              if(_selectItem && _selectItem.length>0){
                _type =  _selectItem[0].tagType
              }
            }
          }
          return _type
        },
           // 对外暴露事件，重新加载
         async reSearchData(paramsOptions = {}) {
             await this.tryTillGetData()
             console.log("========descriptions=====reSearchData=====")
        },
         // 循环直到获取到数据
        // CACTION_RECORD: "1"  日志
        // CDATETIME_CREATED: "2023-05-05T00:00:00" 记录时间
        // CDATETIME_MODIFIED: "2023-05-05T00:00:00"
        // CENTERPRISE_CODE: 2465318660504
        // CID: 1
        // CINSTANCE_ID: "1"
        // CORG_CODE: 2465318660504
        // CPHASE_ESTIMATED_TIME: "2023-05-05T00:00:00" 预计完成时间
        // CREMARK: "1"
        // CROWREMARK: "1"
        // CSTATE: "A"
        // CTASK_ID: 1
        // CTASK_IMAGES: Array(0) 图片
        // CTASK_PHASE_ID: 1
        // CTASK_PHASE_NAME: null 阶段
        // CTASK_STATE_ID: 1
        // CTASK_STATE_NAME: null 状态
        // CUSER_CREATED: "SYS"
        // CUSER_MODIFIED: "SYS"
        // IS_READONLY: true
        // IS_UPDATE: false
         async tryTillGetData() {
            // debugger
            let _self = this
            this.dataList =[]
            this.loading_container = true
            let dataObj = this.$refs[_self.field.options.name] 
            if (!!dataObj) {
                // 干正事
                this.dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
                this.loading_container = false
                this.maxLoadTime = 10
            } else {
                if (_self.maxLoadTime > 0) {
                    setTimeout(async () => {
                        _self.maxLoadTime = _self.maxLoadTime - 1
                        await this.tryTillGetData()
                    }, 1000)
                }
            }
        },
      }
    }
  </script>
  
  <style lang="scss" scoped>
// .el-descriptions-row{
//   .el-descriptions-item__cell{
//     width: 120px !important;
//   }
// }  
::v-deep .el-descriptions-row {
    .el-descriptions-item__cell{
      width: 50px !important;
      overflow: hidden;
    }
  }
  </style>
  