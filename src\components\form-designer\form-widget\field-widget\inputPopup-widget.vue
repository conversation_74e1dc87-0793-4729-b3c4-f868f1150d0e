<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div v-show="!isReadMode" class="flex">
      <el-select :readonly="field.options.readonly" :disabled="field.options.disabled" v-model="fieldModel"
        :placeholder="field.options.placeholder">
        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.key">
        </el-option>
      </el-select>
      <el-button @click.native="popupButtonClickEvent" slot="append" icon="el-icon-zoom-in"></el-button>
    </div>
  </form-item-wrapper>
</template>

<script>
// import emitter from '@/libs/mitt'
import FormItemWrapper from './form-item-wrapper'
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

export default {
  name: "inputPopup-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'globalOptionData', 'globalModel','getPageInstance',],
  data() {
    return {
      activeFlag: false,
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      selectOptions: []
    }
  },
  computed: {
    pageInstance() {
        // 获取列表示例
        return this.getPageInstance()
      },
    standardBulletBoxChange_State() {
      return this.$store.state.standardBulletBoxChange
    }
  },
  watch: {
    standardBulletBoxChange_State: {
      handler(n, o) {
        // 回调函数
        let params = n.value
        if (params.fieldName == this.field.options.name) {
          console.log("=======emitter.on(getStandardBulletBox, (params) =========")
          this.changeEvent(params.postData)
        }
      },
      deep: true
    }
  },

  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList()
    this.initFieldModel()
    this.initEventHandler()
    this.buildFieldRules()

    this.handleOnCreated()
  },

  mounted() {
    // this.$nextTick(()=>{
    //   // 注册监听 标准弹框事件
    //  emitter.on("getStandardBulletBox", (params) => {
    //   debugger
    //   // 回调函数
    //   console.log("=======emitter.on(getStandardBulletBox, (params) ========="+params.fieldName)
    //   if(params.fieldName==this.field.options.name){
    //     this.changeEvent(params.postData)
    //   }
    // })
    // })
    this.handleOnMounted()
    this.init()

  },

  beforeDestroy() {
    //  移除监听
    //emitter.off("getStandardBulletBox")
    this.unregisterFromRefList()
  },

  methods: {

    init() {
      let defaultItem = {
        key: this.formModel[this.field.options.name],
        label: !!this.formModel[this.field.options.name + `_TEXT`] ? this.formModel[this.field.options.name + `_TEXT`] : this.formModel[this.field.options.name]
      }
      this.selectOptions = [defaultItem]
    },
    popupButtonClickEvent() {

      this.$actions.setGlobalState({
        type: "standardBulletBox",
        sourceKey: this.field.options.inputPopupSourceCode, //"CDEVICE_NO"
        fieldName: this.field.options.name,
        fullPath: this.$route.fullPath,
      });
      console.log("====发起===popupButtonClickEvent==============sourceKey:" + this.field.options.inputPopupSourceCode + "===fieldName:" + this.field.options.name)

    },
    // 弹框选择改变 触发事件
    changeEvent(params) {

      if (!!params) {
        let postData = JSON.parse(params)
        this.selectOptions = postData.data
        this.$nextTick(() => {
          // this.formModel[this.field.options.name] = postData.data[0].key 
          // 直接赋值无效，需要使用this.setValue
          let newVal = postData.data[0].key
          this.setValue(newVal)
          // if(!!this.field.options.matchField){
          //     this.rowData[this.field.options.matchField] = postData.data[0].label
          // }
         // console.log("=======changeEvent==========this.setValue(newVal)=========" + newVal)
          let onAfterPopupChangeStr = this.field.options.onAfterPopupChange
          let otherOptions={
            formModel:this.formModel,
            fieldOptions:this.field.options
          }
          let onAfterPopupChange = new Function('value', 'originData',"otherOptions", onAfterPopupChangeStr)
          //  选择弹框数据后，其它自定义操作 方法
          if (!!onAfterPopupChangeStr) {
            try {
              onAfterPopupChange.call(this, newVal, postData,otherOptions)
            } catch (error) {
              this.$message({
                message: '（onAfterPopupChange）错误，请检查！！！',
                type: 'error'
              });
              return
            }
          }
        })
      }

    },
  }
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//
</style>
