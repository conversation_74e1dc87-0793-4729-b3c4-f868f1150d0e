<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <!-- el-upload增加:name="field.options.name"后，会导致又拍云上传失败！故删除之！！ -->
    <el-upload class="vform-picture-upload" :ref="field.options.name" :disabled="field.options.disabled || isReadMode"
      :action="realUploadURL" :headers="uploadHeaders" :data="uploadData"
      :with-credentials="field.options.withCredentials" :multiple="field.options.multipleSelect" :file-list="fileList"
      :show-file-list="field.options.showFileList" list-type="picture-card"
      :class="{ 'hideUploadDiv': uploadBtnHidden || isReadMode }" :limit="field.options.limit"
      :on-exceed="handlePictureExceed" :on-preview="handlePicturePreview" :before-upload="beforePictureUpload"
      :http-request="httpRequestFn" :on-success="handlePictureUpload" :on-error="handleUploadError"
      :on-remove="handlePictureRemove">
      <div slot="tip" class="el-upload__tip" v-if="!!field.options.uploadTip">{{ field.options.uploadTip }}</div>
      <i class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>

    <el-dialog title="" v-if="showPreviewDialogFlag" :visible.sync="showPreviewDialogFlag" v-dialog-drag append-to-body
      width="60%" :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="true"
      :close-on-press-escape="true" :destroy-on-close="true">
      <img :src="previewUrl" width="100%" alt="" />
    </el-dialog>
  </form-item-wrapper>
</template>

<script>
import request from '@/libs/request'
import FormItemWrapper from './form-item-wrapper'
import emitter from '@/utils/emitter'
import i18n, { translate } from "@/utils/i18n";
import { deepClone } from "@/utils/util";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import BMF from "browser-md5-file";
export default {
  name: "picture-upload-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'globalOptionData', 'globalModel','getPageInstance',],
  data() {
    return {
      uploadArray:[],
      minioObj: {
        endPoint: "************",// "服务ip",
        port: "6630",//端口,
        useSSL: false,
        accessKey: "oyxA5eCJ2DV4UXv3",//"账号",
        secretKey: "OFKNj8q0a6lt7pFi7ln4xyfX0JbRTM1x",//"密码"
      },
      oldFieldValue: null, //field组件change之前的值
      fieldModel: [],
      fieldModelImageList: [],
      rules: [],
      jiamiText:"",
      uploadHeaders: {},
      uploadData: {
        key: '',  //七牛云上传文件名
        //token: '',  //七牛云上传token

        //policy: '',  //又拍云上传policy
        //authorization: '',  //又拍云上传签名
      },
      fileList: [],  //上传文件列表
      uploadBtnHidden: false,

      previewUrl: '',
      showPreviewDialogFlag: false,
    }
  },
  computed: {
    pageInstance() {
        // 获取列表示例
        return this.getPageInstance()
      },
    realUploadURL() {
      let uploadURL = this.field.options.uploadURL
      if (!!uploadURL && ((uploadURL.indexOf('DSV.') > -1) || (uploadURL.indexOf('DSV[') > -1))) {
        let DSV = this.getGlobalDsv()
        return eval(this.field.options.uploadURL)
      }

      return this.field.options.uploadURL
    },

  },
  watch:{
    fieldModel:{
      handler(n,o){
        if(!!!n){
          this.fileList =[]
        }
       
      },
      deep:true
    }
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList()
    this.initFieldModel()
    this.initEventHandler()
    this.buildFieldRules()

    this.handleOnCreated()
  },

  mounted() {
    this.handleOnMounted()
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },

  methods: {
    // 自定义上传方法
    httpRequestFn(item) {
      return new Promise((resolve, reject) => {
        const bmf = new BMF();
        bmf.md5(
          item.file,
          (err, md5) => {
            this.jiamiText = md5;

            resolve(true); // 97027eb624f85892c69c4bcec8ab0f11
          },
          (progress) => {
            console.log("progress number:", progress);
          }
        );
      })
      .then(() => {
        const stringValue =
         "taskmanager"+ "¤"+ item.file.name + "¤" + item.file.type 
        var encoder = new TextEncoder("utf8");
        var byts = encoder.encode(stringValue);
        var bytslength = byts.length;
        var array1 = Array.from(byts);
        var array2 = this.IntToBytesLittleEndian(bytslength, 4);
        this.uploadArray = [...array2, ...array1];

      })
      .then(() => {
        var _this = this;
        var reader = new FileReader();
        reader.readAsArrayBuffer(item.file);
        reader.onload = function (resData) {
          // currentTargetArrayBuffer == this.result 来自 reader 内部返回值
          let currentTargetArrayBuffer = resData.currentTarget.result
          var result = [];
          var byts = new Uint8Array(currentTargetArrayBuffer);
          var array4 = Array.from(byts);
          result = [..._this.uploadArray, ...array4];
          // debugger
          let _url = `api/SYS/Images/BytePost/UploadFile` // 固定取值地址
          let params = result
           request["post"](_url, params).then(res=>{
          
           // ==================debugger=====res======================
              // CBUCKET_NAME: "taskmanager"
              // CDATETIME_CREATED: "2023-06-01T09:11:06.350"
              // CDATETIME_MODIFIED: "2023-06-01T09:11:06.350"
              // CENTERPRISE_CODE: 0
              // CID: 6777514513530885
              // CIMAGE_BODY: "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABY9JREFUWEetV39sU1UU/s7bD4ZZoiLE9XUBs0hIXLIECYkERJYoG9trEaOjSiD8WJhb2yVMlKh/MP+AqIlO1tdBiIMZ0cQRDOvrAGPiICoEgzCUSCTTEKCv45dBGAQ3do+5bV95q1vbTe5fW++55/vuueec7zzCBNaTB1on3eGcDSy4Qh4nor2m5m2bgCvQeA8VHQzOVe7xDgBPp5w9iZycWrOq/pT99+nhtkeHgJKo1vDzaFjjIuDoDlaSELsAcoARMN2+RqehzwNjNRPWA+hnEjVRrfF7CVZkBBYpoJ44MOumy+9PJZE1AYcRXEHgPQlnm0yX/0O7M9UIvAimDhAeZoWWRKu9hxyGngCHDPUiAS7vd/kP289lRUA19EYA22IHCetMzbdrtHA6u4LlrIgOgKYTuF6AlpPgI+ZSf7Nq6DwhAqoRbAZ4M0DXQLTO1BpC6fJGPbB9NoaHOwCUMXD4fxFQDb0dwFowfodCa03NezSbpH083FaSw9wB8LMM2ht1eWvGFYEnenYXDA7c/g7APBB+VHJp9aVKb1824JaN82v9Mc6DjIQmSRD4layeoNjY7hQYPgPgEYBDBQX5q/58oe7v8YAnbXuac9WBqZLECvlbRgLFXW1lQhGn48lG7abmrZ0QcMoh1dB1AF6A9+cXFr56vnzNXcskWQXObr2GBb5KlNn7psv/9oMATz5JWN/CjHcAHKXJcEee912PFxUAtSvQDIU2J354I+LyfZwO3GnoTQwsA7AgeRPimojm35u2QozAWwB9AGAAnFdquusukL1bMfHKqOZPNJvRXalGMASwK7F7E8BFAKWkUFOk2tuSKWqOULCOKNbK5TPPJ1kesX8EvycbRvobBK8CPFXairy8zv6q13+LRVD6YG433f6sckbt0j0gNIMwi9Sw/gMY8616HYuAGtJbQfCnEnUYwU5ZYkx4N6r5tmaKgLVvRZJUo3UxoHyT2OhlxmeThoc6zi/bcMPuTDX0CECXgdwFpqvujtyzwEFUa2redqehb2VQCRP6iMWvpsufSOr7nhxhfT1xTLjmMOF4LAkd4bY5ihDrE4oWfxGbcDi7dY0FDAbviLr89ZY7iwARywqqkK0aoFMAz2Lgp6jLV37/xjIBlTdjT8joY8KuewrtHiFG0zqDhXmTxEZZEXYCVpUQizURd6NsLMmVjIIUXOamqNvfYqmgReCpzs78G5Ov/APgGBG32KvlP2pogdkJpIY69Z3j++Kg6fLvTiRlPwNnLQLTeoKFeQN8a7REz0jAdsNfmMgT1bxn0yVa8cG2MnFPnJZKaBEoPvTpFDF09/q4CRCUBpnhIJzIUYTnYlXjH5myXDUCAYB8RLQxonk/kvYz9rU4hvLzzHERsBRMvhvyablZ4ZUNZ8xVFArOVYibAHhkIg7nK5WXK+qvxJK8W59BAuezI2AENwMca0gEPjKoKJ6r1d7+VGSZK6zQc3E7mgJwWfxv7MMQ6iIvxXu9XMXGtpkCOecyEohPvPgC4JkgfKvkFnguVdb+NRq4pR2xPcZFEPWSwjsj1b5wqn1RSC9VCGfSElCN4GIQ7wFjGgMHhsRtz7Wlm26lAR8k0ArOx/FMz5MY006OSaAoFFiiEMlZLxeg/Q/lDHv6qhpl3Y5YNtW8yQotl5NvpqSU+85Q6zNMyrExCST7PMgwtYalIIoLlG3ZwK8wiZet2T8bAo5ufSEJHBmbQEIRSaHZkWpvbxrwC8xYFnX7TmYDbNmoRtAHcCAdgRNSHKQ+p06+tpufE+C68QBbtgpIjmSlzFgVdfs+t/sYMRERY2fE7UuC2CeliQCnnOkdFLcXpiZ2shUndT3xMUEKlTCwUjYkhpjQl6+dQOonmbU3QgtUI/AaiLaAMZlAFwTzl1G375MHcPsxXfwLFM/ZPPbkGB0AAAAASUVORK5CYII="
              // CIMAGE_DESC: null
              // CIMAGE_DIRECTORY: "http://************:6630/taskmanager/6777514513530885.png"
              // CIMAGE_NAME: "reportFixOrder.png"
              // CIMAGE_SIZE: "1493"
              // CIMAGE_TYPE: "image/png"
              // CINSTANCE_ID: ""
              // CMD5: "aa415b9017a3a9a7145162f79a137bf4"
              // CORG_CODE: 0
              // CROWREMARK: ""
              // CSTATE: "A"
              // CUSER_CREATED: "SYS"
              // CUSER_MODIFIED: "SYS"
              // IS_READONLY: true
              // IS_UPDATE: false
            // ==================debugger===========================
            if(res.Success){
                let _urlItem = res.Data
                _this.fieldModelImageList.push(_urlItem)
                _this.syncUpdateFormModel(_this.fieldModelImageList)
                _this.$message({
                  message: !!res.Content?res.Content:'上传成功!!',
                  type: "success",
                });
            }else{
               _this.$message({
                message: !!res.Content?res.Content:'上传失败！！',
                type: "error",
              });
            }
          })
          // UpdateImage(result).then((res) => {
          //   res = res.data
          //   if (res.code === 200 && res.data.Success) {
          //     _this.$message({
          //       message: res.data.Content,
          //       type: "success",
          //     });
          //     _this.getdatasoures();
          //     _this.updateFierl = false;
          //   } else {
          //     _this.$message({
          //       message: res.data.Content,
          //       type: "error",
          //     });
          //   }
          // });
        };
      });
    },
    IntToBytesLittleEndian(number, length) {
      var bytes = [];
      var i = 0;
      do {
        bytes[i++] = number & 255;
        number = number >> 8;
      } while (i < length);
      return bytes;
    },
    getdatasoures() {
      // const text = {
      //   BucketName: "flowchart"
      // };
      // GetPageListByQueryJson(text).then((res) => {

      //   if (res.code === 200 && res.data.Success) {
      //     this.tableData = res.data.Datas;

      //   } else {
      //     this.$message({
      //       message: res.message.msg || res.data.Content,
      //       type: "error",
      //     });
      //   }
      //   this.loading = false
      // });
    },
    handlePictureExceed() {
      console.log("=========handlePictureExceed============")
      let uploadLimit = this.field.options.limit
      this.$message.warning(this.i18nt('render.hint.uploadExceed').replace('${uploadLimit}', uploadLimit))
    },

    handlePicturePreview(file) {
      console.log("=========handlePicturePreview============")
      this.previewUrl = file.url
      this.showPreviewDialogFlag = true
    },

    beforePictureUpload(file) {
      console.log("=========beforePictureUpload============")
      let fileTypeCheckResult = false
      if (!!this.field.options && !!this.field.options.fileTypes) {
        let uploadFileTypes = this.field.options.fileTypes
        if (uploadFileTypes.length > 0) {
          fileTypeCheckResult = uploadFileTypes.some((ft) => {
            return file.type === 'image/' + ft
          })
        }
      }
      if (!fileTypeCheckResult) {
        this.$message.error(this.i18nt('render.hint.unsupportedFileType') + file.type)
        return false;
      }

      let fileSizeCheckResult = false
      let uploadFileMaxSize = 5  //5MB
      if (!!this.field.options && !!this.field.options.fileMaxSize) {
        uploadFileMaxSize = this.field.options.fileMaxSize
      }
      fileSizeCheckResult = file.size / 1024 / 1024 <= uploadFileMaxSize
      if (!fileSizeCheckResult) {
        this.$message.error(this.i18nt('render.hint.fileSizeExceed') + uploadFileMaxSize + 'MB')
        return false;
      }

      this.uploadData.key = file.name
      return this.handleOnBeforeUpload(file)
    },

    handleOnBeforeUpload(file) {
      console.log("=========handleOnBeforeUpload============")
      if (!!this.field.options.onBeforeUpload) {
        let bfFunc = new Function('file', this.field.options.onBeforeUpload)
        let result = bfFunc.call(this, file)
        if (typeof result === 'boolean') {
          return result
        } else {
          return true
        }
      }

      return true
    },

    updateFieldModelAndEmitDataChangeForUpload(fileList, customResult, defaultResult) {
     // debugger
      console.log("=========updateFieldModelAndEmitDataChangeForUpload============")
      let oldValue = deepClone(this.fieldModel)
      if (!!customResult && !!customResult.name && !!customResult.url) {
        this.fieldModel.push({
          name: customResult.name,
          url: customResult.url
        })
      } else if (!!defaultResult && !!defaultResult.name && !!defaultResult.url) {
        this.fieldModel.push({
          name: defaultResult.name,
          url: defaultResult.url
        })
      } else {
        this.fieldModel = deepClone(fileList)
      }

      // this.syncUpdateFormModel(this.fieldModel)
      // this.emitFieldDataChange(this.fieldModel, oldValue)
    },

    handlePictureUpload(res, file, fileList) {
      // console.log('test----', file)
      // console.log('test2222----', fileList)
      // console.log('test@res----', res)
      console.log("=========handlePictureUpload============")
      if (file.status === 'success') {
        let customResult = null
        if (!!this.field.options.onUploadSuccess) {
          let customFn = new Function('result', 'file', 'fileList', this.field.options.onUploadSuccess)
          customResult = customFn.call(this, res, file, fileList)
        }

        this.updateFieldModelAndEmitDataChangeForUpload(fileList, customResult, res)
        this.fileList = deepClone(fileList)
        this.uploadBtnHidden = fileList.length >= this.field.options.limit
      }
    },

    updateFieldModelAndEmitDataChangeForRemove(file, fileList) {
      console.log("=========updateFieldModelAndEmitDataChangeForRemove============")
      let oldValue = deepClone(this.fieldModel)
      let foundFileIdx = -1
      this.fileList.map((fi, idx) => {
        if ((fi.name === file.name) && ((fi.url === file.url) || (!!fi.uid && fi.uid === file.uid))) {  /* 这个判断有问题？？ */
          foundFileIdx = idx
        }
      })
      if (foundFileIdx > -1) {
        this.fieldModel.splice(foundFileIdx, 1)
      }

      this.syncUpdateFormModel(this.fieldModel)
      this.emitFieldDataChange(this.fieldModel, oldValue)
    },

    handlePictureRemove(file, fileList) {
      console.log("=========handlePictureRemove============")
      this.updateFieldModelAndEmitDataChangeForRemove(file, fileList)
      this.fileList = deepClone(fileList)
      this.uploadBtnHidden = fileList.length >= this.field.options.limit

      if (!!this.field.options.onFileRemove) {
        let customFn = new Function('file', 'fileList', this.field.options.onFileRemove)
        customFn.call(this, file, fileList)
      }
    },

    handleUploadError(err, file, fileList) {
      console.log("=========handleUploadError============")
      if (!!this.field.options.onUploadError) {
        let customFn = new Function('error', 'file', 'fileList', this.field.options.onUploadError)
        customFn.call(this, err, file, fileList)
      } else {
        this.$message({
          message: this.i18nt('render.hint.uploadError') + err,
          duration: 3000,
          type: 'error',
        })
      }
    },

  }
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

::v-deep .vform-picture-upload .el-upload--picture-card {
  width: 69px !important;
  height: 69px !important;
  line-height: 76px !important;

}

::v-deep .vform-picture-upload .el-upload-list__item {
  width: 69px !important;
  height: 69px !important;
  line-height: 76px !important;
}

.full-width-input {
  width: 100% !important;
}

.hideUploadDiv {
  ::v-deep div.el-upload--picture-card {
    /* 隐藏最后的图片上传按钮 */
    display: none;
    // width: 69px !important;
    // height: 69px !important;
    // line-height: 82px !important;
  }

  ::v-deep div.el-upload--text {
    /* 隐藏最后的文件上传按钮 */
    display: none;
  }

  ::v-deep div.el-upload__tip {
    /* 隐藏最后的文件上传按钮提示 */
    display: none;
  }
}
</style>
