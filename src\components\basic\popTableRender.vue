<template>
    <div>
        <el-input
            placeholder="请选择"
            suffix-icon="el-icon-plus"
            @focus="focusEvent"
            v-bind="$attrs"
            v-on="$listeners"
            >
        </el-input>
        <nvxeModal  ref="popTableRenderRef" @confirmEvent="submitEvent()" :title="title"  v-model="showPopTableRender">
              <vxe-grid 
                @cell-dblclick="cellDblclickEvent"
                :size="gridDefaultOptions.size"
                :border="gridDefaultOptions.border" 
                :loading="gridDefaultOptions.loading"
                :stripe="gridDefaultOptions.stripe" 
                :resizable="gridDefaultOptions.resizable"
                :show-overflow="gridDefaultOptions.showOverflow" 
                :show-footer-overflow="gridDefaultOptions.showFooterOverflow"
                :highlight-current-row="gridDefaultOptions.highlightCurrentRow"
                :show-header-overflow="gridDefaultOptions.showHeaderOverflow"
                :highlight-hover-row="gridDefaultOptions.highlightHoverRow" 
                height="auto"
                  :pager-config="pagerConfig"
                  :columns="gridDefaultOptions.columns" 
                  :data="gridDefaultOptions.data"
                  @page-change="pageChangeEvent"
                  >
             </vxe-grid>
        </nvxeModal>
    </div>
</template>
<script>
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'popTableRender',// 弹框表格数据
    props:{
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
        // 是否可用
        disabled:{
            type:Boolean,
            default:false,
        }  
    }, 
    data(){
        return {
              // 表格默认配置属性
            gridDefaultOptions: {
                size: "mini",
                border: true,
                stripe: true,
                resizable: true,
                showOverflow: true,
                showFooterOverflow: true,
                showHeaderOverflow:true,
                highlightCurrentRow: true,
                highlightHoverRow: true,
                // filterConfig: {},
                 loading: false,
                // params: {},
                // height: 0,
                // align: "left",
                columns: [],
                data: [],
            },
            config:config,
            pagerConfig:{
                currentPage:1,
                pageSize:10,
                total:0,
            },
            title:"弹框数据",
            loading:false,
            showPopTableRender:false,// 是否显示弹框
          
        }
    },
    methods:{
        // 聚焦输入框
        focusEvent(params){
            this.showPopTableRender = true
            this.$nextTick(()=>{
                this.loadData()
            })
        },
        // 确认
        submitEvent(){
          
        },
        //  双击 选中单元格 触发事件
        cellDblclickEvent(params){
            //debugger
            let row = params.row
            let matchField= this.paramsItem.matchField //转换字段 CTABLE_NAME|CDISPLAY_NAME
         
            let postParams = {
                row,
                matchField
            }
            this.$emit("changeEvent",postParams)
            setTimeout(()=>{
                this.showPopTableRender = false
            },100)

        },
        // 格式化 转换字段列表
        formatMatchField(){

        },
        // 翻页时间触发
        pageChangeEvent(pageInfo){
            this.pagerConfig.currentPage = pageInfo.currentPage
            this.$nextTick(()=>{
                this.loadData()
            })
        },
        // 处理列头显示问题
        handleColumns(columns=[]){
            if(columns && columns.length>0){
                columns = columns.filter(item=>{
                    if(item.iisShowList && !!item.title){
                        return item
                    }
                })
            }
            return columns
        },
         // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
       loadData(){
            // if(this.selectDataList.length >1){
            //     return
            // }
            let _self = this
            this.gridDefaultOptions.loading = true
            let sourceKey = this.paramsItem.sourceKey
            //debugger
            let searchParams = this.paramsItem.searchParams

            let _url=this.config.virModule+"Data/PopSource"
            let params = {
                   "PageIndex":this.pagerConfig.currentPage,
                   "PageSize":this.pagerConfig.pageSize,
                   SourceType:"Table", //Dict Table Tree
                   Params:{
                    sourceKey,// 表名
                    filter:{} // 字段过滤参数
                   }
                };
                if(!!searchParams){
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                       let searchParamsArr = searchParams.split(',')
                       searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                          params.Params.filter[paramItem[0]] =paramItem[1]
                       })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    }
                }
            request['post'](_url, params).then(res=>{
                //debugger
                if(res.Success){
                    let resData = res.Datas
                    this.gridDefaultOptions.loading = false
                    this.gridDefaultOptions.columns = this.handleColumns(resData.TableDetial)
                    this.gridDefaultOptions.data = resData.TableData
                    this.pagerConfig.total = res.TotalRows
                }

            })
       },
    }
}
</script>
<style lang="scss" scoped>

</style>