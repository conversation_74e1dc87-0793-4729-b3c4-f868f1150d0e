<template>
    <vxe-modal ref="nvxeModalRef" destroy-on-close fullscreen v-model="showEditBox" :show-footer="false">
        <!-- 基础设置 -->
        <div v-show="(stepsActive == 1)">
            <div class="w-full h-full flex justify-between overflow-hidden">
                <div></div>
                <div style="height: 100%;width: 600px; border:1px solid #ccc" class="flex justify-center h-full">
                    <el-form ref="templateForm" :rules="rules" style="margin:10px;" label-width="80px"
                        :model="formBaseSetting">
                        <el-form-item label="功能名称" prop="CNAME">
                            <el-input class="controlWidth" v-model="formBaseSetting.CNAME"></el-input>
                        </el-form-item>
                        <el-form-item label="功能编码" prop="CCODE">
                            <el-input class="controlWidth" v-model="formBaseSetting.CCODE"></el-input>
                        </el-form-item>
                        <el-form-item label="模板类型" prop="CTEMPLATE_TYPE">
                            <el-select class="controlWidth" v-model="formBaseSetting.CTEMPLATE_TYPE" placeholder="请选择">
                                <el-option v-for="item in templateOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <!-- <el-form-item label="功能分类" prop="type">
                            
                            <el-select class="controlWidth" v-model="formBaseSetting.type" placeholder="请选择">
                                <el-option
                                v-for="item in templateOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                             </el-select>
                        </el-form-item> -->
                        <!-- <el-form-item label="启用流程" prop="activeFlow">
                            <el-switch
                                v-model="formBaseSetting.activeFlow"
                                active-color="#13ce66"
                                inactive-color="#ccc">
                            </el-switch>
                        </el-form-item> -->
                        <!-- <el-form-item label="功能排序" prop="CDESC">
                            <el-input-number v-model="formBaseSetting.CDESC" controls-position="right"
                                :min="1"></el-input-number>
                        </el-form-item> -->
                        <el-form-item label="功能说明" prop="CDESC">
                            <el-input class="controlWidth" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
                                placeholder="请输入功能说明" v-model="formBaseSetting.CDESC"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div></div>
            </div>
        </div>
        <!-- 功能设计 -->
        <div v-show="(stepsActive == 2)">
            <VFormDesigner :show-footer="false" ref="vfDesignerRef" :designer-config="designerConfig"
                :global-dsv="globalDsv">
            </VFormDesigner>
        </div>

        <template #header>

            <div class="w-full h-full flex justify-between items-center overflow-hidden">
                <div class="flex items-center" style="width:300px;margin-left:5px;">
                    <!-- {{`${actionTitle} 表单模型`}} -->
                    <div class="w-full ">
                        <el-steps style="height: 8px;" :active="stepsActive" finish-status="success" simple>
                            <el-step title="基础设置"></el-step>
                            <el-step title="功能设计"></el-step>
                        </el-steps>
                    </div>

                </div>
                <div>
                    <div v-show="(stepsActive == 2)" class="flex  items-center">
                        <div @click="hideAction('left')"><svg width="28px" height="28px" viewBox="0 0 28 28"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                class="sc-kOcGyv hnIuVN">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g transform="translate(5.000000, 5.000000)"
                                        :stroke="formTemplatePanel.left ? '#000000' : '#DDDDDD'" stroke-width="1.5">
                                        <rect x="0.75" y="0.75" width="16.5" height="16.5" rx="2"></rect>
                                        <line x1="7.5" y1="1.5" x2="7.5" y2="16.5" stroke-linecap="square"></line>
                                        <line x1="1.5" y1="7" x2="4.5" y2="7" stroke-linecap="round"></line>
                                        <line x1="1.5" y1="10.5" x2="4.5" y2="10.5" stroke-linecap="round"></line>
                                    </g>
                                </g>
                            </svg></div>
                        <div @click="hideAction('bottom')"><svg width="28px" height="28px" viewBox="0 0 28 28"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                class="sc-hFxENk bAABtC">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <rect :stroke="formTemplatePanel.bottom ? '#000000' : '#DDDDDD'" stroke-width="1.5" x="5.75"
                                        y="5.75" width="16.5" height="16.5" rx="2"></rect>
                                    <line x1="13.5" y1="8.5" x2="13.5" y2="23.5"
                                        :stroke="formTemplatePanel.bottom ? '#000000' : '#DDDDDD'" stroke-width="1.5"
                                        stroke-linecap="square"
                                        transform="translate(13.500000, 16.000000) rotate(-90.000000) translate(-13.500000, -16.000000) ">
                                    </line>
                                    <line x1="9.5" y1="19" x2="11.5" y2="19"
                                        :stroke="formTemplatePanel.bottom ? '#000000' : '#DDDDDD'" stroke-width="1.5"
                                        stroke-linecap="round"></line>
                                    <line x1="15.5" y1="19" x2="18.5" y2="19"
                                        :stroke="formTemplatePanel.bottom ? '#000000' : '#DDDDDD'" stroke-width="1.5"
                                        stroke-linecap="round"></line>
                                </g>
                            </svg></div>
                        <div @click="hideAction('right')"><svg width="28px" height="28px" viewBox="0 0 28 28"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                class="sc-cnHmbd fMoJvE">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g transform="translate(14.000000, 14.000000) scale(-1, 1) translate(-14.000000, -14.000000) translate(5.000000, 5.000000)"
                                        :stroke="formTemplatePanel.right ? '#000000' : '#DDDDDD'" stroke-width="1.5">
                                        <rect x="0.75" y="0.75" width="16.5" height="16.5" rx="2"></rect>
                                        <line x1="7.5" y1="1.5" x2="7.5" y2="16.5" stroke-linecap="square"></line>
                                        <line x1="1.5" y1="7" x2="4.5" y2="7" stroke-linecap="round"></line>
                                        <line x1="1.5" y1="10.5" x2="4.5" y2="10.5" stroke-linecap="round"></line>
                                    </g>
                                </g>
                            </svg></div>
                    </div>
                </div>


                <div style="padding:2px;" class="flex">
                    <el-button size="mini" :disabled="(stepsActive == 1)" @click="nextStepAction(1)">上一步</el-button>
                    <el-button size="mini" :disabled="(stepsActive == 2)" @click="nextStepAction(2)">下一步</el-button>
                    <el-button size="mini" :loading="loadingBtn" @click="preSubmitEvent()" type="success">保存</el-button>
                    <el-button size="mini" @click="cancelBtn()" type="info">取消</el-button>
                    <el-button v-if="$store.state.actionType.value == 'iisEdit' && formBaseSetting.CID>0" size="mini" :loading="loadingBtn" @click="saveAndPreview()" type="success">保存并预览</el-button>
                </div>
            </div>
        </template>
    </vxe-modal>
</template>
<script>
import VFormDesigner from '@/components/form-designer/index.vue'
import request from '@/libs/request'
import dayjs from 'dayjs'
const default_formBaseSetting= {
                CNAME: '',//功能名称
                CCODE: "",//功能编码
                CTYPE: "1",//功能分类
                CTEMPLATE_TYPE: "Web",//模板分类
                CSEQ: 10,//功能排序
                CDESC: "",//功能说明
                CJSON_DATA: "",//功能m模板JSON
            }
export default {
    name: "edit-formDesign",
    components: {
        VFormDesigner,
    },
    data() {
        return {
         
            formBaseSetting: Object.assign({},default_formBaseSetting),
            rules: {
                CNAME: [
                    { required: true, message: '请输入功能名称', trigger: 'blur' },
                    // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
                ],
                CCODE: [
                    { required: true, message: '请输入功能编码', trigger: 'blur' }
                ],
                // type: [
                //     { required: true, message: '请选择功能分类', trigger: 'change' }
                // ],
                // templateType: [
                //     { required: true, message: '请选择模板类型', trigger: 'change' }
                // ],

            },
            stepsActive: 1,// 步骤下标
            actionTitle: "添加",
            //templateName:"",//模板名称
            templateType:"1",//模板类型
            templateOptions: [{ //模板列表
                value: 'Web',
                label: 'Web'
            }, {
                value: 'App',
                label: 'App'
            },
            { //模板列表
                value: 'WebHome',
                label: 'WebHome'
            },
            {
                value: 'AppHome',
                label: 'AppHome'
            }
            
            ],
            loadingBtn: false,
            showEditBox: false,
            designerConfig: {
                //logoHeader: false,
                //toolbarMaxWidth: 510
            },

            //全局数据源变量
            globalDsv: {
                testApiHost: 'http://www.test.com/api'
            },
        }
    },
    computed: {
        // 面板隐藏属性 add by andy
        formTemplatePanel() {
            return this.$store.state.formTemplatePanel
        }


    },
    watch: {
        // 监听弹框是否打开
        showEditBox(n, o) {
            if (!!n) {
                this.$nextTick(() => {
                    this.setActionTitle()
                    setTimeout(() => {
                        this.init()
                    }, 100)
                })
            } else {
                // 关闭时候，清空设计表单
                this.clearDesigner()
                // 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
                this.$refs["templateForm"].resetFields()
                // this.$refs["templateForm"].clearValidate()
                this.stepsActive = 1
            }
        }
    },
    methods: {
        // 初始化数据
        init() {

            let $formRef = this.$refs["vfDesignerRef"]
            if (!!$formRef) {
                $formRef.designer.clearDesigner() //无论添加修改，首次先清空
                this.$refs["templateForm"].clearValidate()
                this.stepsActive = 1
                if (this.$store.state.actionType.value == "iisAdd") {
                    // 添加

                } else {
                    // 修改
                    this.formModifyData()
                }
            }
        },
       
        // 表单修改前赋值
        async formModifyData() {
           // debugger
            let routerName = this.$route.name
            // debugger
            let rowData = this.$store.state.currentRow.value[routerName];
            this.formBaseSetting.CID = !!rowData.CID?rowData.CID:"" // 功能ID
            this.formBaseSetting.CNAME = rowData.CNAME // 功能名称
            this.formBaseSetting.CCODE = rowData.CCODE // 功能编码
            this.formBaseSetting.CTYPE = rowData.CTYPE // 功能分类
            this.formBaseSetting.CTEMPLATE_TYPE = rowData.CTEMPLATE_TYPE // 模板分类
            this.formBaseSetting.CSEQ = rowData.CSEQ // 功能排序
            this.formBaseSetting.CDESC = rowData.CDESC // 功能说明

            if(rowData?.CJSON_DATA && !!rowData.CJSON_DATA){
                this.formBaseSetting.CJSON_DATA = rowData.CJSON_DATA // JSON 数据
            }else{
                this.formBaseSetting.CJSON_DATA  = await this.getRowTemplateData()
                //console.log("CJSON_DATA:",this.formBaseSetting.CJSON_DATA)
                
            }
           
        },
          // 获取当前选中行模板数据
          async getRowTemplateData() {
            //debugger
            let formJsonData = ""
            let _url = `api/MD/VisualFormDesigner/GetByID`

            let params = {
                id: this.formBaseSetting.CID
            }
            await request["get"](_url, null, params).then(res => {
     
                    if (res.Success && res.Datas && !!res.Datas.CJSON_DATA) {
                        formJsonData = res.Datas.CJSON_DATA
                    }

                });

            return formJsonData
        },
        // 步骤，提交前验证表单
        nextStepAction(step = 1) {
            let _self = this
            this.$refs["templateForm"].validate((valid) => {
                if (valid) {
                    _self.stepsActive = step
                    //debugger
                    if (step == 2 && _self.$store.state.actionType.value == "iisEdit") {
                        _self.$nextTick(() => {
                            _self.doJsonImport()
                        })
                    }
                }
            });
        },
        // 隐藏面板功能
        hideAction(type = "left") {
            //debugger
            let params = {
                type,
                value: !this.$store.state.formTemplatePanel[type],
            }
            this.$store.commit("set_formTemplatePanel", params)
        },
        // 设置添加、修改弹框标题
        setActionTitle() {
            if (this.$store.state.actionType.value == "iisAdd") {
                this.actionTitle = "添加"
                // 添加
            } else {
                // 修改
                this.actionTitle = "修改"
            }
        },
        // 清空设计表单
        clearDesigner() {
            let $formRef = this.$refs["vfDesignerRef"]
            if (!!$formRef) {
                $formRef.designer.clearDesigner() // 无论添加修改，都要线清空
            }
        },
        // 编辑时>>导入
       async doJsonImport() {
           //debugger
            try {
                let designer = null
                let $formRef = this.$refs["vfDesignerRef"]
                if (!!$formRef) {
                    designer = $formRef.designer
                }
                if (!!designer) {
                    let importTemplate =""
                    if(!!this.formBaseSetting.CJSON_DATA){
                         importTemplate = await this.getRowTemplateData() //当前行模板数据
                    }else{
                        importTemplate = this.formBaseSetting.CJSON_DATA
                    }
                   
                    if(!!importTemplate){
                        let importObj = JSON.parse(importTemplate)
                        designer.loadFormJson(importObj)
                        // designer.emitHistoryChange() // 无需历史记录
                        designer.emitEvent('form-json-imported', [])
                    }else{
                        return
                    }
                 
                }

            } catch (ex) {
                this.$message.error(ex + '')
            }
            this.$nextTick(() => {
                let valueChange = Math.floor(Math.random() * 10000 + 1)
                this.$store.commit("set_containerDragEndChange", valueChange);
            })
        },
        // 获取当前选中行模板数据
        getRowTemplateData_backup() {
            let routerName = this.$route.name
            let rowData = this.$store.state.currentRow.value[routerName];
            let formJsonData = rowData.CJSON_DATA
            //debugger
            return formJsonData
        },

        // 生成随机码
        getRandomNum() {
            let randomNum = Math.floor(Math.random() * 10000 + 1)
            return randomNum
        },
        preSubmitEvent() {
            this.loadingBtn = true
            let _self = this
            this.$refs["templateForm"].validate((valid) => {
                if (valid) {
                    _self.submitEvent()
                } else {
                    _self.loadingBtn = false
                }
            });

        },

        saveAndPreview(){
            let _self = this
            this.$refs["templateForm"].validate(async(valid) => {
                if (valid) {
                   await _self.submitEvent(true)
                   //debugger
                   let routeData = this.$router.resolve({
                    path: '/designview',
                    query: {
                        formName: this.formBaseSetting.CID,
                        retrunPath: "designlist"
                    }
                    });
                    window.open(routeData.href, '_blank');
                } else {
                    _self.loadingBtn = false
                }
            });
        },
        cancelBtn() {
            this.formBaseSetting.CID =""
            this.showEditBox = false
        },
        async submitEvent(_showEditBox=false) {
            if (!!!this.formBaseSetting.CNAME || !!!this.formBaseSetting.CCODE) {
                this.$message({
                    message: '缺少必填参数!!',
                    type: 'warning'
                });
                return
            }
            let formJson = this.$refs["vfDesignerRef"].getFormJson()
            //debugger
            if(formJson && formJson.widgetList && formJson.widgetList.length==0){
                // 直接修改名称后直接保持时 使用
                if(!!this.formBaseSetting.CJSON_DATA){
                    formJson = JSON.parse(this.formBaseSetting.CJSON_DATA)
                }
               
            }
            // 每次编辑保存 都重新赋值
            formJson.formConfig.pageCID = this.formBaseSetting.CID //  添加保存当前页面唯一的CID,用于复制页面时，区分不同的页面
            // 默认添加路径
            let _url = `api/MD/VisualFormDesigner/add`
            // debugger
            let params = {
                CID:this.formBaseSetting.CID,
                CNAME: this.formBaseSetting.CNAME,//功能名称
                CCODE: this.formBaseSetting.CCODE,//功能编码
                CTYPE: this.formBaseSetting.CTYPE,//功能分类
                CTEMPLATE_TYPE: this.formBaseSetting.CTEMPLATE_TYPE,//模板分类
                //CSEQ: this.formBaseSetting.CSEQ,//功能排序,后台自动分配，否则分页错误！
                CDESC: this.formBaseSetting.CDESC,//功能说明
                CJSON_DATA:!!formJson?JSON.stringify(formJson):"",//功能m模板JSON 转换成字符串存储
            }
            if(this.$store.state.actionType.value == "iisEdit" || params.CID>0){
                // 修改接口路径
                _url = `api/MD/VisualFormDesigner/Update`
                //功能排序,更新时，需要提交接口参数中
                params.CSEQ =this.formBaseSetting.CSEQ
            }
            // this.$store.commit("set_formTemplateList",postData)
            await request["post"](_url, params).then(res => {
                if (!!res.Success) {
                    this.$message({
                        message: res.Content,
                        type: 'success'
                    });
                   
                } else {
                    this.$message({
                        message: res.Content,
                        type: 'error'
                    });
                }
            });

            let _self = this
            setTimeout(() => {
                _self.$emit("submitSuccess", {})
                _self.loadingBtn = false
                if(!_showEditBox){
                    _self.showEditBox = false
                    _self.formBaseSetting= Object.assign({},default_formBaseSetting)
                }
                
            }, 1000)
        },


    }

}
</script>
<style lang="scss" scoped>::v-deep .el-form-item {
    .el-form-item__error {
        // 修复弹框里面的表单错误提示信息
        line-height: 0.3 !important;
    }
}

::v-deep .vxe-modal--body {
    .vxe-modal--content {
        // 隐藏弹框多余的滚动条
        overflow: hidden !important;
    }
}

.controlWidth {
    // 表单控件宽度
    width: 500px
}</style>