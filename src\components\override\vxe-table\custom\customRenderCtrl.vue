<template>
     <div v-text="fieldModelTitle"></div>
</template>
<script>
export default {
    name: "customRenderCtrl",// 自定义行渲染控件
    props:{
        field:{
            type:String,
            default:""
        },
        itemOptions:{
            type:Object,
            default(){
                return {}
            }
        },
          // 当前字段选择行
        rowData: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    data() {
        return {
            fieldModelTitle:"",
        }
    },
    computed:{
        onBeforeRenderConfig() {
            let _onBeforeRender = ""
            try {
                _onBeforeRender = this.itemOptions.paramsItem.controlConfig.onBeforeRender
            } catch (error) {
                _onBeforeRender = ""
            }
            return _onBeforeRender
        },
   
    },

    mounted(){
       this.$nextTick(async()=>{
        await this.init()
       })
      
    },
    methods: {
       async init(){
           //////////////////////////////表格>>渲染前逻辑控件(onBeforeRender)/////////////////////////////////////////
           this.fieldModelTitle = this.rowData[this.field]
           //debugger
           let onBeforeRenderStr =  this.onBeforeRenderConfig
                    let otherOptions={
                       currentRow:this.rowData,
                    }
                    let onBeforeRender = new Function('paramsData', onBeforeRenderStr)
                    //  选择弹框数据后，其它自定义操作 方法
                    if (!!onBeforeRenderStr) {
                        try {
                         this.fieldModelTitle = await onBeforeRender.call(this, otherOptions)
                         if( this.fieldModelTitle ==undefined ||  this.fieldModelTitle ==null){
                            this.fieldModelTitle = this.rowData[this.field]
                         }
                        } catch (error) {
                          
                            this.$message({
                                message: '表格>>下拉(onBeforeRender)错误，请检查！！！',
                                type: 'error'
                            });
                            this.fieldModelTitle = this.rowData[this.field]
                              return
                        }
                     }
            //////////////////////////////表格>>渲染前逻辑控件(onBeforeRender)/////////////////////////////////////////
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//
</style>