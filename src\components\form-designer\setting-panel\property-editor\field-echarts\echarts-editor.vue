<template>
  <div>
    <el-form-item label-width="0">
            <commonParamsOnChange contrlType="select" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">高度
        <el-tooltip effect="light" content="默认为0,auto自动高度，小于1为占用百分比">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input-number v-model="optionModel.containerSize.height" controls-position="right" :min="0"></el-input-number>
    </el-form-item>
    <el-form-item label="">
        <span slot="label">设为被动加载
          <el-tooltip effect="light" content="默认 立即加载数据，设为被动加载后，数据需要通过其它方式触发加载，如 查询，点击等">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
        <el-switch v-model="optionModel.isPassiveDataLoad"></el-switch>
    </el-form-item>
    <CtrlListDialog v-show="!!optionModel.isPassiveDataLoad" :selected-widget="selectedWidget" :designer="designer" :optionModel="optionModel" optionModelField="shareDataTableName" formLabel="共享数据源控件"></CtrlListDialog>
    <el-form-item label-width="0">
      <selectDataSourceApi  contrlType="echarts" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></selectDataSourceApi>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">图表配置</el-divider>
    </el-form-item>
    <el-form-item label="背景颜色">
      <!-- <el-input type="text" v-model="optionModel.backgroundColor"></el-input> -->
      <el-color-picker
        v-model="optionModel.backgroundColor"
        show-alpha
        :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="主标题">
      <el-input type="text" v-model="optionModel.echartTitle.text"></el-input>
    </el-form-item>
    <el-form-item label="显示主标题ICON">
      <el-switch v-model="optionModel.showTextIcon"></el-switch>
    </el-form-item>
    <el-form-item label="主标题ICON颜色">
      <el-color-picker
        v-model="optionModel.textIconColor"
        show-alpha
        :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="子标题">
      <el-input type="text" v-model="optionModel.echartTitle.subtext"></el-input>
    </el-form-item>
    <el-form-item label="标题位置">
      <el-select v-model="optionModel.echartTitle.left">
        <el-option label="左" value="left"></el-option>
        <el-option label="居中" value="center"></el-option>
        <el-option label="右" value="right"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="图表类型">
      <el-select v-model="optionModel.chartConfig.compType">
        <el-option label="柱状图" value="bar"></el-option>
         <el-option label="时间状态图" value="barTimeStatus"></el-option>
         <el-option label="折线图" value="line"></el-option>
         <el-option label="趋势图" value="trend"></el-option>
         <!--  <el-option label="散点图" value="scatter"></el-option> -->
        <el-option label="饼图" value="pie"></el-option>
        <el-option label="混合图" value="mix"></el-option>
      
      </el-select>
    </el-form-item>
    <el-form-item label="X轴（显示字段）">
      <el-select v-model="optionModel.xAxisKey">
        <el-option label="选择" value=""></el-option>
        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType=='trend'" label="Y轴字段(趋势图)">
      <el-select v-model="optionModel.trendYAxisKey">
        <el-option label="选择" value=""></el-option>
        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType=='trend'" label="分组字段(趋势图)">
      <el-select v-model="optionModel.trendGroupByField">
        <el-option label="选择" value=""></el-option>
        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'barTimeStatus'" label="开始时间">
      <el-select v-model="optionModel.startDay">
        <el-option label="选择" value=""></el-option>
        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'barTimeStatus'" label="结束时间">
      <el-select v-model="optionModel.endDay">
        <el-option label="选择" value=""></el-option>
        <el-option v-for="(item, index) in optionModel.tableColumns" :key="index" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType!='trend'" label="图表序列">
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType!='trend'" label-width="0">
      <echartsSeriesItem :optionModel="optionModel" :designer="designer" :selected-widget="selectedWidget">
      </echartsSeriesItem>
    </el-form-item>
    <!-- <el-form-item label-width="0">
        <el-divider class="custom-divider">交换事件</el-divider>
      </el-form-item> -->
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label-width="0">
      <el-divider class="custom-divider">布局配置</el-divider>
    </el-form-item>
  
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴方向">
      <el-radio-group v-model="optionModel.xAxisDirection">
        <el-radio-button label="horizontal">水平</el-radio-button>
        <el-radio-button label="verticle">垂直</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="X轴两边不留白">
      <el-switch v-model="optionModel.xConfig.boundaryGap"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴名称">
      <el-input type="text" v-model="optionModel.xConfig.axisName"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴MIN">
      <el-input-number type="text" v-model="optionModel.xConfig.min"></el-input-number>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴MAX">
      <el-input-number type="text" v-model="optionModel.xConfig.max"></el-input-number>
    </el-form-item>
    <el-form-item label="隐藏X轴轴线">
      <el-switch v-model="optionModel.xConfig.hideAxisLineShow"></el-switch>
    </el-form-item>
    <el-form-item label="隐藏X轴刻度标签">
      <el-switch v-model="optionModel.xConfig.hideAxisLabelShow"></el-switch>
    </el-form-item>
    <el-form-item label="显示X轴分隔线">
      <el-switch v-model="optionModel.xConfig.splitLineShow"></el-switch>
    </el-form-item>
    <el-form-item v-show="!optionModel.xConfig.hideAxisLineShow" label="显示X轴刻度">
      <el-switch v-model="optionModel.xConfig.axisTickShow"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.xConfig.axisTickShow" label="X轴刻度朝内">
      <el-switch v-model="optionModel.xConfig.axisTickInside"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴标签配置">
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴标签间隔设定">
      <el-select v-model="optionModel.xConfig.axisLabel.interval">
        <el-option label="自动" value="auto"></el-option>
        <el-option label="强制显示所有" :value="0"></el-option>
        <el-option label="间隔1个" :value="1"></el-option>
        <el-option label="间隔2个" :value="2"></el-option>
        <el-option label="间隔3个" :value="3"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴标签旋转角度">
      <el-input-number v-model="optionModel.xConfig.axisLabel.rotate" controls-position="right"
        :min="0"></el-input-number>
    </el-form-item>
    <!-- 默认情况下，类目轴对应到 dataset 第一列。 -->
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="X轴类型">
      <el-select v-model="optionModel.xConfig.type">
        <el-option label="类目轴" value="category"></el-option>
        <el-option label="数值轴" value="value"></el-option>
        <el-option label="时间轴" value="time"></el-option>
        <el-option label="对数轴" value="log"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item  label-width="0">
      <el-divider class="custom-divider">Y轴配置</el-divider>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="Y轴名称">
      <el-input type="text" v-model="optionModel.yConfig.axisName"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="Y轴MIN">
      <el-input-number type="text" v-model="optionModel.yConfig.min"></el-input-number>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="Y轴MAX">
      <el-input-number type="text" v-model="optionModel.yConfig.max"></el-input-number>
    </el-form-item>
    <el-form-item label="显示Y轴轴线">
      <el-switch v-model="optionModel.yConfig.axisLineShow"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.yConfig.axisLineShow" label="Y轴轴线颜色">
      <el-color-picker
        v-model="optionModel.yConfig.axisLineStyleColor"
        show-alpha
        :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="隐藏Y轴刻度标签">
      <el-switch v-model="optionModel.yConfig.hideAxisLabelShow"></el-switch>
    </el-form-item>
    <el-form-item label="隐藏Y轴分隔线">
      <el-switch v-model="optionModel.yConfig.hideSplitLineShow"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.yConfig.axisLineShow" label="显示Y轴刻度">
      <el-switch v-model="optionModel.yConfig.axisTickShow"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.yConfig.axisTickShow" label="Y轴刻度朝内">
      <el-switch v-model="optionModel.yConfig.axisTickInside"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="Y轴类型">
      <el-select v-model="optionModel.yConfig.type">
        <el-option label="数值轴" value="value"></el-option>
        <el-option label="类目轴" value="category"></el-option>
        <el-option label="时间轴" value="time"></el-option>
        <el-option label="对数轴" value="log"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="Y轴数据格式">
      <el-input placeholder="{value} °C" type="text" v-model="optionModel.yConfig.formatter.value"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="保留小数位">
      <el-select v-model="optionModel.yConfig.valueToFixed">
        <el-option label="0" :value="0"></el-option>
        <el-option label="1" :value="1"></el-option>
        <el-option label="2" :value="2"></el-option>
        <el-option label="3" :value="3"></el-option>
        <el-option label="4" :value="4"></el-option>
        <el-option label="5" :value="5"></el-option>
        <el-option label="6" :value="6"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="开启区域缩放">
      <el-switch v-model="optionModel.enableDataZoom"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableDataZoom" label-width="0">
      <echartsDataZoom :optionModel="optionModel" :designer="designer" :selected-widget="selectedWidget">
      </echartsDataZoom>
    </el-form-item>
    <el-form-item label="其它Y轴">
    </el-form-item>
    <el-form-item label-width="0">
      <echartsYAxisItems :optionModel="optionModel" :designer="designer" :selected-widget="selectedWidget">
      </echartsYAxisItems>
    </el-form-item>
    <el-form-item  label-width="0">
      <el-divider class="custom-divider">其它配置</el-divider>
    </el-form-item>
    <el-form-item label="图例位置">
      
      <el-select v-model="optionModel.legendConfig.position">
        <el-option label="底部" value="bottom"></el-option>
        <el-option label="底部-左" value="bottomLeft"></el-option>
        <el-option label="底部-右" value="bottomRight"></el-option>
        <el-option label="左边" value="left"></el-option>
        <el-option label="右边" value="right"></el-option>
        <el-option label="头部" value="top"></el-option>
        <el-option label="头部-左" value="topLeft"></el-option>
        <el-option label="头部-右" value="topRight"></el-option>
        <el-option label="无" value="none"></el-option>
      </el-select>
    </el-form-item>
  
  
   
    <!-- <el-form-item label-width="0">
      <el-divider class="custom-divider">提示框配置</el-divider>
    </el-form-item> -->
    <el-form-item label="触发类型">
      <el-select v-model="optionModel.chartConfig.tooltip.trigger">
        <el-option label="数据项触发" value="item"></el-option>
        <el-option label="坐标轴触发" value="axis"></el-option>
        <el-option label="不触发" value="none"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">样式</el-divider>
    </el-form-item>

    <el-form-item v-show="optionModel.chartConfig.compType != 'pie' && optionModel.chartConfig.compType != 'mix'" label="显示标签">
      <el-switch v-model="optionModel.chartConfig.comp.showLabel"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'bar'" label-width="80" label="柱状图类型">
      <el-radio-group v-model="optionModel.chartConfig.comp.type">
        <el-radio-button label="basic">基础柱状图</el-radio-button>
        <el-radio-button label="total">堆叠柱状图</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'line'" label="折线图类型">
      <el-select v-model="optionModel.chartConfig.comp.type">
        <el-option label="基础折线图" value="basic"></el-option>
        <el-option label="堆叠折线图" value="stack"></el-option>
        <el-option label="区域面积图" value="areaStyle"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'line'" label="平滑曲线">
      <el-switch v-model="optionModel.chartConfig.comp.smooth"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'line'" label="数据点颜色">
      <!-- <el-input type="text" v-model="optionModel.chartConfig.comp.itemColor.value"></el-input> -->
      <el-color-picker :predefine="predefineColors" v-model="optionModel.chartConfig.comp.itemColor.value" >
    </el-color-picker>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'scatter'" label="散点形状">
      <el-select v-model="optionModel.chartConfig.comp.type">
        <el-option label="圆形" value="auto"></el-option>
        <el-option label="矩形" value="auto1"></el-option>
        <el-option label="三角形" value="auto2"></el-option>
        <el-option label="图钉" value="auto"></el-option>
        <el-option label="菱形" value="auto1"></el-option>
        <el-option label="箭头" value="auto2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'pie'" label="饼图类型">
      <el-select v-model="optionModel.chartConfig.comp.roseType">
        <el-option label="基础饼图" :value="false"></el-option>
        <el-option label="圆环图" :value="true"></el-option>
        <el-option label="玫瑰图" value="area"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'pie'" label="是否显示内容">
        <el-checkbox  v-model="optionModel.chartConfig.comp.showLabel"></el-checkbox>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType == 'pie'" label="是否显示数值">
      <el-checkbox @change="change_showLabelValue" v-model="optionModel.chartConfig.comp.showLabelValue"></el-checkbox>
    </el-form-item>

    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label-width="0">

      <el-divider class="custom-divider">图表grid偏移</el-divider>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="左left">
      <span slot="label">左left
        <el-tooltip effect="light" content="支持px,%.">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input type="text" v-model="optionModel.grid.left"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="右right">

      <el-input type="text" v-model="optionModel.grid.right"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="底部bottom">
      <el-input type="text" v-model="optionModel.grid.bottom"></el-input>
    </el-form-item>
    <el-form-item v-show="optionModel.chartConfig.compType != 'pie'" label="顶部top">
      <el-input type="text" v-model="optionModel.grid.top"></el-input>
    </el-form-item>
  </div>
</template>
<script>
import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
import CtrlListDialog from '@/components/form-designer/setting-panel/CtrlListDialog.vue'
import echartsSeriesItem from "./echartsSeriesItem.vue";
import echartsYAxisItems from "./echartsYAxisItems.vue";
import echartsDataZoom from "./echartsDataZoom.vue";
import i18n from "@/utils/i18n";
// import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
  name: "echarts-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    commonParamsOnChange,
    echartsSeriesItem, selectDataSourceApi,CtrlListDialog,echartsYAxisItems,echartsDataZoom
  },
  data() {
    return {
      actionName: "",
      loadingTableSubmit: false,
      useFormatParamsFn: useFormatParams(this),
      predefineColors: [
      '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#c71585',
            '#c7158577'	
        ]
    };
  },
  mounted() {
    // if(!!this.optionModel.actionName){
    //   this.actionName =  this.optionModel.actionName
    // }
  },
  methods: {
    // change_backgroundColor(val){
    //   this.$set(this.optionModel, "backgroundColor", val)
    // },
    change_showLabelValue(val){
      //debugger
      this.$set(this.optionModel.chartConfig.comp, "showLabelValue", val)
    }
    // 多集合 数据集改变
    // changeDataSet(val) {
    //   //debugger
    //   this.optionModel.tableColumns = this.optionModel.dataSetAllModel[val]
    // },
    // actionNameChangeEvent(value){

    //   this.optionModel.actionName = value
    // },
    // 开始运行API
    // loadTableData() {
    //   // debugger
    //   // 列头查询 preUrl 路径固定不变
    //   let actionName = this.optionModel.actionName
    //   let _url = this.useFormatParamsFn.getVFormDataSearchUrl(actionName);
    //   if (!!_url) {
    //     this.loadingTableSubmit = true
    //   } else {
    //     return
    //   }
    //   // 合并普通、高级查询参数
    //   let params = this.useFormatParamsFn.getVFormSearchParams(actionName);

    //   request["post"](_url, params).then((res) => {
    //     //debugger
    //     if (res && res.Data && res.Data.DataSet) {
    //       this.optionModel.dataSetAllModel = res.Data
    //       this.optionModel.dataSetData = res.Data.DataSet
    //       // 测试数据，编辑时预览使用
    //       let params = {
    //         key: 666666,
    //         value: res.Datas
    //       }
    //       this.$store.commit("set_mainTableDataList", params);
    //     }

    //     this.loadingTableSubmit = false
    //   })
    // }
  }
};
</script>
  
<style scoped></style>