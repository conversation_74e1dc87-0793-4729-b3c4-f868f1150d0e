<template>
    <div>
        <el-dialog width="65%" :title="`控件配置- ${title}`" :visible.sync="showEditCtrlDialogFlag"
            v-if="showEditCtrlDialogFlag" :show-close="true" class="small-padding-dialog" append-to-body
            :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
            <!-- hello world -->
            <div v-if="currentCtrlType != 'customSelect' && currentCtrlType != 'customRenderCtrl' ">
                <el-form size="small" :inline="true" :model="searchItemConfig" label-width="100px">
                  
                    <el-form-item label="选择加载API">
                        <el-select @change="change_sourceApi(searchItemConfig, $event)"
                            v-model="searchItemConfig.actionName" style="width:193.33px" placeholder="请选择查询">
                            <el-option label="请选择" value=""></el-option>
                            <el-option :key="queryIndex + queryItem.value"
                                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                                :value="queryItem.value"></el-option>
                        </el-select>
                    </el-form-item>
                  
                    <el-form-item v-show="!searchItemConfig.actionName ||currentCtrlType == 'statusColumn'" label="数据源key">
                        <el-input style="width:193.33px" v-model="searchItemConfig.sourceKey"></el-input>
                    </el-form-item>
                    <el-form-item v-show="!searchItemConfig.actionName" label="字段转换">
                        <el-input style="width:193.33px" v-model="searchItemConfig.matchField"></el-input>
                    </el-form-item>
                    <el-form-item v-show="!searchItemConfig.actionName" label="过滤参数">
                        <el-input style="width:193.33px" v-model="searchItemConfig.searchParams"></el-input>
                    </el-form-item>
                    <!-- 自定义选择发生改变后，逻辑事件事件：暂时只支持 标准弹框,下拉控件 -->
                    <el-form-item v-show="vShowOnAfterChange()" label-width="140" label="选择改变后事件">
                        <el-button
                            type="text"
                            @click="showOnAfterChangeCodeWindow()"
                        >
                        <span :style="[{ color: (!!searchItemConfig.onAfterChange) ? `green` : `` }]">{{ (!!searchItemConfig.onAfterChange)?
                      `编辑改变后事件` : `添加改变后事件` }}</span>
                    </el-button>
                    </el-form-item>
                    <!-- 控件渲染前逻辑事件：暂时只支持 下拉控件 -->
                    <!-- <el-form-item v-show="currentCtrlType == 'reportSelect'" label-width="140" label="渲染前事件">
                        <el-button
                            type="text"
                            @click="showOnBeforeRenderCodeWindow()"
                        >
                        <span :style="[{ color: (!!searchItemConfig.onBeforeRender) ? `green` : `` }]">{{ (!!searchItemConfig.onBeforeRender)?
                      `编辑渲染前事件` : `添加渲染前事件` }}</span>
                    </el-button>
                    </el-form-item> -->
                    <el-form-item v-show="currentCtrlType == 'reportSelectTable'" label="分页数" prop="CPAGE_SIZE">
                        <el-input-number style="width:193.33px" v-model="searchItemConfig.CPAGE_SIZE"
                            :min="1"></el-input-number>
                    </el-form-item>
                    <el-form-item v-show="currentCtrlType == 'reportSelectTable'" label="是否分页" prop="CIS_PAGE">
                        <el-checkbox false-label="N" true-label="Y" v-model="searchItemConfig.CIS_PAGE"></el-checkbox>
                    </el-form-item>
                    
                </el-form>
            </div>
            <div v-if="currentCtrlType == 'customSelect'">
                 <div class="flex">
                    <el-button size="mini" @click="ctrlSearchParams('add')" type="primary"
                        icon="vxe-icon-add">&nbsp;新加</el-button>
                </div>
                <div style="height:5px"></div>
            </div>
            <div v-if="currentCtrlType == 'customRenderCtrl'">
               <!-- 自定义渲染前-->
               <div class="flex justify-start items-center">
                        <div>渲染前事件:</div>
                        <div> </div>
                        <div>
                            <el-button
                            type="text"
                            @click="showOnBeforeRenderCodeWindow()"
                        >
                        <span :style="[{ color: (!!searchItemConfig.onBeforeRender) ? `green` : `` }]">{{ (!!searchItemConfig.onBeforeRender)?
                      `编辑渲染前事件` : `添加渲染前事件` }}</span>
                    </el-button>
                        </div>
                    </div>
            </div>
            <el-table  v-if="currentCtrlType != 'customRenderCtrl' && currentCtrlType != 'statusColumn' " border :data="tableData" style="width: 100%" max-height="300">
                <el-table-column type="index" fixed :index="indexMethod">
                </el-table-column>
                <el-table-column fixed prop="field" label="字段名" header-align="center" width="180">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.field"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="字段描述" header-align="center" width="180">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.title"></el-input>
                    </template>
                </el-table-column>
                <el-table-column  prop="iisShowList" label="是否显示" header-align="center" align="center" width="120">
                    <template slot-scope="scope">
                        <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.iisShowList"> </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column  prop="isSelectTextField" label="下拉Text" header-align="center" align="center" width="80">
                    <template slot-scope="scope">
                        <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.isSelectTextField"> </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="isSelectKeyField" label="下拉Key" header-align="center" align="center" width="80">
                    <template slot-scope="scope">
                        <el-checkbox :true-label="1" :false-label="0" v-model="scope.row.isSelectKeyField"> </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column  prop="width" label="列宽" header-align="center" align="center" width="130">
                    <template slot-scope="scope">

                        <el-input-number style="width:100px" :controls="false" v-model="scope.row.width"
                            controls-position="right" :min="0"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" header-align="center" align="center" width="120">
                    <template slot-scope="scope">
                        <el-button @click.native.prevent="deleteRow(scope.$index, tableData)" type="text" size="small">
                            移除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- hello world -->
            <div slot="footer" class="dialog-footer">
                <div class="flex justify-between">
                    <div></div>
                    <div> <el-button size="large" type="danger" @click="resetConfig()">清空配置</el-button></div>
                    <div>
                        <el-button size="large" type="primary" @click="submitSearchItem()">{{ i18nt('designer.hint.confirm')
                        }}</el-button>
                        <el-button size="large" type="" @click="showEditCtrlDialogFlag = false">{{
                            i18nt('designer.hint.cancel')
                        }}</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog title="设置查询参数映射" :visible.sync="showParamsMapBox" v-if="showParamsMapBox" :show-close="true"
            class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
            :destroy-on-close="true">
            <!-- hello world -->
            <div style="margin-bottom:10px" class="flex">
                <div><el-button :disabled="!searchItemConfig.actionName" @click="reloadingParamsEvent()"
                        :load="isBtnReloading" size="mini" type="primary">重新加载</el-button>
                    <span v-show="currentCtrlType == 'reportSelectTable'" style="margin-left:10px;">
                        默认搜索框字段值可设置为：$searchKey</span>
                </div>

            </div>
            <el-table border :data="gridSetparamsMap.data" style="width: 100%" max-height="300">
                <el-table-column fixed prop="pfield" label="名称" header-align="center" width="180">
                </el-table-column>
                <el-table-column prop="ptitle" label="标题" header-align="center" width="180">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.ptitle"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="mfield" label="字段/值" header-align="center" align="center" width="120">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.mfield"></el-input>
                    </template>
                </el-table-column>


            </el-table>
            <!-- hello world -->
            <div slot="footer" class="dialog-footer">
                <div class="flex justify-between">
                    <div></div>
                    <div></div>
                    <div>
                        <el-button size="large" type="primary" @click="submitParamsMapBox()">{{
                            i18nt('designer.hint.confirm') }}</el-button>
                        <el-button size="large" type="" @click="showParamsMapBox = false">{{ i18nt('designer.hint.cancel')
                        }}</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
        <commonEventDialog :functionName="functionName" :jsCodeTitle="jsCodeTitle" :eventCodeConfig="currentEditSettingBtnOption" @submitEvent="submitCommonEventDialog" ref="commonEventDialogRef"></commonEventDialog>
    </div>
</template>
<script>
const default_searchItemConfig = {
    CDATASOURCE_ID: -1,// 数源ID CID
    CDATASET_ID: -1,// 数据集ID
    actionName: "",// 执行API
    CIS_PAGE: "N",// 是否分页
    CPAGE_SIZE: 10,// 分页数
    sourceKey: "",// 数据源key
    matchField: "",// 字段转换
    searchParams: "",// 过滤参数
    CDATAS: [],//  表格数据>>配置参数JSON,存储时，需要转换为字符串
    CMAP_PARAMETER: [],// 查询参数>>JSON字符串，查询查询映射列表KEY:VALUE ,存储时，需要转换为字符串
    onAfterChange:"",// JS自定义代码逻辑，标准弹框 onAfterPopupChange 选择数据发生改变后
    onBeforeRender:"",// JS自定义 单元格渲染逻辑代码
}
import commonEventDialog from "@/components/form-designer/setting-panel/commonEventDialog.vue"
import i18n from "@/utils/i18n";
import cloneDeep from "clone-deep"
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: "mappongParamsDialog",
    mixins: [i18n],
    components:{commonEventDialog},
    props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
        currentRow: Object,
    },
    data() {
        return {
            jsCodeTitle:"",
            functionName:"",
            currentEditSettingBtnOption:{},// 当前操作按钮配置
            // 查询参数映射  { label: 'left', value: 'left' },
            gridSetparamsMap: {
                data: []
            },
            title: "",
            isBtnReloading: false,
            showParamsMapBox: false,
            useFormatParamsFn: useFormatParams(this),
            needConfigType: ["reportSelect", "reportSelectTable", "customSelect","standardFrame","reportMulSelect"],
            tableData: [], // 控件配置表格字段信息
            loading_tableData: false,// 表格数据是否加载中...
            dataSourceList: [],// 数据源列表
            dataSetList: [],// 数据集合列表 
            dataModelList: [],// 模型实体数据
            currentCtrlType: "text",// 当前控件类型
            showEditCtrlDialogFlag: false,// 控件配置 弹框 是否显示
            searchItemConfig: Object.assign({}, default_searchItemConfig),// 搜索输入框配置 中的 控件类型配置
        }
    },
    watch: {
        showEditCtrlDialogFlag(n, o) {
            if (!!n) {
                this.showEditCtrlEvent()
            } else {
                this.resetData()
            }
        }
    },
    methods: {
        // 是否显示 onAfterChange 逻辑编码框
        vShowOnAfterChange(){
           // debugger
            // let flag = (!this.searchItemConfig.actionName && (this.currentCtrlType == 'standardFrame'||this.currentCtrlType == 'reportSelect'))

            let flag = false
                if(this.currentCtrlType == 'standardFrame' ||this.currentCtrlType == 'reportSelect'){
                    flag = true
                }
            return flag
        },
         // 提交
        submitCommonEventDialog(params){
            //debugger
            let code = params.code
            /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            this.$set(this.searchItemConfig, this.functionName, code)
        },
        // 自定义选择发生改变后逻辑代码功能
        showOnAfterChangeCodeWindow(){
            this.functionName ="onAfterChange"
            // 自定义代码 弹框 方法标题+参数名称
            this.jsCodeTitle = this.currentRow.field+"."+this.functionName+"(paramsData)"
            this.currentEditSettingBtnOption = this.searchItemConfig; // 当前菜单按钮属性
            let commonEventDialogRef = this.$refs["commonEventDialogRef"]
            if(commonEventDialogRef){
                commonEventDialogRef.showDialog = true
            }
        },
          // 自定义选择发生改变后逻辑代码功能
          showOnBeforeRenderCodeWindow(){
            // debugger
            this.functionName ="onBeforeRender"
            // 自定义代码 弹框 方法标题+参数名称
            this.jsCodeTitle = this.currentRow.field+"."+this.functionName+"(paramsData)"
            this.currentEditSettingBtnOption = this.searchItemConfig; // 当前菜单按钮属性
            let commonEventDialogRef = this.$refs["commonEventDialogRef"]
            if(commonEventDialogRef){
                commonEventDialogRef.showDialog = true
            }
        },
        indexMethod(index) {
            return index + 1;
        },
        // 自定义本地控件时，添加触发事件
        ctrlSearchParams(type = 'add') {
            switch (type) {
                // 新增一行
                case 'add':
                    let newItem = {
                        field: 'newOption',
                        title: '新子项',
                        iisShowList: 0,
                        isSelectTextField: 0,
                        isSelectKeyField: 0,
                        width: 200,
                        fieldOrder: 100,
                    }
                    this.tableData.push(newItem)
                    break;
                default:
                    break;
            }
        },
        // 控件配置表格 >>移除表格数据
        deleteRow(index, rows) {
            rows.splice(index, 1);
        },
        // 提交 控件类型 配置信息
        submitSearchItem() {
            // debugger
            this.searchItemConfig.CDATAS = this.tableData // 控件配置表格字段信息
            this.searchItemConfig.CMAP_PARAMETER = this.gridSetparamsMap.data//this.map_tableData // 参数映射表格数据
            // if (this.searchItemConfig.CDATASOURCE_ID != -1) {
            //     this.editMenuForm.searchItemConfig = cloneDeep(this.searchItemConfig)
            // } else {
            //     this.editMenuForm.searchItemConfig = {}
            // }

            this.showEditCtrlDialogFlag = false
            this.$emit('submitSuccess', this.searchItemConfig)
        },
        resetData() {
            this.tableData = [] // 控件配置表格字段信息
            //this.map_tableData=[]// 参数映射表格数据
            this.gridSetparamsMap.data = [] // 参数映射表格数据
            this.dataSetList = []// 数据集合列表 
            this.dataModelList = []// 模型实体数据
            this.searchItemConfig = Object.assign({}, default_searchItemConfig)// 搜索输入框配置 中的 控件类型配置
        },
        // 清空控件类型配置信息
        resetConfig() {
            this.$confirm('此操作将删除控件配置数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.resetData()
            }).catch(() => {

            });
        },
        // 控件配置 弹框
        async showEditCtrlEvent() {
            // debugger

            this.currentCtrlType = this.currentRow.controlType
            switch (this.currentCtrlType) {
                case "customRenderCtrl":
                    this.title = "自定义渲染控件"
                    break;
                case "reportMulSelect":
                    this.title = "下拉框(多选)配置"
                    break;
                case "reportSelect":
                    this.title = "下拉框配置"
                    break;
                case "reportSelectTable":
                    this.title = "下拉框（表）配置"
                    break;
                case "customSelect":
                    this.title = "下拉框（自定义）配置"
                    break;
                case "standardFrame":
                    this.title = "标准弹框配置"
                    break;
                case "popupTextarea":
                    this.title = "弹框资料(仅列表页)"
                    break;
                case "statusColumn":
                    this.title = "状态栏"
                    break;    
                default:
                    this.title = "text"
                    break;
            }
            // await this.getDataSourceList()
            this.$nextTick(() => {
                this.setEditDefaultVal()
            })
        },
        // 编辑时，设置默认值
        async setEditDefaultVal() {
            if (!!this.currentRow.controlConfig) {
                this.searchItemConfig = Object.assign({}, default_searchItemConfig, this.currentRow.controlConfig)// 搜索输入框配置 中的 控件类型配置
            }

            // if (this.searchItemConfig.CDATASOURCE_ID != -1) {
            //     // 查询数据集
            //     this.dataSetList = await this.getDataSetList(this.searchItemConfig.CDATASOURCE_ID)
            // }
            // 重置 赋值
            this.tableData = []
            //this.map_tableData=[]
            this.gridSetparamsMap.data = [] // 参数映射表格数据
            if (this.searchItemConfig.CDATAS && this.searchItemConfig.CDATAS.length > 0) {
                this.tableData = this.searchItemConfig.CDATAS // 控件配置表格字段信息
            }
            if (this.searchItemConfig.CMAP_PARAMETER && this.searchItemConfig.CMAP_PARAMETER.length > 0) {
                this.gridSetparamsMap.data = this.searchItemConfig.CMAP_PARAMETER// 参数映射表格数据
            }

        },
        // 重新加载参数映射列表
        async reloadingParamsEvent() {
            this.isBtnReloading = true
            let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.searchItemConfig.actionName);

            let datasetId = CDATASET_ID //this.searchItemConfig.CDATASET_ID
            // 首次添加
            let _resDataList = await this.loadSearchParamsMap(datasetId)
            let paramsMapDataList = this.formatParamsMapData(_resDataList)
            this.gridSetparamsMap.data = paramsMapDataList
            this.isBtnReloading = false
        },
        // 打开参数映射弹框//设置查询参数映射
        async setSearchParamsMap() {
            //debugger
            let paramsMapDataList = []
            let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.searchItemConfig.actionName);

            let datasetId = CDATASET_ID
            //let datasetId = this.searchItemConfig.CDATASET_ID
            let dataSearchMapData = cloneDeep(this.searchItemConfig.CMAP_PARAMETER)  // JSON字符串，查询查询映射列表KEY:VALUE ,存储时，需要转换为字符串
            if (!!dataSearchMapData && dataSearchMapData.length > 0) {
                // 编辑时
                paramsMapDataList = dataSearchMapData
            } else {
                // 首次添加
                let _resDataList = await this.loadSearchParamsMap(datasetId)
                paramsMapDataList = this.formatParamsMapData(_resDataList)
            }
            // 赋值表格
            this.gridSetparamsMap.data = paramsMapDataList
            // 打开参数映射弹框
            this.showParamsMapBox = true
        },
        // 格式化查询参数映射
        formatParamsMapData(dataList) {
            let newDataList = []
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    let newItem = {
                        pfield: oldItem.key,
                        ptitle: oldItem.desc,
                    }

                    newDataList.push(newItem)
                })
            }

            return newDataList
        },
        // 加载 参数 列表(查询参数映射列表)
        async loadSearchParamsMap(datasetId) {
            if (datasetId == -1) {
                return
            }
            let params = {
                Id: datasetId
            }
            let ParameterMap = ""
            let _url = "api/MD/DataSet/GetInputParameterByDataSetId"
            await request['post'](_url, params).then(res => {
                // debugger
                if (res && res.Datas && res.Datas.length > 0) {
                    ParameterMap = res.Datas
                }
            })
            return ParameterMap
        },
        // 提交查询参数映射
        submitParamsMapBox() {
            //debugger
            this.searchItemConfig.CMAP_PARAMETER = this.gridSetparamsMap.data//this.map_tableData // 参数映射表格数据
            this.showParamsMapBox = false
        },
        // 获取数据源数据
        async getDataSourceList() {
            this.dataSourceList = []
            let params = {
                condition: "",
                typeId: 0
            }
            //let _url = "api/MD/DataSource/GetAll"
           // let _url = "api/MD/DataSource/GetByType"
           let _url = "/api/MD/DataSource/GetList"
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                    // debugger CHOST
                    this.dataSourceList = res.Datas
                }
            })
        },
        // 数据源API 改变
        async change_sourceApi(formItem, actionName) {
            this.tableData = []
            let dataSetId = this.useFormatParamsFn.getVFormDataSetID(this.searchItemConfig.actionName);
            // debugger
            if (dataSetId != -1 && !!this.searchItemConfig.actionName) {
                this.loading_tableData = true
                let _modelList = await this.getDataModelList(dataSetId)
                this.dataModelList = this.formatDataModel(_modelList)
                this.tableData = cloneDeep(this.dataModelList)
                this.loading_tableData = false
            } else {
                this.dataModelList = []
            }
        },
        // 数据源数据选择改变，触发数据集合改变
        async change_dataSource(formItem, sourceId) {
            if (sourceId != -1) {
                this.dataSetList = await this.getDataSetList(sourceId)
            } else {
                this.dataSetList = []
            }
            this.resetDataSetTableData(formItem, sourceId)
        },
        // 清空数据集表格数据
        resetDataSetTableData(formItem, sourceId) {
            // 重置
            this.tableData = []
            formItem.CDATASET_ID = -1
        },
        // 获取数据集合
        async getDataSetList(sourceId) {
            let dataSetList = []
            let params = {
                //typeId:0,
                condition: `{CID:${sourceId}}`
            }
            let _url = "api/MD/DataSet/GetAll" //20230724
            //let _url = "api/MD/DataSet/GetAllDataSet"
            
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                    // debugger
                    dataSetList = res.Datas
                }
            })
            return dataSetList
        },
        // 数据集数据选择改变，触发数据模型改变
        async change_dataSet(formItem, dataSetId) {
            //  debugger
            if (dataSetId != -1) {
                this.loading_tableData = true
                let _modelList = await this.getDataModelList(dataSetId)
                this.dataModelList = this.formatDataModel(_modelList)
                this.tableData = cloneDeep(this.dataModelList)
                this.loading_tableData = false
            } else {
                this.dataModelList = []
            }

        },
        // 获取数据源数据
        async getDataModelList(datasetId) {
            let dataModelList = []
            let params = {
                condition: "",
                datasetId: datasetId
            }
           // let _url = "api/MD/DataSetModel/GetAll"
          // let _url = "api/MD/DataSetModel/GetAllDataSet"
           let _url = "api/MD/DataSetModel/GetList"
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                    dataModelList = res.Datas
                }
            })
            return dataModelList
        },
        // 格式化模型数据
        formatDataModel(dataList) {
            let newDataList = []
            if (dataList && dataList.length > 0) {
                let innerField = [
                    "CID",
                    "CDATETIME_CREATED",
                    "CUSER_CREATED",
                    "CDATETIME_MODIFIED",
                    "CUSER_MODIFIED",
                    "CINSTANCE_ID",
                    "CROWREMARK",
                    "CENTERPRISE_CODE",
                    "CORG_CODE",
                ]
                dataList.forEach(oldItem => {
                    let newItem = {
                        field: oldItem.CCOLUMN_NAME,
                        title: oldItem.CCOLUMN_DESC,
                        CID: oldItem.CID,
                        titleHelp: "",
                        iisShowList: 0,
                        isSelectTextField: 0,
                        isSelectKeyField: 0,
                        width: 200,
                        titleAlign: "center",
                        fieldOrder: 100,
                        align: "center",
                        controlType: "text",
                        groupTitle: "",
                        iisSummary: 0,
                        fieldRules: ""
                    }
                    // 内置字段不显示
                    if (innerField.includes(newItem.field)) {
                        newItem.iisShowList = 0
                    }
                    newDataList.push(newItem)
                })
            }

            return newDataList
        },
    }
}
</script>