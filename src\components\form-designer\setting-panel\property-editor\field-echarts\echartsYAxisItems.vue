<template>
    <div class="option-items-pane">

        <draggable tag="ul" :list="optionModel.yAxisExList"
            v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
            <li v-for="(option, idx) in optionModel.yAxisExList" :key="idx">
                <el-checkbox v-model="option.axisLineShow">
                    <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.remarkName" size="mini"
                        style="width: 150px"></el-input>
                    <i class="iconfont icon-drag drag-option"></i>
                    <el-button circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
                        icon="el-icon-minus" class="col-delete-button"></el-button>
                </el-checkbox>
            </li>
        </draggable>



        <div>
            <el-button type="text" @click="addQueryOption">+添加Y轴</el-button>
        </div>

        <el-dialog v-dialog-drag title="编辑Y轴" :visible.sync="showEditOptionDialogFlag" v-if="showEditOptionDialogFlag"
            :show-close="true" class="small-padding-dialog" append-to-body :close-on-click-modal="false"
            :close-on-press-escape="false" :destroy-on-close="true">
            <el-form :model="editItemForm" :rules="editItemFormRules" ref="editItemForm" label-width="120px">
                <el-form-item label="备注" prop="remarkName">
                    <el-input style="width:409px" v-model="editItemForm.remarkName"></el-input>
                </el-form-item>
                <el-form-item label="Y轴名称">
                    <el-input type="text" v-model="editItemForm.axisName"></el-input>
                </el-form-item>
                <el-form-item label="显示Y轴轴线">
                    <el-switch v-model="editItemForm.axisLineShow"></el-switch>
                </el-form-item>
                <el-form-item label="Y轴轴线颜色">
                    <el-color-picker v-model="editItemForm.axisLineStyleColor" show-alpha :predefine="predefineColors">
                    </el-color-picker>
                </el-form-item>
                <el-form-item label="隐藏Y轴刻度标签">
                    <el-switch v-model="editItemForm.hideAxisLabelShow"></el-switch>
                </el-form-item>
                <el-form-item label="隐藏Y轴分隔线">
                    <el-switch v-model="editItemForm.hideSplitLineShow"></el-switch>
                </el-form-item>
                <el-form-item label="显示Y轴刻度">
                    <el-switch v-model="editItemForm.axisTickShow"></el-switch>
                </el-form-item>
                <el-form-item label="Y轴刻度朝内">
                    <el-switch v-model="editItemForm.axisTickInside"></el-switch>
                </el-form-item>
                <el-form-item label="Y轴类型">
                    <el-select v-model="editItemForm.type">
                        <el-option label="数值轴" value="value"></el-option>
                        <el-option label="类目轴" value="category"></el-option>
                        <el-option label="时间轴" value="time"></el-option>
                        <el-option label="对数轴" value="log"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Y轴位置">
                    <el-select v-model="editItemForm.position">
                        <el-option label="右边" value="right"></el-option>
                        <el-option label="左边" value="left"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Y轴数据格式">
                    <el-input placeholder="{value} °C" type="text" v-model="editItemForm.labelformatValue"></el-input>
                </el-form-item>
                <el-form-item label="偏移量">
                    <el-input-number v-model="editItemForm.offset" :min="0" label="偏移量"></el-input-number>
                </el-form-item>
                <!-- { remarkName: '新Y轴' + newValue, axisName: "", type: 'value', labelformatValue: '', color: '', position: "right", offset: 0, axisLineShow: true, yAxisIndex: newValue } -->
                <!-- <el-form-item v-show="!!editItemForm.isTargetLine" label="目标线颜色">
                    <el-color-picker :predefine="predefineColors" v-model="editItemForm.targetLineColor">
                    </el-color-picker>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标线类型">
                    <el-select v-model="editItemForm.targetLineType">
                        <el-option label="实线" value="solid"></el-option>
                        <el-option label="横虚线" value="dashed"></el-option>
                        <el-option label="点虚线" value="dotted"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标值">
                    <el-input-number v-model="editItemForm.targetLineValue" :min="0" label="目标值"></el-input-number>
                </el-form-item>
                <el-form-item v-show="!!editItemForm.isTargetLine" label="目标线宽">
                    <el-input-number v-model="editItemForm.targetLineWidth" :min="0" label="目标线宽"></el-input-number>
                </el-form-item>
                <el-form-item label="名称" prop="seriesName">
                    <el-input style="width:409px" v-model="editItemForm.seriesName"></el-input>
                </el-form-item> -->

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm')
                }}</el-button>
                <el-button size="large" type="" @click="showEditOptionDialogFlag = false">{{ i18nt('designer.hint.cancel')
                }}</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
const default_editItemForm = {
    remarkName: '新Y轴',
    axisName: "",
    type: 'value',
    labelformatValue: '',
    axisLineStyleColor: '',
    position: "right",
    offset: 0,
    axisLineShow: true,
    yAxisIndex: 1,
    hideAxisLabelShow: false,
    hideSplitLineShow: true,
    axisTickShow: false,
    axisTickInside: false

}

// import cloneDeep from "clone-deep" 
import { defaultsDeep } from "lodash-es";
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n";

export default {
    name: "echartsYAxisItems", // 其它Y轴列表
    mixins: [i18n],
    components: {
        Draggable,
    },
    props: {
        designer: Object,
        selectedWidget: Object,
        globalDsv: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            predefineColors: [
                '#E3B76D',
                '#6794A7',
                '#014D64',
                '#01A2D9',
                '#C6D3DF',
                '#7AD2F6',
                '#7C260B',
                '#00887D',
                '#EE8F71',
                '#ADADAD',
                '#76C0C1',
                '#ff4500',
                '#ff8c00',
                '#ffd700',
                '#90ee90',
                '#00ced1',
                '#1e90ff',
                '#c71585',
                '#c7158577'

            ],
            showEditOptionDialogFlag: false,
            currentEditOption: {},// 当前编辑菜单按钮
            editItemForm: Object.assign({}, default_editItemForm),
            editItemFormRules: {
                remarkName: [
                    { required: true, message: '必填', trigger: 'blur' },
                ],
           
            }
        }
    },
    computed: {
        optionModel() {
            return this.selectedWidget.options
        },

    },
    methods: {


        // 提交修改按钮菜单属性
        submitEditMenu() {
           
            this.showEditOptionDialogFlag = false
            this.currentEditOption.remarkName = this.editItemForm.remarkName
            this.currentEditOption.axisName = this.editItemForm.axisName
            this.currentEditOption.type = this.editItemForm.type
            this.currentEditOption.labelformatValue = this.editItemForm.labelformatValue
            this.currentEditOption.axisLineStyleColor = this.editItemForm.axisLineStyleColor
            this.currentEditOption.position = this.editItemForm.position
            this.currentEditOption.yAxisIndex = this.editItemForm.yAxisIndex
            this.currentEditOption.hideAxisLabelShow = this.editItemForm.hideAxisLabelShow
            this.currentEditOption.hideSplitLineShow = this.editItemForm.hideSplitLineShow
            this.currentEditOption.axisTickShow = this.editItemForm.axisTickShow
            this.currentEditOption.axisTickInside = this.editItemForm.axisTickInside
        },
        // 弹框编辑菜单属性--- 初始化弹框属性参数
        showEditDialogEvent(option) {
 
            this.currentEditOption = option // 当前菜单属性
            this.showEditOptionDialogFlag = true
            this.editItemForm.remarkName = option.remarkName
            this.editItemForm.axisName = option.axisName
            this.editItemForm.type = option.type
            this.editItemForm.labelformatValue = option.labelformatValue
            this.editItemForm.axisLineStyleColor = option.axisLineStyleColor
            this.editItemForm.position =option.position
            this.editItemForm.offset =option.offset
            this.editItemForm.axisLineShow = option.axisLineShow
            this.editItemForm.yAxisIndex =option.yAxisIndex
            this.editItemForm.hideAxisLabelShow =option.hideAxisLabelShow
            this.editItemForm.hideSplitLineShow =option.hideSplitLineShow
            this.editItemForm.axisTickShow = option.axisTickShow
            this.editItemForm.axisTickInside =option.axisTickInside
        },
        deleteOption(option, index) {
            this.selectedWidget.options.yAxisExList.splice(index, 1)
        },
        // 添加
        addQueryOption() {
            if (!this.selectedWidget.options.hasOwnProperty("yAxisExList")) {
                this.$set(this.selectedWidget.options, "yAxisExList", [])
            }
            let newValue = this.selectedWidget.options.yAxisExList.length + 1
            this.selectedWidget.options.yAxisExList.push(
                { remarkName: '新Y轴' + newValue, axisName: "", type: 'value', labelformatValue: '', axisLineStyleColor: '', position: "right", offset: 0, axisLineShow: true, yAxisIndex: newValue, hideAxisLabelShow: false, hideSplitLineShow: true, axisTickShow: false, axisTickInside: false }
            )
        },




    }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
}

li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
}

.drag-option {
    cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
}

.dialog-footer .el-button {
    width: 100px;

}
</style>
