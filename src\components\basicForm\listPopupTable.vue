<template>
    <div>
        <div class="flex justify-between">
            <el-input v-model="currentItem" placeholder="" suffix-icon="el-icon-plus" @focus="focusEvent" v-bind="$attrs"
                v-on="$listeners">
            </el-input>
        </div>
        <!-- :row-config="{isCurrent:true}" -->
        <nvxeModal ref="popTableRenderRef" @confirmEvent="submitEvent()" :showFooter="showFooter"
            :title="fieldTitle + ' ' + title" v-model="showPopTableRender">
            <vxe-grid @cell-dblclick="cellDblclickEvent" :size="gridDefaultOptions.size" :border="gridDefaultOptions.border"
                :loading="gridDefaultOptions.loading" :stripe="gridDefaultOptions.stripe"
                :resizable="gridDefaultOptions.resizable" :show-overflow="gridDefaultOptions.showOverflow"
                :show-footer-overflow="gridDefaultOptions.showFooterOverflow"
                :highlight-current-row="gridDefaultOptions.highlightCurrentRow"
                :show-header-overflow="gridDefaultOptions.showHeaderOverflow" :data="gridDefaultOptions.data"
                :checkbox-config="{ highlight: true }" :highlight-hover-row="gridDefaultOptions.highlightHoverRow" height="500"
                ref="listPopupTableRef" :columns="gridDefaultOptions.columns" @page-change="pageChangeEvent">
                <!-- :pager-config="pagerConfig" -->
                <template #top>
                     <!-- 合并关键字搜索 -->
                    <div v-show="tableColumnsList && tableColumnsList.length>0" style="margin-bottom:5px" class="flex">
                        <!-- <template v-for="(item, index) in tableColumnsList">
                            <el-input :key="index" v-if="!!item.iisQuery" clearable @keyup.native="keyupEvent" size="mini"
                                v-model="searchModelObj[`${item.field}`]"
                                :placeholder="!!item.title ? item.title : item.field"></el-input>
                        </template> -->
                        <el-input  clearable @keyup.native="keyupEvent" size="mini"
                                v-model="searchkey"
                                placeholder="关键字 全表格搜索"></el-input>
                        <el-button style="margin-left:10px" size="mini" :loading="loadingBtn" @click="searchEvent()"
                            type="primary">查询</el-button>
                    </div>
                </template>
            </vxe-grid>
        </nvxeModal>
    </div>
</template>
<script>
import config from '@/config'
import request from '@/libs/request'
import cloneDeep from 'clone-deep'
import Fuse from "fuse.js";
// import Sortable from '@/components/override/vxe-table/templates/vxe-table/common/Sortable.min.js'
export default {
    name: 'listPopupTable',// 弹框表格数据
    props: {
        // 当前字段:value
        currentValue: {
            type: [String, Number],
            default: ""
        },
        // 当前字段 KEY
        field: {
            type: String,
            default: ""
        },
        // 当前字-title
        fieldTitle: {
            type: String,
            default: ""
        },
        // 当前字段值
        currentValue: {
            type: String,
            default: ""
        },
        // 当前字段配置信息
        dataSearchOptions: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前表单对象
        formData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段所在行信息
        // row: {
        //     type: Object,
        //     default() {
        //         return {}
        //     }
        // },
        // 当前字段:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段 - 父表信息,也就是调用这个弹框数据的表格
        // $tableInfo: {
        //     type: Object,
        //     default() {
        //         return {}
        //     }
        // },
        // 是否可用
        // disabled:{
        //     type:Boolean,
        //     default:false,
        // }  
    },
    data() {
        return {
            fuse: null,
            currentItem: '',// 当前选择项
            loading_nvxeModal: true,
            loadingBtn: false,
            searchModelObj: {},
            searchWholeModelObj:{},// 全部关键字
            searchkey: "",// 查询关键字
            showFooter: true,// 是否显示提交按钮
            // 表格默认配置属性
            gridDefaultOptions: {
                size: "mini",
                border: true,
                stripe: true,
                resizable: true,
                showOverflow: true,
                showFooterOverflow: true,
                showHeaderOverflow: true,
                highlightCurrentRow: true,
                highlightHoverRow: true,
                // filterConfig: {},
                loading: false,
                // params: {},
                // height: 0,
                // align: "left",
                columns: [],
                data: [],
            },
            config: config,
            pagerConfig: {
                enabled: true,// 是否启用分页
                currentPage: 1,
                pageSize: 10,
                total: 0,
            },
            title: "弹框数据",
            loading: false,
            showPopTableRender: false,// 是否显示弹框

        }
    },
    computed: {
        tableColumnsList() {
            let _tableColumns = []
            try {
                let dataList = JSON.parse(this.paramsItem.controlExtension)
                _tableColumns = dataList.CDATAS.filter(item => {
                    this.$set(this.searchWholeModelObj, item.field, null)
                    if (!!item.iisQuery) {
                        // this.searchModelObj[item.field]="" // 无效
                        this.$set(this.searchModelObj, item.field, null)
                        
                        return item
                    }
                    
                    
                })
            } catch (error) {
                _tableColumns = []
            }
            return _tableColumns
        },
        controlExtension() {
            let _controlExtension = {}
            try {
                _controlExtension = JSON.parse(this.paramsItem.controlExtension)
            } catch (error) {
                _controlExtension = {}
            }
            return _controlExtension
        },
        // 其它配置参数，如 联动参数
        otherParams() {
            let _otherParams = null
            try {
                if (this.paramsItem?.fieldExtension) {
                    let fieldExtension = JSON.parse(this.paramsItem.fieldExtension)
                    _otherParams = fieldExtension.otherParams
                }

            } catch (error) {
                _otherParams = null
            }
            return _otherParams
        },
    },
    watch: {
        currentValue(n, o) {
            //debugger
            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
            }
        },
        showPopTableRender(n, o) {
            if (!n) {
                this.gridDefaultOptions.columns = []
                this.gridDefaultOptions.data = []
            }
        }
    },

    mounted() {
        this.$nextTick(() => {
            // debugger
            this.currentItem = this.currentValue
        })
        let _self = this

        if (this.controlExtension && this.controlExtension.hasOwnProperty("CIS_MULTIPLE") && this.controlExtension.CIS_MULTIPLE == 'Y') {
            this.title = this.paramsItem.title
            this.showFooter = true
        } else {
            this.title = this.paramsItem.title + " (双击选中)"
            this.showFooter = false
        }
    },
    methods: {
       
        fuseSearch() {
            this.gridDefaultOptions.loading = true
            let dataList = cloneDeep(this.gridDefaultOptions.copyData)
            let $table = this.$refs["listPopupTableRef"]
          
            if(!this.searchkey){
                this.gridDefaultOptions.data =[]
                // 关键字为空 直接 
                $table.reloadData(dataList)
                this.gridDefaultOptions.loading = false
                return
            }
            let keysList = Object.keys(this.searchWholeModelObj).toString()
            const options = {
                shouldSort: true, // 是否按分数对结果列表排序
                includeScore: true, //  是否应将分数包含在结果集中。0分表示完全匹配，1分表示完全不匹配。
                threshold: 0.1, // 匹配算法阈值。阈值为0.0需要完全匹配（字母和位置），阈值为1.0将匹配任何内容。
                /**
                 * 确定匹配与模糊位置（由位置指定）的距离。一个精确的字母匹配，即距离模糊位置很远的字符将被视为完全不匹配。
                 *  距离为0要求匹配位于指定的准确位置，距离为1000则要求完全匹配位于使用阈值0.8找到的位置的800个字符以内。
                 */
                location: 0, // 确定文本中预期找到的模式的大致位置。
                distance: 100,
                maxPatternLength: 32, // 模式的最大长度
                minMatchCharLength: 1, // 模式的最小字符长度 
                keys: Object.keys(this.searchWholeModelObj).length>1?Object.keys(this.searchWholeModelObj):[keysList],//['author.tags.value'] ['ID','CODE','NAME','TYPE_ID','TYPE_NAME']
            }
           // let dataList = cloneDeep(this.gridDefaultOptions.copyData)
            const fuse = new Fuse(dataList, options)
            const result = fuse.search(this.searchkey)
           
            if(result && result.length>0){
                this.gridDefaultOptions.data =[]
                let tempData =[]
                result.forEach(row=>{
                    tempData.push(row.item)
                })
                setTimeout(()=>{
                    $table.reloadData(tempData)
                    this.gridDefaultOptions.loading = false
                },300)
                
            }else{
                $table.reloadData([])
                this.gridDefaultOptions.loading = false
            }
        },
        // 采用本地过滤搜索查询，非服务器查询
        searchEvent() {
             this.fuseSearch() 
        },
        // 表格查询框 回车键
        keyupEvent($event) {
            if ($event.keyCode === 13) {
                this.searchEvent()
            }
        },
        // 聚焦输入框
        focusEvent(params) {
            this.showPopTableRender = true

            this.$nextTick(() => {

                this.loadData()

            })
        },
        // 确认
        submitEvent() {
            if (this.controlExtension && this.controlExtension.hasOwnProperty("CIS_MULTIPLE") && this.controlExtension.CIS_MULTIPLE == 'Y') {
                this.CIS_MULTIPLE_FN()
            } else {

            }
        },
        // 多选时提交数据处理方式
        CIS_MULTIPLE_FN() {
            let _self = this
            let _listPopupTableRef = this.$refs["listPopupTableRef"]
            if (_listPopupTableRef) {
                let checkboxRecords = _listPopupTableRef.getCheckboxRecords()
                if (checkboxRecords && checkboxRecords.length == 0) {
                    this.$message.error("请先选择数据！！")
                    return
                } else {
                    let $grid = this.$tableInfo.$grid
                    //往表格插入临时数据，从指定位置插入一行或多行；第二个参数：row 指定位置、null从第一行插入、-1 从最后插入
                    let newDataList = this.dataModelTransfer(checkboxRecords)
                    //debugger
                    $grid.insertAt(newDataList, -1)
                    this.$nextTick(() => {
                        _self.showPopTableRender = false
                    })
                }
            }
        },
        //tableCNAME,tableCCONTENT|popupCNAME,popupCCONTENT
        //CDEVICE_TYPE_ID,CDEVICE_TYPE_NAME|TYPE_ID,TYPE_NAME
        dataModelTransfer(popupDataList = []) {
            let newDataList = []
            // 如果配置了，按规则转换，否则全部返回
            if (this.paramsItem.matchField) {
                let matchFieldList = this.paramsItem.matchField.split('|')
                let tableFieldsStr = matchFieldList[0] // 表格数据
                let popupFieldsStr = matchFieldList[1] // 弹框数据
                if (tableFieldsStr.includes(',')) {
                    // 多个字段 赋值 eg:CTABLE_NAME,CTID|CDISPLAY_NAME,CID【表单字段|弹框返回字段】
                    let tableFieldsList = tableFieldsStr.split(',')
                    let popupFieldsList = popupFieldsStr.split(',')
                    popupDataList.forEach(popupDataRow => {
                        let newDataRowItem = {}
                        tableFieldsList.forEach((tableFieldItem, index) => {
                            newDataRowItem[tableFieldItem] = popupDataRow[popupFieldsList[index]]
                        })
                        newDataList.push(newDataRowItem)
                    })
                } else {
                    // 单个字段
                    popupDataList.forEach(popupDataRow => {
                        let newDataRowItem = {}
                        newDataRowItem[tableFieldsStr] = popupDataRow[popupFieldsStr]
                        newDataList.push(newDataRowItem)
                    })
                }
            } else {
                // 没有配置换回，全部返回
                newDataList = popupDataList
            }

            return newDataList
        },

        //  双击 选中单元格 触发事件  afa 
        //字段转换必填：CTABLE_NAME,CTID|CDISPLAY_NAME,CID【说明：弹框字段赋值到表单字段，分割符号'|'  多个字段>>表格字段A,表格字段B|弹框字段A,弹框字段B】
        cellDblclickEvent(params) {
            // debugger
            // 多选时，双击无效
            if (this.controlExtension && this.controlExtension.hasOwnProperty("CIS_MULTIPLE") && this.controlExtension.CIS_MULTIPLE == 'Y') {
                return
            }
            let row = params.row

            let newDataRow = this.dataModelTransfer([row])[0]
            // debugger
            for (const [key, value] of Object.entries(newDataRow)) {
                this.formData[key] = value
            }
            setTimeout(() => {
                this.showPopTableRender = false
            }, 100)

        },

        // 翻页时间触发
        pageChangeEvent(pageInfo) {
            // this.pagerConfig.currentPage = pageInfo.currentPage
            // this.$nextTick(() => {
            //     this.loadData()
            // })
        },
        // 处理列头显示问题
        handleColumns(columns = []) {
            // debugger
            let newColumns = cloneDeep(columns)
            // 设置 操作列 模板
            let checkBoxColumn = [{ fixed: 'left', type: 'checkbox', headerAlign: 'center', align: 'center', width: 60 }]
            // let  dragColumn= [{  slots: { default: 'dragColumn' }, visible: true, title: '#', headerAlign: 'center', align: 'center', width: 60 }]
            if (newColumns && newColumns.length > 0) {
                newColumns = newColumns.filter(item => {
                    if (item.iisShowList) {
                        return item
                    }
                })
            }
            // 使用自定义的model，非服务器查询model
            if (this.controlExtension && this.controlExtension.hasOwnProperty("CDATAS") && this.controlExtension.CDATAS.length > 0) {
                newColumns = cloneDeep(this.controlExtension.CDATAS)
                if (newColumns && newColumns.length > 0) {
                    newColumns = newColumns.reduce((initList, nextItem) => {
                        //debugger
                        if (!nextItem.title) {
                            nextItem.title = !!nextItem.title ? nextItem.title : nextItem.field
                        }
                        if (!!nextItem.iisShowList) {
                            initList.push(nextItem)
                        }

                        return initList
                    }, [])
                }
            }
            // debugger 
            if (this.controlExtension && this.controlExtension.hasOwnProperty("CIS_MULTIPLE") && this.controlExtension.CIS_MULTIPLE == 'Y') {
                // 默认下标  插入 第一个位置
                newColumns.unshift(...checkBoxColumn)
            }
            // 插入拖拽列
            // newColumns.unshift(...dragColumn)
            return newColumns
        },

        getFilterParams() {
            let newparams = {}
            try {
                if (this.otherParams.includes('#')) {
                    // 多个参数："CWC_ID:@CLINE_ID#CWC_ID:@CLINE_ID"
                    // ...待处理
                } else {
                    //单个参数：CWC_ID:@CLINE_ID
                    let paramsList = this.otherParams.split(':')
                    newparams[paramsList[0]] = this.formData[paramsList[1].replace('@', '')]
                }
            } catch (error) {
                newparams = {}
            }

            return newparams
        },
        // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
        loadData() {


            this.loading_nvxeModal = true
            let _self = this
            this.gridDefaultOptions.loading = true
            let dataSetId = ""
            try {
                dataSetId = this.controlExtension.CDATASET_ID
            } catch (error) {

            }

            let _url = `/api/MD/DataSet/GetListByDataSetId`// this.config.moduleSub + "PopSource"
            let params = {
                "Id": dataSetId,
                // filter:this.searchModelObj, // 字段过滤参数
                "Parameter": this.getFilterParams()
            }
            if (!dataSetId) {
                return
            }
            request['post'](_url, params).then(res => {
                // debugger
                if (res.Success) {
                    // CODE: "1"
                    // ID: 100000000004579
                    // NAME: "BSB_1"
                    // TYPE_ID: 110000000000090
                    // TYPE_NAME: "锂电生箔机"
                    // let resData = res.Datas
                    // debugger
                    this.gridDefaultOptions.loading = false
                    this.gridDefaultOptions.columns = this.handleColumns()
                    this.gridDefaultOptions.data = res.Datas
                    this.gridDefaultOptions.copyData =cloneDeep(res.Datas)

                    this.pagerConfig.total = res.TotalRows

                }
                this.loading_nvxeModal = false
            })
            setTimeout(() => {
                _self.loading_nvxeModal = false
            }, 10000)
        },
    }
}
</script>
<style lang="scss" scoped></style>