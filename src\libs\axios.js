import axios from 'axios'
import actions from '@/micors/actions'

import { getCookie, getLocalStorageStore } from '@/util/setStore'
var componyCode=getLocalStorageStore('mian_code')
var originCode=getLocalStorageStore('multiOrganization')
var userInfo=getLocalStorageStore('userInfo')||getCookie('userInfo')
class HttpRequest {

  constructor(baseUrl = baseURL) {
     if(componyCode&&originCode){
     // baseUrl=baseUrl+componyCode?.CAPI_PRE+originCode?.CAPI_PRE
     }
    this.baseUrl = baseUrl
    this.queue = {}
    this.timer = null // 声明计时器
  }
  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      headers: {
        'Accept':"currentusername$$"+userInfo?.CUSER_NAME+"_-enterprisecode$$"+userInfo?.CENTERPRISE_CODE
      },
      //timeout: 1000, // default is `0` (no timeout)
    }
    return config
  }
  destroy(url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {
       let token = getLocalStorageStore("token")||getCookie('token');
       if(token){
         config.headers['Authorization']='Bearer '+token
       // config.headers['token']=token
       }
        var language=getLocalStorageStore('language')
        if(language){
          config.headers['language']=language
       }
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show() // 不建议开启，因为界面不友好
      }
      this.queue[url] = true
      return config
    }, error => {
      return Promise.reject(error)

    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      const { data, status } = res
      if (res.data.code != 400) {
        return { data, status }
      }
    }, error => {
      this.destroy(url)
      if (error && error.statusCode) {
        switch (error.statusCode) {
          case 400:
            error.errMsg = '错误请求'
            break
          case 401:
            error.errMsg = '未授权，请重新登录'
            break
          case 403:
            error.errMsg = '拒绝访问'
            break
          case 404:
            error.errMsg = '请求错误,未找到该资源'
            break
          case 405:
            error.errMsg = '请求方法未允许'
            break
          case 408:
            error.errMsg = '请求超时'
            break
          case 500:
            error.errMsg = '服务器端出错'
            break
          case 501:
            error.errMsg = '网络未实现'
            break
          case 502:
            error.errMsg = '网络错误'
            break
          case 503:
            error.errMsg = '服务不可用'
            break
          case 504:
            error.errMsg = '网络超时'
            break
          case 505:
            error.errMsg = 'http版本不支持该请求'
            break
          default:
            error.errMsg = `连接错误${error.response.status}`
        }
      } else {
        error.errMsg = '连接到服务器失败'
        error.statusCode = ''
      }
      let errTip = error.errMsg + ' ' + error.statusCode + ' 请稍后重试!'

      clearTimeout(this.timer)
      this.timer = setTimeout(function () {
        // Message.error(errTip)
      }, 2000) // 避免多次同一时间弹出多条错误信息
      //debugger
      //window.$wujie?.bus.$emit("addErrLog", "formsetting:"+errTip); // 提交错误信息到主应用
      actions.setGlobalState({
        errorLogList: "formsetting:" + errTip,
        type: 'errorLogList'
      })
      return Promise.reject(errTip);

    })
  }
  request(options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}
export default HttpRequest
