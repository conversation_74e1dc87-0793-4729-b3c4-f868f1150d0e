<template>
    <div class="formsetting" >
         <vxe-modal
          ref="nvxeModalRef" 
          @show="showEvent"
          @close="closeEvent"
          :width="width"
          :height="height"
          :margin-size="-600"
          :title="title||defaultTitle"
          :fullscreen="fullscreen" 
          :position="position"
          show-footer 
          v-bind="$attrs"
          v-on="$listeners">
            
           
              <template v-if="$slots.default" v-slot:default>
                     <slot></slot>
             </template>
               <!-- 重写footer 并且默认值 -->
               <template  v-slot:footer>
                     <template v-if="$slots.footer">
                         <slot name="footer"></slot>
                    </template>
                      <template v-else>
                        <div v-if="showCustomFooter">
                            <el-button :loading="loadingBtn" @click="preSubmitEvent()" type="success">保存</el-button>
                            <el-button @click="cancelBtn()" type="info">取消</el-button>
                       </div>
                    </template>
             </template>
         </vxe-modal>
    </div>
</template>
<script>
import modalTemplate from "@/components/override/vxe-table/templates/modal-template.vue";
const {actionType} = require('@/enum/enumData')
 import { useDebounceFn,useElementSize } from '@vueuse/core'
export default {
    name:"nvxe-modal",// 自定义弹框
    components:{modalTemplate},
    props:{
         // 重写》窗口的宽度》,设置默认值
        width: {
            type: [Number, String],
            default: "700",
        },
        // 重写》窗口的高度》,设置默认值
        height: {
            type: [Number, String],
            default: "550",
        },
        // 重写,正标题
        title:{
            type:String,
            default:""
        },
        // 副标题
        subTitle:{
            type:String,
            default:""
        },
           // 重写,是否显示底部
        showCustomFooter:{
            type:Boolean,
            default:true
        },
        // 重写,设置默认值,是否全屏
        fullscreen:{
            type:Boolean,
            default:false
        },
        // 重写,弹框首次出现位置
        position:{
            type:Object,
            default(){
              return { 
                       top:!!this.fullscreen?0:50
                    }
                }
        },
         // 是否禁用提交按钮
         disableSubmitBtn:{
            type:Boolean,
            default:false
        },
    },
    data(){
        return {
            maxLoadTime:6,
            elementModalSize:{},
            showEditBox:false,// 是否显示
            refInstance:null,// 表格单实例
            actionType:actionType,
            loadingBtn:false,// 提交按钮提交中...
            defaultTitle:"",// 弹框默认标题
            modalOptions:{
                resize:true,  
                fullscreen:false, 
                title:"",
                loading:false,  
                width:600,
                //height:600, 
                position:{top:50},
                escClosable:true,  
                showFooter:true,  
                showZoom:true,  
            }
        }
    },
    computed: {
        //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
        actionType_state() {
          return this.$store.state.actionType;
        },
      },
      watch:{
        // 添加/修改/删除 按钮触发事件
        actionType_state:{
            handler(n,o){
              this.actionTypeChange(n)
            },
             deep:true,
             immediate:false
        },
      },
    mounted(){
       
        this.debouncedFn_Submit = useDebounceFn(() => {
            this.submitBtn()
        }, 1000)
    },  
    methods:{
       // 获取弹框 Ref 实例,方便操作原生控件方法，属性等
        // newRef 每次都重新获取实例？
        getRefInstance(newRef=false){
            //表格单实例
            if(!this.refInstance || newRef){
                this.refInstance = this.$refs["nvxeModalRef"]//.$refs["csoftiModalRef"];
            }
            return  this.refInstance
        },
          // 添加/修改/删除/ 触发事件
        actionTypeChange(n){
            let _self = this
           if(n){
            let btnType = n.value
            switch (btnType) {
                case this.actionType.iisAdd:
                  _self.defaultTitle ="添加 "+_self.subTitle
                    break;
                case this.actionType.iisEdit:
                 _self.defaultTitle ="编辑 "+_self.subTitle   
                    break;
                default:
                    break;
            }
           }
        },
         // 预提交
       preSubmitEvent(){
          this.loadingBtn = true
          this.debouncedFn_Submit()
       },
        // 提交按钮 回调事件
        submitBtn(){
            let params ={}
            this.$emit('confirmEvent',params)
            this.loadingBtn = false
        },
        // 取消按钮 回调事件
        cancelBtn(){
            const $modal = this.getRefInstance()
            if($modal){
                let params ={}
                this.$emit('cancelEvent',params)
                $modal.close()
                this.$emit('closeEvent',params)
            }
        },
        showEvent(e){
           
            this.$nextTick(()=>{
              let modalObj = this.$refs["nvxeModalRef"]
              //debugger
              if(modalObj){
                this.elementModalSize.width = Number(modalObj.width)
                this.elementModalSize.height = Number(modalObj.height)
               // debugger
              }
              let params={
                e,
                showEditBox:this.showEditBox,
                elementModalSize:this.elementModalSize
            } 
              this.$emit('showEvent',params)
            })
           
        },
    //     tryGetModalRef(){
    //         let _self = this
    //         if(!!_self.$refs["refBodyNvxeModal"]){
    //             _self.elementModalSize = useElementSize(_self.$refs["refBodyNvxeModal"]);
    //             debugger
    //             _self.maxLoadTime = 0
    //         }else{
    //             console.log("===tryGetModalRef===");
    //             if( _self.maxLoadTime<6){
    //                 setTimeout(()=>{
    //                     _self.maxLoadTime =  _self.maxLoadTime+1
    //                         this.tryGetModalRef()
    //                 },300)
    //             }
    //         }
    //   },
        closeEvent(e){
            let params={
                e,
                showEditBox:this.showEditBox
            } 
            this.$emit('closeEvent',params)
        }
        
    }
}
</script>
<style lang="scss">

// 无界 样式定位 弹框
// $translateLR: 17%;
// .formsetting {
//     // 修复弹框位置相对居中 不正确，和大小有关
//     .vxe-modal--box{
//         left:50%;
//         top:50%;
//         -webkit-transform: translate($translateLR, 12%);
//         -ms-transform: translate($translateLR, 12%);
//         transform: translate($translateLR, 12%);
        
//     }
// }
// //  fixed 修复Select下拉弹框位置 不正确
// .el-select-dropdown{
//      position: absolute!important
// }
// //  fixed 修复日期弹框位置 不正确
// .el-date-picker {
//     position: absolute !important;
// }
</style>