<template>
    <div>
      <el-form-item  label="显示标题">
          <el-switch v-model="optionModel.showTitle"></el-switch>
      </el-form-item>
      <el-form-item  label="显示边框">
          <el-switch v-model="optionModel.showBorderStyle"></el-switch>
      </el-form-item>
      <el-form-item  label="垂直布局">
          <el-switch v-model="optionModel.isVertical"></el-switch>
      </el-form-item>
      <el-form-item  label="布局数">
          <el-input-number v-model="optionModel.columnNumber"  :min="1" :max="4" label="描述文字"></el-input-number>
      </el-form-item>
      <el-form-item label-width="0">
            <selectDataSourceApi contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></selectDataSourceApi>
        </el-form-item>
        <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <groupSearchItemList :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></groupSearchItemList>
      </el-form-item>
    </div>
  </template>
  
  <script>
   import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
   import groupSearchItemList from './groupSearchItemList.vue'
    import i18n from "@/utils/i18n"
    export default {
      name: "descriptionsList-editor",
      mixins: [i18n],
      components:{selectDataSourceApi,groupSearchItemList},
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
    }
  </script>
  
  <style scoped>
    .html-content-editor {
      font-size: 13px;
    }
  </style>
  