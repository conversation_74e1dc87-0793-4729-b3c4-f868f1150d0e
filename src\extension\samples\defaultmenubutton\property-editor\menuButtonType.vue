<template>
    <div>
        <el-form-item label="显示类型">
            <el-select v-model="optionModel.type">
                <el-option label="default" value=""></el-option>
                <el-option label="primary" value="primary"></el-option>
                <el-option label="success" value="success"></el-option>
                <el-option label="warning" value="warning"></el-option>
                <el-option label="danger" value="danger"></el-option>
                <el-option label="info" value="info"></el-option>
                <el-option label="text" value="text"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="组件大小">
            <el-select v-model="optionModel.size">
                <el-option v-for="item in widgetSizes" :key="item.value" :label="item.label"
                            :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
        <!-- 按钮Icon -->
        <el-form-item :label="i18nt('designer.setting.buttonIcon')">
            <el-input type="text" v-model="optionModel.icon"></el-input>
            <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
        </el-form-item>
        <el-form-item v-show="optionModel.icon" label="仅显示图标">
            <el-switch :active-value="1" :inactive-value="0" size="mini"
                    v-model="optionModel.showIconOnly"></el-switch>
        </el-form-item>
       
    </div>
</template>
<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "menuButtonType",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data(){
        return {
         widgetSizes: [
          {label: 'default', value: ''},
          {label: 'large', value: 'large'},
          {label: 'medium', value: 'medium'},
          {label: 'small', value: 'small'},
          {label: 'mini', value: 'mini'},
        ],
        }
    },
    methods:{
        changeEvent(val){
            // debugger
            // let tt = this.optionModel
        }
    }
  }
</script>

<style lang="scss" scoped>

</style>