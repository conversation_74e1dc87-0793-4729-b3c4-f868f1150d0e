<template>
  <div>
    <el-form-item label-width="0">
            <commonParamsOnChange contrlType="select" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
      <el-divider class="custom-divider-margin-top">{{ i18nt('designer.setting.optionsSetting') }}</el-divider>
    </el-form-item>
    <el-form-item label="提交选中文本">
      <el-switch v-model="optionModel.isPostSelectedText"></el-switch>
    </el-form-item>
    <el-form-item v-if="!!optionModel.isPostSelectedText" label="">
      <span  slot="label">选择文本字段
        <el-tooltip effect="light" content="开启数据源后,需要先点击预加载后 方可获取可选数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
        <el-select allow-create filterable v-model="optionModel.postSelectedTextField" placeholder="请选择查询">
          <el-option value="">请选择</el-option>
          <el-option :key="labelKeyIndex" v-for="(labelKeyItem, labelKeyIndex) in optionModel.dataSetAllModel"
            :label="labelKeyItem.title" :value="labelKeyItem.field"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.labelKeyName')">
      <span v-if="!!optionModel.dsEnabled" slot="label">{{ i18nt('designer.setting.labelKeyName') }}
        <el-tooltip effect="light" content="开启数据源后,需要先点击预加载后 方可获取可选数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <template v-if="!optionModel.dsEnabled">
        <el-input v-model="optionModel.labelKey"></el-input>
      </template>
      <template v-else>
        <el-select allow-create filterable v-model="optionModel.labelKey" placeholder="请选择查询">
          <el-option value="">请选择</el-option>
          <el-option :key="labelKeyIndex" v-for="(labelKeyItem, labelKeyIndex) in optionModel.dataSetAllModel"
            :label="labelKeyItem.title" :value="labelKeyItem.field"></el-option>
        </el-select>
      </template>
    </el-form-item>
    
    <el-form-item :label="i18nt('designer.setting.valueKeyName')">
      <span v-if="!!optionModel.dsEnabled" slot="label">{{ i18nt('designer.setting.valueKeyName') }}
        <el-tooltip effect="light" content="开启数据源后,需要先点击预加载后 方可获取可选数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>


      <template v-if="!optionModel.dsEnabled">
        <el-input v-model="optionModel.valueKey"></el-input>
      </template>
      <template v-else>
        <el-select allow-create filterable v-model="optionModel.valueKey" placeholder="请选择查询">
          <el-option value="">请选择</el-option>
          <el-option :key="valueKeyIndex" v-for="(valueKeyItem, valueKeyIndex) in optionModel.dataSetAllModel"
            :label="valueKeyItem.title" :value="valueKeyItem.field"></el-option>
        </el-select>
      </template>
    </el-form-item>
    <el-form-item  label="">
      <span  slot="label">横向显示
        <el-tooltip effect="light" content="默认竖立，勾选则为横向">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.flexRowStyle"></el-switch>
    </el-form-item>
    <el-form-item label="字体大小">
        <el-input-number  v-model="optionModel.fontSize" controls-position="right" ></el-input-number>
    </el-form-item>
    <el-form-item  label="">
      <span  slot="label">强制转换为字符串
        <el-tooltip effect="light" content="默认不转换，某些情况值和绑定值不相符，需要转换">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.convertToString"></el-switch>
    </el-form-item>
    <el-form-item v-if="hasConfig('childrenKey')" :label="i18nt('designer.setting.childrenKeyName')">
      <el-input v-model="optionModel.childrenKey"></el-input>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.dsEnabled')">
      <el-switch v-model="optionModel.dsEnabled"></el-switch>
    </el-form-item>
 
  
    <el-form-item v-if="!!optionModel.dsEnabled" label-width="0">
      <selectDataSourceApi contrlType="commonSelect" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></selectDataSourceApi>
    </el-form-item>

    <el-form-item v-if="!optionModel.dsEnabled" label-width="0">
      <!-- 注意：引用其它控件 -->
      <option-items-setting :designer="designer" :selected-widget="selectedWidget"></option-items-setting>
    </el-form-item>
    <el-form-item v-if="!!optionModel.dsEnabled" label="开启下拉树">
      <el-switch v-model="optionModel.dataTreeEnabled"></el-switch>
    </el-form-item>
    <el-form-item v-if="!!optionModel.dataTreeEnabled && !!optionModel.dsEnabled" label="树结构子ID">
      <span slot="label">树结构子ID
        <el-tooltip effect="light" content="需要先点击预加载后 方可获取可选数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select v-model="optionModel.treeRowField" placeholder="请选择查询">
        <el-option value="">请选择</el-option>
        <el-option :key="index" v-for="(item, index) in optionModel.dataSetAllModel" :label="item.title"
          :value="item.field"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="!!optionModel.dataTreeEnabled && !!optionModel.dsEnabled" label="树结构父ID">
      <span slot="label">树结构父ID
        <el-tooltip effect="light" content="需要先点击预加载后 方可获取可选数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select v-model="optionModel.treeParentField" placeholder="请选择查询">
        <el-option value="">请选择</el-option>
        <el-option :key="parentIndex" v-for="(parentItem, parentIndex) in optionModel.dataSetAllModel"
          :label="parentItem.title" :value="parentItem.field"></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
import i18n from "@/utils/i18n"
import OptionItemsSetting from "@/components/form-designer/setting-panel/option-items-setting"
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
export default {
  name: "optionItems-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    OptionItemsSetting, selectDataSourceApi,commonParamsOnChange
  },
  computed: {
    dataSourceList() {
      if (!this.designer.formConfig || !this.designer.formConfig.dataSources) {
        return []
      } else {
        return this.designer.formConfig.dataSources
      }
    },

  },
}
</script>

<style scoped></style>
