<template>
    <div>
        <el-switch @change="changeEvent"  v-model="currentItem" :active-value='activeValue' :inactive-value='inactiveValue' v-bind="$attrs" v-on="$listeners"></el-switch>
    </div>
</template>
<script>
import config from '@/config'
// import request from '@/libs/request'
export default {
    name: "statusColumn",// 自定义 状态列 可更新空间 :disabled="true"
    props: {
       
       dataFrom: {
           type: String,
           default: "form"
       },

       // 当前字段表单
       formData: {
           type: Object,
           default() {
               return {}
           }
       },
       // 当前字段:其它配置信息
       paramsItem: {
           type: Object,
           default() {
               return {}
           }
       },
       // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
       dataSearchOptions: {
           type: Object,
           default() {
               return {};
           },
       },
       // 当前字段:value
       currentValue: {
           type: [String, Number,Boolean],
           default: ""
       },

       // 是否可用
        //    disabled: {
        //        type: Boolean,
        //        default: false,
        //    }
   },
    // 注入额外的配置信息
    inject: {
        // 获取列表页面整体实体
        getPageInstance: {
            default: () => { }
        }
    },
    data() {
        return {
            currentItem:"",
            config: config,
            activeValue:"A", // 默认值
            inactiveValue:"D"// 默认值
        }
    },
    watch:{
        currentValue(n, o) {
           // debugger
            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
            }
        },
      
    },
    created(){
      this.setActiveValueAndInactiveValue()
    },
    mounted() {
        this.$nextTick(() => {
            //debugger
            this.currentItem = this.currentValue
        })
    },
    methods: {
        // 设置 activeValue 和 inactiveValue 的初始值，默认A/D
        setActiveValueAndInactiveValue(){
            if(!!this.paramsItem.sourceKey){
                if(this.paramsItem.sourceKey.includes('#')){
                    this.paramsItem.sourceKey.replace(" ",'').split('#').forEach((item,index)=>{
                        if(index==0){
                            this.activeValue =item.replace(" ",'')
                        }
                        if(index==1){
                            this.inactiveValue =item.replace(" ",'')
                        }
                    })
                }
            }
        },
     
        // 事件改变触发事件
        changeEvent(val) {
            let _self = this
            this.$nextTick(()=>{
            let params ={
                    value: val,
                    text: val,
            }
                _self.$emit("changeEvent",params)
            })
            
        },
    }
}
</script>
<style lang="scss" scoped>

</style>