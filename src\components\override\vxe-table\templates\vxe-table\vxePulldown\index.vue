<!--
 * @Description: pulldown
 * @Author: foyang
 * @Date: 2022-10-28 11:27:17
 * @LastEditTime: 2022-11-07 09:02:34
 * @LastEditors: foyang
-->
<template>
    <vxe-pulldown ref="xDowns" class="pulldownCla">
        <template #default>
            <span class="filterCla" @click="dropdownShow">
                <i class="vxe-icon-custom-column" ></i>
            </span>
        </template>
        <template #dropdown>
            <div class="downClaCla">
                <div class="groupCla">
                    <vxe-checkbox v-model="checkboxAllVal" class="checkboxOneCla" label="all" @change="checkboxAll">全部</vxe-checkbox>
                    <template v-for="(column,index) in dropdownList">
                        <vxe-checkbox v-if="column.field" class="checkboxCla" 
                        v-model="column.visible" :key="index"  
                        @change='dropdownChange'>{{ column.title }}</vxe-checkbox>
                    </template>
                </div>
                <!-- <vxe-checkbox-group v-model="checkboxVal" class="groupCla" >
                    <vxe-checkbox class="checkboxCla" 
                        v-for="item in dropdownList" :key="item.id" :label="item.field" 
                        @change='dropdownChange' :content="item.title"
                    />
                </vxe-checkbox-group> -->
                <div class="footerCla">
                    <vxe-button type="text" status="primary" content="还原" @click="cancelClick"></vxe-button>
                    <vxe-button type="text" status="primary" content="确定" @click="enterClick"></vxe-button>
                </div>
            </div>
        </template>
    </vxe-pulldown>
</template>
<script>
export default {
    name: 'vxePulldownTemplate',
    props: {
        
    },
    data() {
        return {
            timer: '',
            checkboxVal: [],
            dropdownList: [],
            checkboxAllVal: true
        }
    },
    mounted() {
        this.timer = setInterval(() => {
            if (document.readyState === 'complete') {
                this.initData()
                clearInterval(this.timer);
            }
        }, 1000);
    },
    computed: {
    },
    watch: {
        checkboxVal(val) {
            if((this.dropdownList&&this.dropdownList.length) == this.checkboxVal.length) {
                this.checkboxAllVal = true
            }
        }
    },
    methods: {
        checkboxAll(val) {
            console.log(val);
            let checkeds = val.checked || val
            this.dropdownList.map(i => {
                if(checkeds == true) {
                    i.visible = true
                } else {
                    i.visible = false
                }
                return i
            })
            console.log(this.dropdownList);
        },
        initData(val) {
          // 设置下拉容器列表数据
            this.dropdownList = this.$parent.$parent.$parent.$refs.csoftiTableRef.getColumns()// 行配置
            // console.log(this.$parent.$parent.$parent.$refs.csoftiTableRef.getColumns(),'this.dropdownList');
            // if (dropdownList && dropdownList.length > 0) {
            //     this.dropdownList = dropdownList.filter(i => {
            //         if(i.field) {
            //             this.checkboxVal.push(i.field)
            //             return i
            //         }
            //     })
            // }
        },
        dropdownChange(val) {
            console.log(this.dropdownList);
        },
        dropdownShow() {
            this.$refs.xDowns.showPanel()
        },
        cancelClick() {
           if(this.dropdownList.length !== this.checkboxVal.length) this.checkboxAll(true)
            this.$refs.xDowns.hidePanel()
            this.$parent.$parent.$parent.$refs.csoftiTableRef.resetColumn()
        },
        // 显示隐藏列确定事件
        enterClick() {
            this.$refs.xDowns.hidePanel()
            this.$parent.$parent.$parent.$refs.csoftiTableRef.refreshColumn()
            // let tableColumns = this.$parent.$parent.$parent.$refs.csoftiTableRef.getColumns();
            // if(this.checkboxVal.length > 0) {
            //     tableColumns.map(i => {
            //         // console.log(i.field,'i');
            //         this.checkboxVal.map(j => {
            //             // console.log(j,'j');
            //             if([i.field].includes(j)) {
            //                 console.log(i.field,j, 'true');
            //                 // this.$parent.$parent.$parent.$refs.csoftiTableRef.hideColumn(i.field)
            //             }else {
            //                 console.log(i.field,j, 'false');
            //                 // this.$parent.$parent.$parent.$refs.csoftiTableRef.hideColumn(j)
            //                 // console.log(i.field,'true');
            //             }
            //         })
            //         return i
            //     });
            //     console.log(tableColumns,'tableColumns');
            //     // this.$parent.$parent.$parent.$refs.csoftiTableRef.reloadColumn(tableColumns)
            // }else {
            //     // this.$parent.$parent.$parent.$refs.csoftiTableRef.reloadColumn(tableColumns)
            // }
        }

    },
}
</script>
<style lang='scss' scoped>
    .pulldownCla {
        // z-index: 9999!important;
        .filterCla {
            display: inline-block;
            width: 35px;
            height: 35px;
            line-height: 35px;
            border: 1px solid #dcdfe6;
            border-radius: 50%;
            text-align: center;
            cursor: pointer;
            &:hover {
                background: #ebeef5;
            }
        }
        .downClaCla {
            border-radius: 5px!important;
            box-shadow: 0 0 10px rgb(235, 234, 234);
            padding: 10px;
            display: flex;
            flex-direction: column;
            .groupCla {
                width: 150px;
                height: 190px;
                overflow: auto;
                .checkboxCla {
                display: flex;
                justify-items: left;
                height: 15px;
                margin-bottom: 5px;
                }
                .checkboxOneCla{
                  padding-left: 10px;
                }
            }
            .footerCla {
              height:30px;
              line-height: 30px;
              border-top: 1px solid #e8eaec;
              display: flex;
              justify-content: space-between;
      
            }
        }
    }
</style>
