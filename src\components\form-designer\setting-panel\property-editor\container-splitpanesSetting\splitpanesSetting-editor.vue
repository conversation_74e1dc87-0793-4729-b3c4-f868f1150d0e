<!--
  因panes属性并不包含于options属性之中，故只能跟其他options属性之内的属性值合并设置，此处选择与customClass合并！！
-->

<template>
    <div>
      <el-form-item label="分栏设置"></el-form-item>
      <el-form-item  label="背景颜色">
        <el-color-picker
            v-model="selectedWidget.options.bgColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="拖拽按钮颜色">
        <el-color-picker
            v-model="selectedWidget.options.btnColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
      <el-form-item label="">
        <span slot="label">作为主容器
          <el-tooltip effect="light" content="作为主容器时,需要距离顶部10PX，">
          <i class="el-icon-info"></i></el-tooltip>
		  	</span>
        <el-switch v-model="selectedWidget.options.asMainContainer"></el-switch>
     </el-form-item>
     <el-form-item label="">
			<span slot="label">高度偏移量
				<el-tooltip effect="light" content="当高度不一致时，需要用到，可以负值">
				<i class="el-icon-info"></i></el-tooltip>
			</span>
      <el-input-number v-model="selectedWidget.options.OffsetHeightNumber" controls-position="right" ></el-input-number>
		</el-form-item>
    <el-form-item label="">
			<span slot="label">高度
				<el-tooltip effect="light" content="默认为空，自动计算高度，小于1为占用百分比，否则实际高度">
				<i class="el-icon-info"></i></el-tooltip>
			</span>
      <el-input-number v-model="selectedWidget.options.settingHeight" controls-position="right"  :min="0" ></el-input-number>
		</el-form-item>
    
      <el-form-item label="上下布局">
        <el-switch v-model="selectedWidget.options.horizontal"></el-switch>
     </el-form-item>
     <el-form-item label="显示第一个栏">
        <el-switch v-model="selectedWidget.options.firstSplitter"></el-switch>
     </el-form-item>
     <el-form-item label="从右向左">
        <el-switch v-model="selectedWidget.options.RightToLeft"></el-switch>
     </el-form-item>
     <el-form-item label="双击最大化">
        <el-switch v-model="selectedWidget.options.dblClickSplitter"></el-switch>
     </el-form-item>
     <el-form-item label="是否推动下一个">
        <el-switch v-model="selectedWidget.options.pushOtherPanesDefault"></el-switch>
     </el-form-item>
     <el-form-item label="是否隐藏拖拽按钮">
        <el-switch v-model="selectedWidget.options.hideDragItem"></el-switch>
     </el-form-item>
     <el-form-item label="默认隐藏" prop="defaultHide">
          <el-select  v-model="selectedWidget.options.defaultHideType" placeholder="请选择">
            <el-option label="未选择" value=""></el-option>
            <el-option label="【左侧】" value="left"></el-option>
            <el-option label="【右侧】" value="right"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label-width="0" class="panes-setting">
        <draggable tag="ul" :list="selectedWidget.panes"
                   v-bind="{group:'panesGroup', ghostClass: 'ghost', handle: '.drag-option'}">
          <li v-for="(tpItem, tpIdx) in selectedWidget.panes" :key="tpIdx" class="col-item">
            <!-- <el-checkbox v-model="tpItem.options.active" disabled @change="(evt) => onTabPaneActiveChange(evt, tpItem)"
                         style="margin-right: 8px"></el-checkbox> -->
             <span>占比%:</span>            
            <el-input-number controls-position="right" :controls="false" :min="10" placeholder="高/宽占比" type="text" v-model="tpItem.options.wtPercent" style="width: 80px">
              <!-- <template slot="prepend"></template> -->
            </el-input-number>
            <el-input type="text" v-model="tpItem.options.label" style="width: 90px"></el-input>
            <i class="iconfont icon-drag drag-option"></i>
            <el-button circle plain size="mini" type="danger" @click="deleteTabPane(selectedWidget, tpIdx)"
                       icon="el-icon-minus" class="col-delete-button"></el-button>
          </li>
          <div>
            <el-button type="text" @click="addPane(selectedWidget)">+添加分栏项</el-button>
          </div>
        </draggable>
      </el-form-item>
      <el-form-item label="容器margin-top">
        <el-input-number v-model="selectedWidget.options.containerMarginTop" controls-position="right" ></el-input-number>
		 </el-form-item>
     <el-form-item label="容器margin-right">
        <el-input-number v-model="selectedWidget.options.containerMarginRight" controls-position="right" ></el-input-number>
		 </el-form-item>
     <el-form-item label="容器margin-bottom">
        <el-input-number v-model="selectedWidget.options.containerMarginBottm" controls-position="right" ></el-input-number>
		 </el-form-item>
     <el-form-item label="容器margin-left">
        <el-input-number v-model="selectedWidget.options.containerMarginLeft" controls-position="right" ></el-input-number>
		 </el-form-item>
    </div>
  </template>
  
  <script>
    import i18n from "@/utils/i18n"
    import Draggable from 'vuedraggable'
    import {deepClone, generateId, getDefaultFormConfig, overwriteObj} from "@/utils/util"
    export default {
      name: "splitpanesSetting-editor",
      componentName: 'PropertyEditor',
      mixins: [i18n],
      components: {
        Draggable,
      },
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
      data() {
        return {
          cssClassList: [],
          predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
        }
      },
      created() {
        this.cssClassList = deepClone(this.designer.getCssClassList())
        //监听表单css代码改动事件并重新加载！
        this.designer.handleEvent('form-css-updated', (cssClassList) => {
          this.cssClassList = cssClassList
        })
      },
      methods: {
        onTabPaneActiveChange(evt, tpItem) {
          //TODO: !!! splitpanes Setting-editor
        },
        // 重新计算宽高占用比例
        recaculateWT(){
          //debugger
          let tabPanes = this.selectedWidget.panes
          let totalNum = tabPanes.length
          let sPercent = Number((100/totalNum).toFixed(2))
          tabPanes = tabPanes.map(item=>{
            item.options.wtPercent = sPercent
            return item
          })

        },
        addOption(panesWidget) {
            const tabPanes = panesWidget.panes
            let newTabPane = deepClone( this.designer.getContainerByType('split-pane') )
            newTabPane.id = 'pane-' + generateId()
            newTabPane.options.name = newTabPane.id
            newTabPane.options.label = 'pane ' + (tabPanes.length + 1)
            tabPanes.push(newTabPane)
            this.$nextTick(()=>{
              this.recaculateWT()
            })
        },
        addPane(curpanes) {
          this.addOption(curpanes)
          this.designer.emitHistoryChange()
        },
       
        deleteTabPane(curpanes, tpIdx) {
          if (curpanes.panes.length === 1) {
            this.$message.info(this.i18nt('最后一个不可删除'))
            return
          }
  
          this.deleteTabPaneOfpanes(curpanes, tpIdx)
          
          this.designer.emitHistoryChange()
        },
        deleteTabPaneOfpanes(panesWidget, tpIdx) {
            panesWidget.panes.splice(tpIdx, 1)
            this.$nextTick(()=>{
              this.recaculateWT()
            })
        },
  
      }
    }
  </script>
  
  <style lang="scss" scoped>
    li.col-item {
      list-style: none;
  
      span.col-span-title {
        display: inline-block;
        font-size: 13px;
        width: 120px;
      }
  
      .col-delete-button {
        margin-left: 6px;
      }
    }
  
    .panes-setting {
      ul {
        padding-inline-start: 0;
        padding-left: 0; /* 重置IE11默认样式 */
        margin: 0;
      }
  
      .drag-option {
        cursor: move;
      }
  
      li.ghost {
        background: #fff;
        border: 2px dotted $--color-primary;
      }
    }
  
  </style>
  