<template>
  <el-form-item :label="i18nt('designer.setting.defaultValue')">
    <span slot="label">{{i18nt('designer.setting.defaultValue')}}
          <el-tooltip effect="light" content="时间默认值 当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+1年:add-1-year">
            <i class="el-icon-info"></i></el-tooltip></span>
    <!-- <el-date-picker :type="optionModel.type" v-model="optionModel.defaultValue" @change="emitDefaultValueChange"
                    :format="optionModel.format" :value-format="optionModel.valueFormat" style="width: 100%">
    </el-date-picker> -->
    <el-input v-model="optionModel.defaultValue"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "date-defaultValue-editor",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
