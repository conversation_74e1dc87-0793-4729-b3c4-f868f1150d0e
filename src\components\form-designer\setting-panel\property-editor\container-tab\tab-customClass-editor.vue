<!--
  因tabs属性并不包含于options属性之中，故只能跟其他options属性之内的属性值合并设置，此处选择与customClass合并！！
-->

<template>
  <div>
    <el-form-item label="">
      <span slot="label">隐藏TAB项
        <el-tooltip effect="light" content="默认开启，主要与自定义控件结合使用，隐藏自定义控件的TAB项">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.hideTabsName"></el-switch>
    </el-form-item>
    <el-form-item v-show="optionModel.hideTabsName" label="">
      <span slot="label">隐藏TAB边框
        <el-tooltip effect="light" content="默认开启，隐藏TAB边框">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.hideTabsBorder"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">高度偏移量
        <el-tooltip effect="light" content="需要调整高度偏移量，可正，可负（参考:-52）">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input-number v-model="optionModel.OffsetHeight" controls-position="right"></el-input-number>
    </el-form-item>
  
    <el-form-item label-width="0">
      <commonParamsOnChange contrlType="tab" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item  label-width="0">
      <selectDataSourceApi contrlType="tab" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></selectDataSourceApi>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">开启左偏移
        <el-tooltip effect="light" content="默认 样式>>左偏移padding-left:0px,开启后 左偏移10px">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.onPaddingLeft"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">开启右偏移
        <el-tooltip effect="light" content="默认 样式>>右偏移padding-right:0px,开启后 右偏移10px ">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.onPaddingRight"></el-switch>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.customClass')">
      <el-select v-model="optionModel.customClass" multiple filterable allow-create
                 default-first-option>
        <el-option v-for="(item, idx) in cssClassList" :key="idx" :label="item" :value="item"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="控件背景颜色">
      <el-color-picker
            v-model="optionModel.bgColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="TAB选中项背景颜色">
      <el-color-picker
            v-model="optionModel.activeTabColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="TAB项字体颜色">
      <el-color-picker
            v-model="optionModel.tabItemTextColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="TAB选中项字体颜色">
      <el-color-picker
            v-model="optionModel.activeTabTextColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
   
    <el-form-item label="TAB栏目颜色">
      <el-color-picker
            v-model="optionModel.tabBarColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item label="TAB内容颜色">
      <el-color-picker
            v-model="optionModel.tabBodyColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.tabPaneSetting')"></el-form-item>
    <el-form-item label-width="0" class="panes-setting">
      <draggable tag="ul" :list="selectedWidget.tabs"
                 v-bind="{group:'panesGroup', ghostClass: 'ghost', handle: '.drag-option'}">
        <li v-for="(tpItem, tpIdx) in selectedWidget.tabs" :key="tpIdx" class="col-item">
          <!-- span style="margin-right: 12px">{{tpIdx + 1}}</span -->
          <el-checkbox v-model="tpItem.options.active" disabled @change="(evt) => onTabPaneActiveChange(evt, tpItem)"
                       style="margin-right: 8px">{{i18nt('designer.setting.paneActive')}}</el-checkbox>
          <el-input type="text" @click.native="showEditItemDialogEvent(tpItem,tpIdx)" v-model="tpItem.options.label" style="width: 155px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button circle plain size="mini" type="danger" @click="deleteTabPane(selectedWidget, tpIdx)"
                     icon="el-icon-minus" class="col-delete-button"></el-button>
        </li>
        <div>
          <el-button type="text" @click="addTabPane(selectedWidget)">{{i18nt('designer.setting.addTabPane')}}</el-button>
        </div>
      </draggable>
    </el-form-item>
    <el-dialog title="TAB选项卡配置 编辑" :visible.sync="showEditItemDialogFlag" v-if="showEditItemDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editItemForm"  ref="editItemForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input style="width:330px" v-model="editItemForm.label"></el-input>
        </el-form-item>
        <el-form-item label="唯一名称" prop="name">
          <el-input style="width:330px" disabled v-model="editItemForm.name"></el-input>
        </el-form-item>
        <el-form-item label="" prop="IndexNumber">
          <span slot="label">标签下标
            <el-tooltip effect="light" content="标签下标,默认为空，需要手动填写，当需要接口动态隐藏改标签时使用">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-input-number style="width:330px" v-model="editItemForm.IndexNumber"></el-input-number>
        </el-form-item>
        <el-form-item label="" >
          <span slot="label">TAB关联
            <el-tooltip effect="light" content="默认false 关联另外一个TAB，常用于默认菜单，不同TAB不同的菜单，然后隐藏TAB项">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-switch v-model="editItemForm.linkOtherTab"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.linkOtherTab" label="关联TAB项" prop="activeTabItem">
          <span slot="label">关联TAB项
            <el-tooltip effect="light" content="触发控件列表第一个为TAB（标签页）时，可以关联选择对象的项">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-select style="width:330px" size="mini" v-model="editItemForm.activeTabItem" placeholder="请选择">
            <el-option v-for="item in activeTabItemList" :key="item.name" :label="item.label"
                :value="item.name">
            </el-option>
        </el-select>
        
        </el-form-item>
        <el-form-item label="" >
          <span slot="label">总是触发
          <el-tooltip effect="light" content="默认false 切换时仅触发一次（下次切换不再触发），设为true，每次切换都会触发控件查询">
            <i class="el-icon-info"></i></el-tooltip>
        </span>
          <el-switch v-model="editItemForm.isReloadEveryTime"></el-switch>
        </el-form-item>
        <el-form-item label-width="0">
            <commonParamsOnChange style="width:425px;" contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="editItemForm"></commonParamsOnChange>
        </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditItem()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type="" @click="showEditItemDialogFlag = false">{{ i18nt('designer.hint.cancel')
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
  import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
  import i18n from "@/utils/i18n"
  import Draggable from 'vuedraggable'
  import {deepClone} from "@/utils/util";

  export default {
    name: "tab-customClass-editor",
    componentName: 'PropertyEditor',
    mixins: [i18n],
    components: {
      Draggable,
      commonParamsOnChange,
      selectDataSourceApi,
    },
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        cssClassList: [],
        activeTabItemList:[],
        showEditItemDialogFlag:false,
        editItemForm:{
          label:"",
          name:"",
          IndexNumber:null,
          activeTabItem:"",
          isReloadEveryTime:false,
          linkOtherTab:false,
        },
        predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
      }
    },
    watch:{
      showEditItemDialogFlag(n,o){
     
      if(n){
        setTimeout(()=>{
         // debugger
             this.activeTabItemList =[]
             if(this.optionModel.triggerCtrlNames && this.optionModel.triggerCtrlNames.length>0){
                this.triggerCtrlTab = this.optionModel.triggerCtrlNames[0].value
                let _widgetObj = this.designer.formWidget.getWidgetRef(this.triggerCtrlTab)
                if(_widgetObj){
                  //debugger
                    if (_widgetObj.widget.tabs && _widgetObj.widget.tabs.length>0){
                     // debugger
                      _widgetObj.widget.tabs.forEach(item=>{
                        let newTabItem ={
                            label:item.options.label,
                            name:item.options.name
                        }
                        this.activeTabItemList.push(newTabItem)
                      })
                    
                    }
                    
                }

             }
           
     
        },300)
      }
  }
 },
    created() {
      //debugger
      this.cssClassList = deepClone(this.designer.getCssClassList())
      //监听表单css代码改动事件并重新加载！
      this.designer.handleEvent('form-css-updated', (cssClassList) => {
        this.cssClassList = cssClassList
      })
    },
    methods: {
      showEditItemDialogEvent(tpItem,tpIdx){
        // debugger 
        //options: Object
        // active: true
        // customClass: ""
        // disabled: false
        // hidden: false
        // label: "设备状态信息"
        // name: "tab1"
        this.editItemForm = tpItem.options
        this.showEditItemDialogFlag = true
      },
      submitEditItem(){
        this.showEditItemDialogFlag = false
      },
      onTabPaneActiveChange(evt, tpItem) {
       // debugger
        //TODO: !!!
      },

      addTabPane(curTabs) {
        this.designer.addTabPaneOfTabs(curTabs)
        this.designer.emitHistoryChange()
      },

      deleteTabPane(curTabs, tpIdx) {
        if (curTabs.tabs.length === 1) {
          this.$message.info(this.i18nt('designer.hint.lastPaneCannotBeDeleted'))
          return
        }

        this.designer.deleteTabPaneOfTabs(curTabs, tpIdx)
        this.designer.emitHistoryChange()
      },

    }
  }
</script>
<style>
 .commonParamsOnChange-option-items-pane ul {list-style:none;margin:0px;} 
</style>
<style lang="scss" scoped>

  li.col-item {
    list-style: none;

    span.col-span-title {
      display: inline-block;
      font-size: 13px;
      width: 120px;
    }

    .col-delete-button {
      margin-left: 6px;
    }
  }

  .panes-setting {
    ul {
      padding-inline-start: 0;
      padding-left: 0; /* 重置IE11默认样式 */
      margin: 0;
    }

    .drag-option {
      cursor: move;
    }

    li.ghost {
      background: #fff;
      border: 2px dotted $--color-primary;
    }
  }

</style>
