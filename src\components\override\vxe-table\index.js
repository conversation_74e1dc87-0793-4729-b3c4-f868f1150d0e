/**
 * @desc  描述 导入自定义 vxe-table 组件 方便升级扩展等  使用时,原有组件前面添加 'n' 意为NEW
 *
 * @params 参数
 *
 * @return 返回
 *
 * <AUTHOR>
 *
 * @created 2022/10/10
 */

import Vue from 'vue'
 // 全局注册组件 nvxeGrid
import nvxeGrid from './nvxe-grid.vue'
Vue.component('nvxeGrid', nvxeGrid)

// 全局注册组件 nvxeEditGrid
import nvxeEditGrid from './nvxe-edit-grid.vue'
Vue.component('nvxeEditGrid', nvxeEditGrid)

// 全局注册组件 nvxeModal
import nvxeModal from './nvxe-modal.vue'
Vue.component('nvxeModal', nvxeModal)

// 全局注册组件 nvxePage
import nvxePage from './nvxe-page.vue'
Vue.component('nvxePage', nvxePage)