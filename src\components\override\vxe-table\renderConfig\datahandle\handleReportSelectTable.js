
import { useFormatParams } from "@/hooks/useFormatParams"
export function handleReportSelectTable(vueInstance) {
   return {
      vueInstance: vueInstance,
      useFormatParamsFn: useFormatParams(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      testFn() {
         console.log("hello world")
      },
     async getOptionDataList(){
        console.log("====handleReportSelectTablet====getOptionDataList")
        return []
      }
    

   }
}