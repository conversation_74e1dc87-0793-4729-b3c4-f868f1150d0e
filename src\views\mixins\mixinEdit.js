/**
 * @desc  编辑页面通用类
 *
 * @params 参数
 * 
 * <AUTHOR>
 *
 * @created 2022/10/13
 */

 const {actionType} = require('@/enum/enumData')
 import config from '@/config'
 import { useElementSize } from "@vueuse/core";
//  import { useUrlSearchParams } from '@vueuse/core'
//const { useUrlSearchParams } = require("@vueuse/core"); 
 import request from '@/libs/request'
 export default {
    name:"mixinEdit",
    props:{
        paramsFromList: {
            type: Object,
            default() {
                return {
                    responseData:{}, // 原始查询出来的数据
                    layoutLevel:1, //层级级别 1:主表，2:从表，3:孙表
                    subRow:{},// 当前选中从表行
                    mainRow:{},// 当前主表选中行
                    subTableInfo:{} // 当前从表配置信息，tableHeader,tableDetail(列头字段配置信息)
                };
            },
        },
    },
    data(){
        return{
              elementModalSize:{width:0,height:0},// 弹框元素的长，宽
              maxLoadTime_editContentBoxRef:6,// 最大循环次数
              maxLoadTime_formItemRenderRef:6,// 最大循环次数
              config:config,
              request:request, // 全局请求参数
              splitpanesOptions:{
                pane1H:0,// 分栏1高度，需要在 渲染后 initData() 重新赋值
                pane2H:0,// 分栏2高度
                pane1P:32,//分栏1 默认占高 比列
                pane2P:68,//分栏2 默认占高 比列
              }, 
                 // 表单配置信息
              formRenderConfig:{
                formData:{}, // 表单字段
                formItems:[], //表单字段渲染条件
                formRules:{
                        // tableName: [
                        //     { required: true, message: '请输入表名', trigger: 'blur' },
                        //     { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
                        // ]
                }
            },//表单渲染配置
              editGridOptions:{
                columns:[],
                data:[],
                validRules:{}
              },
              editContentOptions:{
                height: 0, // 主体内容 contentBoxRef 高 
                width: 0,  // 宽
              },
              loading_nvxeModal:false,// 是否加载中
              subTitle:"",// 当前页面附加标题
              showEditBox:false, // 是否显示主表弹框
              //showSubEditBox:false, // 是否显示从表弹框
              loading_tableData:false,// 是否表格数据加载中...
              actionType:actionType,//操作类型，参考枚举
        }
    },
    computed: {
        // 弹框实际可用高度 -127
        elementModalSizeH(){
            return this.elementModalSize.height-127
        },
           //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
         actionType_state() {
            return this.$store.state.actionType;
          },
        // 当前主表选中行
        currentRow() {
          let routerName = this.$route.name
          return this.$store.state.currentRow[routerName];
        },
        // 当前主表选中行>>查询子表详情
        currentRowDetail() {
          let routerName = this.$route.name
          return this.$store.state.currentRowDetail[routerName];
        },
    },
    watch:{
        showEditBox(n,o){
            if(n){
              this.loading_nvxeModal = true
              setTimeout(()=>{
                this.initData()
            },100)
            }else{
                
                // 退出时，清除动作
                this.resetData()
            }
            this.setSubTitle()
          
        }
    },
    methods:{
        // 设置副标题
        setSubTitle(){
              // 赋值标题
              this.subTitle = this.paramsFromList.responseData.Datas.MainTable.TableHeader.tableDesc
                //层级级别 1:主表，2:从表，3:孙表
                if(this.paramsFromList.layoutLevel==2){
                    // 从表标题
                    this.subTitle = this.paramsFromList.subTableInfo.TableHeader.tableDesc
                }
        },
         // 弹框打开后，回调事件
        showEvent(params){
            this.elementModalSize = params.elementModalSize
        },
           // 弹框关闭后，回调事件
        closeEvent(params){
            this.paramsFromList.layoutLevel=1
            this.showEditBox = false
        },
        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
        
        // 初始化主表表单
      initDefaultFormData(){
          //debugger
          let formItems = this.getTableColumns() // 获取主表 或从表 列头信息
          let filterItems = this.getFilterFormItems(formItems)
          let filterData = this.getFilterFormData(filterItems)
          let formRules = this.getFormRules(filterItems)
          this.formRenderConfig.formItems = filterItems // 渲染条件
          this.formRenderConfig.formData = filterData // 渲染字段
          this.formRenderConfig.formRules = formRules // 验证规则
           this.clearFormValidate()
      },
      // 清除 表单验证
      clearFormValidate(){
        let _self = this
        if(!!_self.$refs["formItemRenderRef"] && !!_self.$refs["formItemRenderRef"].$refs["originFormRef"]){
            _self.$refs["formItemRenderRef"].$refs["originFormRef"].clearValidate()
            _self.maxLoadTime_formItemRenderRef = 6
        }else{
            //console.log("===clearFormValidate===");
            if( _self.maxLoadTime_formItemRenderRef>0){
                setTimeout(()=>{
                    _self.maxLoadTime_formItemRenderRef =  _self.maxLoadTime_formItemRenderRef-1
                        this.clearFormValidate()
                },300)
            }
        }
      },
        // 获取需要渲染的表格列头信息>>渲染表单
      getTableColumns(){
          //debugger
          let responseData = this.paramsFromList.responseData
          // 主表列头信息
          let columns = responseData.Datas.MainTable.TableDetail
          //层级级别 1:主表，2:从表，3:孙表
          if(this.paramsFromList.layoutLevel==2){
            // 是否从表，默认主表
            columns =  this.paramsFromList.subTableInfo.TableDetail
          }
          return columns
      },
         // 获取表单验证规则 ,必填字段
      getFormRules(itemsList){
          let rulesObj ={}
          itemsList.forEach(item=>{ 
              if(!!item.iisRequired){
                  // 必填字段
                  rulesObj[item.field] = [ { required: true, message: '请输入'+item.title, trigger: 'blur' }]
              }
          })
          return rulesObj
      },
      // 根据调节剂过滤查询需要显示的表单字段条件列表
      getFilterFormItems(itemsList){
          let newItemList = itemsList.filter(item=>{ 
              if(!item.iisShowForm){
                  return item
              }
          })
         // debugger
          return newItemList
      },
      // 从渲染条件中过滤表单字段列表和设置初始默认值
      getFilterFormData(dataList){
          let newFormObj = {}
          if(dataList && dataList.length>0){
              dataList.forEach(item=>{
              if(item && !!item.field){
                  if(item.fieldDefault!="0" && !!item.fieldDefault){
                      switch (item.controlType) {
                          case "text":
                            newFormObj[item.field] = item.fieldDefault
                              break;
                          case "date":
                          case "dateTime":   
                          // 时间格式必须正确，否则控件会导致无法使用 
                            newFormObj[item.field] = null
                              break;    
                          case "number":
                          newFormObj[item.field] = item.fieldDefault+0
                          break;
                          default:
                          newFormObj[item.field] = item.fieldDefault
                              break;
                      }
                  }else{
                      newFormObj[item.field] = item.fieldDefault
                  }
                
                
              }
          })
          }
          return newFormObj
      },
   
      // 表单渲染器 回调事件
      formRenderChangeEvent(params){
        // console.log('=====formRenderChangeEvent===='+JSON.stringify(params.data))

      },
      editFormActionBtn(type='add'){
            const $grid = this.$refs["editFormGrid"]
            switch (type) {
                // 新增一行
                case 'add':
                    $grid.insert({
                      // name: 'xxx'
                    })
                    break;
                // 移除一行   
                case 'remove':
                    let result= $grid.removeCheckboxRow()
                    break;
                default:
                    break;
            }
        },
       // 初始化 editContentBoxRef 宽 高
       initEditContentBoxWH(editContentBoxRefName ="editContentBoxRef"){
         let _self = this
          if(!!this.$refs[editContentBoxRefName]){
            this.maxLoadTime_editContentBoxRef =6
            const { width, height } = useElementSize(this.$refs[editContentBoxRefName]);
            this.editContentOptions.width = width;
            this.editContentOptions.height = height;
            setTimeout(()=>{
              // 初始化分栏 默认高度 
              this.splitpanesOptions.pane1H = this.editContentOptions.height * (this.splitpanesOptions.pane1P/100)
              this.splitpanesOptions.pane2H = this.editContentOptions.height * (this.splitpanesOptions.pane2P/100)
            },100)
          }else{
            if( _self.maxLoadTime_editContentBoxRef>0){
                setTimeout(()=>{
                    _self.maxLoadTime_editContentBoxRef =  _self.maxLoadTime_editContentBoxRef-1
                        this.initEditContentBoxWH()
                },300)
            }
          }
      },
         // 初始化数据 弹框高度/宽度
        initData(){
            this.initEditContentBoxWH("editContentBoxRef")
           // this.initEditContentBoxWH("editContentBoxDrawerRef")
        },
         // 拖拽分栏触发事件,重新分配高度
         resizedEvent(e){
            if(e && e.length>0){
                this.splitpanesOptions.pane1H = Number(e[0].size).toFixed(2)/100 * this.editContentOptions.height
                this.splitpanesOptions.pane2H = Number(e[1].size).toFixed(2)/100 * this.editContentOptions.height
            }
        },
          // 退出弹框时 清除缓存
          resetData(){
              this.paramsFromList.layoutLevel =1 // ！！重要：重置当前页面等级，否则区分主表编辑弹框
              this.initDefaultFormData()
          },
          // 设置需要提交的数据，主或从
          setSubmitData(){
            let responseData = this.paramsFromList.responseData
            let TableHeader = responseData.Datas.MainTable.TableHeader
            // 层级级别 1:主表，2:从表，3:孙表
            if(this.paramsFromList.layoutLevel==2){
                // 是否从表，默认主表
                //debugger
                TableHeader =  this.paramsFromList.subTableInfo.TableHeader
                let keyField = TableHeader.keyField// 从表 关联主表外键
                // ！！！重要： 设置从表与主表的外键关联，否则添加时 找不到新添加的数据
                this.formRenderConfig.formData[keyField] = this.paramsFromList.mainRow.CID 
            }
            
            let submitData ={
                TableDetail:[this.formRenderConfig.formData],
                TableHeader,
            }

            return submitData
          },
         // 获取提交数据
         getSubmitData(){
          let submitData =this.setSubmitData()
          // 添加
          let _url=this.config.virModule+"Data/Add"
          if(this.actionType_state.value == this.actionType.iisEdit){
            // 编辑
              _url=this.config.virModule+"Data/Update"
          }
          let params = submitData
           //request['post'](_url, params)
          request['post'](_url, params).then(res=>{
              //debugger
              this.$message({
                message: res.Content,
              type: 'success'
              });
            let _self = this
            setTimeout(()=>{
                _self.showEditBox = false
                let postParams ={
                    layoutLevel:this.paramsFromList.layoutLevel
                }
                _self.$emit("submitSuccess",postParams)
            },300)
            
          })
      },
        submitEvent() {
          this.$refs["formItemRenderRef"].$refs["originFormRef"].validate(async (valid) => {
              if (valid) {
                  //表单>>校验通过
                  this.getSubmitData()
                
              } else {
                  //表单>>校验不通过
                  return false;
              }
              });
        },
    }
}