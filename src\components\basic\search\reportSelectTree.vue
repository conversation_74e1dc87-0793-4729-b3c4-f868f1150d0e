<template>
    <el-select v-bind="$attrs"
         v-on="$listeners" :publicAttribute="publicAttribute" 
         v-model="fieldModel_selectTree" class="customform" size="mini"
        ref="reportSelectTreeRef" :clearable="true" @clear="clearFn"
         :filterable="true" :filter-method="filterMethod" 
        :allow-create="false" :automatic-dropdown="false"
        placeholder="请选择" @focus="handleFocusCustomEvent" 
        @blur="handleBlurCustomEvent" @change="changeEvent">
        <el-option style="display:none" v-for="item in options" :key="item.value" :label="item.label"
             :value="item.value">
        </el-option>
        <el-tree class="main-select-el-tree" ref="elTreeRef" :data="treeDataList" :node-key="selectConfig.key" highlight-current
            :props="defaultProps" @node-click="handleNodeClick" :current-node-key="currentNodeKeyValue"
            :expand-on-click-node="false" default-expand-all>
            <span class="custom-tree-node" slot-scope="{ node, data }">
                <!-- <i class=""></i> -->
                {{ data[selectConfig.text] }}
            </span>
        </el-tree>
    </el-select>
</template>
<script>
  import cloneDeep from "clone-deep"
import baseMixin from './minxin'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: 'reportSelectTree',
    mixins: [baseMixin],
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    components: {},
    props: {
        refreshRandom:{
            type:[Number,String],
            default:null
        }, // 刷新标记
        defaultValue:String, // 默认值
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem: Object, // rowItem 配置信息对象
        fieldName: {
            type: String,
            default: ""
        },
        // 查询配置
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            treeRowField: "", // 树结构子ID
            treeParentField: "", // 树结构父ID
            //optionItems:[],
            treeDataList: [],// 当前树列表数据
            copyTreeDataList:[],// 备份
            currentNodeKeyValue: "",//当前选中的节点
            // 配置选项
            defaultProps: {
                children: "children",
                label: "label",
            },
            fieldModel_selectTree: "",
            publicAttribute: {
                value: "",
                controlType:this.controlType
            },// 对外开发属性值
            useFormatParamsFn: useFormatParams(this),
            currentItem: null,
            configFormItem: null,
            loading: false,
            options: [],
            selectConfig: {
                key: "",
                text: ""
            },
        }
    },
    computed: {
        // add by andy
        pageInstance() {
          // 获取列表示例
          return this.getPageInstance()
        },
        isPassiveDataLoad() {
            // debugger
            let isPassiveDataLoad =false
            try {
                isPassiveDataLoad = this.configOptions.isPassiveDataLoad
            } catch (error) {
                isPassiveDataLoad =false
            }
            return isPassiveDataLoad
        }
    },
    watch:{
        fieldModel_selectTree(n,o){
            this.publicAttribute.value = n
        },
         // 是否刷新
         refreshRandom(n,o){
            if(n){
                if(!!this.isPassiveDataLoad){
                    this.handleFocusCustomEvent(true)
                }
                console.log("===reportSelect=====refreshRandom-======")
            }
        },
    },
    mounted() {
       setTimeout(() => {
         
            if(!this.isPassiveDataLoad){
                this.handleFocusCustomEvent()
            }
       }, 1000);
      if(this.defaultValue){
        // 设置默认值
        this.fieldModel_selectTree = this.defaultValue
      }
      this.publicAttribute.controlType ="reportSelectTree"
    },
    methods: {
        clearFn(){
            //debugger
            this.treeDataList = cloneDeep(this.copyTreeDataList)
        },
        filterMethod(query){
            if(query){
                clearTimeout(this.filterMethodTimer)
                this.filterMethodTimer = setTimeout(()=>{
                    this.filterDataList(query)
                },300)
            }else{
                this.treeDataList = cloneDeep(this.copyTreeDataList)
            }
          
        },
        filterDataList(query){
            this.treeDataList = cloneDeep(this.copyTreeDataList)
            let _dataFilter = this.treeDataList.filter(item=>{
                return item[this.selectConfig.text].indexOf(query)>-1
            })
            this.treeDataList = _dataFilter
        },
        handleNodeClick(node) {
            let currentNodeValue  = node[(this.selectConfig.key || 'value')] // 选中值
            let currentNodeLabel  = node[(this.selectConfig.text || 'label')] // 选中文本
            this.fieldModel_selectTree = currentNodeValue+""
            let reportSelectTreeRef = this.$refs["reportSelectTreeRef"]
            if(reportSelectTreeRef){
                // 关闭下拉框
                reportSelectTreeRef.blur();
            }
            // 直接赋值表单查询字段 的值
            //this.searchForm[this.fieldName] = this.fieldModel_selectTree
            this.publicAttribute.value = this.fieldModel_selectTree 
            //this.$set(this.searchInputForm, this.fieldName, this.fieldModel_selectTree)
            this.currentNodeKeyValue = this.fieldModel_selectTree
            console.log("currentNodeLabel:",currentNodeLabel)
            console.log("currentNodeKeyValue:",this.currentNodeKeyValue)
            let params = {
                    field: this.fieldName,
                    value: this.fieldModel_selectTree,
                    type: 'change'
                }
            this.$emit("changeEvent", params)
        },
       
        handleFocusCustomEvent() {
             //debugger
            if (this.options.length == 0) {
                if (this.configOptions) {
                    this.configFormItem = this.configOptions
                    if (this.configFormItem.hasOwnProperty('CDATAS') && !!this.configFormItem.CDATAS) {
                        this.getSelectKeyText(this.configFormItem.CDATAS)
                        console.log('this.selectConfig:', this.selectConfig)
                        // 树结构子ID CID 树结构父ID PID
                        try {
                            this.treeRowField = this.configFormItem.treeRowField
                            this.treeParentField = this.configFormItem.treeParentField
                        } catch (error) {

                        }
                        console.log('treeRowField:', this.treeRowField)
                        console.log('treeParentField:', this.treeParentField)
                    }
                }
                this.loadSelectOptions()
            }
            if(!this.fieldModel_selectTree){
                this.treeDataList = cloneDeep(this.copyTreeDataList)
            }
        },
        handleBlurCustomEvent() {

        },

        // 获取配置的KEY & TEXT
        getSelectKeyText(dataList) {
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (!!item.isSelectTextField) {
                        this.selectConfig.text = item.field
                    }
                    if (!!item.isSelectKeyField) {
                        this.selectConfig.key = item.field
                    }
                })
            } else {
                this.selectConfig = {
                    key: "",
                    text: ""
                }
            }
        },
        // 设置下拉初始化功能
        async loadSelectOptions() {
            this.loading = true
            let _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.configFormItem.actionName);
            if (_dataList && _dataList.length > 0) {
                this.options = this.formatDatas(_dataList)
                //debugger
                this.treeDataList = this.buildTree(_dataList, this.treeRowField, this.treeParentField)
                this.copyTreeDataList = cloneDeep(this.treeDataList)
            } else {
                this.options = [{ value: "", label: "全部" }]
            }

            this.loading = false
            setTimeout(() => {
                this.loading = false
            }, 10000)
        },
        // 格式化返回数据
        formatDatas(dataList) {
            let options = [{ value: "", label: "全部" }]
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    if (!!oldItem[this.selectConfig.key]) {
                        let newItem = {
                            value: oldItem[this.selectConfig.key]+"",
                            label: oldItem[this.selectConfig.text]
                        }
                        options.push(newItem)
                    }

                })
            }
            return options
        },
      
        buildTree(dataList, treeRowField, treeParentField) {
           // debugger
            let map = {};
            dataList.forEach((node) => {
                map[node[treeRowField]] = node;
                node.children = [];
            });
            let roots = [];
            dataList.forEach((node) => {
                if (!!node[treeParentField]) {
                    map[node[treeParentField]].children.push(node);
                } else {
                    roots.push(node);
                }
            });
            return roots;
        }

    }
}
</script>


<style lang="scss" scoped>
.main-select-el-tree {
    .custom-tree-node {
        font-size: 14px;
    }
}

::v-deep .customform {
    .el-tag--info {
        width: auto;
        height: 24px;
        color: #1989FA;
        border-radius: 2px;
        border: 1px solid #1989FA;
        background: rgba(65, 158, 251, 0.10);

        .el-tag__close {
            font-size: 16px;
            color: #1989FA;
            background: none;

            &:hover {
                color: #1989FA;
                background: none;
            }
        }
    }
}
</style>