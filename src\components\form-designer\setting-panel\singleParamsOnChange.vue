<template>
  <div class="singleParamsOnChange-option-items-pane">
    <el-form-item label="启用汇总功能">
      <el-switch v-model="optionModel.enableSumDataToMainTable"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableSumDataToMainTable" label="">
      <span slot="label">选择被汇总表
        <el-tooltip effect="light" content="选择主表唯一名称">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input placeholder="点击选择" suffix-icon="el-icon-plus" @click.native="showEditDialogEvent()" readonly
        v-model="optionModel.sumDataToMainTableName" size="mini" style="width: 150px"></el-input>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableSumDataToMainTable" label="">
      <span slot="label">选择被汇总表API
        <el-tooltip effect="light" content="选择加载主表数据的API接口">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-select @change="dataSourceChangeEvent" v-model="optionModel.toSumDataActionName" placeholder="请选择">
        <el-option value="">请选择</el-option>
        <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
          :label="queryItem.label" :value="queryItem.value"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableSumDataToMainTable" label="">
      <span slot="label">获取被汇总表模型
        <el-tooltip effect="light" content="预加载后，方便选择主表模型字段【一次性使用，下次还需要点击加载】">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-button :loading="isLoadingTableModel" @click="loadTableModel()" type="success">预加载</el-button>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableSumDataToMainTable" label-width="0">
      <el-divider class="custom-divider">字段对应列表
        <el-tooltip effect="light" content="从表字段汇总到主表字段 一一对应，可以选择多个">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item v-show="!!optionModel.enableSumDataToMainTable" label-width="0">
      <div class="flex justify-between">
        <div style="font-size: 13px;">汇总字段</div>
        <div></div>
        <div> <el-button style="height:32px;" @click="addSumOption" type="text" icon="vxe-icon-add"
            size="mini">&nbsp;添加</el-button></div>
      </div>
    </el-form-item>
    <draggable v-show="!!optionModel.enableSumDataToMainTable" tag="ul" :list="optionModel.sumTableKeyValueList"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(optionItem, idx) in optionModel.sumTableKeyValueList" :key="idx">

        <div class="flex">
          <el-select size="mini" v-model="optionItem.key" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in tableColumnsList"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
          <div>to</div>
          <el-select size="mini" v-model="optionItem.value" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in sunTableColumnsList"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
          <el-button circle plain size="mini" type="danger" @click="deleteSumOption(optionItem, idx)" icon="el-icon-minus"
            class="col-delete-button"></el-button>
        </div>
      </li>
    </draggable>
    <el-dialog title="触发控件 选择" :visible.sync="showFlag" v-if="showFlag" v-dialog-drag append-to-body :show-close="true"
      custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <div>

        <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
          highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
      </div>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" size="large" @click="submitNodeEvent()">
          确定</el-button><el-button size="large" @click="showFlag = false">
          取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>

import Draggable from 'vuedraggable'
import cloneDeep from "clone-deep"
import i18n from "@/utils/i18n"
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
import { useFormatParams } from "@/hooks/useFormatParams"
import request from '@/libs/request'
export default {
  name: "singleParamsOnChange", //参数改变触发控件
  mixins: [i18n, propertyMixin],
  props: {
    ctrTitle: "触发控件列表",
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
    // 当前表格列头模型数据
    tableColumnsList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  components: {
    Draggable,
  },
  data() {
    return {
      useFormatParamsFn: useFormatParams(this),
      isLoadingTableModel: false,
      toSumDataActionName: "",
      showFlag: false,
      nodeCtrlValue: "",
      loadingTableSubmit: false,
      currentEditOption: null,
      sunTableColumnsList: []
    }
  },
  methods: {
    // 添加汇总字段
    addSumOption() {
      // 先校验是否已经存在此元素，否则初始化
      if (!this.optionModel.hasOwnProperty('sumTableKeyValueList')) {
        this.$set(this.optionModel, "sumTableKeyValueList", [])
      }
      this.optionModel.sumTableKeyValueList.push(
        { key: '', value: "", }
      )
    },
    // 移除汇总字段
    deleteSumOption(option, index) {
      // 是否可以移除
      this.optionModel.sumTableKeyValueList.splice(index, 1)
    },
    async loadTableModel() {
      let toSumDataActionName = this.optionModel.toSumDataActionName
      let datasetId = this.useFormatParamsFn.getVFormDataSetID(toSumDataActionName);
      let dataModel = await this.getDataModelList(datasetId)
      this.sunTableColumnsList = this.formatModelFields(dataModel)
    },
    // 获取数据源数据
    async getDataModelList(datasetId) {
      let dataModelList = []
      let params = {
        condition: "",
        datasetId: datasetId
      }
      //let _url = "api/MD/DataSetModel/GetAll"
      //let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })

      return dataModelList
    },
    // 格式化实体字段
    formatModelFields(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: !!oldItem.CCOLUMN_DESC ? oldItem.CCOLUMN_DESC : oldItem.CCOLUMN_NAME,
            CID: oldItem.CID,
            fieldDefault: oldItem.CCOLUMN_DEFAULT,
          }
          newDataList.push(newItem)
        })
      }
      return newDataList

    },
    dataSourceChangeEvent() {

    },
    showEditDialogEvent() {
      this.showFlag = true
    },


    submitNodeEvent() {
      this.optionModel.sumDataToMainTableName = this.nodeCtrlValue
      this.showFlag = false
    },
    onNodeTreeClick(params) {
      this.nodeCtrlValue = ""
      let hasChildren = params.hasOwnProperty("children")
      let ctrlValue = params.label
      if (!hasChildren) {
        this.nodeCtrlValue = ctrlValue
      } else {
        this.$message.warning('此节点不可选！')
      }
    },
    // 获取数据源控件列表
    getNodeTreeData() {

      let dataList = cloneDeep(this.designer.nodeTreeData)
      return dataList
    },
  }
}
</script>
<style lang="scss" scoped>
.singleParamsOnChange-option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}

.node-tree ::v-deep {
  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 12px;
  }

  .el-tree-node__content {
    padding-left: 0 !important;
  }

  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 12px;
    overflow: visible !important;
    /* 加入此行让el-tree宽度自动撑开，超出宽度el-draw自动出现水平滚动条！ */
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree>.el-tree-node:before {
    border-left: none;
  }

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: "";
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: "";
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px dashed #4386c6;
    bottom: 0px;
    height: 100%;
    top: -10px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px dashed #4386c6;
    height: 20px;
    top: 12px;
    width: 16px;
  }

  .el-tree-node.is-current>.el-tree-node__content {
    background: #c2d6ea !important;
  }

  .el-tree-node__expand-icon {
    margin-left: -3px;
    padding: 6px 6px 6px 0px;
    font-size: 16px;
  }

}
</style>
    
  