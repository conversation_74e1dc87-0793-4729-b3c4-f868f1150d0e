<template>
    <div>
      <el-form-item  label="内边框padding">
          <el-input-number v-model="optionModel.padding" :min="0" ></el-input-number>
      </el-form-item>
      <el-form-item label="显示靠右">
        <el-switch v-model="optionModel.showMenuRight"></el-switch>
      </el-form-item>
      <el-form-item  label="高度【0自动】">
          <el-input-number v-model="optionModel.height" :min="0" ></el-input-number>
      </el-form-item>
      <el-form-item label-width="0">
            <commonParamsOnChange contrlType="colorBlockInfo" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
      <el-form-item label-width="0">
            <selectDataSourceApi contrlType="colorBlockInfo" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></selectDataSourceApi>
        </el-form-item>
        <el-form-item label-width="0">
      <el-divider class="custom-divider">其它信息项</el-divider>
    </el-form-item>
      <el-form-item  label="按行顺序显示">
          <span slot="label">按行顺序显示
            <el-tooltip effect="light" content="默认按列顺序显示,取数组的第一个显示，需要逐个选择不同的字段,如果设置为按行顺序显示，则显示字段选择相同">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-switch v-model="optionModel.readByRow"></el-switch>
      </el-form-item>
      <el-form-item  label="显示传值">
          <span slot="label">显示传值
            <el-tooltip effect="light" content="默认不显示传值，可控制是否显示传值字段">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-switch v-model="optionModel.showValueField"></el-switch>
      </el-form-item>
      
    <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <colorBlockInfoItemList :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></colorBlockInfoItemList>
      </el-form-item>
    </div>
  </template>
  
  <script>
   import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
    import i18n from "@/utils/i18n"
    import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
    import colorBlockInfoItemList from './colorBlockInfoItemList.vue'
    export default {
      name: "colorBlockInfo-editor",
      mixins: [i18n],
      components:{selectDataSourceApi,colorBlockInfoItemList,commonParamsOnChange},
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
    }
  </script>
  
  <style scoped>

  </style>
  