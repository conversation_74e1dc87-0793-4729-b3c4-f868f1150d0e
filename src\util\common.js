import { MD5Crypto} from './md5'
//md5加密
export function md5(parms) {
  return MD5Crypto(parms).substr(8, 16)
}
// 判断是否勾选自动登录
export function isAutoLogin(data){
    console.log(data);
    if(data.check.length>0){
       if(data.check.includes('Password')){
           RememberPassword(data)
           return
       } 
       if(data.check.includes('User')){
        window.localStorage.removeItem('form');
            RememberUser(data)
          return
       }
       window.localStorage.removeItem('user');
    }else{
        window.localStorage.removeItem('form');
        window.localStorage.removeItem('user');
    }

}
// 记住密码
export function RememberPassword(data){
    window.localStorage.setItem('form', JSON.stringify(data)); 
 }
// 记住用户名
export function RememberUser(data){
    window.localStorage.setItem('user', JSON.stringify({check:data.check,UserName:data.UserName,Enterprise:data.Enterprise})); 
}
// // 自动登录
// export function autoLogin(data){
  

// }
/**
 * 浏览器判断是否全屏
 */
 export const fullscreenToggel = () => {
    if (fullscreenEnable()) {
      exitFullScreen();
    } else {
      reqFullScreen();
    }
  };

/**
 * 浏览器判断是否全屏
 */
 export const fullscreenEnable = () => {
    var isFullscreen = document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen
    return isFullscreen;
  }
  
  /**
   * 浏览器全屏
   */
  export const reqFullScreen = () => {
    if (document.documentElement.requestFullScreen) {
      document.documentElement.requestFullScreen();
    } else if (document.documentElement.webkitRequestFullScreen) {
      document.documentElement.webkitRequestFullScreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.documentElement.mozRequestFullScreen();
    }
  };
  /**
   * 浏览器退出全屏
   */
  export const exitFullScreen = () => {
    if (document.documentElement.requestFullScreen) {
      document.exitFullScreen();
    } else if (document.documentElement.webkitRequestFullScreen) {
      document.webkitCancelFullScreen();
    } else if (document.documentElement.mozRequestFullScreen) {
      document.mozCancelFullScreen();
    }
  };
  /**
 * 设置主题
 */
export const setTheme = (name) => {
    document.body.className = name;
  }
   /**
 * 递归遍历数据
 */

     
export const  traversalMeun = function(data) {
    let index = 0;
    const mapTree = (org) => {
      const haveChildren = Array.isArray(org.Children) && org.Children.length > 0;
      index++;
      return {
        ...org,
        sortLabel: index,
        Children: haveChildren ? org.Children.map(child => mapTree(child)) : null,
      };
    };
    const list = data.map(org => mapTree(org));
    console.log(list);
    document.getElementById('123')
}

