<template>
    <div class="flex w-full justify-between">
        <div class="flex justify-start contails1">
            <el-button style="height:32px;" @click="actionBtn('iisAdd')" type="primary" icon="vxe-icon-add"
                size="mini">新建</el-button>
            <el-button style="height:32px;" @click="actionBtn('iisEdit')" type="success" icon="vxe-icon-edit"
                size="mini">编辑</el-button>
            <el-button @click="actionBtn('iisDel')" type="danger" icon="vxe-icon-edit" size="small">删除</el-button>
            <el-upload style="margin: 0 10px;" :show-file-list="false" :httpRequest="jsonData_importFn" action=""
                ref="upload" accept=".json">
                <el-button @click="" icon="el-icon-upload2" type="primary" size="small">导入</el-button>

            </el-upload>
            <vxe-button size="medium" style="margin-right:2px;" transfer>
                <template #default>更多</template>
                <template #dropdowns>
                    <vxe-button @click="actionBtn('jsonData_export')" icon="el-icon-download"   type="text" content="导出"></vxe-button>
                    <vxe-button @click="actionBtn('tongbu')" icon="el-icon-tickets"  type="text" content="同步"></vxe-button>
                </template>
            </vxe-button>
            <!-- <el-button @click="actionBtn('jsonData_export')" icon="el-icon-download" type="primary"
                size="small">导出</el-button>

            <el-button @click="actionBtn('tongbu')" icon="el-icon-tickets" type="primary" size="small">
                同步</el-button> -->

            <!-- <el-button  @click="actionPushView()" type="info" icon="vxe-icon-info-circle" size="small">预览选择行</el-button>
              <el-button  @click="testAction()" type="info" icon="vxe-icon-info-circle" size="small">测试查询</el-button>
              <el-button  @click="jumpToPath('/table')" type="success"  size="small">返回>>表单页</el-button>
               <el-button  @click="testAction()" type="success"  size="small">testA</el-button> -->
        </div>
        <!-- 通用查询 -->
        <commonSearchVue></commonSearchVue>
    </div>
</template>
<script>
// import axios from 'axios'
import request from '@/libs/request'
import commonSearchVue from './commonSearch.vue'
const { actionType } = require('@/enum/enumData')
export default {
    // 操作按钮列表
    name: "actionButtons",
    components: { commonSearchVue },
    // 注入额外的配置信息
    inject: {
        // 获取列表页面整体实体
        getPageInstance: {
            default: () => { }
        }
    },
    data() {
        return {

            commonTop3SearchFields: [],// 当前表格前3个需要（普通）查询列头字段
            actionType: actionType
        }
    },
    // 路由进入之前
    beforeRouteUpdate(to, from, next) {
        // 在渲染该组件的对应路由被 confirm 前调用
        // 不！能！获取组件实例 `this`
        // 因为当守卫执行前，组件实例还没被创建
        next(vm => {
            // beforeRouteEnter不能通过this访问组件实例，但是可以通过 vm 访问组件实例
            //vm.getCurrentTableColumns()
            console.log('====action =beforeRouteEnter=====')
        })
    },
    methods: {
        jsonData_importFn(file) {
            let _self = this
            const reader = new FileReader();
            // 异步处理文件数据
            reader.readAsText(file.file, 'UTF-8');
            // 处理完成后立马触发 onload
            reader.onload = fileReader => {
                const fileData = fileReader.target.result;
                if (fileData) {
                    let params = JSON.parse(fileData)
                    let _url = `/api/MD/VisualFormDesigner/Import`
                    request['post'](_url, params).then(res => {
                        if (!!res.Success) {
                            _self.$message({
                                message: '导入成功！',
                                type: 'success'
                            });
                            setTimeout(() => {
                                _self.actionBtn("jsonData_import_Refresh")
                            }, 300)
                        } else {
                            _self.$message.error("导入失败！")
                        }
                    })
                }
                // 上面的两个输出相同
            };
        },
        // actionBtn(type,row){

        // },
        designFormNow() {

        },
        // 路由跳转
        jumpToPath(path) {
            this.$router.push(
                {
                    path: path,
                    // query:{
                    //     formName 
                    // }
                })
        },
        injectFn() {
            let $pageInstance = this.getPageInstance()
            // let tt = $pageInstance.helloworld
            // debugger
        },
        // 当前主表选中行
        currentRow_get() {
            let routerName = this.$route.name
            return this.$store.state.currentRow.value[routerName];
        },
        // 当前主表选中行>>查询子表详情
        currentRowDetail_get() {
            let routerName = this.$route.name
            return this.$store.state.currentRowDetail.value[routerName];
        },
        //从缓存或重新加载表格列头信息
        // getCurrentTableColumns(){
        //     //debugger
        //     let curColumns = this.currentRowDetail_get()
        //     this.setTop3CommonSearchFields(curColumns)
        // },
        // 设置前三个需要普通查询的字段
        // setTop3CommonSearchFields(columns){
        //    this.commonTop3SearchFields =[]
        //    for(let i =0;i<columns.length;i++){
        //         let column = columns[i]
        //         if(this.commonTop3SearchFields.length<3){
        //             if(!!column.iisQuery){
        //                this.commonTop3SearchFields.push(column)
        //             }
        //         }else{
        //             // 超过三个后 跳出
        //             break
        //         }

        //    }
        // },
        // 统一方法调度 执行 
        actionBtn(type = this.actionType.iisAdd) {
            //debugger
            this.$store.commit("set_actionType", type)
            this.$emit("actionChange", type)
        },
        // 路由跳转+参数 （根据不同参数获取不同的表单和数据）
        actionPushView(formName) {
            let rowInfo = this.currentRow_get()
            // debugger
            this.$router.push(
                {
                    path: '/viewdetail',
                    query: {
                        formName: rowInfo.CID
                    }
                })
        },
        // 当前主表选中行>>查询子表详情
        currentRowDetail_get() {
            let routerName = "table"
            return this.$store.state.currentRowDetail.value[routerName];
        },
    }
}
</script>
<style lang="scss" scoped>
.contails1 .el-button {
    height: 2rem;
}
</style>