<template>
  <div>
    <el-form :model="formConfig" size="mini" label-position="left" label-width="120px" class="setting-form"
      @submit.native.prevent>
      <el-collapse v-model="formActiveCollapseNames" class="setting-collapse">
        <el-collapse-item name="1" :title="i18nt('designer.setting.basicSetting')">
          <el-form-item v-if="showResetData_parentPageNAME" label="弹框父类">
            <el-input disabled v-show="false" style="width:205px;" v-model="formConfig.parentPageCID"></el-input>
            <el-input disabled style="width:205px;" v-model="formConfig.parentPageNAME"></el-input>
            <el-button type="text" @click="selectPopupBox()">+ 选择弹框父类</el-button>
            <el-button type="text" @click="reset_parentPageCID()"> 清除</el-button>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formSize')">
            <el-select v-model="formConfig.size">
              <el-option v-for="item in formSizes" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelPosition')">
            <el-radio-group v-model="formConfig.labelPosition" class="radio-group-custom">
              <el-radio-button label="left">{{ i18nt('designer.setting.leftPosition') }}</el-radio-button>
              <el-radio-button label="top">{{ i18nt('designer.setting.topPosition') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelAlign')">
            <el-radio-group v-model="formConfig.labelAlign" class="radio-group-custom">
              <el-radio-button label="label-left-align">{{ i18nt('designer.setting.leftAlign') }}</el-radio-button>
              <el-radio-button label="label-center-align">{{ i18nt('designer.setting.centerAlign') }}</el-radio-button>
              <el-radio-button label="label-right-align">{{ i18nt('designer.setting.rightAlign') }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelWidth')">
            <el-input-number v-model="formConfig.labelWidth" :min="0" style="width: 100%"></el-input-number>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formCss')">
            <el-button type="info" icon="el-icon-edit" plain round
              @click="editFormCss">{{ i18nt('designer.setting.addCss') }}</el-button>
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.customClass')">
            <el-select v-model="formConfig.customClass" multiple filterable allow-create default-first-option>
              <el-option v-for="(item, idx) in cssClassList" :key="idx" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.globalFunctions')">
            <el-button type="info" icon="el-icon-edit" plain round
              @click="editGlobalFunctions">{{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <el-form-item label-width="0">
            <el-divider class="custom-divider">表单实体类</el-divider>
          </el-form-item>
          <el-form-item label="实体类编辑">
            <el-button type="primary" plain round
              @click="openSetting">{{ i18nt('designer.setting.editAction') }}</el-button>
          </el-form-item>
          <el-form-item label="新增API">
            <el-select @change="changeAdd" v-model="postAddModelUrl" placeholder="请选择新增API">
              <el-option value="">请选择</el-option>
              <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
                :label="queryItem.label" :value="queryItem.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="编辑API">
            <el-select @change="changeEdit" v-model="postModelUrl" placeholder="请选择编辑API">
              <el-option value="">请选择</el-option>
              <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
                :label="queryItem.label" :value="queryItem.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <span slot="label">不区分功能API
            <el-tooltip effect="light" content="如果使用此API，其它（新增，编辑）API 不再生效">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
            <el-select @change="changeExecuteApi" v-model="executeApiUrl" placeholder="请选择统一API">
              <el-option value="">请选择</el-option>
              <el-option :key="queryIndex + queryItem.value" v-for="(queryItem, queryIndex) in designer.formConfig.queryList"
                :label="queryItem.label" :value="queryItem.value"></el-option>
            </el-select>
          </el-form-item>
          <!--  <el-form-item label="执行API">
              <el-button type="success">执行</el-button>
          </el-form-item>
          <el-form-item label-width="50px" label="元数据">
            <el-table
              :data="[]"
              style="width: 100%">
              <el-table-column
                prop="date"
                label="字段"
                width="100">
              </el-table-column>
              <el-table-column
                prop="name"
                label="描述"
                width="100">
              </el-table-column>
            
            </el-table>
          </el-form-item> -->
          <!-- <el-form-item label-width="0">
            <el-divider class="custom-divider">{{ i18nt('designer.setting.formSFCSetting') }}</el-divider>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formModelName')">
            <el-input type="text" disabled v-model="formConfig.modelName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRefName')">
            <el-input type="text" disabled v-model="formConfig.refName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRulesName')">
            <el-input type="text" disabled v-model="formConfig.rulesName"></el-input>
          </el-form-item> -->
        </el-collapse-item>

        <el-collapse-item v-if="showEventCollapse()" name="2" :title="i18nt('designer.setting.eventSetting')">
          <el-form-item label="onBeforeSubmit" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onBeforeSubmit')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <!-- <el-form-item label="onBeforeDisabled" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onBeforeDisabled')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item> -->
          <el-form-item label="onBeforeSubmitValidate" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onBeforeSubmitValidate')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <el-form-item label="onFormCreated" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onFormCreated')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <el-form-item label="onFormMounted" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onFormMounted')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <!-- -->
          <el-form-item label="onFormDataChange" label-width="150px">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onFormDataChange')">
              {{ i18nt('designer.setting.addEventHandler') }}</el-button>
          </el-form-item>
          <!-- -->
          <!--
          <el-form-item label="onFormValidate">
            <el-button type="info" icon="el-icon-edit" plain round @click="editFormEventHandler('onFormValidate')">
              {{i18nt('designer.setting.addEventHandler')}}</el-button>
          </el-form-item>
          -->
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <el-dialog :title="i18nt('designer.setting.editFormEventHandler')" :visible.sync="showFormEventDialogFlag"
      v-if="showFormEventDialogFlag" :show-close="true" class="small-padding-dialog" v-dialog-drag append-to-body
      :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">

      <demoOfJSEditCode></demoOfJSEditCode>
      <el-alert type="info" :closable="false" :title="'form.' + eventParamsMap[curEventName]"></el-alert>
      <code-editor :mode="'javascript'" :readonly="false" v-model="formEventHandlerCode" ref="ecEditor"></code-editor>
      <el-alert type="info" :closable="false" title="}"></el-alert>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showFormEventDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button>
        <el-button type="primary" @click="saveFormEventHandler">
          {{ i18nt('designer.hint.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="i18nt('designer.setting.formCss')" :visible.sync="showEditFormCssDialogFlag"
      v-if="showEditFormCssDialogFlag" :show-close="true" class="small-padding-dialog" v-dialog-drag append-to-body
      :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <demoOfJSEditCode></demoOfJSEditCode>
      <code-editor :mode="'css'" :readonly="false" v-model="formCssCode"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showEditFormCssDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button>
        <el-button type="primary" @click="saveFormCss">
          {{ i18nt('designer.hint.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="i18nt('designer.setting.globalFunctions')" :visible.sync="showEditFunctionsDialogFlag"
      v-if="showEditFunctionsDialogFlag" :show-close="true" class="small-padding-dialog" v-dialog-drag append-to-body
      :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <demoOfJSEditCode></demoOfJSEditCode>
      <code-editor :mode="'javascript'" :readonly="false" v-model="functionsCode" ref="gfEditor"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showEditFunctionsDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button>
        <el-button type="primary" @click="saveGlobalFunctions">
          {{ i18nt('designer.hint.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="应用列表选择【双击选择】" v-dialog-drag :visible.sync="showPopupBoxFlag" v-if="showPopupBoxFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <div slot="title">
        <div>
          应用列表选择【双击选择】
          <el-tooltip content="选择需要弹框的页面" effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
      </div>
      <!-- 弹框表格 -->
      <nvxeGrid ref="mainTableRef" @cell-dblclick="cellDblclickEvent" :requstConfig="requstConfig"
        :columns="gridOptions.columns" height="350px">
        <template #top>
          <div style="margin-bottom:5px" class="flex">
            <el-input :clearable="true" placeholder="功能名称" style="width:305px;margin-right:5px;"
              v-model="tableDataSearchKey"></el-input>
            <el-button size="mini" type="primary" @click="tableDataSearchEvent()">查询</el-button>
            <el-button size="mini" type="primary" @click="resetTableDataSearchEvent()">重置</el-button>
          </div>
        </template>
      </nvxeGrid>


      <div slot="footer" class="dialog-footer">
        <!-- <el-button size="large" type="primary" @click="submitSelectFunction()">{{ i18nt('designer.hint.confirm')
          }}</el-button> -->
        <el-button size="large" type="" @click="showPopupBoxFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
      </div>
    </el-dialog>
    <!-- 实体类编辑 弹框-->
    <!-- <el-dialog title="实体类编辑" :visible.sync="dialogVisible" v-if="dialogVisible" :show-close="true"
      class="small-padding-dialog" v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false"
      append-to-body :destroy-on-close="true" width="1220px">
      <el-form size="mini" label-position="left" label-width="120px" class="setting-form" @submit.native.prevent>
        <el-row :gutter="8">
          <el-col :span="6">
            <el-form-item label="选择加载API">
              <el-select @change="change_actionName" v-model="actionName" placeholder="请选择查询">
                <el-option value="">请选择</el-option>
                <el-option :key="queryIndex + queryItem.value"
                  v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                  :value="queryItem.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="">
              <el-button :loading="loadingSubmit" @click="loadColsData()" type="success">开始加载</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="">
              <el-button @click="removeAllCols()" type="danger">移除所有</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-container>
          <el-aside width="200px">
            <div style="height:30px;margin-top: 1px;" class="flex justify-center items-center w-full ">
              <div style="width:100px;"></div>
              <div style="width:100px;font-size:12px;font-weight:  bold;">字段名</div>
              <div style="width:100px;"></div>
            </div>
            <div class="option-items-pane">
              <draggable tag="ul" :list="designer.formConfig.formItems"
                v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
                <li style="margin-bottom:2px;" v-for="(option, idx) in designer.formConfig.formItems" :key="idx">
                  <i class="iconfont icon-drag drag-option"></i>
                  <el-input v-model="option.field" size="mini" style="width: 120px"></el-input>

                  <el-button v-show="!option.type" style="margin-left:10px;" circle plain size="mini" type="danger"
                    @click="deleteOption(option, idx)" icon="el-icon-minus" class="col-delete-button"></el-button>
                </li>
              </draggable>
            </div>
          </el-aside>
          <el-main>
            <el-table :data="designer.formConfig.formItems" style="width: 95%" :cell-style="{ padding: '1px 0' }" border
              ref="singleTable" stripe>


              <el-table-column label="中文描述" width="150" prop="title">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.title"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="标题帮助" width="150" prop="titleHelp">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.titleHelp"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="默认值" width="150" prop="fieldDefault">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.fieldDefault"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="必填？" width="66" prop="iisRequired">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini"
                    v-model="scope.row.iisRequired"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="列表显示？" width="106" prop="iisShowList">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini"
                    v-model="scope.row.iisShowList"></el-switch>
                </template>
              </el-table-column>
              <el-table-column :label="i18nt('designer.setting.columnWidth')" width="100" prop="width">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.width"></el-input>
                </template>
              </el-table-column>

              <el-table-column label="标题宽" width="100" prop="labelWidth">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.labelWidth"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="表头对齐" width="100" prop="titleAlign">
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.titleAlign">
                    <el-option value="left">left</el-option>
                    <el-option value="center">center</el-option>
                    <el-option value="right">right</el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="表格对齐" width="100" prop="align">
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.align">
                    <el-option value="left">left</el-option>
                    <el-option value="center">center</el-option>
                    <el-option value="right">right</el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="控件类型" width="100" prop="controlType">
                <template slot-scope="scope">
                  <el-select size="mini" v-model="scope.row.controlType">
                    <el-option :key="index" v-for="(itemControl, index) in controlTypeList"
                      :value="itemControl.value">{{ itemControl.label }}</el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="数据源key" width="100" prop="sourceKey">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.sourceKey"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="字段转换" width="100" prop="matchField">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.matchField"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="过滤参数" width="100" prop="searchParams">
                <template slot-scope="scope">
                  <el-input size="mini" v-model="scope.row.searchParams"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="普通查？" width="86" prop="iisQuery">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini" v-model="scope.row.iisQuery"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="高级查？" width="86" prop="iisHQuery">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini" v-model="scope.row.iisHQuery"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="汇总？" width="66" prop="iisSummary">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini" v-model="scope.row.iisSummary"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="只读？" width="66" prop="iisReadOnly">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini"
                    v-model="scope.row.iisReadOnly"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="排序？" width="66" prop="iisSortable">
                <template slot-scope="scope">
                  <el-switch :active-value="1" :inactive-value="0" size="mini"
                    v-model="scope.row.iisSortable"></el-switch>
                </template>
              </el-table-column>

            </el-table>
          </el-main>
        </el-container>
        <el-row :gutter="0">
          <el-col :span="4">
            <el-button type="primary" size="small" icon="el-icon-plus" plain round @click="addNewField">添加新字段</el-button>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" type="primary" @click="colSubmit">{{i18nt('designer.hint.confirm')}}</el-button>
          <el-button size="large" @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog> -->
    <vxe-modal width="90%"  title="实体类编辑" destroy-on-close :position="{ top: 20 }" v-model="dialogVisible"
      :show-footer="true">
      <div class="flex ">
        <div class="flex justify-start items-center ml-5">
          <div>选择加载API</div>
          <div class="ml-2">
            <el-select @change="change_actionName" style="width:168px" v-model="actionName" placeholder="请选择查询">
              <el-option label="请选择" value=""></el-option>
              <el-option :key="queryIndex + queryItem.value"
                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                :value="queryItem.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex justify-start items-center ml-5">
          <el-button :loading="loadingSubmit" @click="loadColsData()" type="success">开始加载</el-button>
          <el-button @click="removeAllCols()" type="danger">移除所有</el-button>
        </div>
      </div>
      <el-skeleton :loading="loading_container" animated>
        <div class="mt-5">
          <vxe-grid  border
           :max-height="500"
            resizable keep-source size="mini"
            :show-overflow="true" :show-header-overflow="true" ref="editFormGrid"
            :loading="loading_container" :checkbox-config="{
                highlight: true,
                range: false,
                strict: true,
            }" 
            :columns="editGridOptions.columns" :data="designer.formConfig.formItems"
            :edit-rules="editGridOptions.validRules"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true}">
            <template v-slot:operate="{ row,...restItem }">
                  <el-button size="mini"
                      @click="deleteOption(restItem, row)"
                      type="text">删除</el-button>
                  <!-- <el-button size="mini"
                      @click="rowGridOperateAction(restItem, row)"
                      type="text"><span
                          :style="[{ color: !!row.fieldRules ? `green` : `` }]">{{
                              !!row.fieldRules ? `编辑规则` : `添加规则` }}</span></el-button> -->
              </template>
          <template #controlConfigSlots="{ row }">
                  <el-button @click="showCtrlConfigEvent(row)" size="mini" type="text">
                    <span :style="[{ color: (!!row.controlConfig && Object.keys(row.controlConfig).length>0) ? `green` : `` }]">{{ (!!row.controlConfig && Object.keys(row.controlConfig).length>0)?
                      `编辑配置` : `添加配置` }}</span>
                  </el-button>
                </template>

                <template v-slot:dragColumn="{ row }">
                    <span style="cursor: move !important;" class="drag-btn">
                        <i class="vxe-icon-sort"></i>
                    </span>
                </template>
        </vxe-grid>
        </div>
     
      </el-skeleton>

      <!-- 重写footer 并且默认值 -->
      <template v-slot:footer>
        <div class="flex justify-between">
          <div>
            <el-button class="addPrimaryFieldBtn" type="primary" size="small" icon="el-icon-plus" plain round @click="addNewField">添加新字段</el-button>
          </div>
          <div>

          </div>
          <div>
            <el-button @click="dialogVisible = false">取消</el-button>
            <!-- <el-button @click="colSubmit()" type="primary">确定</el-button> -->
          </div>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import Sortable from '@/libs/Sortable.min.js'
import demoOfJSEditCode from '@/components/form-designer/setting-panel/demoOfJSEditCode.vue'
import { 
  alignList,
    controlTypeList } from '@/enum/enumData'
import Draggable from 'vuedraggable'
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
import i18n from "@/utils/i18n"
import CodeEditor from '@/components/code-editor/index'
import { deepClone, generateId, insertCustomCssToHead, insertGlobalFunctionsToHtml } from "@/utils/util"
export default {
  name: "form-setting",
  mixins: [i18n],
  components: {
    CodeEditor,
    Draggable,
    demoOfJSEditCode,
  },
  props: {
    designer: Object,
    formConfig: Object,
  },
  inject: ['getDesignerConfig'],
  data() {
    return {
      showResetData_parentPageNAME:true,
      showPopupBoxFlag:false,
      loading_container:false,
      controlTypeList: controlTypeList,//控件类型
      actionName: "",// 实体加载路径
      postModelUrl: "",//编辑时候API
      executeApiUrl:"",// 不区分功能API
      postAddModelUrl:"",// 新增时候API
      dialogVisible: false,// 实体类弹框编辑
      designerConfig: this.getDesignerConfig(),
      loadingSubmit: false,
      useFormatParamsFn: useFormatParams(this),

      formActiveCollapseNames: ['1', '2'],
      // 主表查询配置
      requstConfig: {
        postUrl: `api/MD/VisualFormDesigner/`,
        actionName: "GetAll",
        postType: "get",
        postParams: {
          //?condition=&start=1&length=20
        },
      },
      // 主表配置
      gridOptions: {
        columns: [{
          type: "seq",
          fixed: "left",
          title: "序号",
          width: 50,
        },
        {
          field: "CNAME",
          title: "功能名称",
          width: "220",
        },
        {
          field: "CCODE",
          title: "功能编码",
          width: "220",
        },

        {
          field: "CDESC",
          title: "功能说明",
          //width: "220",
        },

        ],
        data: [],

      },
      formSizes: [
        { label: 'default', value: '' },
        { label: 'large', value: 'large' },
        { label: 'medium', value: 'medium' },
        { label: 'small', value: 'small' },
        { label: 'mini', value: 'mini' },
      ],

      showEditFormCssDialogFlag: false,
      formCssCode: '',
      cssClassList: [],

      showEditFunctionsDialogFlag: false,
      functionsCode: '',

      showFormEventDialogFlag: false,
      formEventHandlerCode: '',
      curEventName: '',

      eventParamsMap: {
        'onBeforeSubmit': 'onBeforeSubmit(postParams) {',
        'onBeforeSubmitValidate': 'onBeforeSubmitValidate(postParams) {',
        'onBeforeDisabled': 'onBeforeDisabled(postParams) {',
        'onFormCreated': 'onFormCreated() {',
        'onFormMounted': 'onFormMounted() {',
        'onFormDataChange': 'onFormDataChange(fieldName, newValue, oldValue, formModel, subFormName, subFormRowIndex) {',
        //'onFormValidate':     'onFormValidate() {',
      },
      testTableData: [],
      editGridOptions:{
        columns: [
         {
            type: 'checkbox', width: 50, align: 'center',
            headerAlign: 'center', fixed: "left",
          },
          {
            type: 'seq', title: "序号", align: 'center',
            headerAlign: 'center', fixed: "left", width: 60
          },
          {
                        fixed: 'right',
                        slots: { default: 'operate' },
                        title: '操作',
                        headerAlign: 'center',
                        align: 'center',
                        width: 80,
                    },
          { slots: { default: 'dragColumn' }, visible: true, title: '#', headerAlign: 'center', align: 'center', width: 60 },
          { field: 'field', fixed: "left", width: 150, title: '字段名', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          { field: 'title', fixed: "left", width: 150, title: '中文描述', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          { field: 'titleHelp', width: 120, title: '标题帮助', editRender: { name: "input", autoselect: true, defaultValue: '' } },
          { field: 'fieldDefault', width: 120, title: '默认值', editRender: { name: "input", autoselect: true, defaultValue: '' },
          titlePrefix: {
                            content: `(注意：与控件类型关联)如时间默认值 当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+1年:add-1-year`,
                            //useHTML:false
                        },},

          { field: 'iisReadOnly', width: 100, align: "center", headerAlign: "center", title: '只读？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisRequired', width: 100, align: "center", headerAlign: "center", title: '必填？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisShowList', width: 130, align: "center", headerAlign: "center", title: '列表显示？', editRender: { name: "vxeCheckbox", defaultValue: 1 } },

          { field: 'width', width: 100, align: "right", title: '列表宽', editRender: { name: "input", autoselect: true, defaultValue: '100' }, },

          { field: 'labelWidth', width: 100, align: "right", title: '标题宽', editRender: { name: "input", autoselect: true, defaultValue: '110' }, },
    
          { field: 'titleAlign', width: 130, align: "center", headerAlign: "center", title: '表头对齐', editRender: { name: "$select", options: alignList, defaultValue: 'center' }, },
          { field: 'align', width: 130, align: "center", headerAlign: "center", title: '表格对齐', editRender: { name: "$select", options: alignList, defaultValue: 'center' }, },
          { field: 'iisQuery', width: 120, align: "center", headerAlign: "center", title: '普通查？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisHQuery', width: 120, align: "center", headerAlign: "center", title: '高级查？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisSummary', width: 120, align: "center", headerAlign: "center", title: '汇总？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },
          { field: 'iisSortable', width: 120, align: "center", headerAlign: "center", title: '排序？', editRender: { name: "vxeCheckbox", defaultValue: 0 } },

        ],
        validRules:[]
      }
    }
  },
  created() {
    //导入表单JSON后需要重新加载自定义CSS样式！！！
    this.designer.handleEvent('form-json-imported', () => {
     // debugger
      this.formCssCode = this.formConfig.cssCode
      this.postAddModelUrl = this.formConfig.postAddModelUrl
      this.postModelUrl = this.formConfig.postModelUrl
      this.executeApiUrl = this.formConfig.executeApiUrl
      this.actionName = this.formConfig.actionName
      insertCustomCssToHead(this.formCssCode)
      this.extractCssClass()
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList))
    })
  },
  mounted() {
   // debugger
    
    /* SettingPanel和FormWidget为兄弟组件, 在FormWidget加载formConfig时，
       此处SettingPanel可能无法获取到formConfig.cssCode, 故加个延时函数！ */
    setTimeout(() => {
      //debugger
      this.formCssCode = this.formConfig.cssCode
      this.postAddModelUrl = this.formConfig.postAddModelUrl
      this.postModelUrl = this.formConfig.postModelUrl
      this.executeApiUrl = this.formConfig.executeApiUrl
      this.actionName = this.formConfig.actionName
      insertCustomCssToHead(this.formCssCode)
      this.extractCssClass()
      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList))
     
    }, 1200)
  },
  methods: {
    // 重置 并查询
    resetTableDataSearchEvent() {
      this.tableDataSearchKey = ""
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        // 覆盖原有的查询的参数
        this.requstConfig.postParams = {
          condition: this.tableDataSearchKey
        }
        mainTableRef.searchTableData()
      }
    },
     // 提交过滤查询
     tableDataSearchEvent() {
      //debugger
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        // 覆盖原有的查询的参数
        this.requstConfig.postParams = {
          condition: this.tableDataSearchKey
        }
        mainTableRef.searchTableData()
      }
    },
    reset_parentPageCID(){
      this.showResetData_parentPageNAME = false
      this.$set(this.formConfig,'parentPageCID','')
      this.$set(this.formConfig,'parentPageNAME','')
      this.showResetData_parentPageNAME = true
    
    },
    cellDblclickEvent(tableInfo) {
      this.formConfig.parentPageCID = tableInfo.row.CID
      this.formConfig.parentPageNAME = tableInfo.row.CNAME
      this.$nextTick(() => {
        this.showPopupBoxFlag = false
      })
    },
     // 选择弹框
     selectPopupBox() {
      this.showPopupBoxFlag = true
    },
    change_actionName(val){
      this.$set(this.designer.formConfig, 'actionName', val || "")
    },
       // 非树结构拖动方法 
       rowDrop(tableRefName) {
            let _self = this
            this.$nextTick(() => {
                //debugger
                const xTable = _self.$refs[tableRefName]
                if (!!!xTable) {
                    return
                }
                let fullData = xTable.getTableData().fullData
                _self.sortableRowDropObj = Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
                    handle: '.drag-btn',
                    ghostClass: "sortable-ghost",  // drop placeholder的css类名
                    chosenClass: "sortable-chosen",  // 被选中项的css 类名
                    onEnd: (evt) => {
                       
                        let { newIndex, oldIndex } = evt
                        const currRow = fullData.splice(oldIndex, 1)[0]
                        fullData.splice(newIndex, 0, currRow)
                        //_self.optionModel.tableColumns = fullData  
                        _self.designer.formConfig.formItems= fullData  
                    }
                })
            })
        },
    // 设置统一API ,优先执行
    changeExecuteApi(val){
      //debugger
      this.$set(this.designer.formConfig, 'executeApiUrl', val || "")
      // 清空其它API 设置
      this.postAddModelUrl = ""
      this.postModelUrl = ""
      this.$set(this.designer.formConfig, 'postAddModelUrl',  "")
      this.$set(this.designer.formConfig, 'postModelUrl',  "")
    },
    changeAdd(val){
      //debugger
      this.$set(this.designer.formConfig, 'postAddModelUrl', val || "")
      
    },
    changeEdit(val){
      this.$set(this.designer.formConfig, 'postModelUrl', val || "")
    },
    // 移除所有列表
    removeAllCols() {
      this.$confirm("确定删除所有列", this.i18nt('render.hint.prompt'), {
        confirmButtonText: this.i18nt('render.hint.confirm'),
        cancelButtonText: this.i18nt('render.hint.cancel')
      }).then(() => {
        //this.designer.formConfig.formItems =[]
        this.designer.formConfig.formItems = []
      }).catch(error => {
        //this.$message.error(error)
      })
    },
    // 移除指定列
    deleteOption(option, row) {
      let index = option.rowIndex
      this.$confirm("确定删除该项", this.i18nt('render.hint.prompt'), {
        confirmButtonText: this.i18nt('render.hint.confirm'),
        cancelButtonText: this.i18nt('render.hint.cancel')
      }).then(() => {
        this.designer.formConfig.formItems.splice(index, 1)
      }).catch(error => {
        //this.$message.error(error)
      })
    },
    addNewField() {
      //this.$set(this.designer.formConfig, 'formItems', this.designer.formConfig.formItems || [])
      let newId = generateId()
      this.designer.formConfig.formItems.push({
        field: "newfield" + newId,
        title: "新字段名" + newId,
        iisShowList: 1,// 列表显示，
        titleAlign: "center",
        align: "left",
        controlType: "text",
        labelWidth: 110,
        width: "120",
      })
    },

    // 开始加载列头信息
    async loadColsData() {
      // this.loadingSubmit= true
      // // 列头查询 preUrl 路径固定不变
      // let _url = this.useFormatParamsFn.getVFormDataSearchUrl(this.actionName);
      // // 合并普通、高级查询参数
      // let params = this.useFormatParamsFn.getVFormSearchParams(this.actionName);

      // request["post"](_url, params).then((res)=>{

      // 	this.designer.formConfig.formItems = this.designer.formConfig.formItems.concat(this.testTableData)
      // 	this.loadingSubmit= false
      // })
      let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.actionName);
      // debugger
      if (!!CDATASET_ID) {
        this.loadingSubmit = true
      } else {
        return
      }
      let dataModelList = await this.getDataModelList(CDATASET_ID)
      // debugger
      this.designer.formConfig.formItems = this.formatDataModel(dataModelList)
      this.loadingSubmit = false
      setTimeout(() => {
        this.loadingSubmit = false
      }, 10000);
    },
    // 获取 表格列头数据信息
    async getDataModelList(datasetId) {
      let dataModelList = []
      let params = {
        condition: "",
        datasetId: datasetId
      }
      //let _url = "api/MD/DataSetModel/GetAll"
      // let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })
      return dataModelList
    },
    // 格式化模型数据
    formatDataModel(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        let innerField = [
          "CID",
          "CDATETIME_CREATED",
          "CUSER_CREATED",
          "CDATETIME_MODIFIED",
          "CUSER_MODIFIED",
          //"CSTATE",
          "CINSTANCE_ID",
          "CROWREMARK",
          "CENTERPRISE_CODE",
          "CORG_CODE",
        ]
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: oldItem.CCOLUMN_DESC,
            CID: oldItem.CID,
            titleHelp: "",
            fieldDefault: "",
            iisShowList: 1,
            width: 200,
            titleAlign: "center",
            fieldOrder: 100,
            align: "center",
            controlType: "text",
            groupTitle: "",
            iisSummary: 0,
            fieldRules: "",
            iisQuery: 0,
            iisHQuery: 0,
            iisSummary: 0,
            iisReadOnly: 0,
            iisMoney: 0,
            iisSortable: 0,
            sourceKey: "",
            matchField: "",
            searchParams: "",
          }
          // 内置字段不显示
          if (innerField.includes(newItem.field)) {
            newItem.iisShowList = 0
          }
          newDataList.push(newItem)
        })
      }

      return newDataList
    },
    // 确认表格列更改
    colSubmit() {
      this.dialogVisible = false;
    },
    openSetting() {
      //this.dialogVisible = true;
      this.loading_container = true
      this.dialogVisible = true;
      let _self = this
      this.$nextTick(() => {

        setTimeout(() => {
          _self.loading_container = false
          _self.rowDrop("editFormGrid")
        }, 1000)
      })
    },
    showEventCollapse() {
      if (this.designerConfig['eventCollapse'] === undefined) {
        return true
      }

      return !!this.designerConfig['eventCollapse']
    },

    editFormCss() {
      this.formCssCode = this.designer.formConfig.cssCode
      this.showEditFormCssDialogFlag = true
    },

    extractCssClass() {
      let regExp = /\..*{/g
      let result = this.formCssCode.match(regExp)
      let cssNameArray = []

      if (!!result && result.length > 0) {
        result.forEach((rItem) => {
          let classArray = rItem.split(',')  //切分逗号分割的多个class
          if (classArray.length > 0) {
            classArray.forEach((cItem) => {
              let caItem = cItem.trim()
              if (caItem.indexOf('.', 1) !== -1) {  //查找第二个.位置
                let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf('.', 1))  //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim())
                }
              } else if (caItem.indexOf(' ') !== -1) {  //查找第一个空格位置
                let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf(' '))  //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim())
                }
              } else {
                if (caItem.indexOf('{') !== -1) {  //查找第一个{位置
                  let newClass = caItem.substring(caItem.indexOf('.') + 1, caItem.indexOf('{'))
                  cssNameArray.push(newClass.trim())
                } else {
                  let newClass = caItem.substring(caItem.indexOf('.') + 1)
                  cssNameArray.push(newClass.trim())
                }
              }
            })
          }
        })
      }

      //this.cssClassList.length = 0
      this.cssClassList.splice(0, this.cssClassList.length)  //清除数组必须用splice，length=0不会响应式更新！！
      this.cssClassList = Array.from(new Set(cssNameArray))  //数组去重
    },

    saveFormCss() {
      this.extractCssClass()
      this.designer.formConfig.cssCode = this.formCssCode
      insertCustomCssToHead(this.formCssCode)
      this.showEditFormCssDialogFlag = false

      this.designer.emitEvent('form-css-updated', deepClone(this.cssClassList))
    },

    editGlobalFunctions() {
      this.functionsCode = this.designer.formConfig.functions
      this.showEditFunctionsDialogFlag = true
    },

    saveGlobalFunctions() {
      const codeHints = this.$refs.gfEditor.getEditorAnnotations()
      let syntaxErrorFlag = false
      if (!!codeHints && (codeHints.length > 0)) {
        codeHints.forEach((chItem) => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true
          }
        })

        if (syntaxErrorFlag) {
          this.$message.error(this.i18nt('designer.setting.syntaxCheckWarning'))
          return
        }
      }

      this.designer.formConfig.functions = this.functionsCode
      insertGlobalFunctionsToHtml(this.functionsCode)
      this.showEditFunctionsDialogFlag = false
    },

    editFormEventHandler(eventName) {
      this.curEventName = eventName
      this.formEventHandlerCode = this.formConfig[eventName]
      this.showFormEventDialogFlag = true
    },

    saveFormEventHandler() {
      const codeHints = this.$refs.ecEditor.getEditorAnnotations()
      let syntaxErrorFlag = false
      if (!!codeHints && (codeHints.length > 0)) {
        codeHints.forEach((chItem) => {
          if (chItem.type === 'error') {
            syntaxErrorFlag = true
          }
        })

        if (syntaxErrorFlag) {
          this.$message.error(this.i18nt('designer.setting.syntaxCheckWarning'))
          return
        }
      }


      //this.formConfig[this.curEventName] = this.formEventHandlerCode
      /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
      this.$set(this.formConfig, this.curEventName, this.formEventHandlerCode)

      this.showFormEventDialogFlag = false
    },

  }
}
</script>
<style>
  .sortable-ghost{
    background-color: #E6A23C !important;
  }
  .sortable-chosen{
    background-color: #67C23A !important;
  
  }
.el-table .warning-selected-row {
  background-color: #E6A23C !important;
}

.el-table .success-selected-row {
  background-color: #67C23A !important;
}
</style>
<style lang="scss" scoped>
.option-items-pane ul {
  height: auto;
  //overflow: auto;
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}

li.col-item {
  list-style: none;

  span.col-span-title {
    display: inline-block;
    font-size: 13px;
    width: 120px;
  }

  .col-delete-button {
    margin-left: 6px;
  }
}

.panes-setting {
  ul {
    padding-inline-start: 0;
    padding-left: 0;
    /* 重置IE11默认样式 */
    margin: 0;
  }

  .drag-option {
    cursor: move;
  }

  li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
  }
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 12px 15px;
  }
}

.button-row {
  border-bottom: 1px solid #e1e2e3;
  margin-bottom: 12px;
}

.drag-sort-col {
  padding-top: 8px;
  cursor: move;
}

/////////////
.setting-form {
  ::v-deep .el-form-item__label {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 6px;
  }

  .radio-group-custom {
    ::v-deep .el-radio-button__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .custom-divider.el-divider--horizontal {
    margin: 10px 0;
  }
}

.setting-collapse {
  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
  }

  ::v-deep .el-collapse-item__header {
    font-style: italic;
    font-weight: bold;
  }
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 12px 15px;
  }
}

// add by andy
::v-deep .el-form {
  .el-button--primary.is-plain {
    color: #409EFF !important;
    background: #ecf5ff !important;
    border-color: #b3d8ff !important;
  }

}
.addPrimaryFieldBtn   {
    color: #fff !important;
  }
// add by andy el-button el-button--primary el-button--small is-plain is-round

</style>
