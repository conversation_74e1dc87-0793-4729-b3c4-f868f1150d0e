// 搜索模块 通用属性方法
import dayjs from 'dayjs'
export default {
    name: "searchItemMixin",
    data() {
        return {
            publicAttribute: {
                value: "",
                searchKey:'',
                controlType:"",
                pageIndex:1,
                pageSize:10,
                valueString:"",// 多选时，改为字符串
            },// 对外开发属性值
            value: "",// 当前选中的值的，对外暴露属性，仅供外部查询使用
            
        }
    },
    methods: {
        // 初始化，默认值等
        init() {
              setTimeout(()=>{
               // debugger
                if(!!this.$attrs.value){
                   // debugger
                    this.value = this.$attrs.value
                    let params ={
                        value:this.$attrs.value,
                        type:'init'
                    }
                    this.$emit("changeEvent",params)
                }
              },1000)
        },
        // 选择改变触发事件
        changeEvent(val) {
            //debugger
            this.value = val
            let params = {
                value: val,
                type: 'change'
            }
            //debugger
            if(val && Array.isArray(val)){
               this.publicAttribute.value = val.join('¤')
            }
            this.$emit("changeEvent", params)
        },
        // 查询栏目 点击 重置清空数据，外部调用
        clear() {
            if (!!this.fieldName && !!this.controlType && this.searchForm) {
                // 重置
                if (!!this.value && !!this.searchForm[this.fieldName]) {
                    let defaultValue = this.paramsItem.defaultValue
                    switch (this.controlType) {
                        case "date":
                        case "dateTime":
                        case "datetime":
                        case 'month':
                            let newDefaultVal = this.setDateTimeDefaultVal(this.controlType, defaultValue)
                            this.$set(this.searchForm, this.fieldName, newDefaultVal)
                            break
                        case 'number':
                            let newDefaultVal2 = Number(defaultValue)
                            this.$set(this.searchForm, this.fieldName, newDefaultVal2)
                            break
                        default:
                            this.$set(this.searchForm, this.fieldName, defaultValue)
                            break
                    }
                    this.value = ""
                }
            }
        },
        // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
        setDateTimeDefaultVal(type, defaultVal) {
            let dValue = null
            let dateFormatStr = ""
            // 指定日期 格式化 格式
            switch (type) {
                case "date":
                    dateFormatStr = 'YYYY-MM-DD'
                    break;
                case "dateTime":
                case "datetime":
                    dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                    break;
                case 'month':
                    dateFormatStr = 'YYYYMM'
                    break
                default:
                    dateFormatStr = ""
                    break
            }
            if (!!defaultVal) {
                if (defaultVal == 'curdate') {
                    // 当前日期
                    dValue = dayjs().format(dateFormatStr);
                } else if (defaultVal.includes('-')) {
                    // 指定日期加减
                    //dayjs().add(7, 'day').format('YYYY-MM-DD');
                    //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                    let daysetArray = defaultVal.split('-')
                    dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                } else {
                    //空日期
                    dValue = null
                }
            }

            return dValue
        },

    }
}