<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div :ref="field.options.name" :publicAttribute="publicAttribute" class="flex items-center"  :style="{height:contentBoxHeight}"
    :class="[!!field.options.showMenuRight ?'justify-end':'justify-start']">
        <div style="margin: 0 10px;" :style="{height:containerBoxHeight+'px',fontSize:!!field.options.fontSize?field.options.fontSize+'px':'16px'}" class="flex  overflow-hidden  h-full justify-start items-center ">
          <div @click="clickItem(item,index)" class="flex justify-center  items-center childBorder overflow-hidden":key="index"  v-for="(item,index) in colorBlockInfoItemsList" style="cursor: pointer; " :style="{padding:!!field.options.padding?field.options.padding+'px':'15px' ,width:!!item.blockWidth?item.blockWidth+'px':'auto',backgroundColor:`${!!item.bgColorKey?getDataByFieldName(item.bgColorKey,index):item.bgColor}`}">
            <span :style="{color:!!item.fontColorKey?getDataByFieldName(item.fontColorKey,index):item.fontColor}">
             {{!!getDataByFieldName(item.totalField,index) ?getDataByFieldName(item.totalField,index):item.title }}
            </span>
            <span v-if="!!field.options.showValueField" :style="{color:item.valueFontColor,marginLeft:!!item.valueField?'10px':''}">
              {{ !!item.valueField ?getDataByFieldName(item.valueField,index):''}}
            </span>
          </div>
       </div>
    </div>
   

  </static-content-wrapper>
</template>

<script>
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
// const XEUtils = require('xe-utils') // vxeTable 通用库

export default {
  name: "colorBlockInfo-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['getPageInstance', 'sourceVFormRenderState'],
  components: {
    StaticContentWrapper,
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    // 控件来源父集 add by andy
    contentBoxHeight: {
      type: [Number, String],
      default: 0
    },
    // 控件来源父集 add by andy
    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  data() {
    return {
      optionList:[],
      publicAttribute: {
        value: null,
        item: null,
      },// 对外开发属性值
      dataObj: {},
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
    }
  },
  computed: {
    pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
    },
    colorBlockInfoItemsList() {
      let _list = this.field.options.colorBlockInfoItems.filter(item => {
        //debugger
        if (!!item.check) {
          return item
        }
      })
      return _list
    },
    containerBoxHeight(){
      //debugger
      let testHeight = 70
      if (this.designState) {
        // 预览状态下，固定高度分配
        return testHeight 
      } else {
        if(!!this.field.options.height){
          return parseInt(this.field.options.height)
        }else{
          return  parseInt(this.contentBoxHeight) -2 //this.contentBoxHeight
        }
        
      }
    }

  },
  mounted() {
    let _self = this
    this.$nextTick(() => {
      setTimeout(() => {
        _self.tryTillGetData()
      }, 300)
    })
  },

  created() {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
    clickItem(item,index){
       //debugger
      let _value = this.getDataByFieldName(item["valueField"],index)
      let params = {
        formCtrlName: "colorBlockInfo",
        contrlName: this.field.options.name,
        value: _value,// 过滤查询字段
        triggerCtrlNames: this.field.options.triggerCtrlNames, //重点： 触发控件名称
      }
      this.publicAttribute.value = _value
      this.publicAttribute.item =item
      this.useHandleVFormEventFn.reSearchData(params);
    },
    // 对外暴露事件，重新加载
    async reSearchData(params) {
      //debugger
      try {
        let formCtrlName = params.formCtrlName
        if(formCtrlName == 'defaultmenubutton'){
          // 重新查询，清空查询条件
          this.publicAttribute.value = null
          this.publicAttribute.item =null
        }
      } catch (error) {
        
      }
     this.tryTillGetData()
    },
    getDataByFieldName(fieldName='',index=0){
      // 按行读取数据
      //debugger
      if(!!this.field.options.readByRow && this.optionList.length>0){
          let itemObj = this.optionList[index]
          //debugger
          try {
            if(itemObj.hasOwnProperty(fieldName)){
                let formatValue = this.optionList[index][fieldName]
                console.log(formatValue)
                return formatValue
          }else{
              return null
            } 
          } catch (error) {
            
          }
      }else{
           // 按列读取数据
           if(this.dataObj && Object.keys(this.dataObj).length>0){
                if(this.dataObj.hasOwnProperty(fieldName)){
                  let formatValue = this.dataObj[fieldName]
                  return formatValue
                }
             }else{
              return null
            } 
      }
            
        },
    // 触发控件列表>>触发事件 >>循环直到获取到数据
    async tryTillGetData() {
      // 干正事
      let _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
      //debugger
      if (_dataList && _dataList.length > 0) {
        if(!!this.field.options.readByRow){
          this.optionList = _dataList
        }else{
          this.dataObj = _dataList[0]
        }
        
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.childBorder {
      border-right: 1px solid #ccc;
      &:last-child {
      border-right: 0px solid #b2b6b6;
    }
  }
</style>