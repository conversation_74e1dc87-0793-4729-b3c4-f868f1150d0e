<template>
    <el-drawer  :size="getWidth()"
                 ref="editDrawerRef"
                 :title="title"
                :visible.sync="showEditBox"
                :direction="direction"
                >
                <div style="padding: 1px 15px;">
                    <div>
                        <el-skeleton :loading="loading_nvxeModal" animated :throttle="10">
                               <!-- :key="$route.fullPath" -->
                            <VFormRender :contentBoxHeight="getContentBoxHeight()" sourceVFormRender="editContentBoxDrawerRef.editPreForm" :key="randomKey" :designer="designer" ref="editPreForm">
                            </VFormRender>
                       </el-skeleton>
                    </div>
                    <div style="border-top:1px solid #ccc;" v-if="!loading_nvxeModal && !!getItemField('showFooter')" class="flex justify-between">
                            <div></div>
                            <div></div>
                            <div style="margin-top:2.5px;">
                                <el-button size="medium" @click="cancelBtn()">取消</el-button>
                                <el-button size="medium" :loading="loadingBtn" @click="preSubmitEvent()" type="primary">确定</el-button>
                            </div>
                    </div>
                    
                </div>
         </el-drawer>   
</template>

<script>
// import dayjs from 'dayjs'
import { useElementSize } from "@vueuse/core";
import request from '@/libs/request'
import {
    useFormatParams
} from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import VFormRender from '@/components/form-render/index'
export default {
    name: "edit-formDesign",
    components: {
        VFormRender
    },
    // 注入列表页面整体实体 'sourceVFormRenderState'
    inject: ['getPageInstance'],
    props: {
        formConfig: Object,
        // 控件来源父集 add by andy
        sourceVFormRender:{
            type: String,
            default: "editContentBoxDrawerRef.editPreForm"
        },
    },
    provide() { 
        // 主要解决深层次的组件嵌套，祖先组件向子孙组件之间传值。
        return {
            //  返回当前页面整体实例
            sourceVFormRenderState:"editContentBoxDrawerRef.editPreForm",
          } 
      },
    data() {
        return {
            sourceVFormRenderState:"editContentBoxDrawerRef.editPreForm",
            editContentOptions:{
                height: 0, // 主体内容 contentBoxRef 高 
                width: 0,  // 宽
              },
            maxLoadTime_editContentBoxRef:6,
            direction: 'rtl',
            randomKey:"edit-formDesign",
            width: 50,// 弹框宽度百分比
            title: "", // 标题
            loading_nvxeModal: true,
            loadingBtn: false,
            showEditBox: false,
            designer: null,
            useFormatParamsFn: useFormatParams(this),
            useHandleVFormEventFn: useHandleVFormEvent(this),
            popupItemAfterSuccessOrErrorEvents:[],// 弹框自身关闭后事件列表
            //useFormatParamsFn: useFormatParams(this, "editContentBoxRef"),
        }
    },
    computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
    },
    watch: {
        // 监听弹框是否打开
        showEditBox(n, o) {
            //debugger
            if (!!n) {
                setTimeout(()=>{
                    this.randomKey = Math.floor(Math.random() * 10000 + 1)
                    this.getWidth()
                    this.init()
                    this.$nextTick(()=>{
                        this.initEditContentBoxWH()
                    })
                },500)

            } else {
                let _self = this
                this.randomKey = Math.floor(Math.random() * 10000 + 1)
                this.resetData()
                this.$nextTick(()=>{
                    // 弹框关闭后执行事件
                    _self.afterShowEditBoxCloseEvent()
                })
            }
        }
    },
    mounted() {

    },
    methods: {
        // 弹框关闭后执行事件
        afterShowEditBoxCloseEvent(){
            let _self = this
           if(this.popupItemAfterSuccessOrErrorEvents.length>0){
                 let eventList = this.popupItemAfterSuccessOrErrorEvents
                    if(eventList && eventList.length>0){
                        for(let i=0;i<eventList.length;i++){
                            let eventItem = eventList[i]
                            // 添加额外的属性,是否刷新的父控件
                            eventItem["isFromParent"] =true
                            _self.useHandleVFormEventFn.handleCommonClickEvent(eventItem)
                        }
                    }
           }
        },
        async init() {
                this.loading_nvxeModal = true
                let newFormJson = await this.getRowTemplateData()
                this.loading_nvxeModal = false
            this.$nextTick( () => {
            
                this.$refs['editPreForm'].setFormJson(newFormJson)
                if (newFormJson) {
                    this.designer = JSON.parse(newFormJson)

                }
                // 重置表单数据，并清除校验状态
                this.$refs['editPreForm'].resetForm()
                let popupParams = this.$store.state.popupParams;
                //debugger
                if (popupParams && popupParams.value == 'iisEdit') {
                    // 编辑状态
                    let rowData = this.getCurrentRow()
                    this.$refs['editPreForm'].setFormData(rowData)
                }
            })
        },
        getContentBoxHeight(){
            let height = this.editContentOptions.height-185
            let showFooter = this.getItemField('showFooter')
            // if(!!showFooter){
            //     height = height - 5
            // }
            return height
        },
        // 初始化 editContentBoxRef 宽 高
       initEditContentBoxWH(editContentBoxRefName ="editDrawerRef"){
         let _self = this
          if(!!this.$refs[editContentBoxRefName]){
            this.maxLoadTime_editContentBoxRef =6
            const { width, height } = useElementSize(this.$refs[editContentBoxRefName]);
            this.editContentOptions.width = width;
            this.editContentOptions.height = height;
          }else{
            if( _self.maxLoadTime_editContentBoxRef>0){
                setTimeout(()=>{
                    _self.maxLoadTime_editContentBoxRef =  _self.maxLoadTime_editContentBoxRef-1
                        this.initEditContentBoxWH()
                },300)
            }
          }
      },
        resetData() {
            // 重置表单数据，并清除校验状态
            this.$refs['editPreForm'].resetForm()
        },
        cancelBtn() {
            this.showEditBox = false
        },
        // 获取当前选中行模板数据
        getCurrentRow() {
            let routerName = this.$route.name
            let rowData = this.$store.state.currentRow.value[routerName];
            let currentRow = rowData
            //debugger
            return currentRow
        },
        preSubmitEvent() {
            this.loadingBtn = true
            this.$refs['editPreForm'].validateForm((valid) => {
                if (valid) {
                    this.submitEvent()
                } else {
                    this.loadingBtn = false
                }
            });
        },
        
        // 表单提交接口
        async submitEvent() {
            // debugger
            let _self = this
            let formData = await this.$refs['editPreForm'].getFormData()
            let datasetId = null
            let selfUpdateActionName = this.designer.formConfig.postModelUrl
            let selfAddActionName = this.designer.formConfig.postAddModelUrl
            let executeApiUrl = this.designer.formConfig.executeApiUrl
            //  debugger
            // 参数提交前 格式化 处理
            let onBeforeSubmitStr =  this.designer.formConfig.onBeforeSubmit
            let onBeforeSubmit = new Function('postParams',onBeforeSubmitStr)
            let _url = `api/MD/DataSet/ExecuteByDataSetId` // 固定取值地址GetListByDataSetId
            let popupParams = this.$store.state.popupParams;

          
            // 默认添加
            let queryItem =  this.useFormatParamsFn.getQueryItemByUrl(selfAddActionName)
            if (popupParams && popupParams.value == 'iisEdit') {
                // 编辑
                queryItem =this.useFormatParamsFn.getQueryItemByUrl(selfUpdateActionName)
            }
            if (!!executeApiUrl) {
                // 不区分功能统一接口API
                queryItem =this.useFormatParamsFn.getQueryItemByUrl(executeApiUrl)
            }
           // debugger
            // 获取提交接口的参数值
            let postParams =await this.useFormatParamsFn.getCommonParamsValue(queryItem)
           // debugger
                // 表单赋值
            for (const [key,val] of Object.entries(postParams)) {
                    if(val =='$formData'){
                        postParams[key] = formData
                    }
            }
               // postParams.formData = formData
            // 提交数据前，数据拦截，格式化参数格式后再提交
            if(!!onBeforeSubmitStr){
                try {
                    postParams = onBeforeSubmit.call(this,postParams)
                } catch (error) {
                    this.$message({
                            message: '提交前，格式化（onBeforeSubmit）参数方法错误，请检查！！！',
                            type: 'error'
                        });
                  return       
                }
            }
            datasetId = this.useFormatParamsFn.getActionParamsValue("CDATASET_ID", queryItem)
          
            let params = {
                Id: datasetId,
                Parameter: postParams
            }
            // debugger
            // return
           let res = await request["post"](_url, params)
           if(res.Success){
              //debugger
               this.$message({
                message: '执行成功！',
                    type: 'success'
                });

                setTimeout(() => {
                    let SuccessParams ={}
                    _self.$emit("submitSuccess", SuccessParams)
                    _self.loadingBtn = false
                    _self.showEditBox = false
                    _self.afterSuccessOrErrorEvents(popupParams)
                }, 1000)
           }
         
        },
          //添加，编辑成功后==》调用事件列表"
          afterSuccessOrErrorEvents(popupParams){
                if(popupParams.hasOwnProperty("afterSuccessOrErrorEvents")){
                    let eventList = popupParams.afterSuccessOrErrorEvents
                    if(eventList && eventList.length>0){
                        for(let i=0;i<eventList.length;i++){
                            let eventItem = eventList[i]
                            // 添加额外的属性,是否刷新的父控件
                            eventItem["isFromParent"] =true
                            this.useHandleVFormEventFn.handleCommonClickEvent(eventItem)
                        }
                    }
                
                }
        },
        // 从vFORM全局表单查询列表中，获取查询项
        getPopupItemByUrl(postUrl = "") {
            let popupItem = null
            // 注意：this.formConfig 此处为列表配置信息，非编辑弹框配置
            if(!!!this.formConfig){
                return ""
            }
            let popupList = this.formConfig.popupList
            if (popupList && popupList.length > 0) {
                let filterList = popupList.filter((item) => {
                    if (item.value == postUrl) {
                        return item
                    }
                })
                if (filterList && filterList.length > 0) {
                    popupItem = filterList[0]
                }
            }
            if (!!popupItem) {
                if (!!popupItem.width) {
                    this.width = Number(popupItem.width)
                }
            }
            return popupItem
        },

        getVFormPopupActionName(postUrl = "") {
            let actionName = ""
            let popupItem = this.getPopupItemByUrl(postUrl)
            if (!!popupItem) {
                actionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupItem)
            }
            return actionName
        },
        // 通过CID 查找模板数据
        async searchTemplateByCID_backup(rowCID = "") {
            let jsonDataStr = ""
            if (!!rowCID) {
                let _url = `api/MD/VisualFormDesigner/GetAll`
                let params = {
                    condition: `{CID:${rowCID}}`,
                    start: 1,
                    length: 10,
                }
                await request["get"](_url, null, params).then(res => {
                    // debugger
                    if (res && res.Datas) {
                        jsonDataStr = res.Datas
                    }
                });
                return jsonDataStr
            }
        },
         // 获取当前选中行模板数据
         async searchTemplateByCID(rowCID = "") {
            let formJsonData = ""
            let _url = `api/MD/VisualFormDesigner/GetByID`
          
            let params = {
                id: rowCID
            }
            if (!!rowCID) {
                await request["get"](_url, null, params).then(res => {
                    if (res.Success && res.Datas) {
                        formJsonData = res.Datas.CJSON_DATA
                    }
                    //debugger
                });
            }
            return formJsonData
        },
        getItemField(field){
            // debugger
            let itemFieldVal =null
            let popupParams = this.$store.state.popupParams;
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let popupItem = this.getPopupItemByUrl(selfActionName)
            if (!!popupItem) {
                if (popupItem.hasOwnProperty(field)) {
                    itemFieldVal = (popupItem[field])
                }
            }
            return itemFieldVal
        },
        // 获取弹框宽度
        getWidth() {
            //debugger
            let width = "50%"
            let popupParams = this.$store.state.popupParams;
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let popupItem = this.getPopupItemByUrl(selfActionName)
            if (!!popupItem) {
                try {
                    this.popupItemAfterSuccessOrErrorEvents =[]
                    if(popupItem.hasOwnProperty("afterSuccessOrErrorEvents")){
                        this.popupItemAfterSuccessOrErrorEvents =popupItem.afterSuccessOrErrorEvents
                    }
                    if (!!popupItem.width) {
                    width = (popupItem.width)
                    if(!width.includes('%')){
                        width = "50%"
                    }
                }
                } catch (error) {
                    width = "50%"
                }
              
            }
            return width
        },
        // 获取当前选中行模板数据
        async getRowTemplateData() {
            //debugger
            let popupParams = this.$store.state.popupParams;
            this.title = popupParams.label
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let commonActionName = this.getVFormPopupActionName(selfActionName)
            let formJsonDataList = await this.searchTemplateByCID(commonActionName)
            if (formJsonDataList && formJsonDataList.length > 0) {
                //return formJsonDataList[0].CJSON_DATA // 接口变更
                return formJsonDataList // 接口变更
            } else {
                return ""
            }

        },
    }

}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
    .el-form-item__error {
        // 修复弹框里面的表单错误提示信息
        line-height: 0.3 !important;
    }
}

::v-deep .vxe-modal--body {
    .vxe-modal--content {
        // 隐藏弹框多余的滚动条
        overflow: hidden !important;
    }
}

.controlWidth {
    // 表单控件宽度
    width: 500px
}
</style>
