<template>
  <container-item-wrapper :widget="widget">
      <div>1111container-item-wrapper</div>
  </container-item-wrapper>
</template>

<script>
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import cloneDeep from "clone-deep"
import refMixin from "@/components/form-render/refMixin"
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper'
import containerItemMixin from "@/components/form-render/container-item/containerItemMixin"
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

export default {
  name: "divContainer-item",
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    formConfig: Object, // add by andy 202301
    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    contentBoxHeight: {
        type: [Number, String],
        default: 0
    },
              // 控件来源父集 add by andy
   sourceVFormRender:{
        type: String,
        default: ""
      },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    designState: {
      type: Boolean,
      default: false
    },
  },
  inject: ['refList', 'sfRefList', 'globalModel','sourceVFormRenderState'], 
  data() {
    return {
      
    }
  },
  computed: {
  

  },
  created() {
    this.initRefList()
  },
  mounted() {
   
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
   


  },
}
</script>

<style lang="scss" scoped></style>
