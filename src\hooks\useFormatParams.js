
// 通用过滤参数
import request from '@/libs/request'
export function useFormatParams(vueInstance, refFormName = 'preForm') {
  return {
    data: [],
    vueInstance: vueInstance,
    refFormName: refFormName,
    isFromParent: false,// 是否来自父类控件的值
    testFn() {
      return this.data
    },
    getRandomNum() {
      return Math.floor(Math.random() * 10000 + 1)
    },
    // 从otherParams获取对象中指定的参数的 值
    getOtherParamsValue(keyName = "", item) {
      let keyNameValue = ""
      let otherParams = item.otherParams
      if (Object.keys(otherParams).length > 0) {
        if (otherParams.hasOwnProperty(keyName)) {
          keyNameValue = otherParams[keyName]
        }
      }
      return keyNameValue
    },
    // 获取指定控件val {{parent.vxetable51263.row.CUSER_NAME}}
    async getSingleControlValueByPath(pathVal) {
      // debugger
      let controlValue = ""
      this.isFromParent = false // 每次重置
      if (pathVal.includes('{{')) {
        let newValue = pathVal.replace('{{', '').replace('}}', '')
        // 分离各个参数
        let newValueList = newValue.split('.')
        let type = newValueList[0]
        if (type == 'parent') {
          //shift() 方法用于把数组的第一个元素从其中删除，并返回第一个元素的值
          newValueList.shift()
          this.isFromParent = true
        }
        controlValue = await this.getControlValue(newValueList)
      }
      return controlValue
    },
    // 从actionParams获取对象中指定的参数的 值
    getActionParamsValue(keyName = "", item) {
      let keyNameValue = ""
      let actionParams = item.actionParams
      if (!!actionParams && Object.keys(actionParams).length > 0) {
        if (actionParams.hasOwnProperty(keyName)) {
          keyNameValue = actionParams[keyName]
        }
      }
      return keyNameValue
    },

    // 获取通用参数值-常用
    async getCommonParamsValue(queryDataObj,boxType='popup') {
       //debugger
      let paramsValue = {}
      if (queryDataObj) {
        let query = this.getActionParamsValue("query", queryDataObj)
        if (!!query) {
          for (const [key, value] of Object.entries(query)) {
            if(value.hasOwnProperty("value")){
              if ((value.value + "").includes('{{')) {
                // 参数默认值
                // 过滤多余字符串：{{defaultmenubutton59838.searchKeyInput.value}}
                let newValue = value.value.replace('{{', '').replace('}}', '')
                // 分离各个参数
                let newValueList = newValue.split('.')
                let type = newValueList[0]
                if (type == 'parent') {
                  //shift() 方法用于把数组的第一个元素从其中删除，并返回第一个元素的值
                  newValueList.shift()
                  this.isFromParent = true
                }
                let controlValue = await this.getControlValue(newValueList)
                // 组合成对象{key:value}
                paramsValue[key] = controlValue
              } else {
  
                paramsValue[key] = value.value
              }
            }else{
              if ((value + "").includes('{{')) {
                // 参数默认值
                // 过滤多余字符串：{{defaultmenubutton59838.searchKeyInput.value}}
                let newValue = value.replace('{{', '').replace('}}', '')
                // 分离各个参数
                let newValueList = newValue.split('.')
                let type = newValueList[0]
                if (type == 'parent') {
                  //shift() 方法用于把数组的第一个元素从其中删除，并返回第一个元素的值
                  newValueList.shift()
                  this.isFromParent = true
                }
                let controlValue = await this.getControlValue(newValueList,boxType)
                // 组合成对象{key:value}
                paramsValue[key] = controlValue
              } else {
  
                paramsValue[key] = value
              }
            }
           
          }
        }

      }
      return paramsValue
    },
    // 获取通用参数值-次用
    // actionParams: Object
    // actionFunction: (...)
    // actionName: (...)
    // query: Object
    //CPARENT_ID: "0"
    async getNormalParamsValue(queryDataObj) {
     //debugger
      let paramsValue = {}
      if (queryDataObj) {
        let query = this.getActionParamsValue("query", queryDataObj)
        if (!!query) {
          for (const [key, value] of Object.entries(query)) {
            if ((value + "").includes('{{')) {
              // 参数默认值
              // 过滤多余字符串：{{defaultmenubutton59838.searchKeyInput.value}}
              let newValue = value.replace('{{', '').replace('}}', '')
              // 分离各个参数
              let newValueList = newValue.split('.')
              let type = newValueList[0]
              if (type == 'parent') {
                //shift() 方法用于把数组的第一个元素从其中删除，并返回第一个元素的值
                newValueList.shift()
                this.isFromParent = true
              }
              let controlValue = await this.getControlValue(newValueList)
              // 组合成对象{key:value}
              paramsValue[key] = controlValue
            } else {

              paramsValue[key] = value
            }
          }
        }

      }
      return paramsValue
    },
    // 获取所有动态参数的值（包括{{input.value}}转换后的值）
    async getVFormParamsValue(queryDataObj) {
      // debugger
      let paramsValue = {}
      let datasetId = null
      if (queryDataObj) {
        datasetId = this.getActionParamsValue("CDATASET_ID", queryDataObj)
      }
      paramsValue = await this.getCommonParamsValue(queryDataObj)
      // 通过DATASETID,查询数据 固定结构的JSON 
      let newParams = {
        Id: datasetId,
        Parameter: paramsValue
      }
      return newParams
    },
   
    // 获取API路径参数 自定义控件的 动态输入框的值
    async getControlValue(newValueList,boxType='popup') {
     //debugger
      let controlValue = ""
      try {
        let type = newValueList[0]

        // debugger
        if (type.includes("defaultmenubutton")) {
          controlValue = this.getDefaultmenubuttonValue(newValueList,boxType)
        } else if (type.includes("vxetable")) {
          controlValue =await this.getVxetableValue(newValueList,boxType)
        } else if (type.includes("$UserInfo")) {
          controlValue = this.getUserInfoValue(newValueList,boxType)
        }
        else if (type.includes("$fullPath")) {
          debugger
          controlValue = this.getFullPathValue(newValueList,boxType)
        }
        else if (type.includes("$searchPro")) { 
          controlValue = this.getSearchProValue(newValueList,boxType)
        }
         else if (type.includes("$iframePostMessage")) { 
          
          controlValue = this.getPostMessageValue(newValueList,boxType)
        }
        else if (type.includes("$Timestamp")) {
          controlValue = Math.floor(Math.random() * 100000000000000 + 1)
        }
        else if (type.includes("$formData")) {
          controlValue = await this.vueInstance.pageInstance.$refs[this.refFormName].getFormData(false)
        }
        else if (type.includes("echart")) {
         // debugger
          controlValue =await this.getCommonCtrlValue(newValueList,boxType)
        } 
        else if (type.includes("checkbox")) {
          controlValue =await this.getCommonCtrlValue(newValueList,boxType)
         } 
         else if (type.includes("colorBlockInfo")) {
          controlValue =await this.getCommonCtrlValue(newValueList,boxType)
         } 
        else {
          newValueList.forEach((item, index) => {
            // 逐个参数解析并获取其值
            if (index == 0) {
              // 首个参数使用内部对象获取 isFromParent 是否来自父类控件的值
              if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
                controlValue = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[item]
              } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
                controlValue = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[item]
              }
              else {
                //  let tt =   this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList
                //  debugger
                controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
              }

              if (controlValue) {
                // 正常$refs 对象
                controlValue = controlValue.$refs[item]
               
              }
              this.isFromParent = false
            } else {
              // 获取对外开发属性KEY的值
              // debugger
              //controlValue = controlValue.$attrs.publicAttribute[item]
              if (index == 1) {
                // 特殊处理，独立控件，数组范围值
                 if ((type.includes("timerange") || type.includes("daterange")) && item.includes('[') && item.includes(']')) {
                   controlValue =controlValue.$attrs.publicAttribute.value
                   controlValue= this.getArrayDataValue(controlValue,item)
                   //debugger
                 } else{
                  //debugger
                  // 其它通用处理
                  controlValue = controlValue.$attrs.publicAttribute[item]
                 }
              } else {
                //debugger
                // 获取 row 中 对应字段的值
                controlValue = controlValue[item]
              }
            }

          })
        }

      } catch (error) {
        controlValue = ""
      }

      return controlValue
    },
    getArrayDataValue(controlValue,item){
      //debugger
      let newControlValue = controlValue
      if(item.includes('[') && item.includes(']')){
        let itemList = item.split('[')
        let valueIndex = Number(itemList[1].replace(']','')) 
        newControlValue = controlValue[valueIndex] 
      }
      return newControlValue
    },
    // 获取按钮列表查询字段值
    getDefaultmenubuttonValue(newValueList,boxType='popup') {
      //debugger
      let controlValue = ""
      newValueList.forEach((item, index) => {
        // debugger
        // 逐个参数解析并获取其值
        if (index == 0) {
          // 首个参数使用内部对象获取
          if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
            controlValue = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[item]
          } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
            
            if(boxType!='basicForm'){
              controlValue = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[item]
            }else{
              controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
            }
          }
          else {
           // debugger
            controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
          }
          this.isFromParent = false

        } else {
          if (index == 1) {
            // 获取对外开发属性KEY的值
            controlValue = controlValue.$refs[item]
          } else {
            // debugger
            // 获取对外开发属性KEY的值
            // controlValue = controlValue.$attrs.publicAttribute[item]
            if (Array.isArray(controlValue)) {
             
              // item = value[0],==>【2023-01-01,2023-02-03】
              // 日期范围控件 特殊处理
              if(item.includes('[') && item.includes(']')){
                let itemList = item.split('[')
                let valueIndex = Number(itemList[1].replace(']','')) // Number('0')
                controlValue = controlValue[0][itemList[0]][valueIndex] //controlValue =【2023-01-01,2023-02-03】
              }else{
                //debugger
                // 因为使用了FOR,所以这里是个数组，并且第一个值
                try {
                  let controlType = controlValue[0].publicAttribute.controlType
                  if(controlType =='reportSelectTree'){
                    // 下拉树
                    controlValue = controlValue[0].publicAttribute[item]
                  }
                  else if (controlType =='reportSelect'){
                    // 下拉框
                    controlValue = controlValue[0].publicAttribute[item]
                  }
                  else if (controlType =='reportSelectTable'){
                    // 下拉表格
                    controlValue = controlValue[0].publicAttribute[item]
                  }
                  else if (controlType =='customMulSelect'){
                   // debugger
                    // 下拉多选
                    controlValue = controlValue[0].publicAttribute[item].toString()
                  }
                  else{
                    controlValue = controlValue[0][item]
                  }
                } catch (error) {
                  controlValue = controlValue[0][item]
                }
                
                // if(!controlValue){
                //   controlValue = backupValue
                // }
              }
            } else {
              // 正常$refs 对象
              controlValue = controlValue.$refs[item]
            }
          }
        }

      })
      // debugger
      return controlValue
    },
  
  GetRequestNew(name) {
    const url = location.search; //获取url中"?"符后的字串
    let theRequest = new Object();
    if (url.indexOf("?") != -1) {
       let str = url.substr(1);
       let strList = str.split("&");
       for(let i = 0; i < strList.length; i ++) {
          theRequest[strList[i].split("=")[0]]=unescape(strList[i].split("=")[1]);
       }
    }
    return theRequest[name];
 },
    // 获取高级查询参数值
    getSearchProValue(newValueList,boxType='popup'){
      let _searchParams=""
      try {
        _searchParams = JSON.stringify(this.vueInstance.$store.state.searchProDrawerBoxChange.value.postData.conditionalList)
      } catch (error) {
        _searchParams=""
      }
      return _searchParams
    },
     // 获取Iframe postMessage弹框参数
    getPostMessageValue(newValueList,boxType='popup'){
      //debugger
      let _postMessageParams=""
      try {
        _postMessageParams = JSON.stringify(this.vueInstance.$store.state.postMessageFromParent.value)
      } catch (error) {
        _postMessageParams=""
      }
      return _postMessageParams
    },
    
    // 获取浏览器路径指定参数值 $fullPath.formName[0]
    getFullPathValue(newValueList,boxType='popup'){
      debugger
      let resVal= ""
      let filterName=""
      let isArrayUrl= false
      let listName =[]
      if(newValueList && newValueList.length>1){
        
        if(newValueList[1].includes('[')){
          listName = newValueList[1].split('[')
          filterName = listName[0]
          isArrayUrl = true
        }else{
          filterName = newValueList[1]
        }
        let _resVal= this.GetRequestNew(filterName)
        // _resVal="123¤456¤789"
        if(_resVal && isArrayUrl && listName[1].includes(']')){
            let _dataIndex = Number(listName[1].replace(']','')) 
            let _resValList = _resVal.split('¤')
            resVal = _resValList[_dataIndex].replace("Â",'')
        }else{
          resVal = _resVal.replace("Â",'')
        }
      }
      return resVal
     
    },
    // 获取用户字段信息
    getUserInfoValue(newValueList,boxType='popup') {
      // debugger
      let controlValue = ""
      // debugger
      newValueList.forEach((item, index) => {
        // 逐个参数解析并获取其值
        if (index == 0) {
          controlValue = this.vueInstance.$UserInfo
        } else {
          controlValue = controlValue[item]
        }
      })
      return controlValue
    },

      // 获取一般参数值
   async getCommonCtrlValue(newValueList,boxType='popup') {
    //debugger
    let controlValue = ""
    for(let index=0;index<newValueList.length;index++){
      let item = newValueList[index]
        // debugger
      // 逐个参数解析并获取其值
      if (index == 0) {
        // 首个参数使用内部对象获取
        //debugger
        if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
          controlValue = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[item]
        } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
          
          if(boxType!='basicForm'){
            controlValue = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[item]
          }else{
            controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
          }
        }
        else {
          //let  widgetRefList = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList
          controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
        }
     
        this.isFromParent = false
      }else {
        if (index == 1) {
          controlValue = controlValue.publicAttribute[item]
        } else {
          // 获取 row 中 对应字段的值
          controlValue = controlValue[item]
        }
      } 
    }
    return controlValue
  },
     // 获取表格当前选中行
   async getVxetableValue(newValueList,boxType='popup') {
      //debugger
      let controlValue = ""
      for(let index=0;index<newValueList.length;index++){
        let item = newValueList[index]
          // debugger
        // 逐个参数解析并获取其值
        if (index == 0) {
          // 首个参数使用内部对象获取
          //debugger
          if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
            controlValue = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[item]
          } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
            if(boxType!='basicForm'){
              controlValue = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[item]
            }else{
              controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
            }
          }
          else {

            controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
          }
          if (controlValue) {
            // 正常$refs 对象
            controlValue = controlValue.$refs[item]
          }
          this.isFromParent = false
        } else {
          if (index == 1) {
            // 获取对外开发属性KEY的值
            // controlValue = controlValue.$attrs.publicAttribute[item]
            // 缓存问题，直接通过对象实体 获取数据
            await controlValue.setPublicAttr() // 重新获取最新的参数数据，防止缓存问题
            controlValue = controlValue.publicAttribute[item]
          } else {
            // 获取 row 中 对应字段的值
            controlValue = controlValue[item]
          }
        }
      }
      return controlValue
    },
    // 获取默认菜单输入框的值
    getInputValue(newValueList,boxType='popup') {
      let controlValue = ""
      newValueList.forEach((item, index) => {
        // 逐个参数解析并获取其值
        if (index == 0) {
          // 首个参数使用内部对象获取
          //controlValue = this.vueInstance.refList[item]
          // controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
          if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
            controlValue = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[item]
          } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
            if(boxType!='basicForm'){
              controlValue = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[item]
            }else{
              controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
            }
          }
          else {
            controlValue = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[item]
          }
          this.isFromParent = false
        } else {
          if (Array.isArray(controlValue)) {
            // 因为使用了FOR,所以这里是个数组，并且第一个值
            controlValue = controlValue[0][item]
          } else {
            // 正常$refs 对象
            controlValue = controlValue.$refs[item]
          }

        }

      })
      return controlValue
    },
    // 逐层查询直到找到 位置
    getTillGetRefFormName(vueInstance, refFormName) {
      // debugger
      let maxNum = 20
      let resData = null
      let newItem = vueInstance.$parent
      for (let i = 0; i < maxNum; i++) {
        if (newItem) {
          if (!!newItem.$refs && !newItem.$refs.hasOwnProperty(refFormName)) {
            newItem = newItem.$parent
          } else {
            if (!!newItem.$refs) {
              resData = newItem.$refs[refFormName].designer.formConfig.queryList
            }
            break
          }
        }

      }
      return resData
    },
    // 从vFORM全局表单查询列表中，获取查询项
    getQueryItemByUrl(postUrl = "", originDataList = []) {
      //debugger
      let queryItem = null
      let queryList = []
      if (this.vueInstance.designer) {
        // 设计模式下
        queryList = this.vueInstance.designer.formConfig.queryList

      } else {
        try {

          if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
            queryList = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].designer.formConfig.queryList
          } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
            queryList = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].designer.formConfig.queryList
          }
          else {
            queryList = this.vueInstance.pageInstance.$refs[this.refFormName].designer.formConfig.queryList
          }
        } catch (error) {
          console.warn('====设计进入递归查询对象====')
          queryList = this.getTillGetRefFormName(this.vueInstance, this.refFormName) //"editContentBoxRef"
        }
      }
      if (!queryList) {
        // 为空 直接返回
        return
      }
      if (queryList && queryList.length > 0) {
        let filterList = queryList.filter((item) => {
          if (item.value == postUrl) {
            return item
          }
        })
        if (filterList && filterList.length > 0) {
          queryItem = filterList[0]
        }
      }
      return queryItem
    },
    // 从vFORM全局表单查询列表中，获取查询项
    getPopupItemByUrl(postUrl = "",boxType='popup') {
      // debugger
      let popupItem = null
      let popupList = []
      if (this.vueInstance.designer) {
        // 设计模式下
        popupList = this.vueInstance.designer.formConfig.popupList
      } else {
        if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
          popupList = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].designer.formConfig.popupList
        } else if (!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
           if(boxType!='basicForm'){
            popupList = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].designer.formConfig.popupList
           }else{
            popupList = this.vueInstance.pageInstance.$refs[this.refFormName].designer.formConfig.popupList
           }
        
        }
        else {
          popupList = this.vueInstance.pageInstance.$refs[this.refFormName].designer.formConfig.popupList
        }
      }
      if (popupList && popupList.length > 0) {
        let filterList = popupList.filter((item) => {
          if (item.value == postUrl) {
            return item
          }
        })
        if (filterList && filterList.length > 0) {
          popupItem = filterList[0]
        }
      }
      return popupItem
    },
    async getVFormPopupSearchParamsNoDataSetId(postUrl = "",boxType='popup') {
      //debugger
      let params = {}
      let popupItem = this.getPopupItemByUrl(postUrl,boxType)
      if (popupItem) {
        params = await this.getCommonParamsValue(popupItem,boxType) //await this.getVFormParamsValue(popupItem)
        //debugger
      }
      return params
    },
    // 获取VFORM设置的查询参数
   async getVFormPopupSearchParams(postUrl = "",boxType='popup') {
      let params = {}
      let popupItem = this.getPopupItemByUrl(postUrl,boxType)
      if (popupItem) {
        params = await this.getVFormParamsValue(popupItem)
      }
      return params
    },
    getVFormPopupActionName(postUrl = "") {
      let actionName = ""
      let popupItem = this.getPopupItemByUrl(postUrl)
      if (!!popupItem) {
        let actionType = this.getOtherParamsValue("actionType", popupItem)
        if (!!actionType && actionType == 'popup') {
          actionName = this.getActionParamsValue("actionName", popupItem)
        } else {
          this.vueInstance.$message.error('VFORM表格actionType查询数据类型错误 actionType:' + actionType);
        }
      }
      return actionName
    },
    async checkRequireParamsIsNull(postUrl){
      let _requireParamsIsNull= false
      try {
        let paramsValue = {}
        let queryItem = this.getQueryItemByUrl(postUrl)
        if (queryItem) {
          paramsValue = await this.getCommonParamsValue(queryItem)
          let queryConfig = this.getActionParamsValue("query", queryItem)
          for (const [k,v] of Object.entries(paramsValue)) {
            if(!_requireParamsIsNull){
               if(queryConfig[k].hasOwnProperty("requireParams") && queryConfig[k].requireParams ) {
                  if(!v){
                     console.error(" 必填参数为空，跳过查询！！Key:",k)
                    _requireParamsIsNull = true
                  }
               }
            }
          }
        }
      } catch (error) {
        _requireParamsIsNull= false
      }
  
      return _requireParamsIsNull
    },
    // 获取VFORM设置的查询参数
    async getVFormSearchParams(postUrl = "") {
      // debugger
      let params = {}
      let queryItem = this.getQueryItemByUrl(postUrl)
      if (queryItem) {
        params = await this.getVFormParamsValue(queryItem)
      }
      ///////////////////////////////获取自身组件传递的参数////////////////////////////////////
      try {
        let originSubItem = this.vueInstance.$store.state.currentClickCtrlOptions
        // 获取自身组件传递的参数
        if (originSubItem && Object.keys(originSubItem).length > 0) {
          let selfParams = await this.getCommonParamsValue(originSubItem);
          if (selfParams && Object.keys(selfParams).length > 0) {
            // 合并覆盖 查询参数
            if (params.hasOwnProperty("Parameter")) {
              params.Parameter = Object.assign({}, params.Parameter, selfParams)
            }
          }
          // 获取临时参数后置空
          this.vueInstance.$store.commit("set_currentClickCtrlOptions", {})
        }
      } catch (error) {

      }
      ///////////////////////////////获取自身组件传递的参数////////////////////////////////////
        // 高级搜索 参数设置
     if(this.vueInstance.$store.state.searchType && this.vueInstance.$store.state.searchType.value=='searchPro'){
      // try {
        
      //   params.Parameter.SearchPro = JSON.stringify(this.vueInstance.$store.state.searchProDrawerBoxChange.value.postData.conditionalList)
      // } catch (error) {
      //   console.error(" 高级搜索参数 错误，请检查！！")
      //   if(params.Parameter && params.Parameter.hasOwnProperty("SearchPro")){
      //     params.Parameter.SearchPro =""
      //   }
      
      // }
    }else{
      try {
        // 普通查询时，移除高级查询参数值，因为SearchPro 优先基本比较高
        delete params.Parameter.SearchPro
      } catch (error) {
        
      }
      
    }
      return params
    },
    //this.requstConfig.postType

    getVFormDataSearchUrl(postUrl = "") {
      let searchUrl = ""
      let queryItem = this.getQueryItemByUrl(postUrl)
     // debugger
      if (!!queryItem) {
        let actionType = this.getOtherParamsValue("actionType", queryItem)
        if (!!actionType && actionType == 'api') {
          searchUrl = this.getActionParamsValue("actionName", queryItem)
        } else {
          this.vueInstance.$message.error('VFORM表格actionType查询数据类型错误 actionType:' + actionType);
        }
      }
      // 校验请求是否为本地API 或 可直连API
      let isWebAPI = this.checkIsWebAPI(postUrl)
      if(queryItem?.actionParams.hasOwnProperty("CDATASOURCE_TYPE_CODE") && isWebAPI){
         // 服务地址端口+ 接口路径API 
          searchUrl = queryItem.actionParams.CHOST_CPORT + queryItem.actionParams.CDATASET_CONTENT
          //debugger
      }
      if(!searchUrl){
        // 如果为空直接默认取值
        searchUrl = "api/MD/DataSet/GetListByDataSetId"
      }
      return searchUrl
    },
    // 校验请求是否为本地API 或 可直连API
    checkIsWebAPI(postUrl = ""){
      let isWebAPI = false
      try {
        let queryItem = this.getQueryItemByUrl(postUrl)
        if(queryItem?.actionParams.CDATASOURCE_TYPE_CODE =="WEBAPI"){
          isWebAPI = true
        }
      } catch (error) {
        isWebAPI = false
      }
      return isWebAPI
    },
    // 获取请求类型
    getPostTypeFn(postUrl = ""){
      let _defaultType = "post"
      try {
        let queryItem = this.getQueryItemByUrl(postUrl)
      
        if(queryItem?.actionParams.hasOwnProperty("CDATASET_TYPE") && queryItem?.actionParams.CDATASET_TYPE =='GET'){
            _defaultType = "get"
        }
      } catch (error) {
        _defaultType = "post"
      }
      return _defaultType
    },
    // 获取数据集ID
    getVFormDataSetID(postUrl = "") {
      // debugger
      let datasetId = ""
      let queryItem = this.getQueryItemByUrl(postUrl)
      if (!!queryItem) {
        datasetId = this.getActionParamsValue("CDATASET_ID", queryItem)
      }
      return datasetId
    },
    // 获取本地接口查询地址
    getLocalAPI(postUrl=""){
      let searchUrl = ""
      let queryItem = this.getQueryItemByUrl(postUrl)
      if(queryItem?.actionParams.hasOwnProperty("CDATASOURCE_TYPE_CODE") && queryItem.actionParams.CDATASOURCE_TYPE_CODE =="WEBAPI"){
          // 服务地址端口+ 接口路径API 
          searchUrl = queryItem.actionParams.CHOST_CPORT + queryItem.actionParams.CDATASET_CONTENT
      }
     return searchUrl
    },
    // 是否根据本地API 查询数据，不用二次转换查询
    async getDBDataByLocalAPI(params,postUrl,callBack=null) {
      let dataList =[]
      let _url = this.getLocalAPI(postUrl)
      if(params.hasOwnProperty("Parameter")){
         params = params.Parameter
      }
      let postType = this.getPostTypeFn(postUrl)
      if(postType == 'post'){
        await request["post"](_url, params).then(res => {
          if ([4, 5].includes(res.Data)) {
            res.Datas = res.Datas
          }
          if (res && res.Datas) {
            dataList = res.Datas
            // 记录总数
            if(callBack){
              callBack(res) 
            }
          }
        });
      }else{
        await request["get"](_url,null,params).then(res => {
          if ([4, 5].includes(res.Data)) {
            res.Datas = res.Datas
          }
          if (res && res.Datas) {
            dataList = res.Datas
            // 记录总数
            if(callBack){
              callBack(res) 
            }
          }
        });
      }
      return dataList
    },
    // 此处为VFORM表单的通用参数
    // 通过数据集ID 从数据库中获取数据列表
    // postUrl= 'query1' 来自选择数据源 选择加载API,大部分为为配置中一级options下的actionName
    async getDBDataByActionName(postUrl = "" ,callBack=null) {
      //debugger
      let _url = `api/MD/DataSet/GetListByDataSetId` // 固定取值地址
     
      let params = await this.getVFormSearchParams(postUrl);
    
      if (!params.hasOwnProperty('Id') || !!!params.Id) {
        console.error("====DataSetId 为空，跳过查询====")
        return
      }

      let dataList = []
      let hasRequireParamsNull = await this.checkRequireParamsIsNull(postUrl);
     // debugger
      if(hasRequireParamsNull){
        // console.error(" 必填参数为空，跳过查询！！")
        return
      }
        ////////////////////
        let isLocalAPI = this.checkIsWebAPI(postUrl)
        if(isLocalAPI){
           dataList = await this.getDBDataByLocalAPI(params,postUrl,callBack)
          // debugger
           return dataList
        }
       // debugger
        ////////////////////
        /// <summary>
        /// 数据集类型
        /// 0:表
        /// 1:视图
        /// 2:自定义脚本
        /// 3:存储过程
        /// 4:GET
        /// 5:POST
        /// 6:FILE
        /// </summary>
      await request["post"](_url, params).then(res => {
        if ([4, 5].includes(res.Data)) {
          res.Datas = JSON.parse(res.Datas)
        }
        if (res && res.Datas) {
          dataList = res.Datas
          // 记录总数
          if(callBack){
            callBack(res) 
          }
        }
      });
      return dataList
    }
  }
}