<template>
    <div>
      <el-form-item label="隐藏头部">
      <el-switch v-model="optionModel.hideHeader"></el-switch>
    </el-form-item>
    <el-form-item label="显示ICON">
      <el-switch v-model="optionModel.showIcon"></el-switch>
    </el-form-item>
    <el-form-item v-show="!!optionModel.showIcon" label="ICON颜色">
        <el-color-picker
            v-model="optionModel.iconColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="控件背景颜色">
        <el-color-picker
            v-model="optionModel.allBgColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="内容背景颜色">
        <el-color-picker
            v-model="optionModel.bodyBgColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
    <el-form-item  label="边框颜色">
        <el-color-picker
            v-model="optionModel.borderColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>
      <el-form-item label="标签字体">
        <el-select v-model="optionModel.fontWeight">
            <el-option label="默认" value="normal"></el-option>
            <el-option label="粗" value="bold"></el-option>
            <el-option label="加粗" value="bolder"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item  label="标签字体颜色">
        <el-color-picker
            v-model="optionModel.labelColor"
            show-alpha
            :predefine="predefineColors">
      </el-color-picker>
    </el-form-item>

    </div>
  </template>
  
  <script>

    import i18n from "@/utils/i18n"

  
    export default {
      name: "cardSetting-editor",
      componentName: 'PropertyEditor',
      mixins: [i18n],
      components: {
      },
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
      data() {
        return {
            predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
        }
      },
      created() {
      
      },
      methods: {
       
  
      }
    }
  </script>

  <style lang="scss" scoped>
  
  
  </style>
  