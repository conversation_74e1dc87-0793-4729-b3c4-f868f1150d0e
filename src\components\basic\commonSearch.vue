<template>
    <!-- <div v-if="searchDataList.length>0" class="flex">
        <el-input style="width:180px" v-for="(item) in searchDataList" :key="item.field" :clearable="true" size="small"  :fieldName="item.field" v-model="searchOptions[item.field]" :placeholder="item.title"></el-input>
        <el-button :loading="loading" @click="searchAction()" type="primary" icon="vxe-icon-add" size="mini">查询</el-button>
    </div> -->
    <div class="flex items-center">
         <div class="flex items-center" v-for="(item) in searchDataList" :key="item.field">
            <span v-show="!!item.title" style="font-size: 12px;">{{ item.title }}</span>
            <template v-if="item.controlType == 'templateType'">
                <el-select style="width:180px;margin: 0px 10px;" size="mini" v-model="searchOptions[item.field]" placeholder="请选择">
                                <el-option v-for="item in templateOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
            </template>
            <template v-else>
                <el-input @keyup.native="keyupEvent" style="width:180px;margin: 0px 10px;" :clearable="true" size="mini" :fieldName="item.field"
                    v-model="searchOptions[item.field]" :placeholder="item.title"></el-input>
            </template>

        </div>
        <el-button style="" :loading="loading" @click="searchAction()" title="查询" size="small" type="primary"
            icon="el-icon-search">
        </el-button>
        <el-button @click="resetAction()" title="重置" size="small" type="primary" icon="el-icon-refresh-right"></el-button>
    </div>
       
</template>
<script>
 import config from '@/config'
//  import request from '@/libs/request'
// import { useUrlSearchParams } from "@vueuse/core";
export default {
    name:'commonSearch',
    data(){
        return {
            maxLoadTime:6,
            loadRefTimer:null,
            loading:false,// 是否提交数据中...
            config:config,
            tableHeader:{},// 搜索表的信息
            searchDataList:[],
            searchOptions:{},
            searchOptionsCopy:{},
            templateOptions: [
            {
                value: null,
                label: '全部'
            },
                { //模板列表
                value: 'Web',
                label: 'Web'
            }, {
                value: 'App',
                label: 'App'
            },
            { //模板列表
                value: 'WebHome',
                label: 'WebHome'
            },
            {
                value: 'AppHome',
                label: 'AppHome'
            }
        ],
        }
    },
    watch:{
        // $route: {
        //     handler(to,from) {
        //        // 重置
        //        this.searchDataList =[]
        //        if(to){
        //             this.$nextTick(()=>{
        //                 this.tryTillGetData() 
        //           })
        //        }
           
        //     },
        //     deep: true,
        //     immediate: false,
        // },
    },
    mounted(){
        this.$nextTick(()=>{
            this.tryTillGetData()
        })
    },
    methods:{
           // 表格查询框 回车键
       keyupEvent($event) {
            if ($event.keyCode === 13) {
                this.searchAction()
            }
        },
        getRequest(name) {
          let urlStr = window.location.search
          let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
          let r = urlStr.substr(1).match(reg);
          if (r != null) {
              return unescape(r[2]);
          };
          return null;
      },
        // 重置
        resetAction(){
            this.searchOptions  = Object.assign({}, this.searchOptionsCopy)
        },
        // 通用查询
        searchAction(){
          let postParams = {}
          for (const [k,v] of Object.entries(this.searchOptions)) {
                    if(!!this.searchOptions[k]){
                        postParams[k]=v
                    }
            }
          //  debugger
          this.loading = true
          this.$store.commit("set_commonSearch",postParams );
          this.loadRefTimer = setTimeout(()=>{
            this.loading = false
          },1000)
        },
     
        // 获取 当前页面原始表格列头信息
         getPageOriginColumnsViewdetail() {

           // debugger
            let routerName = this.$route.name
            if(routerName =="designlist"){
               // 普通型-不同页面，不用路由，没有参数，
                return this.$store.state.pageOriginColumns.value[routerName];
            }else{
                 // 通用性-相同页面路由，不同参数
                 let formName = this.getRequest("formName")
              return this.$store.state.pageOriginColumns_viewdetail.value[formName];
            }
        },
        // 循环直到获取到数据
        tryTillGetData(){
           // debugger
            let _self = this
            let dataObj = this.getPageOriginColumnsViewdetail()
            if(!!dataObj){
              let list =  this.formatDataList(dataObj)
            }else{
                this.searchDataList =[]
                this.tableHeader ={}
                if( _self.maxLoadTime>0){
                    setTimeout(()=>{
                        _self.maxLoadTime =  _self.maxLoadTime-1
                            this.tryTillGetData()
                    },300)
                }
            }
        },
        // 格式化 需要查询的字段列表
        formatDataList(dataObj){
            //debugger
            this.searchDataList =[]
            let searchOptionsTemp={}
            this.tableHeader ={}
            let filterDataList =[]
            let mainColumns =[]
            let routerName = this.$route.name
            if(routerName =="designlist"){
                mainColumns = dataObj
            }else{
                mainColumns = dataObj.MainTable.TableDetail
                this.tableHeader = dataObj.MainTable.TableHeader
            }
          
            if(mainColumns && mainColumns.length>0){
                let maxCount =0
                filterDataList = mainColumns.filter(item=>{
                    if(item.iisQuery && maxCount<3){
                        maxCount = maxCount +1
                        searchOptionsTemp[item.field]=null
                        return item
                    }
                })
            } 
            this.$nextTick(()=>{
                // 必须NEXT 后，否则无法输入
                this.searchOptionsCopy = Object.assign({},searchOptionsTemp)
                this.searchOptions = searchOptionsTemp
                //debugger
                this.searchDataList = filterDataList
            })
        }
    }
}

</script>

<style>
.items-center{
    flex-wrap: wrap;
}
</style>