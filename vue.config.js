// 'use strict'
const BASE_URL = process.env.NODE_ENV === 'production' ? '/subapp/customform' : '/'
const path = require('path')
const IS_PROD = process.env.NODE_ENV === 'production'
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const productionGzipExtensions = ['js', 'css']
const { defineConfig } = require('@vue/cli-service')
const Timestamp = new Date().getTime()
function resolve (dir) {
  return path.join(__dirname, dir)
}

const npmConfigArgv = (process.env.npm_config_argv === undefined) ? null : JSON.parse(process.env.npm_config_argv)
/*
console.log('npm config: ', npmConfigArgv)
const procArgv = process.argv
console.log('npm config: ', procArgv)
*/
let buildProdFlag = false
if (!!npmConfigArgv) {
  npmConfigArgv.original.forEach(cItem => {
    if (cItem === 'build') {
      buildProdFlag = true
    }
  })
}

const mvdir = require('mvdir');
if (IS_PROD && buildProdFlag) {
  mvdir('index_template/index_prod.html', 'public/index.html', { copy: true });
} else {
  mvdir('index_template/index_dev.html', 'public/index.html', { copy: true });
}


module.exports = defineConfig({
    // 打包时不生成.map文件
  productionSourceMap: false,
  publicPath: BASE_URL,
  assetsDir: './',

  /* 开启vue运行时模板编译功能！！ */
  runtimeCompiler: true,

  lintOnSave: false,

  /* 指定node_modules目录中需要做babel转译的依赖库 */
  transpileDependencies: [
    'element-ui', 'vuedraggable',
  ],

  css: {
    loaderOptions: {
      scss: {
        /* 自动引入全局scss文件 */
        prependData: `
          @import "./src/styles/global.scss";
        `
      }
    }
  },

  configureWebpack: (config) => {
    config.output.library = 'customform';
    config.output.libraryTarget = 'umd';

    // config.devtool = 'source-map'
    // config.output.libraryExport = 'default'  /* 解决import UMD打包文件时, 组件install方法执行报错的问题！！ */
    if (process.env.NODE_ENV === 'production') {
      return{
        output: { // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
          filename: `js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`,
          chunkFilename: `js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`

        },
       plugins: [
          // 如果这个值是一个对象，则会通过 webpack-merge 合并到最终的配置中
         // 如果你需要基于环境有条件地配置行为，或者想要直接修改配置，那就换成一个函数 (该函数会在环境变量被设置之后懒执行)。
         //该方法的第一个参数会收到已经解析好的配置。在函数内，你可以直接修改配置，或者返回一个将会被合并的对象
           new CompressionWebpackPlugin({
                 algorithm: 'gzip',//默认值
                 //test:/\.js$|\.html$|.\css/, //匹配文件名
                 test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),//匹配文件名
                 threshold: 10240,//对超过10k的数据压缩
                 deleteOriginalAssets: false //不删除源文件
                 //配置参数详解
                 // asset： 目标资源名称。 [file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
                 // algorithm： 可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
                 // test： 所有匹配该正则的资源都会被处理。默认值是全部资源。
                 // threshold： 只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
                 // minRatio： 只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
             })
       ],

     }
    }
    if (IS_PROD && buildProdFlag) { /* 仅生产环境使用 */
      /* CDN打包，需要修改index.html加入CDN资源 */

      console.log('==========================================')

      config.externals = {
        'vue': 'Vue',
        'element-ui': 'ELEMENT',
        //'quill': 'Quill',
      }
    } else {
      // config.externals = {
      //   'vue': 'Vue',
      //   'element-ui': 'ELEMENT',
      //   'axios': 'axios',
      // }
    }
  },

  chainWebpack: config => {
    // if (process.env.NODE_ENV === 'production') {
    //   config
    //     .plugin('webpack-bundle-analyzer')
    //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);
    // }
    // config.module
    //   .rule('change-prefix')
    //   .test(/\.js$/)
    //   .include.add(path.resolve(__dirname, './node_modules/element-ui/lib'))
    //   .end()
    //   .use('change-prefix')
    //   .loader('change-prefix-loader')
    //   .options({
    //     prefix: 'el-',
    //     replace: 'parent-'
    //   })
    //   .end()

    // 作者：Jerry_W
    // 链接：https://juejin.cn/post/7109402211653779469
    // 来源：稀土掘金
    // 著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
    /* 配置svg图标自动加载 begin */
    config.module
        .rule('svg')
        .exclude.add(resolve('src/icons'))
        .end()
    config.module
        .rule('icons')
        .test(/\.svg$/)
        .include.add(resolve('src/icons'))
        .end()
        .use('svg-sprite-loader')
        .loader('svg-sprite-loader')
        .options({
          symbolId: 'icon-[name]'
        })
    /* 配置svg图标自动加载 end */
  },
  devServer: {
      // 经过一段时间的排查发现是webpack的问题，排查方式就是打开控制台，看这个框的一些属性，通常会有一些关于这个技术的关键词
    //https://webpack.docschina.org/configuration/dev-server/#overlay
    //vue-cli 关闭 Uncaught error 的全屏提示
    client: {
      overlay: false,
    },
    port: 10007,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
      "Access-Control-Allow-Methods": "*",
    }
  },

})
