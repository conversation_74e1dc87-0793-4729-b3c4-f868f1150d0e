<template>
    <div>
        <!-- 动态参数 -->
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'"
            style="font-weight: bold;">参数</div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">
            <div :key="index" v-for="(item, index) in actionParamsList" style="margin-bottom: 5px;"
                class="flex justify-center items-center">
                <div>KEY</div>
                <div style="margin:0 10px;" l-form-item label="KEY" prop="key">
                    <el-input placeholder="key" v-model="item.key"></el-input>
                </div>
                <div>VALUE</div>
                <div style="margin:0 10px;" label="VALUE" prop="value">
                    <el-input placeholder="固定值/+选择获取" v-model="item.value"></el-input>
                </div>
                <div>
                    <el-button type="text" @click="setParams(item)">+选择</el-button>
                </div>
                <div class="flex justify-center items-center" style="margin-left:5px"> 
                    <!-- <el-checkbox style="margin-left:5px;"
                        v-model="item.execNow">立刻执行</el-checkbox> -->
                        <i style="margin-left:5px;"
                        @click="deleteParam(item, index)" class="el-icon-delete"></i>

                </div>
            </div>
        </div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">
            <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
        </div>
        <el-dialog title="参数控件值 选择" :visible.sync="showDataSourceialogFlag" v-if="showDataSourceialogFlag" v-dialog-drag
            append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
            :close-on-press-escape="false" :destroy-on-close="true">
            <div>
                <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id"
                    :expand-on-click-node="false" highlight-current class="node-tree" icon-class="el-icon-arrow-right"
                    @node-click="onNodeTreeClick"></el-tree>
            </div>
            <div slot="footer" class="dialog-footer">

                <el-button type="primary" size="large" @click="submitNodeEvent()">
                    确定</el-button><el-button size="large" @click="showDataSourceialogFlag = false">
                    取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import cloneDeep from "clone-deep"
export default {
    name: "",
    data() {
        return {
            actionParamsList: [],
            showDataSourceialogFlag: false,
            curSetParamsItem: null,// 当前参数设置 编辑 项值
            nodeCtrlValue:"",// 节点选中内容
        }
    },
    props:{
        editItemForm:{
            type:Object,
            default(){
                return {

                }
            }
        }
    },
    methods: {
        // 添加参数
        addParamsEvent() {
            let newValue = this.actionParamsList.length + 1
            this.actionParamsList.push(
                { key: "", execNow: false, value: "" }
            )
        },
        // 移除参数
        deleteParam(item, index) {
            this.actionParamsList.splice(index, 1)
        },
        // 弹框选择 参数 控件 来源
        setParams(item) {
            this.curSetParamsItem = item
            this.showDataSourceialogFlag = true
        },
        getNodeTreeData(){
            let copyData = cloneDeep(this.designer.nodeTreeData)
            let dataList = this.setPublicAttribute(copyData)
            return dataList
        },
         // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData) {
      let dataList = []
      for (let i = 0; i < nodeTreeData.length; i++) {
        let item = nodeTreeData[i]
        if (item.hasOwnProperty("children")) {
          this.setPublicAttribute(item.children)
          dataList.push(item)
        } else {
          let randomNum = Math.floor(Math.random() * 100000000 + 1)
          // 优先子集对外开放属性
          if (item.hasOwnProperty("publicSubAttribute")) {
            //debugger
            item.children = []

            if (item.publicSubAttribute && item.publicSubAttribute.length > 0) {
              item.publicSubAttribute.forEach((subItem, index) => {

                let valueItem = {
                  label: subItem.value,
                  id: subItem.value + "-" + randomNum + index
                }
                let keyItem = {
                  label: subItem.key,
                  id: subItem.key + "-" + randomNum + index,
                  children: [valueItem]
                }
                //debugger
                item.children.push(keyItem)
              })
              // debugger
              dataList.push(item)
            }

          } else {
            // 对外开放属性
            if (item.hasOwnProperty("publicAttribute")) {
              item.children = []
              if (item.publicAttribute && item.publicAttribute.length > 0) {
                item.publicAttribute.forEach((subItem, index) => {
                  let kidItem = {
                    label: subItem,
                    id: subItem + "-" + randomNum
                  }
                  item.children.push(kidItem)
                })
                dataList.push(item)
              }
            } else {
              dataList.push(item)
            }
          }
        }

      }
      return dataList

    },
         // 参数值设置 弹框 树节点 点击事件
        onNodeTreeClick(params) {
            //debugger
            this.nodeCtrlValue =""
            let hasChildren = params.hasOwnProperty("children")
            let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
            let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
            let parentNodeLabel = parentsNode.data.label
            let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
                ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue,parentsNode,params)
            if(!hasChildren){
            this.nodeCtrlValue = ctrlValue
                // this.curSetParamsItem.value = ctrlValue
            }else{
                this.$message.warning('此节点不可选！')
            }
        },
         // 获取第三层 控件名称
    tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params) {
      // debugger
      let newCtrlVal = ctrlValue
      let layoutList = ["defaultmenubutton"]//|| parentsNode.parent.data.label.includes('card'),"card"
      let level3NodeType = ""
      try {
        level3NodeType = parentsNode.parent.data.text
        //  if(layoutList.includes(level3NodeType)){
        if (layoutList.includes(level3NodeType)) {
          newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
        }
      } catch (error) {
        level3NodeType = ""
      }
      return newCtrlVal
    },
    }
}

</script>