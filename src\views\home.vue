<template>
  <div ref="rootRef" class="flex flex-col justify-left">
    <div class="actionButtonClass">
      <!-- 权限操作按钮 -->
      <actionBtttons></actionBtttons>
      
      <!-- {{ contentOptions.height }} -->
    </div>
    <!--注意:h-content 在baseCommon.scss 中统一设置的占用高度 -->
    <!--注意:contentBoxRef 名称固定 不可修改，mixin里面需要根据ref名称获取高度/宽度-->
    <div ref="contentBoxRef" :class="isCilent ? 'flex h-contentNew' : 'flex h-content'">
      <div :style="[
        {
          height: contentOptions.height + 'px',
          width: 100 + '%',
        },
      ]">
        <!-- 主表  -->
        <div class="paddingLR10">
          <nvxeGrid ref="mainTableRef" :showCheckBoxColumn="true" :requstConfig="requstConfig" :height="contentOptions.height"
            :columns="gridOptions.columns" :showOperationColumn="true">
            <template v-slot:operate="{ row }">
              <div>
                <el-button @click="actionPushView(row)" type="text" size="mini">预览</el-button>
                <el-button @click="preCopyRowData(row)" type="text" size="mini">复制页面</el-button>
                <el-button @click="copyURL(row)" type="text" size="mini">复制引用URL</el-button>
                <el-button @click="actionBtn('iisDel', row)" type="text" size="mini"><span
                    style="color:red">删除</span></el-button>
                <el-button @click="actionBtn('iisEdit', row)" type="text" size="mini">编辑</el-button>
                <el-button @click="jumpToParentPath(row)" type="text" size="mini">系统菜单</el-button>
              </div>
            </template>
          </nvxeGrid>
        </div>

      </div>
    </div>
    <!-- 注意:editContentBoxRef名称固定 不可修改,编辑弹框 -->
    <editContentBox :paramsFromList="paramsFromList" ref="editContentBoxRef" @submitSuccess="submitSuccess">
    </editContentBox>
  </div>
</template>
    
<script>
//  import config from '@/config'
import emitterFn from '@/libs/mitt'
import { getCookie } from "@/util/setStore";
import { templateJSONData } from '@/enum/testData'
import editContentBox from "./edit/edit-home.vue";
import listMixin from "@/views/mixins/mixinList"; // 默认导入
import actionBtttons from "@/components/basic/actionBtttons.vue";
export default {
  name: "designlist", // 报表表单设计列表
  mixins: [listMixin], // 导入列表通用方法，可以继承重写 nvxeGrid
  components: {
    actionBtttons,
    editContentBox,
  },
  provide() {
    return {
      sourceVFormRenderState: "",// add by andy
    }
  },
  data() {
    return {
      templateJSONData: templateJSONData,
      tabelConfigs: {
        rowId: 'CID',
        menuConfig: {}, // 右键菜单配置
        footerConfig: {
          leftToolShow: false, // 是否显示左侧工具栏列筛选
          rightToolShow: false, // 是否显示右侧工具栏分页
          pageConfig: { // 分页配置
            total: 0,
            currentPage: 1,
            pageSize: 10,
          }
        }
      },
      currentMainRow: null,// 当前主表选中信息
      searchDetailParams: {
        rowId: null,// 当前选中行ID
        randomNumber: "",// 随机码
      },
      // 主表查询配置
      requstConfig: {
        postUrl: `api/MD/VisualFormDesigner/`,
        actionName: "GetAll",
        postType: "get",
        postParams: {
          //?condition=&start=1&length=20
        },
      },

      loadingData: false, // 主表加载中...
      // 主表配置
      gridOptions: {
        columns: [{
          type: "seq",
          fixed: "left",
          title: "序号",
          width: 50,
        },
        // 重写覆盖 模板默认设置
        {
          fixed: 'right',
          slots: { default: 'operate' },
          title: '操作',
          headerAlign: 'center',
          align: 'center',
          width: 320,
        },

        {
          field: "CNAME",
          title: "功能名称",
          width: "220",
          iisQuery: 1,
        },
        {
          field: "CCODE",
          title: "功能编码",
          width: "220",
          iisQuery: 1,
        },
        // {
        //   field: "type",
        //   title: "功能分类",
        //   width: "220",
        // },
        {
          field: "CTEMPLATE_TYPE",
          title: "模板类型",
          width: "100",
          align: "center",
          iisQuery: 1,
          controlType: "templateType",
        },
        // {
        //   field: "CSEQ",
        //   title: "功能排序",
        //   width: "220",
        // },
        {
          field: "CDESC",
          title: "功能说明",
          //width: "220",
        },
          // {
          //   field: "creater",
          //   title: "创建人",
          //   width: "120",
          // },
          // {
          //   field: "CDATETIME_CREATED",
          //   title: "创建时间",
          //   width: "120",

          // },
        ],
        data: [],

      },

    };
  },
  computed: {
    isCilent() {
      return getCookie("client") ? true : false;
    },
  },
  created() {

  },
  mounted() {
     // 注册监听 
     emitterFn.on("deleteDesign", (params) => {
          let mainTableRef = this.$refs["mainTableRef"]
          if (mainTableRef) {
            mainTableRef.searchTableData()
          }
    })
    // 储存表格列头信息
    this.setPageOriginColumns(this.gridOptions.columns)
    // this.$nextTick(()=>{
    //   this.getTemplateData()
    // })
  },
  destroyed() {
    // 移除监听 
    emitterFn.off("deleteDesign")
  
  },
  methods: {
    preCopyRowData(row) {
      this.$confirm('确定复制当前选中数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.copyRowData(row)
      }).catch(() => {

      });
    },
    // 复制当前选中行数据
    // CCODE: "德芙项目测试"
    // CDATETIME_CREATED: "2023-08-01T11:13:23.03"
    // CDATETIME_MODIFIED: "2023-08-01T11:13:23.03"
    // CDESC: "德芙项目测试"
    // CENTERPRISE_CODE: 100
    // CID: 6915471044575237
    // CINSTANCE_ID: ""
    // CJSON_DATA: "{"widgetList":[{"type":"defaultmenubutton","icon":"
    // CNAME: "德芙项目测试"
    // CORG_CODE: 0
    // CROWREMARK: ""
    // CSEQ: 23
    // CSTATE: "A"
    // CSTATE_BOOL: true
    // CTEMPLATE_TYPE: "Web"
    // CTYPE: "1"
    // CUSER_CREATED: "SYS"
    // CUSER_MODIFIED: "SYS"
    // IS_READONLY: true
    // IS_UPDATE: false
    copyRowData(row) {
      let _self = this
      let _url = `api/MD/VisualFormDesigner/GetByID`
      let _urlAdd = `api/MD/VisualFormDesigner/add`
      let params = {
        "ID": row.CID
      }
      this.request['get'](_url, null, params).then(res => {
        if (!!res.Success) {
          let randomCodeNum = Math.floor(Math.random() * 10000000 + 1)
          let newParams = res.Datas
          delete newParams.CID
          delete newParams.CSEQ
          delete newParams.CDATETIME_CREATED
          delete newParams.CDATETIME_MODIFIED

          newParams.CCODE = newParams.CCODE + "_Copy_" + randomCodeNum
          newParams.CNAME = newParams.CNAME + "_Copy_" + randomCodeNum
          _self.request["post"](_urlAdd, newParams).then(subRes => {
            if (!!subRes.Success) {
              _self.$message({
                message: '复制成功！',
                type: 'success'
              });
              _self.submitSuccess()
            }
          });
        }
      })
    },
    // 调整到主应用路由
    jumpToParentPath(row) {
      let _path = `/customform/designview?formName=` + row.CID
      this.$actions.setGlobalState({
        type: "jumpUrl",
        data: {
          url: "/stytems/systemMenu",
          title: '系统菜单',
          params: {
            name: '' + row.CNAME,
            path: _path
          },
          action: "add",
        },
      });
    },
    // 重写mixins>>普通查询
    commonSearchChange(e) {
      let params = {
        resetPage: true, // 是否重置翻页
        homePageSearch: e.value, //普通查询 commonSearch
        // condition:e.value,
        // searchPro:{} // 高级搜索
      }
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        mainTableRef.searchTableData(params)
      }
    },
    beformDeleteAction() {
      let _self = this
      this.$confirm('此操作将删除该选择数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _self.deleteAction()
      }).catch(() => {

      });
    },
    // 重写删除功能
    deleteAction() {
      //debugger
      // let _self = this
      let _url = `api/MD/VisualFormDesigner/Delete`
      let row = this.currentRow_get()
      let params = {
        "CID": row.CID
      }
      this.request['post'](_url, params).then(res => {
        this.$message({
          message: res.Content,//'执行成功！',
          type: 'success'
        });
        setTimeout(() => {
          // 刷新数据
          let mainTableRef = this.$refs["mainTableRef"]
          if (mainTableRef) {
            mainTableRef.searchTableData()
          }

        }, 1000)
      })
    },
    // 统一方法调度 执行 
    actionBtn(type = 'iisEdit') {
      //debugger
      this.$store.commit("set_actionType", type)
    },
    // 添加、编辑成功 回调事件
    submitSuccess() {
      // debugger
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        mainTableRef.searchTableData()
      }
    },
    // getTemplateData(){
    //  // debugger
    //   let data = this.$store.state.formTemplateList;
    //   if(data && data.length==0){
    //     let ttdata = this.templateJSONData
    //      data.push(...ttdata)
    //   }

    //   let tableRef = this.$refs["mainTableRef"].getRefInstance()
    //   if(!!tableRef){
    //     tableRef.loadData(data)
    //   }
    //   return data
    // },

    // 复制预览路径
    async copyURL(row) {
      //let tableType= row.tableType
      // 单表 /designview?formName=11637
      let copyText = window.location.host+`/subapp/customform/designview?formName=` + row.CID

      try {
        await this.copyToClipboard(copyText);
        setTimeout(() => {
          this.$message({
            message: '引用路径已复制',
            type: 'success'
          });
        }, 100)
      } catch (err) {
        console.error('复制失败: ', err);
      }
    },
    // 预览页面
    actionPushView(row) {
      this.$store.commit("set_currentRow", row);
      this.$nextTick(() => {
        this.$router.push(
          {
            path: '/designview',
            query: {
              formName: row.CID,
              retrunPath: "designlist"
            }
          })
      })

    },
    // 设置从表查询配置信息和其他参数
    // setSearchDetailParams(){
    //     if(!!this.currentMainRow){
    //       this.searchDetailParams.rowId=this.currentMainRow.CID// 当前选中行ID
    //       this.searchDetailParams.resetPage = true // 是否需重置从表分页
    //       this.searchDetailParams.randomNumber=this.getRandomNum()// 随机码
    //     }
    // },
    // // 主表加载数据前，清空从表数据
    // beforeLoadDataEvent(params){
    //    // console.log('=====beforeLoadDataEvent====')
    //     let $table = this.$refs["tab1TableRef"].getRefInstance() 
    //     //删除指定行数据，指定 row 或 [row, ...] 删除多条数据，如果为空则删除所有数据
    //     $table.remove()
    // },
    // 首次>>主表加载数据完毕后 回调函数>>主要用于加载从表数据
    // afterLoadDataEvent(params){
    //     this.currentMainRow = params.currentRow
    //     // 查询从表信息
    //     this.setSearchDetailParams()
    // },
    // 主表切换选中行，加载从表数据
    // currentChangeEvent(params) {
    //     //debugger
    //     this.currentMainRow = params.currentRow
    //     // 查询从表信息
    //     this.setSearchDetailParams()
    // }
  }

};
</script>
    
<style scoped lang="scss"></style>
    