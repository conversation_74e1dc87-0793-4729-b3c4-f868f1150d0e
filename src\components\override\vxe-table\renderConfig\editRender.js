import Vue from 'vue'
// 自定义 下拉配置弹框
import customSelect from '@/components/override/vxe-table/custom/customSelect'
Vue.component('customSelect', customSelect)

// 自定义 逻辑渲染控件
import customRenderCtrl from '@/components/override/vxe-table/custom/customRenderCtrl'
Vue.component('customRenderCtrl', customRenderCtrl)

import reportSelect from '@/components/override/vxe-table/custom/reportSelect'
Vue.component('reportSelect', reportSelect)

import reportMulSelect from '@/components/override/vxe-table/custom/reportMulSelect'
Vue.component('reportMulSelect', reportMulSelect)


import reportSelectTable from '@/components/override/vxe-table/custom/reportSelectTable'
Vue.component('reportSelectTable', reportSelectTable)

// 标准弹框 表格控件
import standardFrame from '@/components/override/vxe-table/custom/standardFrame'
Vue.component('standardFrame', standardFrame)

import VXETable from 'vxe-table'



// 创建一个 可编辑 checkbox 渲染
VXETable.renderer.add('vxeCheckbox', {
    renderEdit (h, renderOpts, { row, column }) {
      let optionDisabled = false
      if(renderOpts.options && !!renderOpts.options.disabled){
          optionDisabled = true
      }
      return [
        <vxe-checkbox checked-value={1} unchecked-value={0} disabled={optionDisabled} v-model={row[column.field]} ></vxe-checkbox>
      ]
    },
     // 可编辑显示模板
     renderCell (h, renderOpts, { row, column }) {
      let optionDisabled = false
      if(renderOpts.options && !!renderOpts.options.disabled){
          optionDisabled = true
      }
      return [
        // <span>{row[column.field]}</span>
         <vxe-checkbox checked-value={1} unchecked-value={0} disabled={optionDisabled}  v-model={row[column.field]} ></vxe-checkbox>
      ]
    }
})

// 创建一个 可编辑 customSelect 渲染
VXETable.renderer.add('customSelect', {
  renderEdit (h, renderOpts, { row, column }) {
   // debugger
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <customSelect defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></customSelect>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
   // debugger
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }

    return [
      // <span>{row[column.field]}</span>
       <customSelect renderType="defaultRender" defaultValue={renderOpts.options.defaultValue} v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></customSelect>
    ]
  }
})

// 创建一个 可编辑 reportSelect 渲染
VXETable.renderer.add('reportSelect', {
  renderEdit (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <reportSelect rowData={row} optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportSelect>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      // <span>{row[column.field]}</span>
      <reportSelect rowData={row} renderType="defaultRender" optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportSelect>
    ]
  }
})

// 创建一个 可编辑 reportMulSelect 渲染
VXETable.renderer.add('reportMulSelect', {
  renderEdit (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <reportMulSelect rowData={row} optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportMulSelect>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <reportMulSelect rowData={row} renderType="defaultRender" optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportMulSelect>
    ]
  }
})
// 创建一个 可编辑 reportSelectTable 渲染
VXETable.renderer.add('reportSelectTable', {
  renderEdit (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <reportSelectTable optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue} rowData={row} fieldName ={column.field}   v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportSelectTable>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <reportSelectTable optionDataList ={renderOpts.options.optionDataList} defaultValue={renderOpts.options.defaultValue} rowData={row} fieldName ={column.field}   v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></reportSelectTable>
    ]
  }
})

// 创建一个 可编辑 standardFrame 标准弹框 渲染
VXETable.renderer.add('standardFrame', {
  renderEdit (h, renderOpts, { row, column }) {
    //console.log("===renderEdit=====standardFrame========")
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <standardFrame paramsItem ={renderOpts.options.paramsItem} defaultValue={renderOpts.options.defaultValue} rowData={row} fieldName ={column.field}   v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled}></standardFrame>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
   // debugger
   // console.log("===renderCell=====standardFrame========")
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <standardFrame paramsItem ={renderOpts.options.paramsItem} defaultValue={renderOpts.options.defaultValue} rowData={row} fieldName ={column.field}   v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled}></standardFrame>
    ]
  }
})

// 创建一个 可编辑 el-switc 渲染
VXETable.renderer.add('customSelectList', {
  renderEdit (h, renderOpts, { row, column }) {

    return [<vxe-select transfer options={renderOpts.options} v-model={row[column.field]}></vxe-select>]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
    // return[<span>{row[column.field]}</span>] 
    return [<vxe-select transfer options={renderOpts.options}  v-model={row[column.field]}></vxe-select>]
  }
})

// 创建一个 可编辑 customRenderCtrl 渲染
VXETable.renderer.add('customRenderCtrl', {
  renderEdit (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <customRenderCtrl rowData={row}  defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></customRenderCtrl>
    ]
  },
   // 可编辑显示模板
   renderCell (h, renderOpts, { row, column }) {
    let optionDisabled = false
    if(renderOpts.options && !!renderOpts.options.disabled){
        optionDisabled = true
    }
    return [
      <customRenderCtrl rowData={row} renderType="defaultRender"  defaultValue={renderOpts.options.defaultValue}  v-model={row[column.field]} field = {renderOpts.options.field} controlType={renderOpts.options.controlType} disabled={optionDisabled} itemOptions={renderOpts.options}></customRenderCtrl>
    ]
  }
})