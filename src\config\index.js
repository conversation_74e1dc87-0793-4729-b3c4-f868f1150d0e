
// export default {
//     module:window.config.virModule,
//     baseUrl: {
//         // dev: 'http://172.16.197.113:6689/', // 本地服务
//         dev: window.config.baseUrl.dev, // 线上服务
//         pro: window.config.baseUrl.pro // 正式发布服务
//       },
// }

export default {
  module:'/api/md/form/',
  moduleSub:'/api/md/formdata/',
  virModule:'api/DEMO/VXETableDemo/',
  baseUrl: {
     // dev: 'http://192.168.1.11:5034/', // .NET CORE    jerry
     // dev: 'http://8.135.117.94:6683/', // .NET CORE  online 
      // dev: 'http://192.168.1.67:6701/', // .NET CORE     美琪项目
      // dev:'http://192.168.1.68:6682/',
      dev: 'http://61.144.186.197:19719',//'http://61.144.186.197:19719/',//,//'http://23o1l19278.zicp.fun:55699/',//'http://61.144.186.197:19719/',//'http://192.168.1.67:6683/','http://23o1l19278.zicp.fun:55699/',
     // dev: 'http://117.43.10.73:5034/', //   德福测试 
     //  dev: 'http://117.43.10.73:5035/', // 
      pro: '/' // 博敏正式发布服务
    }
}