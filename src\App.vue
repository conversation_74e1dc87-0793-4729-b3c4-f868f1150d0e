<template>
  <div style="margin-left:10px;margin-right: 10px;" class="andyHuang" id="appcustomform">
    <!-- <CLDatePicker v-model="currentDate">aaa</CLDatePicker> -->
      <keep-alive v-if="isCache" :exclude="exclude">
      <router-view />
    </keep-alive>
    <router-view v-else />
    <!-- <transition>
      <keep-alive>
        <router-view :key="$route.fullPath" :exclude="exclude" v-if="isCache"></router-view>
      </keep-alive>
    </transition>
    
    <transition>
      <router-view :key="$route.fullPath" v-if="!isCache"></router-view>
    </transition> -->
  </div>
</template>

<script>

import Vue from 'vue'
export default {
  name: "appcustomform",
  data() {
    return {
      currentDate:null,
      isCache: true,
      // this.$store.commit("set_routerName", this.$route.name);
      //切记！exclude,include配置的name，是组件的name，而不是router上的name！
      exclude: [],
      isSamePath: false,
    };
  },
  watch: {
    $route: {
      handler(to, from) {
        this.$store.commit("set_routerName", this.$route.name);
      },
      deep: true,
      immediate: false,
    },
  },
  mounted() {
    // 主应用 发送到 子应用
    this.$actions.onGlobalStateChange(state => {
      console.log('子应用的观察函数：我是', state)
      console.log('登录用户信息', this.$UserInfo)
      //动态路由缓存false代表单个清楚true代表全部清楚
      if (state.type == "router") {
        if (state.keepAlive == true) {
          this.isCache = false;
          setTimeout(() => {
            this.isCache = true;
          }, 200);
        }

        else {
          console.log("===清除指定页面缓存===" + state.keepValue)
          let removeKey = state.keepValue.replace("/customform", "")
          this.getRemoveCacheComponent(removeKey)
          this.$store.commit("set_removeCachekey", removeKey)
          var arr = this.$router.options.routes?.filter(
            (item) => this.$router.options.base + item.path == state.keepValue
          );
          if (arr && arr.length > 0) {
            this.exclude.push(arr[0].name);
            setTimeout(() => {
              this.exclude = [];
            }, 800);
          }
        }
      }
      //标准弹框 推送回调
      if (state.type === "getStandardBulletBox") {
        this.$store.commit("set_standardBulletBoxChange",state)
        console.warn("=========标准弹框 推送回调===emitter.emit====================",state)
      }
       //高级搜索 推送回调
       if (state.type === "searchProDrawerBox") {
        // 普通搜索
        this.$store.commit("set_searchType",'searchPro')
        this.$store.commit("set_searchProDrawerBoxChange",state)
        console.log("=========高级搜索 推送回调===emitter.emit====================")
      }

    }, true)
  },
  methods: {
   
    getRemoveCacheComponent(removeKey){
      try {
        let childComponents=this.$children[0].$children
        if(childComponents && childComponents.length>0){
          let removeCacheComponents = childComponents.filter(item=>{
              if(item.pageCacheKey==removeKey){
                return item
              }
          })
          if(removeCacheComponents && removeCacheComponents.length>0){
            let cacheComponent =removeCacheComponents[0]
            cacheComponent.pruneCacheEntry(removeKey)
          }
        }
      } catch (error) {
        
      }
   
    },
    //  手动删除缓存 如果 exclude 里面有值，那么就返回当前新的实例不从 cache 里面获取。而且 exclude 的优先级是高于 include 的。
    removeKeepAliveCacheForVueInstance(vueInstance) {
      let key =
        vueInstance.$vnode.key ??
        vueInstance.$vnode.componentOptions.Ctor.cid + (vueInstance.$vnode.componentOptions.tag ? `::${vueInstance.$vnode.componentOptions.tag}` : "");
      let cache = vueInstance.$vnode.parent.componentInstance.cache;
      let keys = vueInstance.$vnode.parent.componentInstance.keys;
      if (cache[key]) {
        vueInstance.$destroy();
        delete cache[key];
        let index = keys.indexOf(key);
        if (index > -1) {
          keys.splice(index, 1);
        }
      }
    },
    getRouteKey(route) {
      //debugger
      if (route.meta && route.meta.key) {
        const key = route.meta.key
        if (typeof (key) === 'function') {
          return key(route)
        }
        return key
      } else {
        return ''
      }
    },
    clear() {
      this.isCache = false;
      setTimeout(() => {
        this.isCache = true;
      }, 1000);
    },
  },
};
</script>

<style lang="scss">
// #appcustomform {
//   height: 100%;
// }
</style>
