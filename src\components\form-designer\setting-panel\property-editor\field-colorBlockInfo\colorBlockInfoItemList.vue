<template>
  <div class="option-items-pane">


    <draggable tag="ul" :list="optionModel.colorBlockInfoItems"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in optionModel.colorBlockInfoItems" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input  @click.native="showEditDialogEvent(option)" readonly
            v-model="option.title" size="mini" style="width: 200px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>

    <div>
      <el-button type="text" @click="addOption">+添加项</el-button>
    </div>

    <el-dialog title="信息项 编辑" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input style="width:330px" v-model="editMenuForm.title"></el-input>
        </el-form-item>
        <!-- <el-form-item label="子标题" prop="label">
          <el-input style="width:330px" v-model="editMenuForm.label"></el-input>
        </el-form-item> -->
        <el-form-item label="唯一名称" prop="fieldName">
          <el-input style="width:330px" disabled v-model="editMenuForm.fieldName"></el-input>
        </el-form-item>
        <!-- <el-form-item label="图标ICON">
            <el-input style="width:330px" type="text" v-model="editMenuForm.iconUrl"></el-input>
            <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
        </el-form-item> -->
        <el-form-item label="宽度">
          <el-input-number style="width:330px" v-model="editMenuForm.blockWidth" controls-position="right" ></el-input-number>【默认0自动】
        </el-form-item>
        <el-form-item label="字体大小">
            <el-input-number style="width:330px" v-model="editMenuForm.fontSize" controls-position="right" ></el-input-number>
        </el-form-item>
        <el-form-item label="背景颜色">
          <div class="flex items-center">
            <el-color-picker  
          :predefine="predefineColors" v-model="editMenuForm.bgColor"></el-color-picker>
          <el-select  v-model="editMenuForm.bgColorKey" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in optionModel.dataSetAllModel"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
          【动态颜色字段】
          </div>
            
        </el-form-item>
        <el-form-item label="字体颜色">
          <div class="flex items-center">
            <el-color-picker  
            :predefine="predefineColors" v-model="editMenuForm.fontColor"></el-color-picker>
            <el-select  v-model="editMenuForm.fontColorKey" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in optionModel.dataSetAllModel"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
          【动态颜色字段】
          </div>
            
        </el-form-item>
       
      <el-form-item  label="显示字段">
          <el-select  v-model="editMenuForm.totalField" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in optionModel.dataSetAllModel"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item  label="传值字段">
          <el-select  v-model="editMenuForm.valueField" placeholder="请选择">
            <el-option label="请选择" value=""></el-option>
            <el-option :key="fieldIndex + fieldItem.field" v-for="(fieldItem, fieldIndex) in optionModel.dataSetAllModel"
              :label="fieldItem.title" :value="fieldItem.field"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="数值字体颜色">
             <el-color-picker  
           :predefine="predefineColors" v-model="editMenuForm.valueFontColor"></el-color-picker>
        </el-form-item>
     
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
        }}</el-button>
      </div>
    </el-dialog>
  
  </div>
</template>
  
<script>

const default_editMenuForm = {
  label: "",// 标签
  fieldName: "",// 唯一名称
  title: "",// 
  bgColor:"",
  bgColorKey:"",
  fontColor:"",
  fontColorKey:"",
  fontSize:16,
  valueFontColor:"",
  showBorder:false,
  borderColor:'',
  blockWidth:0,
  borderType:'solid',
  totalField: "",// 
  valueField:"",
  otherParams: { },
  actionParams: {},
}
import Draggable from 'vuedraggable'
import cloneDeep from "clone-deep"
import i18n from "@/utils/i18n";
// import {
//   generateId,
// } from "@/utils/util"
export default {
  name: "colorBlockInfoItemList",
  mixins: [i18n],
  components: {
    Draggable
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
          '#303133',
          '#DCDFE6',
          // '#3799FF',
          // 'rgba(255, 69, 0, 0.68)',
          // 'rgb(255, 120, 0)',
          // 'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577'
        ],
      showEditMenuDialogFlag: false,
      currentEditOption: {},// 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm), // 搜索输入框配置
      
      editMenuFormRules: {
        fieldName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
        ],
      },
   
    }
  },
  computed: {
    
    optionModel() {
      return this.selectedWidget.options
    },

  },

  methods: {
    showEditDialogEvent(option){
          this.currentEditOption = option // 当前菜单属性
          this.showEditMenuDialogFlag = true
        },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.fieldName = this.editMenuForm.fieldName
          this.currentEditOption.title = this.editMenuForm.title
          this.currentEditOption.totalField = this.editMenuForm.totalField
          this.currentEditOption.valueField = this.editMenuForm.valueField
          
          this.currentEditOption.label = this.editMenuForm.label

          this.currentEditOption.borderType = this.editMenuForm.borderType
          this.currentEditOption.blockWidth = this.editMenuForm.blockWidth
          this.currentEditOption.borderColor = this.editMenuForm.borderColor
          this.currentEditOption.showBorder = this.editMenuForm.showBorder
          this.currentEditOption.fontColor = this.editMenuForm.fontColor
          this.currentEditOption.fontSize = this.editMenuForm.fontSize
          this.currentEditOption.valueFontColor = this.editMenuForm.valueFontColor
          this.currentEditOption.fontColorKey = this.editMenuForm.fontColorKey
          this.currentEditOption.bgColorKey = this.editMenuForm.bgColorKey
          
          this.currentEditOption.bgColor = this.editMenuForm.bgColor

          this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
         

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑属性--- 初始化弹框属性参数
    showEditDialogEvent(option) {
      this.currentEditOption = option // 当前属性
      this.showEditMenuDialogFlag = true

      this.editMenuForm.label = option.label
      this.editMenuForm.fieldName = option.fieldName
      this.editMenuForm.title = option.title
      this.editMenuForm.totalField = option.totalField
      this.editMenuForm.valueField = option.valueField
      
      this.editMenuForm.bgColor = option.bgColor
      this.editMenuForm.fontColor = option.fontColor
      this.editMenuForm.fontSize = option.fontSize
      this.editMenuForm.valueFontColor = option.valueFontColor
      this.editMenuForm.fontColorKey = option.fontColorKey
      this.editMenuForm.bgColorKey = option.bgColorKey
      this.editMenuForm.showBorder = option.showBorder
      this.editMenuForm.borderColor = option.borderColor
      this.editMenuForm.blockWidth = option.blockWidth
      this.editMenuForm.borderType = option.borderType
      
      this.editMenuForm.otherParams = cloneDeep(option.otherParams)
      this.editMenuForm.actionParams = cloneDeep(option.actionParams)
    
      this.searchItemConfig = Object.assign({}, this.editMenuForm.searchItemConfig)
   


    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.optionModel.colorBlockInfoItems.splice(index, 1)
      }

    },
    // 添加按钮
    addOption() {
      if(!this.optionModel.hasOwnProperty("colorBlockInfoItems")){
            this.$set(this.optionModel, "colorBlockInfoItems", [])
      }
      let newValue = this.optionModel.colorBlockInfoItems.length + 1
      this.optionModel.colorBlockInfoItems.push(
        { title:"标题"+newValue,showBorder:false, blockWidth:0,borderType:'solid', fieldName: 'colorBlockInfo'+newValue,bgColor:'#67C23A',fontColor:'#fff', valueFontColor:'#fff', totalField:'', disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
      )

    },
  

  }
}
</script>
  
<style lang="scss" scoped>
.option-items-pane ul {
  // list-style: none;
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  // list-style: none;
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}
</style>
  