<template>
    <div> 
        <el-form-item  label="">
            <span slot="label">{{formLabel}}
            <el-tooltip effect="light" :content="formLabelTooltip">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
             <el-input clearable placeholder="点击选择"   
            v-model="optionModel[optionModelField]" size="mini" style="width: 130px"></el-input> 
        
             <el-button size="mini" @click="showCtrlListDialogEvent()" type="primary" icon="el-icon-plus" circle></el-button>
        </el-form-item>
        <!-- 控件列表 弹框-->
        <el-dialog :title="title" :visible.sync="showFlag" v-if="showFlag" v-dialog-drag append-to-body :show-close="true"
            custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false" :close-on-press-escape="false"
            :destroy-on-close="true">
            <div>
                <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id"
                    :expand-on-click-node="false" highlight-current class="node-tree" icon-class="el-icon-arrow-right"
                    @node-click="onNodeTreeClick"></el-tree>
            </div>
            <div slot="footer" class="dialog-footer">

                <el-button type="primary" size="large" @click="submitNodeEvent()">
                    确定</el-button><el-button size="large" @click="showFlag = false">
                    取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import cloneDeep from "clone-deep"
export default {
    name: "CtrlListDialog",// 根据控件类型名称，展示不同的事件类别和行为
    components:{},
    props: {
        title:{
            type:String,
            default:"控件列表 选择"
        },
        formLabelTooltip:{
            type:String,
            default:"默认为空，自身加载数据源，否则一般选择表格作为共享数据源控件"
        },
        // 操作的字段
        optionModelField:{
            type:String,
            default:"shareDataTableName"
        },
        formLabel:{
            type:String,
            default:"共享数据源控件"
        },
        designer: Object,
        optionModel:Object,
        selectedWidget: Object,

    },
    data() {
        return {
            nodeCtrlValue: "",// 节点选中值
            showFlag: false,// 显示弹框
        }
    },
  
    methods: {
        // 获取数据源控件列表
        getNodeTreeData() {
            let dataList = cloneDeep(this.designer.nodeTreeData)
            return dataList
        },
        // 打开控件列表 弹框
        showCtrlListDialogEvent() {
            this.showFlag = true
        },
        // 提交控件列表
        submitNodeEvent() {
            this.$set(this.optionModel,this.optionModelField, this.nodeCtrlValue )
            this.showFlag = false
        },
        // 点击控件列表 回调事件
        onNodeTreeClick(params) {
            this.nodeCtrlValue = ""
            let hasChildren = params.hasOwnProperty("children")
            let ctrlValue = params.label
            if (!hasChildren) {
                this.nodeCtrlValue = ctrlValue
            } else {
                this.$message.warning('此节点不可选！')
            }
        },
    }
}
</script>