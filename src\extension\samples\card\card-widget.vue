<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">
    <el-card :body-style="[{'background-color': widget.options.bodyBgColor,overflow:`auto`,padding: '5px',height:getContentBoxHeight()+'px'}]" :key="widget.id" class="card-container" @click.native.stop="selectWidget(widget)"
             :shadow="widget.options.shadow" :style="{'border':`1px solid ${widget.options.borderColor}`,'background-color': widget.options.allBgColor,width: widget.options.cardWidth + '!important' || ''}"
             :class="[selected ? 'selected' : '', !!widget.options.folded ? 'folded' : '', customClass]">
      <div slot="header"  class="clear-fix">
        <div class="flex w-full justify-between">
          <div class="flex w-full justify-between">
             <div class="flex items-center">
               <div v-show="!!widget.options.showIcon"  style="margin-top: 3px; width: 26px;height: 8px; margin-right: 15px;" :style="[{'background-color':!!widget.options.iconColor?widget.options.iconColor:'#7C260B'}]"></div>
               <div :style="[{color:widget.options.labelColor,'font-weight':!!widget.options.fontWeight?widget.options.fontWeight:'normal'}]">{{widget.options.label}}</div> 
            </div>
              <i v-if="widget.options.showFold" class="float-right"
                :class="[!widget.options.folded ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"
                @click="toggleCard"></i>
          </div>
          <div v-if="(eventList && eventList.length>0)" class="flex justify-end" style="height:20px;width:100%;overflow: hidden;">
             
                <div :style="[{'background-color':item.itemBgColor, 'border-bottom':(currentActiveBtn==item.value?`${item.hideActiveBottomColor?'0px':'1px'} solid ${!!item.itemActiveBottomColor?item.itemActiveBottomColor:'#409EFF'} `:'')}]" v-for="(item,index) in eventList" v-show="!!item.check" @click="clickEvent(item)" style="margin:0 2px;color:#409EFF;cursor:pointer;font-size: 14px;max-width:100px;overflow: hidden;">
                  <div style="padding: 0px 5px;" :style="{'color': item.itemTextColor}" v-show="item.displayType=='1'||item.displayType=='2'||item.displayType=='4'">{{ item.label }} </div>
                  <el-tooltip  v-show="item.displayType=='3'||item.displayType=='1'" :content="item.label" effect="light">

                    <i @click="clickEvent(item)" :style="{'color': item.itemTextColor}" :class="item.iconUrl"></i></el-tooltip>
                </div>
          </div>
        </div>

      </div>
      <draggable :list="widget.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
                 handle=".drag-handler"
                 @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
                 @update="onContainerDragUpdate" :move="checkContainerMove">
        <transition-group name="fade" tag="div" class="form-widget-list">
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                         :index-of-parent-list="swIdx" :parent-widget="widget"></component>
            </template>
            <template v-else>
              <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                         :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
            </template>
          </template>
        </transition-group>
      </draggable>
    </el-card>
  </container-wrapper>
</template>

<script>
  import i18n from "@/utils/i18n"
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
  import Draggable from 'vuedraggable'
  import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
  import refMixinDesign from "@/components/form-designer/refMixinDesign"
  import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
  export default {
    name: "card-widget",
    componentName: 'ContainerWidget',
    mixins: [i18n, containerMixin, refMixinDesign],
    inject: ['refList','sourceVFormRenderState'],
    components: {
      Draggable,
      ContainerWrapper,
      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
      
      previewState: { //是否表单预览状态
        type: Boolean,
        default: false
      },
       // 控件来源父集 add by andy
       contentBoxHeight: {
        type: [Number, String],
        default: 0
      },
            // 控件来源父集 add by andy
      sourceVFormRender:{
        type: String,
        default: ""
      },
    },
    data(){
      return {
        //tabPosition:"",
        currentActiveBtn:"",
        useHandleVFormEventFn: useHandleVFormEvent(this),
      }
    },
    computed: {
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },
      // 获取交互事件列表
      eventList(){
        let _eventList =[]
        try {
          _eventList = this.widget.options.eventList
        } catch (error) {
          _eventList =[]
        }
        return _eventList
      }
    },
    created() {
      this.initRefList()
    },
    methods: {
      // 单击事件
      clickEvent(item){
        //debugger
      },
 
    // 获取内容高
      getContentBoxHeight() {
        //debugger
        let testHeight = 300
        if (this.previewState) {
            // 预览状态下，固定高度分配
            return testHeight
        }else{
           // return this.contentBoxHeight
            return this.contentBoxHeight - 50
        }
      },

      /**
       * 检查接收哪些组件拖放，如不接受某些组件拖放，则根据组件类型判断后返回false
       * @param evt
       * @returns {boolean}
       */
      checkContainerMove(evt) {
        return true
      },

      toggleCard() {
        this.widget.options.folded = !this.widget.options.folded
      },

      /**
       * 设置折叠/打开状态
       * @param folded
       */
      setFolded(folded) {
        this.widget.options.folded = !!folded
      }

    }
  }
</script>

<style lang="scss" scoped>
  .card-container.selected {
    outline: 2px solid $--color-primary !important;
  }

  .card-container {
    margin: 3px;

    .form-widget-list {
      min-height: 28px;
    }
  }

  ::v-deep .el-card__header {
    padding: 10px 12px;
  }

  .folded ::v-deep .el-card__body {
    display: none;
  }

  .clear-fix:before, .clear-fix:after {
    display: table;
    content: "";
  }

  .clear-fix:after {
    clear: both;
  }

  .float-right {
    float: right;
  }

</style>
