<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <template v-if="field.options.type=='lable'">
        <label v-text="labelDefaultValue"></label>
    </template>
    <template v-else>
          <el-input :ref="field.options.name" v-model="fieldModel" v-show="!isReadMode"
                  :disabled="field.options.disabled" :readonly="field.options.readonly"
                  :size="field.options.size" class="hide-spin-button"
                  :type="inputType"
                  :publicAttribute="publicAttribute"
                  :show-password="field.options.showPassword"
                  :placeholder="field.options.placeholder"
                  :clearable="field.options.clearable"
                  :minlength="field.options.minLength" :maxlength="field.options.maxLength"
                  :show-word-limit="field.options.showWordLimit"
                  :prefix-icon="field.options.prefixIcon" :suffix-icon="field.options.suffixIcon"
                  @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent" @input="handleInputCustomEvent"
                  @change="handleChangeEvent">
          <el-button slot="append" v-if="field.options.appendButton" :disabled="field.options.disabled || field.options.appendButtonDisabled"
                    :class="field.options.buttonIcon" @click.native="emitAppendButtonClick"></el-button>
        </el-input>
    </template>
    
    <template v-if="isReadMode">
      <span class="readonly-mode-field">{{fieldModel}}</span>
    </template>
  </form-item-wrapper>
</template>

<script>
  import FormItemWrapper from './form-item-wrapper'
 
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import { useFormatParams } from "@/hooks/useFormatParams"
  export default {
    name: "inputNotFormField-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
         // 控件来源父集 add by andy
       sourceVFormRender:{
        type: String,
        default: ""
      },
      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },

    },
    components: {
      FormItemWrapper,
    },
     // 注入列表页面整体实体 getPageInstance>> add by andy
    inject: ['refList', 'globalOptionData', 'globalModel','getPageInstance'],
    data() {
      return {
        publicAttribute:{
          value:"",
        },// 对外开发属性值
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        rules: [],
        useFormatParamsFn: useFormatParams(this),
        labelDefaultValue:"",

      }
    },

    computed: {
        // add by andy
        pageInstance() {
          // 获取列表示例
          return this.getPageInstance()
        },
       inputType() {
        if (this.field.options.type === 'number') {
          return 'text'  //当input的type设置为number时，如果输入非数字字符，则v-model拿到的值为空字符串，无法实现输入校验！故屏蔽之！！
        }

        return this.field.options.type
      },

    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initFieldModel()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    async mounted() {
      this.handleOnMounted()
      this.labelDefaultValue =await this.getDefaultValue()
      //debugger
      // let tt = this.$store.state.popupParams
      // console.log("==========this.$store.state.popupParams=====")
      // console.log(tt)
      // console.log("==========this.$store.state.popupParams=====")
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {
      // 默认label 默认值
      // e.g  parent.vxetable51263.row.CID
    async  getDefaultValue(){
       // debugger
         if(this.field.options.type!='lable'){
            return
         }
         let controlValue = this.field.options.defaultValue 
         if ((controlValue).includes('{{')) {
            controlValue = await this.useFormatParamsFn.getSingleControlValueByPath(controlValue)
         }
         return controlValue
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

</style>
