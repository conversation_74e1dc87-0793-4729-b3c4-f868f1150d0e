<template>
  <div>
    <el-form-item label="内置类型">
      <span slot="label">内置类型
          <el-tooltip effect="light" content="交互事件，需要选择 '提交表单' 或 '重置表单' 才会启作用，默认执行自定编码事件">
            <i class="el-icon-info"></i></el-tooltip></span>
        <el-select v-model="optionModel.innerEventType">
            <el-option label="默认" value=""></el-option>
            <el-option label="提交表单" value="postForm"></el-option>
            <el-option label="重置表单" value="resetForm"></el-option>
        </el-select>
      </el-form-item>
        <el-form-item label-width="0">
            <selectDataSourceApi contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></selectDataSourceApi>
        </el-form-item>
        <el-form-item label-width="0">
            <commonParamsOnChange contrlType="common" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
        <commonSettingEventDialog contrlType="common" :showOnBeforeSubmit="true" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></commonSettingEventDialog>
    </el-form-item>
  </div>
</template>

<script>
 import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
 import commonSettingEventDialog from '@/components/form-designer/setting-panel/commonSettingEventDialog.vue'
 import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "buttonEvent-editor",
    mixins: [i18n, propertyMixin],
    components:{selectDataSourceApi,commonSettingEventDialog,commonParamsOnChange},
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style lang="scss" scoped>

</style>
