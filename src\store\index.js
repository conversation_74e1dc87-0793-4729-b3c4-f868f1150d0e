import Vue from 'vue'
import Vuex from 'vuex'
const firstAction={
  clickTime:0,
}
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    firstActionPageCID: "",// 首次执行控件的页面CID
    searchType: { // 高级搜索，还是普通的搜索
      key: 'key',// 随机字符串
      value: "search",// searchPro
    },
    // 是否显示基础表单弹框
    showBasicFormBoxState: { 
      key: 'key',// 随机字符串
      value: false,
    },
    // iframe postMessage 弹框参数
    postMessageFromParent:{
         key: 'key',// 随机字符串
        value: {
          // 高级搜索查询参数
        }
    },
    searchProDrawerBoxChange: {
      key: 'key',// 随机字符串
      value: {
        // 高级搜索查询参数
      }
    },
    // 标准弹框发生改变
    standardBulletBoxChange: {
      key: 'key',// 随机字符串
      value: {
        // 普通查询参数
      }
    },
    removeCachekey: "",// 移除缓存关键字
    //是否加载数据完毕，方便加载 表格中的子控件数据
    afterLoadData: "",
    tableFullData: [],// 当前主表表格(树结构)全部数据
    selfControlParams: {},// 点击控件 添加的参数
    runCurrentWidget: "",// 当前需要执行RUN方法的组件
    popupParams: {},  // 当前弹框配置参数
    currentClickCtrlOptions: {},  // 当前点击控件配置参数
    formTemplateList: [],// 表单临时模板列表
    formTemplatePanel: { // 表单设计界面面板 隐藏显示
      key: 'key',// 随机字符串
      left: true,
      right: true,
      bottom: false
    },
    containerDragEndChange: {
      key: 'key',// 随机字符串
      value: {
        // 普通查询参数
      }
    },
    commonSearch: {
      key: 'key',// 随机字符串
      value: {
        // 普通查询参数
      }
    },
    routerName: "",// 当前路由名称
    // 执行按钮类型 e.g:添加/修改/删除
    actionType: {
      key: 'key', // 随机字符串
      value: ''// add /edit/detele
    },
      // 执行按钮类型 e.g:上一条，下一条
      editActionType: {
        key: 'key', // 随机字符串
        value: ''// add /edit/detele
      },
    // 动态表单选中行
    currentRow_viewdetail: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "参数ID/data.key":detailData
      }
    },
    //  动态页面原始表格列头信息 dynamic
    pageOriginColumns_viewdetail: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "参数ID/data.key":detailData
      }
    },
    // 当前主表选中行
    currentRow: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":detailData
      }
    },
    // 刷新前 选中行
    currentOldRow: {
      key: 'key',// 随机字符串
      value: {}
    },
    // 当前主表选中行>>查询子表详情
    currentRowDetail: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":detailData
      }
    },
    // 当前主表查询的所有数据集合
    mainTableDataList: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "formName":data
      }
    },
    // 自定义控件 临时数据
    ctrlData: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
    // 设置当前自定义当前选中节点数据
    customTreeNode: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
    // 共享数据源 临时数据
    ctrlShareData: {
      key: 'key',// 自定KEY字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
    // 当前页面原始表格列头信息
    pageOriginColumns: {
      key: 'key',// 随机字符串
      value: {
        // key:value>> "routerName":dataList
      }
    },
  },
  mutations: {
     // 设置 // 首次执行控件的页面CID
     set_firstActionPageCID(state, data) {
      const now = new Date().getTime();
      console.log("set_firstActionPageCID连续执行时间间隔为:",now - firstAction.clickTime,'PAGCID:',data)
      if (now - firstAction.clickTime > 500) { // 双击时间间隔设置为500毫秒
        state.firstActionPageCID = data
      }
      firstAction.clickTime = now; // 更新点击时间
    },
     // 设置 是否显示基础表单渲染
     set_showBasicFormBoxState(state, data) {
      state.showBasicFormBoxState.key = Math.floor(Math.random() * 10000 + 1)
      state.showBasicFormBoxState.value = data
    },
     // 设置 基础表单渲染配置参数
     set_showBasicFormBoxParams(state, data) {
      state.showBasicFormBoxParams = data
    },
    set_customTreeNode(state, data){
      state.customTreeNode.key = Math.floor(Math.random() * 10000 + 1)
      state.customTreeNode.value[data.key] = data.value
    },
    // // 设置 自定义控件临时数据
    // set_TabsData(state, data) {
    //   state.TabsData.key = Math.floor(Math.random() * 10000 + 1)
    //   state.TabsData.value[data.key] = data.value
    // },
    // 设置 搜索类型
    set_searchType(state, data) {
      state.searchType.key = Math.floor(Math.random() * 10000 + 1)
      state.searchType.value = data
    },
    // 设置 高级搜索 弹框发生改变
    set_searchProDrawerBoxChange(state, data) {
      state.searchProDrawerBoxChange.key = Math.floor(Math.random() * 10000 + 1)
      state.searchProDrawerBoxChange.value = data
    },
     // 设置 iframe postMessage 弹框发生改变
    set_postMessageFromParentChange(state, data) {
      state.postMessageFromParent.key = Math.floor(Math.random() * 10000 + 1)
      state.postMessageFromParent.value = data
    },
    // 设置 标准弹框发生改变
    set_standardBulletBoxChange(state, data) {
      state.standardBulletBoxChange.key = Math.floor(Math.random() * 10000 + 1)
      state.standardBulletBoxChange.value = data
    },
    // 移除缓存关键字
    set_removeCachekey(state, data) {
      console.log("==store=移除缓存关键字set_removeCachekey====" + data)
      state.removeCachekey = data
    },
    // 设置  是否加载数据完毕，方便加载 表格中的子控件数据
    set_afterLoadData(state, data) {
      state.afterLoadData = Math.floor(Math.random() * 10000 + 1)
    },
    // 设置 自定义控件临时数据
    set_ctrlData(state, data) {
      state.ctrlData.key = Math.floor(Math.random() * 10000 + 1)
      state.ctrlData.value[data.key] = data.value
    },
    // 设置 共享数据源
    set_ctrlShareData(state, data) {
      state.ctrlShareData.key = Math.floor(Math.random() * 10000 + 1)
      state.ctrlShareData.value[data.key] = data.value
    },
    // 设置 当前主表表格全部数据
    set_tableFullData(state, data) {
      state.tableFullData = data
    },
    // 设置 点击控件 添加的参数
    set_selfControlParams(state, data) {
      state.selfControlParams = data
    },
    // 设置 当前弹框配置参数
    set_popupParams(state, data) {
      state.popupParams = data
    },
    // 设置 当前点击控件配置参数
    set_currentClickCtrlOptions(state, data) {
      state.currentClickCtrlOptions = data
    },
    // 设置 当前需要执行RUN方法的组件
    set_runCurrentWidget(state, data) {
      state.runCurrentWidget = data
    },
    // 设置  表单设计界面面板 隐藏显示
    set_formTemplatePanel(state, data) {
      state.formTemplatePanel.key = Math.floor(Math.random() * 10000 + 1)
      state.formTemplatePanel[data.type] = data.value
    },
    // 设置 表单临时模板列表
    set_formTemplateList(state, data) {
      state.formTemplateList.push(data)
    },
    // 设置  普通查询参数
    set_containerDragEndChange(state, data) {
      //debugger
      state.containerDragEndChange.key = Math.floor(Math.random() * 10000 + 1)
      state.containerDragEndChange.value = data
    },
    // 设置  普通查询参数
    set_commonSearch(state, data) {
      state.commonSearch.key = Math.floor(Math.random() * 10000 + 1)
      state.commonSearch.value = data
    },
    // 设置 当前路由名称
    set_routerName(state, data) {
      state.routerName = data
    },
    // 设置 执行按钮类型
    set_actionType(state, data) {
      //console.log("====set_actionType change====="+data);
      state.actionType = {
        key: Math.floor(Math.random() * 10000 + 1),
        value: data,
      }
    },
     // 设置 编辑页面 执行按钮类型
     set_editActionType(state, data) {
      state.editActionType = {
        key: Math.floor(Math.random() * 10000 + 1),
        value: data,
      }
    },
    // 设置  动态表单选中行 dynamic
    set_currentRow_viewdetail(state, data) {
      //debugger
      // 根据当前路由名称formName/Pid作为key 存 or 取
      state.currentRow_viewdetail.key = Math.floor(Math.random() * 10000 + 1)
      state.currentRow_viewdetail.value[data.key] = data.value
    },
    // 设置  刷新前 选中行
    set_currentOldRow(state, data) {
      state.currentOldRow.key = Math.floor(Math.random() * 10000 + 1)
      state.currentOldRow.value = data
    },
    // 设置  当前主表选中行
    set_currentRow(state, data) {
      // 根据当前路由名称routerName作为key 存 or 取
      state.currentRow.key = Math.floor(Math.random() * 10000 + 1)
      state.currentRow.value[state.routerName] = data
    },
    // 设置  当前主表选中行>>查询子表详情
    set_currentRowDetail(state, data) {
      // 根据当前路由名称routerName作为key 存 or 取
      state.currentRowDetail.key = Math.floor(Math.random() * 10000 + 1)
      state.currentRowDetail.value[state.routerName] = data
    },
    // 设置  当前主表查询的所有数据集合
    set_mainTableDataList(state, data) {
      // 根据当前路由名称formName作为key 存 or 取
      state.mainTableDataList.key = Math.floor(Math.random() * 10000 + 1)
      state.mainTableDataList.value[data.key] = data.value
    },

    // 设置  当前页面原始表格列头信息
    set_pageOriginColumns(state, data) {
      // 根据当前路由名称routerName作为key 存 or 取
      state.pageOriginColumns.key = Math.floor(Math.random() * 10000 + 1)
      state.pageOriginColumns.value[state.routerName] = data
    },
    // 设置  动态页面原始表格列头信息 dynamic
    set_pageOriginColumns_viewdetail(state, data) {
      //debugger
      // 根据当前路由名称formName/Pid作为key 存 or 取
      state.pageOriginColumns_viewdetail.key = Math.floor(Math.random() * 10000 + 1)
      state.pageOriginColumns_viewdetail.value[data.key] = data.value
    },


  },
  actions: {

  },
  modules: {
  }
})