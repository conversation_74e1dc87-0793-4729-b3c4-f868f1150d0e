<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <el-date-picker :ref="field.options.name" :type="field.options.type" v-model="fieldModel_Computed" v-show="!isReadMode" class="full-width-input"
                    :readonly="field.options.readonly" :disabled="field.options.disabled"
                    :size="field.options.size"
                    :publicAttribute="publicAttribute"
                    :clearable="field.options.clearable" :editable="field.options.editable"
                    :format="field.options.format" 
                    :placeholder="field.options.placeholder || i18nt('render.hint.datePlaceholder')"
                    @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent"
                    @change="handleChangeEvent">
    </el-date-picker>
     <!--      
                    :value-format="field.options.valueFormat" 这个添加后，导致无法修改，所以采用DAYJS 来修改最后的值的-->
    <!-- {{ field.options.valueFormat }} :value-format="field.options.valueFormat"-->
    <template v-if="isReadMode">
      <span class="readonly-mode-field">{{fieldModel}}</span>
    </template>
  </form-item-wrapper>
</template>

<script>
import dayjs from "dayjs";
  import FormItemWrapper from './form-item-wrapper'
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

  export default {
    name: "date-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },

    },
    components: {
      FormItemWrapper,
    },
    inject: ['refList', 'globalOptionData', 'globalModel','getPageInstance',],
    data() {
      return {
        publicAttribute:{
          value:"",
        },// 对外开发属性值
        fieldModel_Computed:null,
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        rules: [],
      }
    },
    computed: {
      pageInstance() {
        // 获取列表示例
        return this.getPageInstance()
      },
    },
    watch:{
      fieldModel(n,o){
       // debugger
        if(n){
          this.fieldModel_Computed= dayjs(n).format()
        }else{
          this.fieldModel_Computed=null
         
        }
      }
    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initFieldModel()
      this.initEventHandler()
      this.buildFieldRules()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
       // add by andy,fix date 默认值，或不勾选的值时if(this.popupFormItemConfig.value =="iisEdit")
       if((this.field.type === 'date'||this.field.type === 'datetime'||this.field.type === 'month'||this.field.type === 'year') && this.popupFormItemConfig.value !="iisEdit"){
          setTimeout(() => {  
            if(!!this.field.options.defaultValue){
              let _defaultValue =this.setDateTimeDefaultVal(this.field.options.type,this.field.options.defaultValue)
              this.fieldModel = _defaultValue
              // 直接重置表单中的字段默认值
              this.formModel[this.field.options.name] = _defaultValue
            }
        
         }, 800)
     }
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {
         // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+1年:add-1-year)
         setDateTimeDefaultVal(type,defaultVal){
            let dValue = null
            let dateFormatStr = ""
            // 指定日期 格式化 格式
            switch (type) {
                case "date":
                     dateFormatStr = 'YYYY-MM-DD'
                    break;
                case "datetime":
                     dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                    break;
                case 'month':
                    dateFormatStr = 'YYYY-MM'
                    break 
                    case 'year':
                    dateFormatStr = 'YYYY'
                    break    
                default:
                     dateFormatStr = ""
                break
            }
            if(!!defaultVal){
                if(defaultVal=='curdate'){
                    // 当前日期
                    dValue = dayjs().format(dateFormatStr);	
                }else if (defaultVal.includes('-')){
                    // 指定日期加减
                    //dayjs().add(7, 'day').format('YYYY-MM-DD');
                    //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                    let daysetArray = defaultVal.split('-')
                    dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                }else{
                    //空日期
                    dValue = null
                }
            }

            return dValue
        },
    }
  }
</script>

<style lang="scss" scoped>
  @import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

  .full-width-input {
    width: 100% !important;
  }

</style>
