<template>
  <container-item-wrapper :widget="widget">
    <div class="" :id="`div` + widget.id" :ref="`div` + widget.id">
      <el-card
        :body-style="[{ 'background-color': widget.options.bodyBgColor, overflow: `auto`, padding: '5px', height: getContentBoxHeight() }]"
        :key="widget.id" class="card-container" :class="[!!widget.options.folded ? 'folded' : '', customClass]"
        :shadow="widget.options.shadow"
        :style="{ 'border': `1px solid ${widget.options.borderColor}`, 'background-color': widget.options.allBgColor, 'width': widget.options.cardWidth + '!important' || '' }"
        :ref="widget.id" v-show="!widget.options.hidden">

        <div slot="header" class="clear-fix">
          <div  class="flex w-full justify-between">
            <div class="flex w-full justify-between">
              <!-- <div class="flex items-center">{{widget.options.label}}</div> -->
              <div class="flex items-center">
                <div v-show="!!widget.options.showIcon"
                  style="margin-top: 3px; width: 26px;height: 8px; margin-right: 15px;"
                  :style="[{ 'background-color': !!widget.options.iconColor ? widget.options.iconColor : '#7C260B' }]"></div>
                <div
                  :style="[{ color: widget.options.labelColor, 'font-weight': !!widget.options.fontWeight ? widget.options.fontWeight : 'normal' }]">
                  {{ widget.options.label }}</div>
              </div>
              <i v-if="widget.options.showFold" class="float-right"
                :class="[!widget.options.folded ? 'el-icon-arrow-down' : 'el-icon-arrow-up']" @click="toggleCard"></i>
            </div>
            <div v-if="(eventList && eventList.length > 0)" class="flex items-center justify-end"
              style="height:20px;width:100%;overflow: hidden;">
  <!-- max-width:200px;overflow: hidden; -->

              <div
                :style="[{ 'background-color': item.itemBgColor, 'border-bottom': (currentActiveBtn == item.value ? `${item.hideActiveBottomColor ? '0px' : '1px'} solid ${!!item.itemActiveBottomColor ? item.itemActiveBottomColor : '#409EFF'} ` : '') }]"
                v-for="(item, index) in eventList" v-show="!!item.check" @click="clickEvent(item)"
              
                style="margin:0 2px;color:#409EFF;cursor:pointer;font-size: 14px;">
                <div >
                  <!-- 文字描述 -->
                  <div style="padding: 0px 5px;cursor:pointer;" :style="{ 'color': item.itemTextColor }"
                    v-show="item.displayType == '1' || item.displayType == '2' || item.displayType == '4'"> <i
                      v-show="!!item.loading && !!item.hideActiveBottomColor" class="vxe-icon-refresh roll"></i>{{
                        item.label }} <span v-show="item.displayType == '4'">{{ textInfo }}</span> </div>
                  <!-- 提示 -->
                  <el-tooltip v-show="item.displayType == '3' || item.displayType == '1'" :content="item.label" effect="light">
                    <!-- 图表 -->
                    <i style="cursor:pointer !important;" @click="clickEvent(item)" :style="{ 'color': item.itemTextColor }"
                      :class="item.iconUrl"></i></el-tooltip>
                </div>
              </div>


            </div>
          </div>

        </div>
         <!-- <template v-if="widget.options.hideHeader">
          <div v-html="displayNoneStyle"></div>
         </template> -->
          <div v-html="displayNoneStyle"></div>
        <template v-if="!!widget.widgetList && (widget.widgetList.length > 0)">
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component :contentBoxHeight="getContentBoxHeight()" :sourceVFormRender="sourceVFormRender"
                :is="getComponentByContainer(subWidget)" :widget="subWidget" :key="swIdx" :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx" :parent-widget="widget" :sub-form-row-id="subFormRowId"
                :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex">
                <!-- 递归传递插槽！！！ -->
                <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                  <slot :name="slot" v-bind="scope" />
                </template>
              </component>
            </template>
            <template v-else>
              <component :contentBoxHeight="getContentBoxHeight()" :sourceVFormRender="sourceVFormRender"
                :is="subWidget.type + '-widget'" :field="subWidget" :designer="null" :key="swIdx"
                :parent-list="widget.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"
                :sub-form-row-id="subFormRowId" :sub-form-row-index="subFormRowIndex"
                :sub-form-col-index="subFormColIndex">
                <!-- 递归传递插槽！！！ -->
                <template v-for="slot in Object.keys($scopedSlots)" v-slot:[slot]="scope">
                  <slot :name="slot" v-bind="scope" />
                </template>
              </component>
            </template>
          </template>
        </template>
      </el-card>
    </div>

  </container-item-wrapper>
</template>

<script>
import { useFullscreen } from '@vueuse/core'
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import refMixin from "@/components/form-render/refMixin"
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper'
import containerItemMixin from "@/components/form-render/container-item/containerItemMixin"
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
export default {
  name: "card-item",
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    contentBoxHeight: {
      type: [Number, String],
      default: 0
    },
    // 控件来源父集 add by andy
    sourceVFormRender: {
      type: String,
      default: ""
    },
  },
  inject: ['refList', 'sfRefList', 'globalModel', 'getPageInstance', 'sourceVFormRenderState'],
  data() {
    return {
      displayNoneStyle:"",
      textInfo: "",// 交互文本描述
      btnLoading: false,
      currentEditItem: null,
      tempDataLoadObj: {},
      currentBtnItem: null,
      fullscreenObj: null,
      currentActiveBtn: "",
      useHandleVFormEventFn: useHandleVFormEvent(this),
    }
  },
  computed: {
    pageInstance() {
      // 获取列表示例
      return this.getPageInstance()
    },
    customClass() {
      return this.widget.options.customClass || ''
    },
    // 获取交互事件列表
    eventList() {
      let _eventList = []
      try {
        _eventList = this.widget.options.eventList
      } catch (error) {
        _eventList = []
      }
      return _eventList
    }
  },
  created() {
    this.initRefList()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  mounted() {
    // debugger
    let element = this.$refs['div' + this.widget.id];
    // 是否全屏显示对象
    this.fullscreenObj = useFullscreen(element)
    this.setHideDragItemStyle()
  },
  methods: {
     // 设置是否隐藏拖拽 按钮
     setHideDragItemStyle(){
      this.displayNoneStyle =""
      if(!!this.widget.options.hideHeader){
        this.displayNoneStyle =`<style type="text/css">#${'div'+this.widget.id} .el-card__header {display:none !important;}</style>`
      }else{
        this.displayNoneStyle =`<style type="text/css">#${'div'+this.widget.id} .el-card__header {display:block !important;}</style>`
      }

    },
    // 触发控件 事件 重新加载数据
    async reSearchData(params) {
      // debugger
      console.log("=card==reSearchData==触发控件 事件 重新加载数据=======")
      let postParams = this.getCtrlDataFromCacheByKey(this.$route.fullPath)
      if (postParams && Object.keys(postParams).length > 0) {
        this.textInfo = postParams.name
      }else{
        this.textInfo =""
      }

    },
    getCtrlDataFromCacheByKey(cacheKey) {
      let ctrlData = null
      try {
        ctrlData = this.$store.state.ctrlData.value[cacheKey] || null
      } catch (error) {
        ctrlData = null
      }
      return ctrlData
    },
    // 对外调用 全屏显示/取消全屏
    toggleFullScreenFn() {
      console.log("=card==toggleFullScreenFn==对外调用全屏显示/取消全屏=======")
      this.setFullScreenToolBox()
    },
    // 设置全屏
    setFullScreenToolBox() {
      // debugger
      if (!this.fullscreenObj.isFullscreen) {
        this.fullscreenObj.enter()
      } else {
        this.fullscreenObj.exit()
      }

    },
    // 单击事件
    async clickEvent(item) {
      // debugger
      if (!item.otherParams || !item.otherParams.eventType) {
        // otherParams: Object
        //   actionType: "ctrlComp"
        //   eventType: "click"
        console.warn("===未配置点击事件===")
        return
      }
      // 限制点击频率
      if (!!this.btnLoading) {
        console.log("====btnLoading===")
        return
      }
      let _self = this
      this.currentEditItem = item
      try {
        // 添加点击效果 加载中...
        _self.$set(item, 'loading', true)
        _self.btnLoading = true
        setTimeout(() => {
          console.log("=====loading off =========" + item.actionParams.actionName)
          _self.$set(item, 'loading', false)
          _self.btnLoading = false
        }, 1000)
      } catch (error) {

      }
      this.currentActiveBtn = item.value
      let triggerCtrlNames = {
        formCtrlName: "card",
        triggerCtrlNames: this.widget.options.triggerCtrlNames, //重点： 触发控件名称
        originSubItem: item,// 原始数据
      }
      item.triggerCtrlNames = triggerCtrlNames
      await this.useHandleVFormEventFn.handleCommonClickEvent(item);

    },
    toggleCard() {
      this.widget.options.folded = !this.widget.options.folded
    },
    // 获取内容高
    getContentBoxHeight() {
      // debugger
      let testHeight = 300
      if (this.previewState) {
        // 预览状态下，固定高度分配
        return testHeight
      } else {
        let _height = (this.contentBoxHeight + "").replace("px", '')
        if(this.widget.options.hideHeader){
          return (parseInt(_height) - 5) + 'px'
        }else{
          return (parseInt(_height) - 40) + 'px'
        }
       
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-card__header {
  padding: 10px 12px;
}

::v-deep .el-card {
  margin-top: 0px;
  margin-bottom: 3px;
}

.folded ::v-deep .el-card__body {
  display: none;
}

.clear-fix:before,
.clear-fix:after {
  display: table;
  content: "";
}

.clear-fix:after {
  clear: both;
 
}

.float-right {
  float: right;
  
}</style>
