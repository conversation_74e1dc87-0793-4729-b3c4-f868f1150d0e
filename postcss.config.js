// console.log('=> => => postcss.config.js start => => =>')
// module.exports = (ctx) => {
//     return {
//       plugins: [
//         require('postcss-plugin-namespace')('.andy<PERSON><PERSON>', {
//           ignore: [
//             /html/, /body/, /span/, /el-form-item/,/el-form-item__error/,/el-collapse-item__wrap/
//           ]
//         }),
//       ]
//     }
//   }
//   console.log('=> => => postcss.config.js end => => =>')
  
//   作者：Lee淳淳同学
//   链接：https://juejin.cn/post/6992944363798003743
//   来源：稀土掘金
//   著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。

// const addCssPrefix = require('postcss-change-css-prefix')

// module.exports = {
//   plugins: [
//     addCssPrefix({
//       prefix: 'el-',
//       replace: 'parent-'
//     })
//   ]
// }

// 作者：Jerry_W
// 链接：https://juejin.cn/post/7109402211653779469
// 来源：稀土掘金
// 著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。