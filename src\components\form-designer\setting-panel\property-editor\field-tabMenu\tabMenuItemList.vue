<template>
  <div class="option-items-pane">


    <draggable tag="ul" :list="optionModel.tabMenuItems"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in optionModel.tabMenuItems" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input  @click.native="showEditDialogEvent(option)" readonly
            v-model="option.title" size="mini" style="width: 200px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>

    <div>
      <el-button type="text" @click="addOption">+添加项</el-button>
    </div>

    <el-dialog title="信息项 编辑" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
       
        <el-form-item label="名称" prop="title">
          <el-input style="width:330px" v-model="editMenuForm.title"></el-input>
        </el-form-item>
        <!-- <el-form-item label="编码" prop="name">
          <el-input style="width:330px" v-model="editMenuForm.name"></el-input>
        </el-form-item> -->
        <el-form-item label="关联TAB项" prop="activeTabItem">
          <span slot="label">关联TAB项
            <el-tooltip effect="light" content="触发控件列表第一个为TAB（标签页）时，可以关联选择对象的项">
              <i class="el-icon-info"></i></el-tooltip>
          </span>
          <el-select style="width:330px" size="mini" v-model="editMenuForm.activeTabItem" placeholder="请选择">
            <el-option v-for="item in activeTabItemList" :key="item.name" :label="item.label"
                :value="item.name">
            </el-option>
        </el-select>
        
        </el-form-item>
       
        <el-form-item label="唯一名称" prop="fieldName">
          <el-input style="width:330px" disabled v-model="editMenuForm.fieldName"></el-input>
        </el-form-item>
        <el-form-item label="图标ICON">
            <el-input style="width:330px" type="text" v-model="editMenuForm.iconUrl"></el-input>
            <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
        </el-form-item>
        <el-form-item label="图标颜色">
             <el-color-picker  
       :predefine="predefineColors" v-model="editMenuForm.iconColor"></el-color-picker>
        </el-form-item>
        <el-form-item label="图标大小">
          <el-input-number style="width:330px" v-model="editMenuForm.iconSize" controls-position="right" ></el-input-number>
        </el-form-item>
        <el-form-item label="背景颜色">
             <el-color-picker  
       :predefine="predefineColors" v-model="editMenuForm.bgColor"></el-color-picker>
        </el-form-item>
        <el-form-item label="字体颜色">
             <el-color-picker  
           :predefine="predefineColors" v-model="editMenuForm.fontColor"></el-color-picker>
        </el-form-item>
      
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm') }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
        }}</el-button>
      </div>
    </el-dialog>
  
  </div>
</template>
  
<script>
//  <!-- title:"标题1",name:"1", iconUrl:'',iconColor:'',iconSize:20,bgColor:'#fff',
//    hoverColor:'lightsalmon',activeColor:"lightsalmon", fontColor:'#606266', fieldName: 'tabMenu1', -->
const default_editMenuForm = {
  label: "",// 标签
  fieldName: "",// 唯一名称
  activeTabItem:"",
  title: "",// 
  name:"",
  iconUrl:"el-icon-menu",
  iconSize:20,
  iconColor:"",
  bgColor:"#fff",
  fontColor:"#606266",
  // hoverColor:"lightsalmon",
  // activeColor:"lightsalmon",
  otherParams: {},
  actionParams: {},
}
import Draggable from 'vuedraggable'
import cloneDeep from "clone-deep"
import i18n from "@/utils/i18n";
// import {
//   generateId,
// } from "@/utils/util"
export default {
  name: "tabMenuItemList",
  mixins: [i18n],
  components: {
    Draggable
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      triggerCtrlTab:"",// 关联TAB对象
      activeTabItemList:[],
      predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585',
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
          '#303133',
          '#DCDFE6',
          // '#3799FF',
          // 'rgba(255, 69, 0, 0.68)',
          // 'rgb(255, 120, 0)',
          // 'hsv(51, 100, 98)',
          'hsva(120, 40, 94, 0.5)',
          'hsl(181, 100%, 37%)',
          'hsla(209, 100%, 56%, 0.73)',
          '#c7158577'
        ],
      showEditMenuDialogFlag: false,
      currentEditOption: {},// 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm), // 搜索输入框配置
      
      editMenuFormRules: {
        fieldName: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
        ],
      },
   
    }
  },
  computed: {
    
    // optionModel() {
    //   return this.selectedWidget.options
    // },

  },
 watch:{
  showEditMenuDialogFlag(n,o){
     
      if(n){
        setTimeout(()=>{
         // debugger
             this.activeTabItemList =[]
             if(this.optionModel.triggerCtrlNames && this.optionModel.triggerCtrlNames.length>0){
                this.triggerCtrlTab = this.optionModel.triggerCtrlNames[0].value
                let _widgetObj = this.designer.formWidget.getWidgetRef(this.triggerCtrlTab)
                if(_widgetObj){
                  //debugger
                    if (_widgetObj.widget.tabs && _widgetObj.widget.tabs.length>0){
                     // debugger
                      _widgetObj.widget.tabs.forEach(item=>{
                        let newTabItem ={
                            label:item.options.label,
                            name:item.options.name
                        }
                        this.activeTabItemList.push(newTabItem)
                      })
                    
                    }
                    
                }

             }
           
     
        },300)
      }
  }
 },
  methods: {
    showEditDialogEvent(option){
          //debugger
          this.currentEditOption = option // 当前菜单属性
          this.showEditMenuDialogFlag = true
         
        },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.fieldName = this.editMenuForm.fieldName
          this.currentEditOption.title = this.editMenuForm.title
          this.currentEditOption.label = this.editMenuForm.label
          this.currentEditOption.activeTabItem = this.editMenuForm.activeTabItem
          
          this.currentEditOption.name = this.editMenuForm.name
          this.currentEditOption.iconUrl = this.editMenuForm.iconUrl
          this.currentEditOption.iconSize = this.editMenuForm.iconSize
          this.currentEditOption.iconColor = this.editMenuForm.iconColor
          this.currentEditOption.bgColor = this.editMenuForm.bgColor
          this.currentEditOption.fontColor = this.editMenuForm.fontColor
          // this.currentEditOption.hoverColor = this.editMenuForm.hoverColor
          // this.currentEditOption.activeColor = this.editMenuForm.activeColor

          this.currentEditOption.otherParams = cloneDeep(this.editMenuForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editMenuForm.actionParams)
         

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑属性--- 初始化弹框属性参数
    showEditDialogEvent(option) {
      this.currentEditOption = option // 当前属性
      this.showEditMenuDialogFlag = true

      this.editMenuForm.label = option.label
      this.editMenuForm.fieldName = option.fieldName
      this.editMenuForm.title = option.title

          this.editMenuForm.name = option.name  
          this.editMenuForm.iconUrl = option.iconUrl
          this.editMenuForm.iconSize = option.iconSize
          this.editMenuForm.iconColor = option.iconColor
          this.editMenuForm.bgColor = option.bgColor
          this.editMenuForm.fontColor = option.fontColor
          this.editMenuForm.activeTabItem = option.activeTabItem

      
      this.editMenuForm.otherParams = cloneDeep(option.otherParams)
      this.editMenuForm.actionParams = cloneDeep(option.actionParams)
    
      this.searchItemConfig = Object.assign({}, this.editMenuForm.searchItemConfig)
   


    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.optionModel.tabMenuItems.splice(index, 1)
      }

    },
    randomString(len) {
            len = len || 32;
            let $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
            let maxPos = $chars.length;
            let pwd = '';
            for (let i = 0; i < len; i++) {
                pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return pwd;
        },
    // 添加按钮
    addOption() {
      if(!this.optionModel.hasOwnProperty("tabMenuItems")){
            this.$set(this.optionModel, "tabMenuItems", [])
      }
      let newValue = this.optionModel.tabMenuItems.length + 1
      let randomNum = this.randomString(6)
      if(this.optionModel.tabMenuItems.length==0){
        randomNum ="1"
      }
      this.optionModel.tabMenuItems.push(
        { title:"标题"+newValue,fieldName: 'tabMenu'+randomNum,name:randomNum,activeTabItem:"",iconUrl:'el-icon-menu',iconColor:'',iconSize:20,bgColor:'#fff', hoverColor:'lightsalmon',activeColor:"lightsalmon", fontColor:'#606266',  disabled:false, check: true, canRemove: true, otherParams: {}, actionParams: {} },
      )

    },
  

  }
}
</script>
  
<style lang="scss" scoped>
.option-items-pane ul {
  // list-style: none;
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  // list-style: none;
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}
</style>
  