
// 统一处理VForm Components
import { useFormatParams } from "@/hooks/useFormatParams"
export function useHandleVFormComponents(vueInstance, refFormName = 'preForm') {
   return {
      data: [],
      vueInstance: vueInstance,
      refFormName: refFormName,
      isFromParent:false,// 是否来自父组件
      useFormatParamsFn: useFormatParams(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      testFn(subItem) {
        // actionParams: Object
            // actionFunction: "insertRowFn"
            // actionName: "vxetable64253"
        // query: Object
        // canRemove: false
        // check: true
        // label: "新增"
        // otherParams: Object
             // actionType: "ctrlComp"
        // value: "iisAdd"
         console.log("hello world")
      },
      // 通过控件名称，获取控件实例
       getInstanceRef(actionName="",boxType='popup'){
          //debugger
          let instanceRef =null
          if(!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxDrawerRef.editPreForm"){
            instanceRef = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[actionName]
          }else if(!this.isFromParent && !!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxRef.editPreForm"){
            if(boxType =='basicForm'){
              instanceRef = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[actionName]
            }else{
              instanceRef = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[actionName]
            }
          }
          else{
            //let widgetRefList = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList
            instanceRef = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[actionName]
          }
          if (!!instanceRef) {
            // 特殊
            if(actionName.includes('splitpanes') || actionName.includes('echarts')|| actionName.includes('card')|| (actionName.includes('tab') && !actionName.includes('table'))){
                 return instanceRef
            }
            else{
                // 正常$refs 对象
               instanceRef = instanceRef.$refs[actionName]
            }
          
          }
          return instanceRef
       },
       // 获取绑定控件对应的对外开放的数值
       getCtrlBandingValue(ctrName){
        // debugger
         let _selectedIndex=null
         let instanceRef = this.getInstanceRef(ctrName)
         if(!!instanceRef){
            try {
              _selectedIndex= instanceRef.publicAttribute.selectedIndex
            } catch (error) {
              _selectedIndex =null
               console.error(actionName+" ====executeActionFunction===:"+functionName+' error:没有对应的属性值，请先配置！')
            }
         }else{
               _selectedIndex =null
               console.error(actionName+" ====executeActionFunction===:"+functionName+' error:没有找到对应的实例！')
         }
         return  _selectedIndex
       },
       // 执行控件实例方法
       executeActionFunction(subItem,boxType='popup'){
         //debugger
           this.isFromParent = false
            if(subItem.hasOwnProperty("isFromParent")){
              this.isFromParent =  subItem.isFromParent
            }
            let actionName = this.useFormatParamsFn.getActionParamsValue("actionName", subItem)
            let functionName = this.useFormatParamsFn.getActionParamsValue("actionFunction", subItem)
            if(!!actionName && !!functionName){
                let instanceRef = this.getInstanceRef(actionName,boxType)
                if(!!instanceRef){
                 // debugger
                   try {
                         instanceRef[functionName](subItem)
                   } catch (error) {
                    console.error(actionName+" ====executeActionFunction===:"+functionName+' error:没有对应的方法，请先配置！')
                   }
                }else{
                    console.error(actionName+" ====executeActionFunction===:"+functionName+' error:没有找到对应的实例！')
                }
            }
       }
   

   }
}