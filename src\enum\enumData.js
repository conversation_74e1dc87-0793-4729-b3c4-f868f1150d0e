//vxetable,customTree,echarts,splitpanes,defaultmenubutton

export const contrlTypeEventList = {
    vxetable:[],
    customTree:[
        {label:"内容改变",value:"change"},
        {label:"聚焦",value:"focus"},
        {label:"失焦",value:"blur"},
    ],
    echarts:[],
    splitpanes:[],
    defaultmenubutton:[]
}

// 固定对齐方式列表
export const fixedList = [
    { label: 'left', value: 'left' },
    { label: '默认', value: null },
    { label: 'right', value: 'right' }
]
// 查询控件类型 可用列表
export const searchCtrlType = [
    { label: '输入框', value: 'text' },
    { label: '数字输入框', value: 'number' },
    { label: '下拉框', value: 'reportSelect' },
    { label: '下拉树', value: 'reportSelectTree' },
    { label: '下拉框(表)', value: 'reportSelectTable'},
    { label: '下拉框(自定义单选)', value: 'customSelect' },
    { label: '下拉框(自定义多选)', value: 'customMulSelect' },
    { label: '日期', value: 'date' },
    { label: '日期时间', value: 'datetime' },
    { label: '日期范围', value: 'daterange' },
    { label: '日期时间范围', value: 'datetimerange' },

    // datetime/datetimerange
]

// 功能按钮 枚举类型
export const actionType = {
    iisAdd: 'iisAdd', // 添加
    iisInsert: 'iisInsert', // 插入
    iisEdit: 'iisEdit', // 编辑
    iisDel: 'iisDel', // 删除
    iisView: 'iisView', // 删除
    iisDisabled: 'iisDisabled', // 作废/禁用
    iisEnable: 'iisEnable', // 启用
    iisPrint: 'iisPrint', // 打印
    iisAudit: 'iisAudit', // 审核
    iisNotAudit: 'iisNotAudit', // 反审
    iisExport: 'iisExport', // 导出
    iisImport: 'iisImport', // 导入
    iisUsersM: 'iisUsersM', // 用户管理
    iisRightsS: 'iisRightsS', // 权限保存
    iisCopy: 'iisCopy', // 复制
    iisRefresh: 'iisRefresh', // 刷新
    iisRefreshWithAction: 'iisRefreshWithAction', //操作后刷新
    iisDetail: 'iisDetail', // 详细
    iisCreate: 'iisCreate', // 创建表
    iisSave: 'iisSave', // 保存
    iisSave: 'iisReset', // 重置
    iisCancel: 'iisCancel', // 撤消
    iisSearch: 'iisSearch', // 查询
    iisSearchMore: 'iisSearchMore', // 多条件 查询
    iisPopup: 'iisPopup', // 弹框
}
// 对齐方式列表
export const alignList = [
    { label: 'left', value: 'left' },
    { label: 'center', value: 'center' },
    { label: 'right', value: 'right' }
]

// 控件类型列表 text, search, number,  integer, float, password, date, time, datetime, week, month, year
export const controlTypeList = [
    { label: '普通输入框', value: 'text' },
    { label: '数字输入框', value: 'number' },
    { label: '标准弹框', value: 'standardFrame' },
    { label: 'ICON图文', value: 'IconTextColumn' },
    { label: '下拉框', value: 'reportSelect' },
    { label: '下拉框(多选)', value: 'reportMulSelect' },
    { label: '下拉框(表)', value: 'reportSelectTable'},
    { label: '下拉框(自定义)', value: 'customSelect' },
    { label: '自定义渲染控件', value: 'customRenderCtrl'},
    // { label: '简单弹框表', value: 'popTable' },
    // { label: '开关', value: 'switch' },
    // { label: '勾选框', value: 'checkbox' },
    { label: '日期选择', value: 'date' },
    { label: '年月选择', value: 'month' },
    { label: '日期时间', value: 'dateTime' },
    // { label: '文本域', value: 'textarea' },
    { label: '状态列(A/D)', value: 'statusColumn' },
    { label: '状态列(Y/N)', value: 'statusColumnYN' },
    { label: '状态列(1/0)', value: 'statusColumn10' },
    { label: '状态列(true/false)', value: 'statusColumnTF'},
    { label: '勾选框', value: 'checkbox' },
    { label: '弹框资料(仅列表页)', value: 'popupTextarea' },
]

// 是否数据列表
export const yesOrNoList = [
    { label: '是', value: '1' },
    { label: '否', value: '0' }
]

//渲染类型
export const reporConditionType = [
    { label: '大于', value: 'gt' },
    { label: '大于或等于', value: 'gte' },
    { label: '等于', value: 'eq' },
    { label: '不等于', value: 'neq' },
    { label: '小于', value: 'lt' },
    { label: '小于或等于', value: 'lte' },
    { label: '包含', value: 'in' },
]
//渲染类型
export const reporConditionOptions = [
    // { label: '无', value: '@' },
    { label: '并', value: 'and' },
    { label: '或', value: 'or' },
]
//渲染类型
export const reportfontbgType = [
    { label: '字体【单元格】', value: 'text' },
    { label: '背景【单元格】', value: 'bg' },
    { label: 'ICON图标', value: 'IconValue' },
    { label: '字体-整行【仅在第一个/行字段中设置有效】', value: 'text_Row' },
    { label: '背景-整行【仅在第一个/行字段中设置有效】', value: 'bg_Row' },
]
// 报表颜色列表
export const reportColorList = [
    { label: '无', value: '' },
    { label: '绿色', value: '#67C23A' },
    { label: '红色', value: '#F56C6C' },
    { label: '橙色', value: '#E6A23C' },
    { label: '蓝色', value: '#409EFF' },
    { label: '紫色', value: '#7030A0' },
    { label: '灰色', value: '#909399' },
]
//列格式化列表
export const fieldFormatList = [
    {value:'',label:'请选择'},
    {value:'YYYY_MM_DD_HH_mm_ss',label:'默认1[YYYY-MM-DD HH:mm:ss]'},
    {value:'YYYY_MM_DD',label:'默认2[YYYY-MM-DD]'},
    {value:'YY_MM_DD',label:'[YY-MM-DD]时格一'},
    {value:'YY_MM_DD_HH_mm',label:'YY-MM-DD HH:mm]时格二'},
    {value:'YY_MM',label:'[YY-MM]时格三'},
    {value:'MM_DD',label:'[MM-DD]时格四'},
    {value:'MM_DD_HH_mm',label:'[MM-DD HH:mm]时格五'},
    {value:'YYYYMM',label:'[YYYYMM]时格六'},
    {value:'NUMBER_MARK',label:'千分符'},
    {value:'NUMBER_WAN',label:'万分符'},
    {value:'DECIMAL_DIGITS2',label:'#,###.00'},
    {value:'DECIMAL_DIGITS3',label:'#,###.000'},
    {value:'DECIMAL_DIGITS4',label:'#,###.0000'},
    {value:'DECIMAL_DIGITS6',label:'#,###.000000'},
]

export const sqlServeSearchType = [
    {value:0,label:'等于',text:'Equal'},
    {value:1,label:'模糊查询',text:'Like'},
    {value:2,label:'大于',text:'GreaterThan'},
    {value:3,label:'大于等于',text:'GreaterThanOrEqual'},
    {value:4,label:'小于',text:'LessThan'},
    {value:5,label:'小于等于',text:'LessThanOrEqual'},
    {value:6,label:'In操作',text:'In'},
    {value:7,label:'NotIn操作',text:'NotIn'},
    {value:8,label:'左模糊',text:'LikeLeft'},
    {value:9,label:'右模糊',text:'LikeRight'},
    {value:10,label:'不等于',text:'NoEqual'},
    {value:11,label:'IsNullOrEmpty',text:'IsNullOrEmpty'},
    {value:12,label:'IsNot',text:'IsNot'},
    {value:13,label:'模糊查询取反',text:'NoLike'},
    {value:14,label:'EqualNull ',text:'EqualNull'},
    {value:15,label:'InLike',text:'InLike'},
    {value:16,label:'区间范围 ',text:'BetweenAnd'}, 
]