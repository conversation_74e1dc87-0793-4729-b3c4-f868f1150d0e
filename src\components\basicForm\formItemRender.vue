<template>
    <div v-if="orderFormItems && orderFormItems.length > 0">
        <el-skeleton
                :loading="loadingForm"
                :rows="10"
                animated
                :throttle="10">
                <el-form  ref="originFormRef" :rules="formRenderConfig.formRules" :model="formRenderConfig.formData"
                    label-width="110px">
                    <div class="flex flex-wrap">
                        <template v-for="(item) in orderFormItems">
                            <template v-if="item.controlType == 'checkbox'">
                                <!-- 勾选框 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-checkbox :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        @change="changeEvent_common(item, $event)"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        v-model="formRenderConfig.formData[item.field]">
                                    </el-checkbox>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'systemCustomList'">
                                <!-- 下拉框 系统变量 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectSystemCustomList 
                                        :disabled="getDisabled(item)" 
                                        :paramsItem="item"  
                                        v-bind="getOtherAttr(item)" 
                                        :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]">
                                    </selectSystemCustomList>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'multipleCustomList'">
                                <!-- 下拉框 系统变量 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectMultipleCustomList 
                                        :disabled="getDisabled(item)" 
                                        :paramsItem="item"  
                                        v-bind="getOtherAttr(item)" 
                                        :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]">
                                    </selectMultipleCustomList>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'listPopupTable'">
                                <!-- 下拉框 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <listPopupTable :disabled="getDisabled(item)" :paramsItem="item"
                                        :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :title="item.title"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]"
                                        >
                                    </listPopupTable>
                                    <!-- {{formRenderConfig.formData[item.field.replace('Text', '')]}}//{{formRenderConfig.formData[item.field]}} -->
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'select'">
                                <!-- 下拉框 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectRender :disabled="getDisabled(item)" :paramsItem="item"
                                        :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]"
                                        :currentValueText="formRenderConfig.formData[item.field]">
                                    </selectRender>
                                    <!-- {{formRenderConfig.formData[item.field.replace('Text', '')]}}//{{formRenderConfig.formData[item.field]}} -->
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'associatedDropdown'">
                                <!-- 联动下拉 类型 this.formRenderConfig.formData-->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <associatedDropdown :disabled="getDisabled(item)" :paramsItem="item"
                                        :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]"
                                        :currentValueText="formRenderConfig.formData[item.field]">
                                    </associatedDropdown>
                                
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'selectTable'">
                                <!-- 下拉框 类型 selectTable -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectTableRender :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :currentValue="formRenderConfig.formData[item.field]">
                                    </selectTableRender>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'selectTreeSelf'">
                                <!-- 下拉框 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectTreeRenderSelfData :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        @changeEvent="changeEvent_select(item, $event)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]"
                                        :currentValueText="formRenderConfig.formData[item.field]">
                                    </selectTreeRenderSelfData>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'popTable'">
                                <!-- 表格弹框 类型 适用返回多个字段，且弹框字段为文本时-->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <popTableRender :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :label="!!item.title ? item.title : item.field"
                                        :formData="formRenderConfig.formData"
                                        @changeEvent="changeEvent_popTable(item, $event)"
                                        v-model="formRenderConfig.formData[item.field]">
                                        ></popTableRender>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'standardFrame'">
                                <!-- 标准弹框 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <standardFrame :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        @changeEvent="changeEvent_common(item, $event)"
                                        v-model="formRenderConfig.formData[item.field]">
                                        ></standardFrame>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'selectTableBinary'">
                                <!-- 下拉框(二进制表) 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <selectTableBinary :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]+''"
                                        @changeEvent="changeEvent_selectTableBinary(item, $event)"
                                        :value="formRenderConfig.formData[item.field]">
                                        ></selectTableBinary>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'selectDictionaryBinary'">
                                <!-- 下拉框(二进制字典) -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <!-- {{item.field}}  {{ formRenderConfig.formData[item.field] }} -->
                                    <selectDictionaryBinary :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]+''"
                                        @changeEvent="changeEvent_selectDictionaryBinary(item, $event)"
                                        :value="formRenderConfig.formData[item.field]">
                                        ></selectDictionaryBinary>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'switch'">
                                <!-- 开关   active-color="#13ce66"
                                            inactive-color="#ff4949"-->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-switch :paramsItem="item" :disabled="getDisabled(item)"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]"
                                        @change="changeEvent_common(item, $event)">
                                    </el-switch>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'statusColumn'">
                                <!-- 状态列(A/D)   active-color="#13ce66"
                                            inactive-color="#ff4949"-->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <!-- {{ formRenderConfig.formData[item.field] }} -->
                                    <statusColumnSwitch 
                                        :disabled="getDisabled(item)" 
                                        :paramsItem="item" 
                                        :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :currentValue="formRenderConfig.formData[item.field]"
                                        :formData="formRenderConfig.formData"
                                        @changeEvent="changeEvent_searchPro(item, $event)"
                                    >
                                    </statusColumnSwitch>
                                
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'statusColumnYN'">
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-switch :paramsItem="item"  :dataSearchOptions="dataSearchOptions" active-value="Y" inactive-value="N"
                                        :disabled="getDisabled(item)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]"
                                        @change="changeEvent_common(item, $event)">
                                    </el-switch>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'statusColumn10'">
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-switch :paramsItem="item"  :dataSearchOptions="dataSearchOptions" :active-value="1" :inactive-value="0"
                                        :disabled="getDisabled(item)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]"
                                        @change="changeEvent_common(item, $event)">
                                    </el-switch>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'textarea'">
                                <!-- 文本域 :style="[{ width: formItemWidth + 'px' }]"类型item.editWidth -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-input :style="[{ width: item.editWidth * 2 + Number(item.labelWidth) + 'px' }]"
                                        :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions" type="textarea"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :rows="getTextareaRows(item)" placeholder="请输入内容" @change="changeEvent_common(item, $event)"
                                        v-model="formRenderConfig.formData[item.field]">
                                    </el-input>

                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'number'">
                                <!-- 计数器 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <inputNumberRender 
                                        v-bind="getOtherAttr(item)" 
                                        :disabled="getDisabled(item)" 
                                        :paramsItem="item"  
                                        :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        :currentValue="formRenderConfig.formData[item.field]"
                                        @changeEvent="changeEvent_select(item, $event)">
                                    </inputNumberRender>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'dateTime'||item.controlType == 'datetimerange' ">
                                <!-- 日期时间选择器 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-date-picker :paramsItem="item"  :dataSearchOptions="dataSearchOptions" :disabled="getDisabled(item)"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]" type="datetime"
                                        value-format="yyyy-MM-dd HH:mm:ss" @change="changeEvent_common(item, $event)"
                                        placeholder="选择日期时间">
                                    </el-date-picker>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'date' ||item.controlType == 'daterange'">
                                <!-- 选择日期 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-date-picker :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]" type="date" value-format="yyyy-MM-dd"
                                        @change="changeEvent_common(item, $event)" placeholder="选择日期">
                                    </el-date-picker>
                                </el-form-item>
                            </template>
                            <template v-else-if="item.controlType == 'yearMonth'">
                                <!-- 选择日期 类型 -->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-date-picker :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        v-model="formRenderConfig.formData[item.field]" type="month" value-format="yyyy-MM"
                                        @change="changeEvent_common(item, $event)" placeholder="选择月份">
                                    </el-date-picker>
                                </el-form-item>
                            </template>
                            <!-- 计数器 类型 -->
                          
                            <template v-else>
                                <!-- 其它文本输入框 v-if="!!item.iisShowEdit"-->
                                <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit"
                                    :text-class-name="item.field + item.title" :label="!!item.title ? item.title : item.field"
                                    :prop="item.field" :key="item.field">
                                    <el-input :disabled="getDisabled(item)" :paramsItem="item"  :dataSearchOptions="dataSearchOptions"
                                        :style="[{ width: getEditWidth(item) + 'px' }]"
                                        :field="item.field"
                                        :formData="formRenderConfig.formData"
                                        @change="changeEvent_common(item, $event)"
                                        v-model="formRenderConfig.formData[item.field]">
                                    </el-input>
                                </el-form-item>
                            </template>
                        </template>

                    </div>
                </el-form>
            </el-skeleton>
       
    </div>
</template>
<script>
/**
 * @desc  表单渲染通用控件
 *
 * @params 参数
 * 
 * <AUTHOR> Huang
 *
 * @created 2022/10/17
 */
 import config from '@/config'
import request from '@/libs/request'
import { useDebounceFn } from '@vueuse/core'
 const cloneDeep = require("clone-deep");
import { orderBy } from "lodash-es";
import listPopupTable from "./listPopupTable.vue"
import statusColumnSwitch from "./statusColumn.vue"
import selectDictionaryBinary from "./selectDictionaryBinary.vue"
import selectTableBinary from "./selectTableBinary.vue"
import popTableRender from "./popTableRender.vue"
import standardFrame from "./standardFrame.vue"
import selectSystemCustomList from "./selectSystemCustomList.vue"
import selectMultipleCustomList from "./selectMultipleCustomList.vue"
import selectRender from "./selectRender.vue"
import inputNumberRender from "./inputNumberRender.vue"
import associatedDropdown from "./associatedDropdown.vue"
import selectTableRender from "./selectTableRender.vue"
import selectTreeRender from "./selectTreeRender.vue"
import selectTreeRenderSelfData from "./selectTreeRenderSelfData.vue"
// import searchProConfig from "./searchProConfig.vue"
export default {
    name: "formItemRender",
    components: {selectMultipleCustomList,listPopupTable,inputNumberRender,statusColumnSwitch,selectTableBinary, selectDictionaryBinary,selectRender,selectSystemCustomList, selectTableRender, selectTreeRender, popTableRender, selectTreeRenderSelfData,standardFrame,associatedDropdown},
    props: {
         // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
         dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
        // 控件默认宽度 209
        formItemWidth: {
            type: Number,
            default: 209,
        },
        // 是否加载中
        isLoading: {
            type: Boolean,
            default: false,
        },
        // 是否禁用所有控件
        disabledAll: {
            type: Boolean,
            default: false,
        },
        // 弹框窗体是否已经显示
        showEditBox: {
            type: Boolean,
            default: false,
        },
        // 表单配置信息
        formRenderConfig: {
            type: Object,
            default() {
                return {
                    formData: {}, // 表单字段
                    formRules: {}, // 表單驗證規格
                    formItems: [] //表单字段渲染条件和默认值
                }
            }
        },
        // 表格列数据
        tableColumns:{
            type: Array,
            default() {
              return [];
            },
        },
        // 是否通过缓存CID 来查询最新的数据，否则使用缓存，默认使用缓存
        searchDetaiById:{
            type:Boolean,
            default:false
        }

    },
    data() {
        return {
            loadingForm:false,
            tableRowDetail:null,
            config:config,
            request:request,
           // cloneDeep:cloneDeep,
        }
    },
    computed: {
         //当前页面按钮操作类型 获取 actionBtttons 点击按钮事件
         actionType_state() {
            return this.$store.state.actionType;
        },
        orderFormItems() {
            let formItems = this.formRenderConfig.formItems
            let dataAfterSort = orderBy(formItems, ["editOrder"], ["asc"]); // 升序排序
            return dataAfterSort
        },
        // 编辑页面按钮操作类型 
        editActionType_state() {
            return this.$store.state.editActionType;
        },
    },
    watch: {
        isLoading(n,o){
            this.loadingForm = n
        },

        // 上一条、下一条按钮触发事件
        editActionType_state: {
            handler(n, o) {
                if (n.value == 'nextItem' || n.value == 'preItem') {
                    if(!!this.searchDetaiById){
                         this.loadingForm = true
                    }
                    this.editActionTypeChangeEvent(n.value)
                }
            },
            deep: true,
            immediate: false
        },
    },
    mounted(){
     
        // setTimeout(()=>{
        //     this.editActionTypeChangeEvent()
        // },500)
        // this.debouncedFn_loadDataDetailByCID = useDebounceFn((params) => {
        //     this.loadDataDetailByCID(params)
        // }, 500)
    },
    methods: {
        // 获取其它配置属性
        getOtherAttr(item){
            let controlType = item.controlType
            let attr={}
            switch (controlType) {
                case 'number':
                attr = this.getNumAttr(item)
                    break;
            
                default:
                attr = this.getCommonAttr(item)
                    break;
            }
            return attr
        },
         // 获取其它配置属性 multiple:true,disabled:true
        getCommonAttr(item){
            //debugger
            let attrConfig = item.sourceKey
            let defaultAttr={}
            // multiple	是否多选	boolean	—	false
            // disabled	是否禁用	boolean	—	false
            if(!!attrConfig){
                if(attrConfig.includes(',')){
                    // 多个参数
                    let attrList = attrConfig.split(',')
                    attrList.forEach(item=>{
                        let  itemList = item.split(':')
                      
                        let attrVal = itemList[1]
                        if(attrVal =='true'){
                            attrVal = true
                        }
                        if(attrVal =='false'){
                            attrVal = false
                        }
                        defaultAttr[itemList[0]]=attrVal
                    })
                }else{
                    // 单个参数
                    let itemList = attrConfig.split(':')
                    let attrVal = itemList[1]
                        if(attrVal =='true'){
                            attrVal = true
                        }
                        if(attrVal =='false'){
                            attrVal = false
                        }
                    defaultAttr[itemList[0]]=attrVal
                   
                }
              
            }
            return defaultAttr
        },
        getNumAttr(item){
            let attrConfig = item.sourceKey // precision:2,step:0.1,max:10
            let defaultAttr={}
            // min	设置计数器允许的最小值	number	—	-Infinity
            // max	设置计数器允许的最大值	number	—	Infinity
            // step	计数器步长	number	—	1
            // step-strictly	是否只能输入 step 的倍数	boolean	—	false
            // precision	数值精度	number
            let numberAttr = ['min','max','step','precision']
            if(!!attrConfig){
                if(attrConfig.includes(',')){
                    // 多个参数
                    let attrList = attrConfig.split(',')
                    attrList.forEach(item=>{
                        let  itemList = item.split(':')
                        if(numberAttr.includes(itemList[0])){
                            defaultAttr[itemList[0]]=Number(itemList[1]) 
                        }else{
                            defaultAttr[itemList[0]]=itemList[1]
                        }
                       
                    })
                }else{
                    // 单个参数
                    let itemList = attrConfig.split(':')
                    if(numberAttr.includes(itemList[0])){
                        defaultAttr[itemList[0]]=Number(itemList[1]) 
                    }else{
                        defaultAttr[itemList[0]]=itemList[1]
                    }
                   
                }
              
            }
            return defaultAttr
        },
        getTextareaRows(item){
            let _rows = 2
           try {
            if(!!item.sourceKey){
                _rows = parseInt(item.sourceKey)
            }
           } catch (error) {
            _rows = 2
           }
           return _rows
        },
        // 获取是否可以编辑
        getDisabled(item){
            //debugger
            let _disabled = !!item.iisReadOnly ||this.disabledAll 
            // 是否编辑时，不可修改 
           //  console.log("===item.iisdisabledWhenEdit==="+item.iisDisabledWhenEdit)
                if(this.actionType_state.value == 'iisEdit' && item.iisDisabledWhenEdit==1){
                    _disabled= true
                }
            return _disabled
        },
        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
        // 上下条信息切换
       async editActionTypeChangeEvent(nextOrPreRow = 'init') {
           
            let formName = this.getRequest("formName")
            if (!formName) {
                // if null return
                return
            }
            let currentPageDataList = []
            let dataListObj = this.$store.state.tableFullDataAllPage.value
            if (!dataListObj || Object.keys(dataListObj).length == 0) {
                // if null return
                return
            }
            if (dataListObj && Object.keys(dataListObj).length > 0 && !!formName && dataListObj[formName].length > 0) {
                // 当前页面表格数据
                currentPageDataList = dataListObj[formName]
            }
            if (currentPageDataList.length == 0) {
                // if null return
                return
            }
            // 当前选中行数据
            let currentItem = this.formRenderConfig.formData
            console.log("===当前选中行数据==currentItem=====",currentItem)
            if (!currentItem.CID) {
                // if null return
                return
            }
            let curRowIndex = 0
            let totalNum = currentPageDataList.length - 1 // 从0 计算，需要减去1
            let isExistIndex = currentPageDataList.findIndex(item => {
                return item.CID == currentItem.CID
            })
            if (isExistIndex > 0) {
                curRowIndex = isExistIndex
            }
            if(nextOrPreRow == 'init'){
                let preNextRowConfig ={
                    key: Math.floor(Math.random() * 10000 + 1),// 随机字符串
                    curRowIndex,
                    totalNum
                }
                this.$store.commit('set_preNextRowConfig',preNextRowConfig)
                return
            }
            if (nextOrPreRow == 'nextItem') {
                curRowIndex = curRowIndex + 1
                let preNextRowConfig ={
                    key: Math.floor(Math.random() * 10000 + 1),// 随机字符串
                    curRowIndex,
                    totalNum
                }
                this.$store.commit('set_preNextRowConfig',preNextRowConfig)
                if (curRowIndex > totalNum) {
                    // 最后一条
                    curRowIndex = totalNum
                   this.$message({
                    type: 'warning',
                    message: '已经是最后一条'
                    });          
                    return
                }

            } else {
                curRowIndex = curRowIndex - 1
                let preNextRowConfig ={
                    key: Math.floor(Math.random() * 10000 + 1),// 随机字符串
                    curRowIndex,
                    totalNum
                }
                this.$store.commit('set_preNextRowConfig',preNextRowConfig)
                if (curRowIndex < 0) {
                    // 第一条
                    curRowIndex = 0
                    this.$message({
                        type: 'warning',
                        message: '已经是第一条'
                    });       
                    return
                }
            }
            this.formRenderConfig.formData = currentPageDataList[curRowIndex]
            //debugger
            this.tableRowDetail = this.formRenderConfig.formData
            if(!!this.searchDetaiById){
                // debugger
                await this.debouncedFn_loadDataDetailByCID()
            }
            let saveVuexParams = {
                key: formName,
                value: this.formRenderConfig.formData
            }
            // 连续编辑时，复制对象，保存历史操作
            let copyNewRow = cloneDeep(this.formRenderConfig.formData)
            this.$store.commit("set_currentCopyRow", copyNewRow);
            this.$store.commit("set_currentRow_viewdetailPreNext", saveVuexParams);

        },
          // 查询表格行数据详情BY CID
          loadDataDetailByCID(){
           
             let _url=this.config.moduleSub+"QueryByID"
             let params = {
                "TableHeader": this.dataSearchOptions,
                "CID": this.tableRowDetail.CID,
            }
            if(!params.CID){
                console.error("当前行 CID is null,无法查询详情")
                return
            }

            this.request['post'](_url, params).then(res=>{
                if(res && res.Success){
                    this.loadingForm = false
                    this.formRenderConfig.formData  = Object.assign({},this.formRenderConfig.formData,res.Datas) 
                }
            })
            setTimeout(()=>{
                this.loadingForm = false
            },10000)
        },
        // 获取编辑宽，如果为0，占一行
        getEditWidth(item) {
            // debugger
            let width = Number(item.editWidth)
            if (Number(width) == 0) {
                width = Number(this.formItemWidth) * 2 + Number(item.labelWidth)
            }
            return width
        },
        // 简单表格弹框 选中回调事件
        changeEvent_popTable(item, event) {
            //debugger
            // 转换字段 
            let matchFieldList = event.matchField.split('|')
            let row = event.row
            let oldFieldsStr = matchFieldList[0]
            let newFieldsStr = matchFieldList[1]
            if (oldFieldsStr.includes(',')) {
                // 多个字段 赋值 eg:CTABLE_NAME,CTID|CDISPLAY_NAME,CID【表单字段|弹框返回字段】
                let oldFieldsList = oldFieldsStr.split(',')
                let newFieldsList = newFieldsStr.split(',')
                oldFieldsList.forEach((item, index) => {
                    let newItemName = newFieldsList[index] // 根据下标获取新字段名
                    this.formRenderConfig.formData[item] = row[newItemName]
                })

            } else {
                // 单个字段 赋值  ==》弹框数据 赋值 到 表单数据 eg:CTABLE_NAME|CDISPLAY_NAME
                this.formRenderConfig.formData[oldFieldsStr] = row[newFieldsStr]
            }
        },
        //下拉框 触发事件
        changeEvent_select(item, event) {
            //debugger
            if (!event) {
                return
            }
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: event.value,
                text: event.text,
                title: item.title,
            };
            // this.formRenderConfig.formData[item.field.replace('Text', '')] = event.value
            this.formRenderConfig.formData[item.field] = event.value
            this.changeEvent(params)
        },
        getBinaryData(list){
            let listVal =0
            if(list && list.length>0){
                list.forEach(item=>{
                    listVal =listVal + Number(item)
                })
            }
            return listVal
       },
        // 二进制 数据字典
        changeEvent_selectDictionaryBinary(item, event){
            if (!event) {
                return
            }
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: this.getBinaryData(event.value),
                text: "",
                title: item.title,
            };
            this.formRenderConfig.formData[item.field] =this.getBinaryData(event.value)
            this.changeEvent(params)
        },
        // 二进制 数据表格
        changeEvent_selectTableBinary(item, event){
            if (!event) {
                return
            }
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: this.getBinaryData(event.value),
                text: "",
                title: item.title,
            };
            //debugger
            this.formRenderConfig.formData[item.field] = this.getBinaryData(event.value)
            this.changeEvent(params)
        },
         //輸入框，勾选框，计数器 选择日期 触发事件
         changeEvent_searchPro(item, event) {
            //debugger
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: event,
                title: item.title,
            };
            this.formRenderConfig.formData[item.field] = event.value
            this.changeEvent(params)
        },
        //輸入框，勾选框，计数器 选择日期 触发事件
        changeEvent_common(item, event) {
            //debugger
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: event,
                title: item.title,
            };
            this.changeEvent(params)
        },
        // 通用事件改變 回調
        changeEvent(data) {
            let params = {
                data,
                form: this.formRenderConfig.formData
            }
            this.$emit("changeEvent", params)
        }
    }
}
</script>
<style scoped lang="scss">

</style>