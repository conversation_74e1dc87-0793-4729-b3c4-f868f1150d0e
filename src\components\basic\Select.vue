<template>
    <div class="Select">
        <el-select :placeholder="placeholders" v-model="values" @focus="handOpen" @blur="handclose" style="width: 100%">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"
                style="display: none" />
        </el-select>

        <ul class="select_solt" v-if="visiable">
            <template v-for="item in options">
                <li v-if="!isSlots" :key="item.value" @click="handclick(item.value)">
                    {{ item.label }}
                </li>
            </template>

            <slot> </slot>
        </ul>
    </div>
</template>
<script>
export default {
    name: "Select",
    props: {
        option: {
            require: true,
            type: Array,
        },
        isSlot: {
            require: true,
            type: Boolean,
        },
        value: {
            require: true,
        },
        placeholder: {
            require: false,
            type: String,
        },
        onChange: {
            require: false,
            type: Function,
        },
    },
    data() {
        return {
            visiable: false,
            options: "",
            values: "",
            placeholders: "",
            isSlots: false,
        };
    },
    watch: {
        value: function (val) {
            this.values = val;
        },
        option:{
            handler(n,o){
                if(n){
                    this.options = n;
                }
            },
            deep:true,

        }
    },
    mounted() {
        //debugger
        this.options = this.option;
        this.values = this.value;
        this.placeholders = this.placeholder;
        this.isSlots = this.isSlot;
    },
    methods: {
        handclick(value) {
            this.values = value;
            this.onChange(value);
        },
        handOpen() {
            this.visiable = true;
            this.$emit("onfocus")
        },
        handclose() {
            setTimeout(() => {
                this.visiable = false;
            }, 200);
        },
    },
};
</script>
<style lang="scss">
// .el-select-dropdown {
//     z-index: -1 !important;
// }

// .Select {
//     position: relative;
//     .select_solt {
//         position: absolute;
//         top: 108%;
//         left: 0;
//         width: 100%;
//         background: #fff;
//         z-index: 2;
//         max-height: 200px;
//         overflow-y: auto;
//         list-style: none;
//         padding: 6px 0;
//         margin: 0;
//         border-radius: 5px;
//         box-sizing: border-box;
//         box-shadow: 0px 0px 3px #ccc;

//         li {
//             list-style: none;
//             font-size: 14px;
//             padding: 0 20px;
//         }

//         li:hover {
//             background: #f5f7fa;
//         }
//     }
// }

// .Select :hover {
//     cursor: pointer;
// }

// /*滚动条样式*/
// ::-webkit-scrollbar {
//     width: 8px;
// }

// ::-webkit-scrollbar-thumb {
//     border-radius: 2px;
//     background: #ddd;
// }

// ::-webkit-scrollbar-track {
//     background: #fff;
// }
</style>
