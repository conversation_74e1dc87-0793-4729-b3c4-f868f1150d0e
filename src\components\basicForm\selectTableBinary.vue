<template>
    <div class="selectRender">
        <el-select 
            v-if="selectDataList.length>0"
            ref="elSelectRef_selectTableBinary"
            @change="changeEvent"
            v-bind="$attrs"
            clearable
            :multiple="is_multiple"
            :value-key="valueKey"
            v-model="currentItem"
            v-on="$listeners"
            :loading="loading"
            placeholder="请选择">
            <el-option  v-for="(item,index) in selectDataList"  :key="index" :label="item.label" :value="item.value">
            </el-option>
         </el-select>
       
        
    </div>
</template>
<script>
 const cloneDeep = require("clone-deep");
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'selectTableBinary',
    components:{},
    props:{
    //   // 表格信息>>dataFrom=talbe 才有效
    //   tableInfo:{
    //       type:Object,
    //       default(){
    //           return {}
    //       } 
    //   },
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
      
        // 当前字段表单
        formData:{
          type:Object,
          default(){
              return {}
          }
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
       // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
       dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
       // 当前字段:value
     currentValue:{
        type:[String,Number,Array],
         default:""
     },

     // 是否可用
     disabled:{
         type:Boolean,
         default:false,
     }  
    }, 
    data(){
        return {
            valueKey:"CID",
            is_multiple:false,
            copyCurrentValue:'',
            config:config,
            loading: false,
            prefixIcon:"", // 头部图标,用于是否加载中 显示
            currentItem:'',// 当前选择项
            currentItemText:'',// 当前选择项描述
            selectDataList:[],// 当前列表数据
        }
    },
    computed:{ 
        dataSourceID(){
            let _id =""
            try {
                _id = this.dataSearchOptions.dbId
            } catch (error) {
                _id =""
            }
            return _id
        }
    },
    watch:{
        currentValue(n,o){
            if(!!n){
               this.currentItem = n
            }else{
                // 清空数据
                 this.currentItem =''
               //  this.currentItemText =''
            }
            // this.$nextTick(()=>{
            //     this.loadData()
            // })
        },
      
        
    },
    mounted(){
      //首次赋值
      this.copyCurrentValue = cloneDeep(this.currentValue)
         this.is_multiple = true
      this.$nextTick(()=>{
       
        this.loadData()
      })
    },
    methods:{
        // 反推选中数据
        initDefaultList(selectDataList) {

            let newList =[]
             selectDataList.forEach(item=>{
                if((Number(item.value) & this.copyCurrentValue) > 0){
                    newList.push(item.value+'')
                }
            })
            if(newList && newList.length>0){
                this.currentItem = newList
            }
        },
      
         // 格式化数据成下拉框所需的
         formatData(dataList=[]){
           // debugger
            let newData =[] 
            let matchField = this.paramsItem.matchField //CCODE,CVALUE
           // let matchField = "bpArtId,bpArtCode"
            let keyValueArr = matchField.split(',')
            this.valueKey = keyValueArr[0]
            if(dataList && dataList.length>0){
                dataList.forEach(item=>{
                    let newItem ={
                        value:item[keyValueArr[0]]+"",
                        label:item[keyValueArr[1]]
                    }
                    newData.push(newItem)
                })
            }
          return newData
        },
       // 加载数据 e.g::::控件选择 下拉框（字典），sourcekey:TBL_BD_TEST
       loadData(){
            if(this.selectDataList.length >1){
                return
            }
           this.loading = true
            let sourceKey = this.paramsItem.sourceKey
           
            let searchParams = this.paramsItem.searchParams
            let _url=this.config.moduleSub+"ComboSource"
            let params = {
                SourceType:"Table", //Dict Table Tree
                SourceID:this.dataSourceID,
                Params:{
                    sourceKey,
                    filter:{}
                 }
                };
                if(!!searchParams){
                   // debugger
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                    let searchParamsArr = searchParams.split(',')
                    searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    }
                }
            let _self = this
            request['post'](_url, params).then(res=>{
                if(res.Success){
                    let resData = res.Datas
                    this.loading = false
                    this.selectDataList = this.formatData(resData)
                    _self.$nextTick(()=>{
                        this.initDefaultList(this.selectDataList)
                     })
                }
            })
       },
    
       // 选择改变回调事件
       changeEvent(val){
           let _self = this
           this.$nextTick(()=>{
                let params ={
                    value:val,
                    text:_self.$refs["elSelectRef_selectTableBinary"].selectedLabel
                } 
               // debugger
                 _self.$emit("changeEvent",params)
           })
       }
    }
}
</script>


<style lang="scss">

</style>