import Vue from 'vue'
import VueRouter from 'vue-router'
import designlist from '../views/home.vue'
import designview from '../views/designview.vue'
//解决 Avoided redundant navigation to current location BUG
// 若push并没有产生效果，可以换位repalce
const originalPush = VueRouter.prototype.replace
VueRouter.prototype.replace = function replace(location) {
  return originalPush.call(this, location).catch(err => err)
}
Vue.use(VueRouter)
const routes = [
  {
    path: '/',
    name: 'home', 
    redirect:'/designlist',
  },
  {
    path: '/designlist',
    name: 'designlist', // 默认 designlist
    meta: {
      title: "设计表单列表",
    },
    component: designlist
  },
  {
    path: '/designview', 
    name: 'designview',
    meta: {
      title: "表单设计",
    },
    component: designview
    //qiankun 动态路由会出问题
    // component: resolve => require(['../views/designview'], resolve),
  },

]

const router = new VueRouter({
  mode: 'history',
  base: window.__POWERED_BY_QIANKUN__?"/customform":process.env.BASE_URL,
  routes
})
export default router

