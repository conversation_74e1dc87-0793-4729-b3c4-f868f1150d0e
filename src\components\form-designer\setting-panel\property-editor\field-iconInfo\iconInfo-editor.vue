<template>
    <div>
      <el-form-item  label="显示边框">
          <el-switch v-model="optionModel.showBorderStyle"></el-switch>
      </el-form-item>
      <el-form-item  label="显示分割栏">
          <el-switch v-model="optionModel.showSplitStyle"></el-switch>
      </el-form-item>
      
      <el-form-item label-width="0">
            <selectDataSourceApi contrlType="iconInfo" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></selectDataSourceApi>
        </el-form-item>
        <el-form-item label-width="0">
      <el-divider class="custom-divider">同环比信息项</el-divider>
    </el-form-item>
    <el-form-item  label-width="0">
        <!-- 注意：引用其它控件 -->
         <iconInfoItemList :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></iconInfoItemList>
      </el-form-item>
    </div>
  </template>
  
  <script>
    import i18n from "@/utils/i18n"
    import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
    import iconInfoItemList from './iconInfoItemList.vue'
    export default {
      name: "iconInfo-editor",
      mixins: [i18n],
      components:{selectDataSourceApi,iconInfoItemList},
      props: {
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
      },
    }
  </script>
  
  <style scoped>

  </style>
  