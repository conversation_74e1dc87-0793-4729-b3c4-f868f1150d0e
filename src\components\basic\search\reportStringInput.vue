<template>
    <el-input @change="changeEvent" v-bind="$attrs" v-on="$listeners"></el-input>
</template>
<script>
import baseMixin from './minxin'
export default {
    name: "reportStringInput",
    mixins:[baseMixin],
    props: {
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: String,// 当前查询字段名称
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
           
        }
    },
    mounted(){
        this.init()
       
    },
    methods: {
      

    }
}

</script>