!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("ClWebPluginsUMD",[],t):"object"==typeof exports?exports.ClWebPluginsUMD=t():e.ClWebPluginsUMD=t()}(self,(()=>(()=>{"use strict";var e={75:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(n)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(a[s]=!0)}for(var p=0;p<e.length;p++){var m=[].concat(e[p]);n&&a[m[0]]||(void 0!==i&&(void 0===m[5]||(m[1]="@layer".concat(m[5].length>0?" ".concat(m[5]):""," {").concat(m[1],"}")),m[5]=i),r&&(m[2]?(m[1]="@media ".concat(m[2]," {").concat(m[1],"}"),m[2]=r):m[2]=r),o&&(m[4]?(m[1]="@supports (".concat(m[4],") {").concat(m[1],"}"),m[4]=o):m[4]="".concat(o)),t.push(m))}},t}},281:(e,t,r)=>{e.exports=function(e){var t=r.nc;t&&e.setAttribute("nonce",t)}},613:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},674:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},846:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(r){!function(e,t,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(n,e,t.options)}(t,e,r)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},857:e=>{var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var i={},a=[],l=0;l<e.length;l++){var s=e[l],p=n.base?s[0]+n.base:s[0],m=i[p]||0,f="".concat(p," ").concat(m);i[p]=m+1;var c=r(f),u={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==c)t[c].references++,t[c].updater(u);else{var d=o(u,n);n.byIndex=l,t.splice(l,0,{identifier:f,updater:d,references:1})}a.push(f)}return a}function o(e,t){var r=t.domAPI(t);return r.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var i=n(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var l=r(i[a]);t[l].references--}for(var s=n(e,o),p=0;p<i.length;p++){var m=r(i[p]);0===t[m].references&&(t[m].updater(),t.splice(m,1))}i=s}}},898:e=>{e.exports=function(e){return e[1]}},927:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(898),o=r.n(n),i=r(75),a=r.n(i)()(o());a.push([e.id,".cursor-pointer{cursor:pointer}.middle{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.middle-right{position:absolute;top:50%;right:-10px;transform:translate(-50%, -50%)}.middle-left{position:absolute;top:50%;left:-10px;transform:translate(-50%, -50%)}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.sticky{position:sticky}.top-0{top:0px}.right-0{right:0px}.bottom-0{bottom:0px}.left-0{left:0px}.w-full{width:100%}.w-screen{width:100vw}.h-full{height:100%}.h-screen{height:100vh}.flex{display:flex}.inline-flex{display:inline-flex}.flex-row{flex-direction:row}.flex-row-reverse{flex-direction:row-reverse}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.justify-start{justify-content:flex-start}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.justify-around{justify-content:space-around}.justify-evenly{justify-content:space-evenly}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.items-baseline{align-items:baseline}.items-stretch{align-items:stretch}.flex-wrap{flex-wrap:wrap}.flex-wrap-reverse{flex-wrap:wrap-reverse}.flex-nowrap{flex-wrap:nowrap}.flex-1{flex:1 1 0%}.flex-auto{flex:1 1 auto}.flex-initial{flex:0 1 auto}.flex-none{flex:none}.shrink{flex-shrink:1}shrink-0{flex-shrink:0}.m-0{margin:0px}.m-px{margin:1px}.mb-10px{margin-bottom:10px}.m-1{margin:.25rem}.m-2{margin:.5rem}.m-3{margin:.75rem}.m-4{margin:1rem}.m-5{margin:1.25rem}.p-0{padding:0px}.p-px{padding:1px}.p-10px{padding:10px}.p-20px{padding:20px}.p-1{padding:.25rem}.p-2{padding:.5rem}.p-3{padding:.75rem}.p-4{padding:1rem}.p-5{padding:1.25rem}.ml-0{margin-left:0px}.ml-px{margin-left:1px}.ml-1{margin-left:.25rem}.ml-2{margin-left:.5rem}.ml-3{margin-left:.75rem}.ml-4{margin-left:1rem}.ml-5{margin-left:1.25rem}.mr-0{margin-right:0px}.mr-px{margin-right:1px}.mr-1{margin-right:.25rem}.mr-2{margin-right:.5rem}.mr-3{margin-right:.75rem}.mr-4{margin-right:1rem}.mr-5{margin-right:1.25rem}.mt-0{margin-top:0px}.mt-px{margin-top:1px}.mt-1{margin-top:.25rem}.mt-2{margin-top:.5rem}.mt-3{margin-top:.75rem}.mt-4{margin-top:1rem}.mt-5{margin-top:1.25rem}.mt-6{margin-top:1.5rem}.mt-7{margin-top:1.75rem}.mt-8{margin-top:2rem}.mt-9{margin-top:2.25rem}.mt-10{margin-top:2.5rem}.mt-11{margin-top:2.75rem}.mt-12{margin-top:3rem}.mt-14{margin-top:3.5rem}.mt-16{margin-top:4rem}.mt-20{margin-top:5rem}.mt-auto{margin-top:auto}.ml-2{margin-left:.5rem}.ml-3{margin-left:.75rem}.ml-4{margin-left:1rem}.ml-5{margin-left:1.25rem}.ml-6{margin-left:1.5rem}.ml-7{margin-left:1.75rem}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-scroll{overflow:scroll}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-hidden{overflow-y:hidden}.overflow-x-visible{overflow-x:visible}.overflow-y-visible{overflow-y:visible}.overflow-x-scroll{overflow-x:scroll}.overflow-y-scroll{overflow-y:scroll}.border-red{border:1px solid red}.border-blue{border:1px solid blue}.border-orange{border:1px solid orange}.bg-white{background-color:#fff}",""]);const l=a},974:e=>{var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nc=void 0;var n={};r.r(n),r.d(n,{Button:()=>w,Input:()=>S,default:()=>E,install:()=>T});var o=r(857),i=r.n(o),a=r(846),l=r.n(a),s=r(974),p=r.n(s),m=r(281),f=r.n(m),c=r(613),u=r.n(c),d=r(674),v=r.n(d),g=r(927),x={};x.styleTagTransform=v(),x.setAttributes=f(),x.insert=p().bind(null,"head"),x.domAPI=l(),x.insertStyleElement=u(),i()(g.A,x),g.A&&g.A.locals&&g.A.locals;var h=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"flex border-orange"},[r("button",[e._v(e._s(e.label))])])};function y(e,t,r,n,o,i,a,l){var s,p="function"==typeof e?e.options:e;if(t&&(p.render=t,p.staticRenderFns=r,p._compiled=!0),n&&(p.functional=!0),i&&(p._scopeId="data-v-"+i),a?(s=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},p._ssrRegister=s):o&&(s=l?function(){o.call(this,(p.functional?this.parent:this).$root.$options.shadowRoot)}:o),s)if(p.functional){p._injectStyles=s;var m=p.render;p.render=function(e,t){return s.call(t),m(e,t)}}else{var f=p.beforeCreate;p.beforeCreate=f?[].concat(f,s):[s]}return{exports:e,options:p}}h._withStripped=!0;var b=y({props:{label:{type:String,default:"Click me 123"}}},h,[],!1,null,null,null);b.options.__file="src/components/Button/index.vue";const w=b.exports;var _=function(){var e=this,t=e.$createElement;return(e._self._c||t)("input",{directives:[{name:"model",rawName:"v-model",value:e.value,expression:"value"}],attrs:{placeholder:e.placeholder},domProps:{value:e.value},on:{input:function(t){t.target.composing||(e.value=t.target.value)}}})};_._withStripped=!0;var j=y({props:{placeholder:{type:String,default:"Enter text"},value:{type:String,default:""}}},_,[],!1,null,null,null);j.options.__file="src/components/Input/index.vue";const S=j.exports;var C={Button:w,Input:S},T=function(e){Object.keys(C).forEach((function(t){e.component(t,C[t])}))};"undefined"!=typeof window&&window.Vue&&T(window.Vue);const E={install:T};return n})()));