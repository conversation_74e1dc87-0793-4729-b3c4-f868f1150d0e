<template>
    <div id="contentBodyRef" class="flex " ref="contentBodyRef" :style="[{ 'height': contentH + 'px','width': contentW + 'px'}]">
        <!-- {{contentH}}--/--- {{contentW}} -->
        <slot :height="contentH" :width="contentW">default content h & w</slot>
    </div>
</template>
<script>
import {useElementSize} from '@vueuse/core'
export default {
    // 通用页面自动获取内容 长 * 宽 px 
    name:"bodyContent",
    data(){
        return{
            contentH:0,
            contentW:0
        }
    },
    mounted(){
         const { width, height } = useElementSize(this.$refs["contentBodyRef"])
         this.contentW=width
         this.contentH=height
    },
    methods:{

    }
}
</script>
<style>
#contentBodyRef{
  height: calc(100vh - 0px);;
  /* padding: 10px; */
}
</style>