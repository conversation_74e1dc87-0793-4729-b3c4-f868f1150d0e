<template>
    <div class="flex">
        <el-select v-bind="$attrs" v-on="$listeners" placeholder="请选择">
            <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.key">
            </el-option>
        </el-select>
        <el-button @click.native="popupButtonClickEvent" slot="append" icon="el-icon-zoom-in"></el-button>
    </div>
</template>
<script>
// import emitter from '@/libs/mitt'
export default {
    name: "standardFrame",// 标准弹框
    props: {
        // 当前编辑字段名称
        field: {
            type: String,
            default: ""
        },
        // // 数据来信:表单form,表格talbe
        // dataFrom: {
        //     type: String,
        //     default: "form"
        // },
        // // 当前字段表单:其它配置信息
        // formData: {
        //     type: Object,
        //     default() {
        //         return {}
        //     }
        // },

        // 当前字段选择行
        rowData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段选择行:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    data() {
        return {
            selectOptions: [],
            activeFlag: false
        }
    },
    computed: {
        sourceKey() {
            let _sourcekey = ""
            try {
                _sourcekey = this.paramsItem.controlConfig.sourceKey
            } catch (error) {
                _sourcekey = ""
            }
            return _sourcekey
        },
        matchField() {
            let _matchField = ""
            try {
                _matchField = this.paramsItem.controlConfig.matchField
            } catch (error) {
                _matchField = ""
            }
            return _matchField
        },
        onAfterChangeConfig() {
            let _onAfterChange = ""
            try {
                _onAfterChange = this.paramsItem.controlConfig.onAfterChange
            } catch (error) {
                _onAfterChange = ""
            }
            return _onAfterChange
        },
        standardBulletBoxChange_State() {
            return this.$store.state.standardBulletBoxChange
        }
    },
    watch: {
        standardBulletBoxChange_State: {
            handler(n, o) {
                // 回调函数
                let params = n.value
                if (params.fieldName == this.field) {
                    //console.log("=======emitter.on vxe-table standardFrame =========")
                     if(!!this.activeFlag){
                        this.changeEvent(params.postData)
                     }
                }
            },
            deep: true
        }
    },
    beforeMount() {
        //  移除监听
        // emitter.off("getStandardBulletBox")
    },
    mounted() {
        this.init()
        // 注册监听 标准弹框事件
        // emitter.on("getStandardBulletBox",(params)=>{
        //     if(params.fieldName==this.field){
        //         if(!!this.activeFlag){
        //             this.changeEvent(params.postData)
        //         }

        //     }
        // })
    },
    destroyed() {
        this.activeFlag = false
        // 组件销毁 移除监听
        // emitter.off("getStandardBulletBox")
    },

    methods: {
        init() {
            let matchField = this.matchField
            let defaultItem = {
                key: this.rowData[this.field],
                label: !!this.rowData[matchField] ? this.rowData[matchField] : this.rowData[this.field]
            }
            this.selectOptions = [defaultItem]
        },
        // 标准弹框 点击事件
        popupButtonClickEvent() {
            if (!!!this.sourceKey) {
                console.error("====标准弹框 点击事件=====sourceKey为空======")
                return
            }
            this.activeFlag = true
            this.$actions.setGlobalState({
                type: "standardBulletBox",
                sourceKey: this.sourceKey,
                fieldName: this.field,
                fullPath: this.$route.fullPath,
            });

        },
        // 弹框选择改变 触发事件
        changeEvent(params) {
           // debugger
            this.activeFlag = false
            if (!!params) {
                let postData = JSON.parse(params)
                this.selectOptions = postData.data
                // debugger
                this.$nextTick(() => {
                    this.rowData[this.field] = postData.data[0].key
                    let matchField = this.matchField
                    //debugger
                    if(!!matchField){
                        this.rowData[matchField] = postData.data[0].label
                    }
                    //////////////////////////////表格>>标准弹框(onAfterChange)/////////////////////////////////////////
                    let onAfterChangeStr =  this.onAfterChangeConfig
                    let otherOptions={
                       currentRow:this.rowData,
                       selectedData:postData,
                    }
                    let onAfterChange = new Function('paramsData', onAfterChangeStr)
                    //  选择弹框数据后，其它自定义操作 方法
                    if (!!onAfterChangeStr) {
                        try {
                            onAfterChange.call(this, otherOptions)
                        } catch (error) {
                            this.$message({
                                message: '表格>>标准弹框(onAfterChange)错误，请检查！！！',
                                type: 'error'
                            });
                        return
                        }
                     }
                    /////////////////////////////表格>>标准弹框(onAfterChange)//////////////////////////////////////////
                })
            }
        },
    }
}
</script>