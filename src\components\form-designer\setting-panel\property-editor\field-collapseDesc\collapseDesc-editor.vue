<template>
  <div>
    <el-form-item label-width="0">
      <el-divider class="custom-divider">
        分组描述配置
        <el-tooltip effect="light"
          content="返回数组必须包含字段：GroupID GroupName  GroupDesc ItemID ItemName ItemDesc  ItemValue ItemSeq">
          <i class="el-icon-info"></i></el-tooltip>
      </el-divider>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">设为被动加载
        <el-tooltip effect="light" content="默认 立即加载数据，设为被动加载后，数据需要通过其它方式触发加载，如 查询，点击等">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-switch v-model="optionModel.isPassiveDataLoad"></el-switch>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">绑定控件
        <el-tooltip effect="light" content="目前支持TAB,绑定控件，暂时手动添加绑定控件。主要用途为绑定控件对外开放的selectedIndex 值和绑定值相同时，才触发加载数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input v-model.trim="optionModel.bandingCtrlName"></el-input>
    </el-form-item>
    <el-form-item label="">
      <span slot="label">绑定值
        <el-tooltip effect="light" content="目前支持TAB,绑定值。主要用途为绑定控件对外开放的selectedIndex 值和绑定值相同时，才触发加载数据">
          <i class="el-icon-info"></i></el-tooltip>
      </span>
      <el-input v-model.trim="optionModel.bandingCtrlValue"></el-input>
    </el-form-item>
    <el-form-item label="风琴模式【默认否】">
      <el-switch v-model="optionModel.accordionLayout"></el-switch>
    </el-form-item>
    <el-form-item label="显示列数【默认1】">
      <el-input-number v-model="optionModel.column" :min="1"></el-input-number>
    </el-form-item>
    <el-form-item label="竖立排列【默认横】">
      <el-switch v-model="optionModel.verticalLayout"></el-switch>
    </el-form-item>
    <el-form-item label="单组【隐藏标题】">
      <el-switch v-model="optionModel.hiddenTitle"></el-switch>
    </el-form-item>
    <el-form-item label="显示边框【默认否】">
      <el-switch v-model="optionModel.showBorder"></el-switch>
    </el-form-item>
    <!-- <el-form-item label="显示冒号【默认否】">
        <el-switch v-model="optionModel.showColon"></el-switch>
      </el-form-item> -->
    <el-form-item label="标题宽【默认50】">
      <el-input-number v-model="optionModel.labelWidth"></el-input-number>
    </el-form-item>
    <el-form-item label-width="0">
      <commonParamsOnChange contrlType="collapseDesc" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></commonParamsOnChange>
    </el-form-item>
    <el-form-item label-width="0">
      <selectDataSourceApi contrlType="collapseDesc" :designer="designer" :selectedWidget="selectedWidget"
        :optionModel="optionModel"></selectDataSourceApi>
    </el-form-item>
    <!-- <el-form-item label-width="0">
      <el-divider class="custom-divider">分组描述配置</el-divider>
    </el-form-item> -->
    <!-- <el-form-item  label="按行顺序显示">
          <span slot="label">按行顺序显示
            <el-tooltip effect="light" content="默认按列顺序显示,取数组的第一个显示，需要逐个选择不同的字段,如果设置为按行顺序显示，则显示字段选择相同">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-switch v-model="optionModel.readByRow"></el-switch>
      </el-form-item>
      <el-form-item  label="显示传值">
          <span slot="label">显示传值
            <el-tooltip effect="light" content="默认不显示传值，可控制是否显示传值字段">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-switch v-model="optionModel.showValueField"></el-switch>
      </el-form-item> -->

  </div>
</template>

<script>
import commonParamsOnChange from '@/components/form-designer/setting-panel/commonParamsOnChange.vue'
import i18n from "@/utils/i18n"
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
// import colorBlockInfoItemList from './colorBlockInfoItemList.vue'
export default {
  name: "collapseDesc-editor",
  mixins: [i18n],
  components: { selectDataSourceApi, commonParamsOnChange },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
}
</script>

<style scoped></style>