<template>
    <div ref="rootRef" class="flex">
        <div ref="contentBoxRef" class="flex h-content " :class="isCilent ? 'flex h-contentNew' : 'flex h-content'"> </div>
        <!-- {{ contentOptions.height }} -->
        <VFormRender  sourceVFormRender="" :contentBoxHeight="contentOptions.height" :designer="designer" ref="preForm">
        </VFormRender>

        <!-- 注意:editContentBoxRef/editContentBoxDrawerRef名称固定 不可修改,编辑弹框 -->
            <editContentBox :formConfig="formConfig" :paramsFromList="paramsFromList" ref="editContentBoxRef"
            @submitSuccess="submitSuccess"></editContentBox>

            <editContentDrawerBox :formConfig="formConfig" :paramsFromList="paramsFromList" ref="editContentBoxDrawerRef"
            @submitSuccess="submitSuccess"></editContentDrawerBox>
            <!-- <vxe-modal title="基础表单渲染" v-model="showBasicFormBox" width="600" show-footer>
      
            </vxe-modal> -->
    </div>
</template>
<script>
import request from '@/libs/request'
import editContentBox from "./edit/edit-formDesign.vue";
import editContentDrawerBox from "./edit/edit-formDesignDrawer.vue";
import listMixin from "@/views/mixins/mixinList"; // 默认导入
import VFormRender from '@/components/form-render/index'
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
export default {
    name: "designviewShow",
    components: { VFormRender, editContentBox,editContentDrawerBox},
    mixins: [listMixin], // 导入列表通用方法，可以继承重写 nvxeGrid
    //inject: ['sourceVFormRenderState'],
    provide() { 
        // 主要解决深层次的组件嵌套，祖先组件向子孙组件之间传值。
        return {
            //  返回当前页面整体实例
            sourceVFormRenderState:"",
          } 
      },
    data() {
        return {
            isCilent:false,
            dataSourceList:[],// 数据源列表
            showBasicFormBox:false,
            pageInstance:this,//兼容处理 hooks 方法 参数
            designer: null,
            formConfig: null,
            useFormatParamsFn: useFormatParams(this),
            useHandleVFormEventFn: useHandleVFormEvent(this),
        }
    },
    computed:{
        showBasicFormBoxState(){
           let _flag =  false
           try {
               _flag = this.$store.state.showBasicFormBoxState
           } catch (error) {
                _flag =  false
           }
           return _flag
        }
    },
    watch:{
        showBasicFormBoxState:{
            handler(n,o){
                //debugger
               // to do list
               if(n.value){
                    this.showBasicFormBox = true
               }
            },
            immediate:false,
            deep:true
        }
    },
    mounted() {
        window.addEventListener('message', this.handleParentPostMessage);
        this.$nextTick(async () => {
            let newFormJson = await this.getRowTemplateData()
            
            this.$refs['preForm'].setFormJson(newFormJson)
            if (newFormJson) {
                this.designer = JSON.parse(newFormJson)
                 
                this.formConfig = this.designer.formConfig
               
            }
        })
    },
    beforeDestroy() {
        this.isCilent = false
        window.removeEventListener('message', this.handleParentPostMessage);
    },
    methods: {
        handleParentPostMessage(event) {
            // if (event.origin !== '*') return;
            // update-h-content 监控来自子应用 NoteRed_with_antv_x6_vue3
            if (event.data.type === 'update-h-content') {
                 this.isCilent = true
                //  if(event.data.payload){
                //     this.$store.commit("set_postMessageFromParentChange",event.data.payload)
                //  }
                console.log("handleParentPostMessage :",event.data)
            }
        },
        getCurrentDataSourceItem(CDATASOURCE_ID=''){
            //debugger
            this.currentDataSourceItem =null
            if(!!CDATASOURCE_ID && this.dataSourceList.length>0){
                //debugger
                 this.currentDataSourceItem = this.dataSourceList.find(item => item.CID == CDATASOURCE_ID)
            }
            return this.currentDataSourceItem
        },
        getRequest(name) {
            let urlStr = window.location.search
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            let r = urlStr.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            };
            return null;
        },
         // 从vFORM全局表单查询列表中，获取查询项
         getPopupItemByUrl(postUrl = "") {
            let popupItem = null
            // 注意：this.formConfig 此处为列表配置信息，非编辑弹框配置
            if(!!!this.formConfig){
                return ""
            }
            let popupList = this.formConfig.popupList
            if (popupList && popupList.length > 0) {
                let filterList = popupList.filter((item) => {
                    if (item.value == postUrl) {
                        return item
                    }
                })
                if (filterList && filterList.length > 0) {
                    popupItem = filterList[0]
                }
            }
            return popupItem
        },
        // 获取当前选中行模板数据
        async getRowTemplateData() {
            let formJsonData = ""
            let resData = null
            let _url = `api/MD/VisualFormDesigner/GetByID`
            let formName = this.getRequest("formName")
            let params = {
                id: formName
            }
            if (!!formName) {
                await request["get"](_url, null, params).then(res => {
                    if (res.Success && res.Datas) {
                        resData = res.Datas
                        formJsonData = res.Datas.CJSON_DATA
                    }
                   
                });
            }
            formJsonData = await this.formatAndCheckIsWebAPI(formJsonData,resData)
            return formJsonData
        },
        // 格式化和检验是否为webAPI 或 可直连API
       async formatAndCheckIsWebAPI(formJsonData,resData){
            let newFormJsonData = formJsonData
            let new_queryList = []
            let _self = this
            if(newFormJsonData){
                try {
                    let formJson = JSON.parse(newFormJsonData)
                    let queryList = formJson.formConfig.queryList
                    //debugger
                    if(queryList && queryList.length>0){
                        let webAPIList = queryList.filter((item)=>{
                            // 检查是否需要本地直接查询API
                           if(item.actionParams.hasOwnProperty("CDATASOURCE_TYPE_CODE") && item.actionParams.CDATASOURCE_TYPE_CODE == "WEBAPI"){
                            return item
                           }
                        })
                        if(webAPIList && webAPIList.length>0){
                            // 查询数据源列表
                            await _self.getDataSourceList()
                            new_queryList = queryList.map((item)=>{
                                //debugger
                                let dataSourceItem = _self.getCurrentDataSourceItem(item.actionParams.CDATASOURCE_ID)
                                if(dataSourceItem){
                                    item.actionParams.CHOST_CPORT = dataSourceItem.CHOST + ":" + dataSourceItem.CPORT
                                }
                                return item
                                
                            })
                        }
                       
                    }
                    if(new_queryList && new_queryList.length>0){
                        formJson.formConfig.queryList = new_queryList
                    }
                    newFormJsonData = JSON.stringify(formJson)
                } catch (error) {
                    
                }
            }
            return newFormJsonData
        },
        // 获取数据源数据
        async getDataSourceList() {
            this.dataSourceList = []
            let params = {
                condition: "",
                typeId: 0
            }
            let _url = "/api/MD/DataSource/GetList"
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                  this.dataSourceList = res.Datas
                }
            })
        },
        submitSuccess() {
            // 刷新主表
            // this.refreshMain()
            let popupParams = this.$store.state.popupParams;
            let selfActionName = this.useFormatParamsFn.getActionParamsValue("actionName", popupParams)
            let popupItem = this.getPopupItemByUrl(selfActionName)
            console.log("======after====submitSuccess======popupItem=======")
           // console.log(popupItem)
            if(popupItem.hasOwnProperty("afterSuccessOrErrorEvents")){
                let eventList = popupItem.afterSuccessOrErrorEvents
                if(eventList && eventList.length>0){
                    for(let i=0;i<eventList.length;i++){
                        let eventItem = eventList[i]
                        this.useHandleVFormEventFn.handleCommonClickEvent(eventItem)
                    }
                }
               
            }
            console.log("======after====submitSuccess=======popupItem=======")
        }

    }
}
</script>