<template>
    <div class="commonSettingEventDialog">
        <el-form-item label-width="0">
            <!-- 交互事件 -->
            <el-divider class="custom-divider">{{ title }}</el-divider>
        </el-form-item>
        <el-form-item label-width="0">
            <div class="flex justify-between">
                <div style="font-weight: bold;font-size: 13px;">事件</div>
                <div></div>
                <div> <el-button style="height:32px;" @click="actionBtn('iisAdd')" type="text" icon="vxe-icon-add"
                        size="mini">&nbsp;添加</el-button></div>
            </div>
        </el-form-item>
        <el-form-item label-width="0">
            <draggable tag="ul" :list="optionModel.eventList"
                v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
                <li v-for="(option, idx) in optionModel.eventList" :key="idx">
                    <el-checkbox v-model="option.check">
                        <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.label" size="mini"
                            style="width: 150px"></el-input>
                        <i class="iconfont icon-drag drag-option"></i>
                        <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger"
                            @click="deleteOption(option, idx)" icon="el-icon-minus" class="col-delete-button"></el-button>
                    </el-checkbox>
                </li>
            </draggable>
        </el-form-item>
        <!-- 交互事件编辑 -->
        <el-dialog :title="`${title}编辑`" :visible.sync="showDialog" v-if="showDialog" :show-close="true"
            class="small-padding-dialog" v-dialog-drag append-to-body :close-on-click-modal="false"
            :close-on-press-escape="false" :destroy-on-close="true">
            <el-form :model="editEventItemForm" :rules="editEventItemFormRules" ref="editEventItemForm" label-width="100px">
                <el-form-item label="标签" prop="label">
                    <el-input v-model="editEventItemForm.label"></el-input>
                </el-form-item>

                <el-form-item label="唯一编码" prop="value">
                    <el-input disabled v-model="editEventItemForm.value"></el-input>
                </el-form-item>

                <el-form-item label="图标ICON">
                    <el-input style="width:330px" type="text" v-model="editEventItemForm.iconUrl"></el-input>
                    <div><a href="https://element.eleme.cn/#/zh-CN/component/icon" target="_blank" sync="true" ><span style="color:cornflowerblue;">打开可用图标链接</span></a> </div>
                </el-form-item>
                <el-form-item label="显示方式">
                    <el-select v-model="editEventItemForm.displayType" placeholder="请选择">
                    <el-option label="请选择" value=""></el-option>
                    <el-option label="文字和ICON" value="1"></el-option>
                    <el-option label="仅文字" value="2"></el-option>
                    <el-option label="仅ICON" value="3"></el-option>
                    <el-option label="仅文字交互标签" value="4"></el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="字体颜色" >
                    <el-color-picker
                        v-model="editEventItemForm.itemTextColor"
                        show-alpha
                        :predefine="predefineColors">
                </el-color-picker>
                </el-form-item>
                <el-form-item label="背景颜色">
                    <el-color-picker
                        v-model="editEventItemForm.itemBgColor"
                        show-alpha
                        :predefine="predefineColors">
                </el-color-picker>
                </el-form-item>
                <el-form-item label="选中底部边框">
                    <el-color-picker
                        v-model="editEventItemForm.itemActiveBottomColor"
                        show-alpha
                        :predefine="predefineColors">
                </el-color-picker>
                </el-form-item>
                <el-form-item label="隐藏底部边框">
                    <el-switch v-model="editEventItemForm.hideActiveBottomColor" 
               ></el-switch>

                </el-form-item>
                <eventTypeByCtrlDialog :contrlType="contrlType" :showOnBeforeSubmit="showOnBeforeSubmit" :editItemForm="editEventItemForm" :designer="designer"
                    :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>

                <!-- 动态参数 后期扩展使用，不可删除 -->
                <div v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType == 'postParamsToCtrl'"
                    style="font-weight: bold;">参数【与API接口参数相同时，会覆盖API查询列表参数】</div>
                <div
                    v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType == 'postParamsToCtrl'">
                    <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
                        <el-form-item label="KEY" prop="key">
                            <el-input placeholder="key" v-model="item.key"></el-input>
                        </el-form-item>

                        <el-form-item label="VALUE" prop="value">
                            <el-input placeholder="value" v-model="item.value"></el-input>
                        </el-form-item>
                        <div style="margin-bottom:20px;margin-left:5px">
                            <i style="margin-left:5px;" @click="deleteParam(item, index)" class="el-icon-delete"></i>

                        </div>
                    </div>
                </div>
                <div
                    v-show="!!editEventItemForm.otherParams.actionType && editEventItemForm.otherParams.actionType == 'postParamsToCtrl'">
                    <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
                </div>
                <div style="font-weight: bold;">提交后 成功或失败后事件处理
                    <el-tooltip effect="light" content="一般指执行查询API后，需要执行的后续动作，如：刷新表格列表数据，或其它">
                         <i class="el-icon-info"></i></el-tooltip>
                </div>
                <el-form-item label-width="0">
                   <!-- 注意：成功后或失败后事件处理-->
                <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
                    :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="large" @click="showDialog = false">
                    取消</el-button>
                <el-button size="large" type="primary" @click="saveCommonEventHandler">
                    确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n"
import afterSuccessOrErrorSetting from '@/components/form-designer/setting-panel/afterSuccessOrErrorSetting'
import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
const default_editEventItemForm = {
    label: "",// 标签
    value:"",
    iconUrl:"",
    displayType:"1",//文字和ICON
    itemTextColor:"",
    itemBgColor:"",
    itemActiveBottomColor:"",
    hideActiveBottomColor:false,
    otherParams: {
        actionType: "-1",// 动作
    },
    actionParams: {
        eventType: "",// 外部传入的参数contrlType>> 决定事件 类型列表
        contrlType: "",// 外部传入的参数 【vxetable,customTree,echarts,splitpanes,defaultmenubutton】
        actionName: "",
    },
}
export default {
    name: "commonSettingEventDialog",
    mixins: [i18n],
    components: {
        eventTypeByCtrlDialog,
        afterSuccessOrErrorSetting,
        Draggable
    },
    props: {
        title: {
            type: String,
            default: "交互事件"
        },
        showOnBeforeSubmit:{
            type:Boolean,
            default:false,
        },
        designer: Object,
        selectedWidget: Object,
        optionModel: Object,
        contrlType: String,// 控件类型名称
    },
    data() {
        return {
            currentEditOption: null,
            showDialog: false,
            actionParamsList: [],//{ key: "", valueType: "1", value: "" }
            editEventItemForm: Object.assign({}, default_editEventItemForm),
            editEventItemFormRules: {
                label: [
                    { required: true, message: '请输入标签名称', trigger: 'blur' },
                ],
                value: [
                    { required: true, message: '请输入唯一编码', trigger: 'blur' },
                ],
            },
            predefineColors: [
            '#E3B76D',
            '#6794A7',
            '#014D64',
            '#01A2D9',
            '#C6D3DF',
            '#7AD2F6',
            '#7C260B',
            '#00887D',
            '#EE8F71',
            '#ADADAD',
            '#76C0C1',
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#1E1F23',
            '#343541'	,
            '#ffffff'					
        ]
        }
    },
    computed: {
        formTemplateList() {
            return this.$store.state.formTemplateList
        }
    },
    // "queryList":[{"label":"组织查询","value":"query1","check":false,"canRemove":true,"otherParams":{"actionType":"api"},"actionParams":{"actionName":"api/DEMO/VXETableDemo/Data/Page","query":{"Pid":"5946218519199749"}}}]
    methods: {
        // 添加参数
        addParamsEvent() {
            let newValue = this.actionParamsList.length + 1
            this.actionParamsList.push(
                { key: "", execNow: false, value: "" }
            )
        },
        // 移除参数
        deleteParam(item, index) {
            this.actionParamsList.splice(index, 1)
        },

        showEditDialogEvent(option) {
            this.currentEditOption = option // 当前菜单属性
            this.showDialog = true

            this.editEventItemForm.label = option.label
            this.editEventItemForm.value = option.value
            this.editEventItemForm.iconUrl = option.iconUrl
            this.editEventItemForm.displayType = option.displayType
            this.editEventItemForm.itemTextColor = option.itemTextColor
            this.editEventItemForm.itemBgColor = option.itemBgColor
            this.editEventItemForm.itemActiveBottomColor = option.itemActiveBottomColor
            this.editEventItemForm.hideActiveBottomColor = option.hideActiveBottomColor
        
            // this.editEventItemForm.onBeforeSubmit = option.onBeforeSubmit
            this.actionParamsList = [] // 默认,重置
            this.editEventItemForm.otherParams = cloneDeep(option.otherParams)
            this.editEventItemForm.actionParams = cloneDeep(option.actionParams)
            if (this.editEventItemForm.actionParams.hasOwnProperty("query")) {
                this.actionParamsList = this.getQueryList()
            }
        },
        // 获取组合后的动态参数
        getQueryList() {
            // debugger
            let queryParamsList = []
            //debugger {key:"",execNow:false,value:""}
            if (this.editEventItemForm.actionParams && this.editEventItemForm.actionParams.query && Object.keys(this.editEventItemForm.actionParams.query).length > 0) {
                //debugger
                for (const [key, value] of Object.entries(this.editEventItemForm.actionParams.query)) {
                    if (!!key) {
                        // let newItem ={key,value} 多参数解析
                        queryParamsList.push(value)
                    }
                }
            }
            return queryParamsList
        },
        deleteOption(option, index) {
            // 是否可以移除
            if (!!option.canRemove) {
                this.optionModel.eventList.splice(index, 1)
            }
        },
        // 添加新的事件交互
        actionBtn(type = 'iisAdd') {
            switch (type) {
                case "iisAdd":
                    this.iisAdd()
                    break;

                default:
                    break;
            }
        },
        iisAdd() {
            if (!this.optionModel.hasOwnProperty("eventList")) {
                this.$set(this.optionModel, "eventList", [])
            }
            let newValue = this.optionModel.eventList.length + 1
            this.optionModel.eventList.push(
              
                { label: '新交互事件' + newValue, value: "event" + newValue,hideActiveBottomColor:false,itemActiveBottomColor:"",itemBgColor:"",itemTextColor:"", iconUrl: "",displayType:"1", check: false, canRemove: true, otherParams: {}, actionParams: {} }
            )
        },
      
        // 设置组合后的动态参数
        setQueryList() {
            let queryParams = {}
            if (this.actionParamsList && this.actionParamsList.length > 0) {
                this.actionParamsList.forEach(item => {
                    if (!!item.key) {
                        queryParams[item.key] = item // 多参数
                    }
                })
            }
            //debugger
            return queryParams
        },
        saveCommonEventHandler() {
            //debugger

            /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            // this.$set(this.formConfig, this.curEventName, this.commonEventHandlerCode)
            let _self = this
            this.$refs["editEventItemForm"].validate((valid) => {
                if (valid) {
                    _self.currentEditOption.label = _self.editEventItemForm.label
                    _self.currentEditOption.value = _self.editEventItemForm.value
                    _self.currentEditOption.iconUrl = _self.editEventItemForm.iconUrl
                    _self.currentEditOption.displayType = _self.editEventItemForm.displayType
                    _self.currentEditOption.itemTextColor = _self.editEventItemForm.itemTextColor
                    _self.currentEditOption.itemBgColor = _self.editEventItemForm.itemBgColor
                    _self.currentEditOption.itemActiveBottomColor = _self.editEventItemForm.itemActiveBottomColor
                    _self.currentEditOption.hideActiveBottomColor = _self.editEventItemForm.hideActiveBottomColor
                   // _self.currentEditOption.onBeforeSubmit = _self.editEventItemForm.onBeforeSubmit
                    _self.currentEditOption.otherParams = cloneDeep(_self.editEventItemForm.otherParams)
                    _self.currentEditOption.actionParams = cloneDeep(_self.editEventItemForm.actionParams)
                    // 动态添加参数列表
                    _self.currentEditOption.actionParams["query"] = _self.setQueryList()

                    _self.showDialog = false
                } else {
                    console.log('saveCommonEventHandler error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>
<style lang="scss">
.commonSettingEventDialog ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
}

li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
}

.drag-option {
    cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
}

.dialog-footer .el-button {
    width: 100px;

}
</style>