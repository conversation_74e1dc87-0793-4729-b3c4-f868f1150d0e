<template>
    <div class="commonParamsOnChange-option-items-pane">
      <el-form-item label-width="0">
        <el-divider class="custom-divider">触发控件列表
          <el-tooltip effect="light" content="当点击表格行、条件查询、树类别时【根据控件不同，触发条件不同】，触发控件重新查询,目前支持表格，图表，描述列表，表单按钮，其它有待开发">
          <i class="el-icon-info"></i></el-tooltip>
        </el-divider>
      </el-form-item>
       <!-- <el-form-item label="触发控件">
            <el-input disabled  v-model="optionModel.triggerCtrlNames" size="mini"
                style="width: 110px"></el-input>
                <el-button @click="showFlag=true" type="text"> + 选择</el-button>  
        </el-form-item> -->
        <el-form-item label-width="0">
            <div class="flex justify-between">
                <div style="font-weight: bold;font-size: 13px;">控件</div>
                <div></div>
                <div> <el-button style="height:32px;" @click="addCtrlOption" type="text" icon="vxe-icon-add"
                        size="mini">&nbsp;添加</el-button></div>
            </div>
        </el-form-item>
        <draggable tag="ul" :list="optionModel.triggerCtrlNames"
          v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
          <li v-for="(option, idx) in optionModel.triggerCtrlNames" :key="idx">
            <el-checkbox v-model="option.check">
              <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.label" size="mini"
                style="width: 150px"></el-input>
              <i class="iconfont icon-drag drag-option"></i>
              <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteCtrlOption(option, idx)"
                icon="el-icon-minus" class="col-delete-button"></el-button>
            </el-checkbox>
          </li>
        </draggable>
          <!-- <div>
            <el-button type="text" @click="addCtrlOption">+添加控件</el-button>
          </div> -->
        <el-form-item label-width="0">
        <el-divider class="custom-divider"></el-divider>
      </el-form-item>
        <el-dialog title="触发控件 选择" :visible.sync="showFlag" v-if="showFlag" v-dialog-drag
        append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
        :close-on-press-escape="false" :destroy-on-close="true">
      <div>
         <!-- <el-form-item label="标签" prop="label">
           <el-input v-model="currentEditOption.label"></el-input>
        </el-form-item> -->
         <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
          highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
      </div>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" size="large" @click="submitNodeEvent()">
          确定</el-button><el-button size="large"  @click="showFlag = false">
          取消</el-button>
      </div>
    </el-dialog>
    </div>
  </template>
  
  <script>
  // import request from '@/libs/request' //
  import Draggable from 'vuedraggable'
  import cloneDeep from "clone-deep"
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
  import { useFormatParams } from "@/hooks/useFormatParams"
  export default {
    name: "commonParamsOnChange", //参数改变触发控件
    mixins: [i18n, propertyMixin],
    props: {
      title:String,
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    components: {
      Draggable,
    },
    data() {
      return {
        actionName: "",
        showFlag:false,
        nodeCtrlValue:"",
        loadingTableSubmit: false,
        currentEditOption:null,
        useFormatParamsFn: useFormatParams(this),
      }
    },
    methods: {
        showEditDialogEvent(option){
          this.currentEditOption = option // 当前菜单属性
          this.showFlag = true
        },
        // 移除控件
        deleteCtrlOption(option, index) {
            // 是否可以移除
            if (!!option.canRemove) {
              this.optionModel.triggerCtrlNames.splice(index, 1)
            }
        },
      // 添加控件
      addCtrlOption() {
          // 先校验是否已经存在此元素，否则初始化
          if(!this.optionModel.hasOwnProperty('triggerCtrlNames')){
            this.$set(this.optionModel, "triggerCtrlNames", [])
          }
          this.optionModel.triggerCtrlNames.push(
            { label: '', value: "", check: false, canRemove: true }
          )
        },
        submitNodeEvent(){
            //  this.optionModel.triggerCtrlName = this.nodeCtrlValue
            this.currentEditOption.value = this.nodeCtrlValue
            this.currentEditOption.label = this.nodeCtrlValue
            this.showFlag = false
        },
        onNodeTreeClick(params){
            this.nodeCtrlValue =""
            let hasChildren = params.hasOwnProperty("children")
           // let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
           // let parentNodeLabel = currentNode.parent.data.label
            let ctrlValue = params.label
            if(!hasChildren){
                this.nodeCtrlValue = ctrlValue
            }else{
                   //debugger
                   //this.$message.warning('此节点不可选！')
                    if(ctrlValue.includes('splitpanes')){
                    this.nodeCtrlValue = ctrlValue
                    }else if(ctrlValue.includes('card')){
                        this.nodeCtrlValue = ctrlValue
                    }
                    else if(ctrlValue.includes('tab') && ctrlValue.length>5){
                        this.nodeCtrlValue = ctrlValue
                    }
                    else{
                        this.$message.warning('此节点不可选！')
                    }
            }
        },
        // 获取数据源控件列表
        getNodeTreeData(){
            
            let dataList = cloneDeep(this.designer.nodeTreeData)
            return dataList
            },
    }
  }
  </script>
  <style lang="scss" scoped>
  .commonParamsOnChange-option-items-pane ul {
    padding-inline-start: 6px;
    padding-left: 6px;
    /* 重置IE11默认样式 */
  }
  
  li.ghost {
    background: #fff;
    border: 2px dotted $--color-primary;
  }
  
  .drag-option {
    cursor: move;
  }
  
  .small-padding-dialog ::v-deep .el-dialog__body {
    padding: 10px 15px;
  }
  
  .dialog-footer .el-button {
    width: 100px;
  
  }
  
  .node-tree ::v-deep {
    .el-tree>.el-tree-node:after {
      border-top: none;
    }
  
    .el-tree-node {
      position: relative;
      padding-left: 12px;
    }
  
    .el-tree-node__content {
      padding-left: 0 !important;
    }
  
    .el-tree-node__expand-icon.is-leaf {
      display: none;
    }
  
    .el-tree-node__children {
      padding-left: 12px;
      overflow: visible !important;
      /* 加入此行让el-tree宽度自动撑开，超出宽度el-draw自动出现水平滚动条！ */
    }
  
    .el-tree-node :last-child:before {
      height: 38px;
    }
  
    .el-tree>.el-tree-node:before {
      border-left: none;
    }
  
    .el-tree>.el-tree-node:after {
      border-top: none;
    }
  
    .el-tree-node:before {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }
  
    .el-tree-node:after {
      content: "";
      left: -4px;
      position: absolute;
      right: auto;
      border-width: 1px;
    }
  
    .el-tree-node:before {
      border-left: 1px dashed #4386c6;
      bottom: 0px;
      height: 100%;
      top: -10px;
      width: 1px;
    }
  
    .el-tree-node:after {
      border-top: 1px dashed #4386c6;
      height: 20px;
      top: 12px;
      width: 16px;
    }
  
    .el-tree-node.is-current>.el-tree-node__content {
      background: #c2d6ea !important;
    }
  
    .el-tree-node__expand-icon {
      margin-left: -3px;
      padding: 6px 6px 6px 0px;
      font-size: 16px;
    }
  
  }</style>
    
  