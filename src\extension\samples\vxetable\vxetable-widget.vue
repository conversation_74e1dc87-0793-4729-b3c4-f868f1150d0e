<template>
  <static-content-wrapper
    class="max-content-height"
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >

    <!-- :tree-config="field.options.treeConfig" vxetableHeight:{{ vxetableHeight }} ${vxetableHeight}px `${field.options.tableHeight}`-->
    <div :style="{'padding': `0px ${!!field.options.offPaddingRight?'0px':'10px'}  0px ${!!field.options.offPaddingLeft?'0px':'10px'}`,'background-color': 'white'}">
      <nvxeGrid
      :layouts="customLayouts"
      :sourceVFormRender="sourceVFormRender"
      :ref="field.options.name"
      :refName="field.options.name"
      :dataSetSelectedModel="field.options.dataSetSelectedModel"
      :designer="designer"
      :designState="designState"
      :columnsTreeNode="field.options.columnsTreeNode"
      :tree-config-custom="field.options.treeConfig"
      :showDragColumn="field.options.activeDragRowFn"
      :pagerCount="field.options.pagerCount"
      :needFormatColunms="true"
      :columns="field.options.tableColumns"
      :data="field.options.tableData"
      :height="calTableHeight"
      :sync-resize="calTableHeight"
      :dataSearchOptions="field.options"
      :publicAttribute="publicAttribute"
      :showPagination="field.options.showPagination"
      :tabelConfigs="field.options.tabelConfigs"
      :requstConfig="field.options.requstConfig"
      :showSeqColumn="field.options.showSeqColumn"
      :showContextMenu_Export="!!field.options.showContextMenu_Export"
      :showContextMenu_Export_fileNameCtrl="field.options.showContextMenu_Export_fileNameCtrl"
      :showCheckBoxColumn="field.options.showCheckBoxColumn"
      :showOperationColumn="field.options.showOperationColumn"
      :operationColumn="field.options.operationColumn"
      :operationButtons="field.options.operationButtons"
      :footerButtons="field.options.footerButtons"
      :showFooterButtons="field.options.showFooterButtons"
      :layoutLevel="field.options.layoutLevel"
      @setPublicAttr="setPublicAttr"
    >
      <!-- 启用表格标题 -->
    <!-- <template v-if="!!field.options.showTopTitle" slot="top">
    
        <div style="margin-left:10px; height: 30px;" class="flex items-center">
            <div v-text="field.options.tableTopTitle"></div>
        </div>
    </template> -->
    </nvxeGrid>
    </div>
  
  </static-content-wrapper>
</template>
  
  <script>
import StaticContentWrapper from "@/components/form-designer/form-widget/field-widget/static-content-wrapper";
import emitter from "@/utils/emitter";
import i18n from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

export default {
  name: "vxetable-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['sourceVFormRenderState'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    contentBoxHeight: {
        type: [Number, String],
        default: 0
      },
        // 控件来源父集 add by andy
     sourceVFormRender:{
        type: String,
        default: ""
      },
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
  },
  data(){
    return {
       // 设立以查询控件列表参数作为启动时的查询条件
       calTableHeight:0,
       publicAttribute:{
          row:null, // 当前选中行
          pageIndex:1,// 当前页码
          pageSize:10 // 每页条数
      },// 对外暴露属性值
    }
  },
  computed: {
    // 自定义分页布局
    customLayouts(){
      let _layout = []
      try {
         _layout = this.field.options.paginationLayout
         if(_layout.length==0){
            _layout =['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']
         }
      } catch (error) {
        _layout =['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']
      }
      return _layout
    },
    contentBoxHeightOffsetHeight(){
     // debugger
      let height = Number((this.contentBoxHeight).toFixed(2))
      console.log("======contentBoxHeight=====change....")
      if(this.field.options.hasOwnProperty("OffsetHeight")){
        height = height + (this.field.options.OffsetHeight) // 高偏移量（主卧主容器时使用，也就是操作按钮区域的高度）
      }
      return height
    }

  },
  watch:{
    contentBoxHeight(n,o){
      console.log("=======contentBoxHeight changing now...=======")
      this.calTableHeight = this.getSettingHeight()
    }
  },
  components: {
    StaticContentWrapper,
  },
  created() {
    this.registerToRefList();
    this.initEventHandler();
  
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  mounted(){
    this.calTableHeight = this.getSettingHeight()
    //debugger
  },
  methods: {
    setPublicAttr(params){
     // debugger
      this.publicAttribute = params.publicAttribute
    },
    getSettingHeight() {
     // debugger
      if (!!this.designState) {
        if(this.contentBoxHeightOffsetHeight==0){
           // 设计模式适，直接返回默认值
           return `300px`;
        }
       
      }
      let tableHeight = Number(this.field.options.tableHeight);
      if (!!tableHeight) {
        if (tableHeight > 1) {
          return `${tableHeight}px`;
        } else {
          // 小于1时， 百分百占用高度
          let maxHeight = this.contentBoxHeightOffsetHeight * tableHeight + "px";
          return maxHeight;
        }
      } else {
        // 默认
        if (this.contentBoxHeightOffsetHeight > 0) {
          return this.contentBoxHeightOffsetHeight + "px";
        } else {
          return ``;
        }
      }
    },

  },
};
</script>
  
  <style lang="scss" scoped>
// 总高度- 占用高度
// .max-content-height{
//     height: calc(100vh - 180px);
//     //overflow: hidden;
// }
// .max-content-height {
//   .vxe-table--body-wrapper {
//   // height: calc(100vh - 297px);
//    height: calc(100vh - 580px) !important;
//   }
// }
</style>
  