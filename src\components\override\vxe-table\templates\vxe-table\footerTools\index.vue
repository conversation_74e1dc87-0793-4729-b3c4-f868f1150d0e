<!--
 * @Description:table底部工具栏
 * @Author: foyang
 * @Date: 2022-11-04 14:53:01
 * @LastEditTime: 2022-11-09 15:33:48
 * @LastEditors: foyang
-->
<template>
  <div class="toolbarCla">
    <div class="leftCla" v-if="footerConfig.leftToolShow" >
      <vxePulldown-template  ref="pulldownRef"/>
      <span class="exportCla" @click="exportTable">
        <i class="vxe-icon-download" ></i>
      </span>
    </div>
    <div class="rightCla">
      <vxePager-template v-if="footerConfig.rightToolShow" :tablePage='footerConfig.pageConfig' @pageChange='pageChange' ref="pageRef"/>
    </div>
  </div>
</template>
<script>
import VxePagerTemplate from "../vxePager/index.vue"  
import VxePulldownTemplate from "../vxePulldown/index.vue"
export default {
  name: 'footerTools',
  components: {
    VxePulldownTemplate,  VxePagerTemplate
  },
  props: {
    /**底部工具栏配置**/ 
    footerConfig: {
      type: Object,
      default() {
        return {
          leftToolShow:true, // 是否显示左侧工具栏列筛选
          rightToolShow: true, // 是否显示右侧工具栏分页
          pageConfig: { // 分页配置
            total: 0,
            currentPage: 1,
            pageSize: 10, 
          }
        }
      }
    },
    size: {
      type: String,
      default: 'mini'
    }
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
  methods: {
    pageChange({ type, currentPage, pageSize, $event}) {
      this.$emit('pageChanges', { type, currentPage, pageSize, $event})
    },
    // 导出表格
    exportTable() {
      this.$parent.$parent.$refs.csoftiTableRef.openExport()
    },
  },
};
</script>
<style lang='scss' scoped>
.toolbarCla {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    line-height: 50px;
    margin-top: 10px;
    background: #fff;
    box-sizing: border-box;
    .leftCla{
      .exportCla {
        display: inline-block;
        width: 35px;
        height: 35px;
        margin-left: 8px;
        line-height: 35px;
        border-radius: 50%;
        text-align: center;
        border: 1px solid #dcdfe6;
        cursor: pointer;
        &:hover {
          background: #ebeef5;
        }
        .vxe-icon-download {
          font-size: 16px;
          width: 100%;
          height: 100%;
        }
      }
    }
    .rightCla {
    }
  }
</style>
