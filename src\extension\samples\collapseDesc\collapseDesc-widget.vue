<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <el-skeleton :throttle="100" :rows="6" :loading="loading_container" animated>
       <div v-if="Object.keys(groupDataObj).length>0" :ref="field.options.name" :publicAttribute="publicAttribute" class="flex items-center">

          <el-collapse class="w-full" :accordion="!!field.options.accordionLayout" v-model="activeNames"
            @change="handleChange">
            <el-collapse-item v-for="(item, index) in Object.keys(groupDataObj)" :key="index" title="" :name="index + 1 + ''">
              <template v-if="!field.options.hiddenTitle && Object.keys(groupDataObj).length==1" slot="title">
                <span :title="item" v-text="item != 'undefined' ? item : '默认分组【返回数据格式错误】'"></span>
                <el-tooltip v-if="groupDataObj[item][0].GroupDesc" effect="light"
                  :content="groupDataObj[item][0].GroupDesc">
                  <i style="margin-left:3px;width: 12px;" class="el-icon-info"></i></el-tooltip>
                <div v-else style="width: 12px;"></div>
              </template>
              <el-descriptions :direction="`${!!field.options.verticalLayout ? 'vertical' : 'horizontal'}`"
                :colon="!!field.options.showColon" title="" :column="(!!field.options.column ? field.options.column : 1)"
                size="small" :border="!!field.options.showBorder">
                <el-descriptions-item v-for="(subitem, subIndex) in orderByItemSeq(groupDataObj[item])" :key="subIndex">
                  <template slot="label">
                    <div class="flex items-center justify-end overflow-hidden" style="height: 30px; "
                      :style="{ width: `${!!field.options.labelWidth ? field.options.labelWidth : 50}px` }">
                      <span :title="subitem.ItemName" v-text="!!subitem.ItemName ? subitem.ItemName : `字段${subIndex}`"></span>
                      <el-tooltip v-if="subitem.ItemDesc" effect="light" :content="subitem.ItemDesc">
                        <i style="margin-left:3px;width: 12px;" class="el-icon-info"></i></el-tooltip>
                      <div v-else style="width: 12px;"></div>
                    </div>
                  </template>
                  <el-input v-model="subitem.ItemValue" placeholder=""></el-input>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
      </div>
      <div  :style="{height: contentBoxHeight+`px`}" v-else class="flex overflow-hidden  h-full w-full justify-center items-center" >
         暂无数据
      </div>
  </el-skeleton>
  <div v-if="designer">【分组描述】</div> 
  </static-content-wrapper>
</template>

<script>
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import { useHandleVFormComponents } from "@/hooks/useHandleVFormComponents"
import { groupBy, orderBy } from "lodash-es";
import cloneDeep from "clone-deep"
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"


export default {
  name: "collapseDesc-widget", // 分组表单描述
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['getPageInstance', 'sourceVFormRenderState'],
  components: {
    StaticContentWrapper,
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    // 控件来源父集 add by andy
    contentBoxHeight: {
      type: [Number, String],
      default: 0
    },
    // 控件来源父集 add by andy
    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  data() {
    return {
      loading_container:false,
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
      useHandleVFormComponentsFn:useHandleVFormComponents(this),
      activeNames: ['1'],// 默认打开第一个
      publicAttribute: {
        value: "" // 对外暴露属性值
      },
      groupDataObj: {},
      formData: {
        // name:'kooriookami',
        // code:'苏州市',
        // desc:'学校',
        // addr:'江苏省苏州市吴中区吴中大道 1188 号',
        // phone:'18100000000',
        // email:'',
        // website:'' 
      }
    }
  },
  computed: {
    pageInstance() {
      // 获取列表示例
      return this.getPageInstance()
    },
  },
  mounted() {
    let _self = this
    this.$nextTick(() => {
      setTimeout(() => {
        //_self.tryTillGetData()
        if (!this.field.options.isPassiveDataLoad) {
          _self.tryTillGetData()
        }
      }, 300)
    })
  },

  created() {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
    // 检验绑定控件和绑定值是否相等
    checkBandingCtrlVal() {
      // debugger
      let _flag = false
      try {
        if (!!this.field.options.bandingCtrlName && !!this.field.options.bandingCtrlValue) {
          let bandingCtlVal = this.useHandleVFormComponentsFn.getCtrlBandingValue(this.field.options.bandingCtrlName)
          if (bandingCtlVal == this.field.options.bandingCtrlValue) {
            // console.warn("this.field.options.bandingCtrlValue:",this.field.options.bandingCtrlValue)
            _flag = true
          } else {
            _flag = false
          }
        }else{
          _flag = true
        }
      } catch (error) {
        _flag = true
      }
      return _flag
    },
    // 对外暴露事件，重新加载
    async reSearchData(params) {
            // 检验绑定控件和绑定值是否相等
            if (this.checkBandingCtrlVal()) {
             this.tryTillGetData()
             //console.warn("对外暴露事件，重新加载:reSearchData collapseDesc-widget")
         }
  
    },
    handleChange(val) {
      console.log(val);
    },
    orderByItemSeq(dataList = []) {
      let _formatDataList = []
      try {
        if (dataList && dataList.length > 0) {
          //  orderBy(dataList, ["fieldOrder"], ["asc"]); // 升序排序
          _formatDataList = orderBy(dataList, ["ItemSeq"], ["asc"]) // 升序排序
        }
      } catch (error) {
        _formatDataList = []
      }

      return _formatDataList
    },
    // 触发控件列表>>触发事件 >>循环直到获取到数据
    async tryTillGetData() {
    
      this.loading_container = true
     
      //  返回数据格式必须包含字段：GroupID GroupName  GroupDesc ItemID ItemName ItemDesc  ItemValue ItemSeq
      let _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
      // let _dataList =[
      //   {GroupID:1, GroupName:'分组1',GroupDesc:'分组描述1',ItemID:1, ItemName:'姓名', ItemDesc:'', ItemValue:'kooriookami', ItemSeq:3},
      //   {GroupID:1, GroupName:'分组1',GroupDesc:'分组描述1',ItemID:2, ItemName:'代码', ItemDesc:'代码', ItemValue:'苏州市', ItemSeq:2},
      //   {GroupID:1, GroupName:'分组1',GroupDesc:'分组描述1',ItemID:3, ItemName:'描述', ItemDesc:'描述', ItemValue:'学校', ItemSeq:1},       
      //   {GroupID:1, GroupName:'分组1',GroupDesc:'分组描述1',ItemID:4, ItemName:'江苏省苏州市吴中区吴中大道', ItemDesc:'地址', ItemValue:'江苏省苏州市吴中区吴中大道 1188 号', ItemSeq:4},
      //   {GroupID:1, GroupName:'分组1',GroupDesc:'分组描述1',ItemID:5, ItemName:'电话', ItemDesc:'电话', ItemValue:'518100000000', ItemSeq:5},
      //   {GroupID:2, GroupName:'分组2',GroupDesc:'分组描述2',ItemID:6, ItemName:'邮箱', ItemDesc:'邮箱', ItemValue:'718200000000', ItemSeq:7},
      //   {GroupID:2, GroupName:'分组2',GroupDesc:'分组描述2',ItemID:7, ItemName:'网址', ItemDesc:'网址', ItemValue:'618300000000', ItemSeq:6},
      //   {GroupID:3, GroupName:'分组3',GroupDesc:'分组描述3',ItemID:8, ItemName:'姓名', ItemDesc:'姓名', ItemValue:'8kooriookami', ItemSeq:8},
      //   {GroupID:3, GroupName:'分组3',GroupDesc:'分组描述3',ItemID:9, ItemName:'代码', ItemDesc:'代码', ItemValue:'苏州市', ItemSeq:9},
      //   {GroupID:3, GroupName:'分组3',GroupDesc:'分组描述3',ItemID:10, ItemName:'描述', ItemDesc:'描述', ItemValue:'学校', ItemSeq:10},       
      //   {GroupID:3, GroupName:'分组3',GroupDesc:'分组描述3',ItemID:11, ItemName:'地址', ItemDesc:'地址', ItemValue:'江苏省苏州市吴中区吴中大道 6666 号', ItemSeq:11},
      //   {GroupID:3, GroupName:'分组3',GroupDesc:'分组描述3',ItemID:12, ItemName:'电话', ItemDesc:'电话', ItemValue:'18400000000', ItemSeq:12},
      //   {GroupID:4, GroupName:'分组4',GroupDesc:'分组描述4',ItemID:13, ItemName:'邮箱', ItemDesc:'邮箱', ItemValue:'18500000000', ItemSeq:13},     
      //   {GroupID:4, GroupName:'分组4',GroupDesc:'分组描述4',ItemID:14, ItemName:'网址', ItemDesc:'网址', ItemValue:'18600000000', ItemSeq:14},
      // ]
      if (_dataList && _dataList.length > 0) {
        //  this.originList = cloneDeep(_dataList)
        this.groupDataObj = groupBy(_dataList, 'GroupName')
      }else{
        this.groupDataObj ={}
      }
      this.loading_container = false
      setTimeout(() => {
        this.loading_container = false
      }, 1000);
    },
  }
}
</script>

<style lang="scss" scoped></style>