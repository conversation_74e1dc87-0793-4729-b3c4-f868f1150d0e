<template>
  <el-form-item label="操作类型">
    <el-select v-model="optionModel.size">
      <el-option v-for="item in widgetSizes" :key="item.value" :label="item.label"
                 :value="item.value">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "size-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        widgetSizes: [
          {label: '自定义', value: ''},
          {label: '添加', value: 'large'},
          {label: '修改', value: 'medium'},
          {label: '删除', value: 'small'},
          {label: '审核', value: 'mini'},
          {label: '反审', value: 'mini2'},
        ],
      }
    }
  }
</script>

<style scoped>

</style>
