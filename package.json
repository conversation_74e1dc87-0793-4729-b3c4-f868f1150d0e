{"name": "vue-child-vform", "version": "2.3.4", "private": false, "scripts": {"serve": "vue-cli-service serve --open src/main.js", "build": "vue-cli-service build", "build-report": "vue-cli-service build --report --dest dist/build", "lib": "vue-cli-service build --report --target lib --dest dist/lib --name VFormDesigner install.js", "lib-render": "vue-cli-service build --report --target lib --dest dist/lib-render --name VFormRender install-render.js", "lint": "vue-cli-service lint"}, "dependencies": {"@vueuse/core": "^5.3.0", "axios": "^0.21.1", "browser-md5-file": "^1.1.1", "clipboard": "^2.0.8", "clone-deep": "^4.0.1", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.6.5", "dayjs": "^1.11.7", "moment": "^2.29.4", "echarts": "^5.4.1", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "splitpanes": "legacy", "vue": "^2.6.11", "vue-json-viewer": "^2.2.22", "vue-router": "^3.5.1", "vue2-editor": "^2.10.2", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "fuse.js": "^7.0.0", "vxe-table": "3.8.24", "xe-utils": "^3.5.20"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "ace-builds": "^1.4.12", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "change-prefix-loader": "^1.0.5", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "mvdir": "^1.0.21", "postcss-change-css-prefix": "^1.0.2", "postcss-plugin-namespace": "^0.0.3", "sass": "^1.45.1", "sass-loader": "^8.0.2", "svg-sprite-loader": "^5.2.1", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.9.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}