<template>
    <div>
        <template v-if="renderType=='editRender'">
            <el-select :multiple="true" @change="changeEvent" v-model="fieldModel" clearable  v-bind="$attrs" v-on="$listeners"  placeholder="请选择">
                <el-option  class=vxe-table--ignore-clear v-for="item in selectOptions" :key="item.key" :label="item.label" :value="item.key">
                </el-option>
            </el-select>
        </template>
            <template v-else>
                <div v-text="fieldModelTitle"></div>
         </template>
    </div>
   
</template>
<script>
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: "reportMulSelect", // 多选 下拉值
    props:{
         // 渲染类型
         renderType:{
            type:String,
            default:"editRender"
        },
        currentValue:{
            type:String,
            default:""
        },
        field:{
            type:String,
            default:""
        },
        controlType:{
            type:String,
            default:""
        },
        disabled:{
            type:<PERSON>olean,
            default:false
        },
        itemOptions:{
            type:Object,
            default(){
                return {}
            }
        },
          // 当前字段选择行
          rowData: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    inject: ['getPageInstance','sourceVFormRenderState'],
    data() {
        return {
            fieldModel:[],
            fieldModelTitle:"",
            useFormatParamsFn: useFormatParams(this),
            selectOptions: [],
            optionConfig:{
                key:"",
                label:""
            },
            //actionName:""

        }
    },
    computed:{
        onAfterChangeConfig() {
            let _onAfterChange = ""
            try {
                _onAfterChange = this.itemOptions.paramsItem.controlConfig.onAfterChange
            } catch (error) {
                _onAfterChange = ""
            }
            return _onAfterChange
        },
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
        cacheDataList(){
            let _dataList =[]
            try {
                _dataList =this.itemOptions.optionDataList
            } catch (error) {
                _dataList =[]
            }
            return _dataList
        },
        actionName(){
            let _actionName =""
            try {
                _actionName =this.itemOptions.paramsItem.controlConfig.actionName    

            } catch (error) {
                _actionName =""
            }
            return _actionName
        },
        // 控件配置信息
        controlConfig(){
            let _config ={}
            try {
                 _config =this.itemOptions.paramsItem.controlConfig    
            } catch (error) {
                _config ={}
            }
            return _config
        },
        // 下拉列表数据
        CDATAS(){
            let _dataList =[]
            try {
                _dataList =this.itemOptions.paramsItem.controlConfig.CDATAS    
            } catch (error) {
                _dataList =[]
            }
            return _dataList
        }
        
       
    },
    watch:{
        "$attrs.value"(n,o){
            this.changeByOtherFn(n)
        },
  
    },
    mounted(){
   
       this.$nextTick(()=>{
         // debugger
          this.selectOptions =this.cacheDataList
          this.init()
       })
    },
    methods: {
         // 其它额外的被动事件导致选中值 发生改变 
         changeByOtherFn(val){
            if(this.renderType!="editRender"){
                this.fieldModelTitle = this.getTitleByVal(val)
            }
          
        },
        changeEvent(val){
            //debugger
            this.fieldModel = val
            this.fieldModelTitle = this.getTitleByVal(val)
               // 获取选中行的原始数据
               let selectedOriginData = this.selectOptions.filter(item=>{
                if(item.key == val){
                    return item
                }
            })
            this.$nextTick(()=>{
                   //////////////////////////////表格>>标准弹框(onAfterChange)/////////////////////////////////////////
                   let onAfterChangeStr =  this.onAfterChangeConfig
                    let otherOptions={
                       currentRow:this.rowData,
                       selectedData:selectedOriginData,
                    }
                    let onAfterChange = new Function('paramsData', onAfterChangeStr)
                    //  选择弹框数据后，其它自定义操作 方法
                    if (!!onAfterChangeStr) {
                        try {
                            onAfterChange.call(this, otherOptions)
                        } catch (error) {
                            this.$message({
                                message: '表格>>多选下拉(onAfterChange)错误，请检查！！！',
                                type: 'error'
                            });
                        return
                        }
                     }
                    /////////////////////////////表格>>下拉(onAfterChange)//////////////////////////////////////////
            })
        },
        getTitleByVal(valList=[]){
            let title =""
            let list =  this.cacheDataList
               if(list && list.length>0){
                list.forEach(item=>{
                    valList.forEach(subItem=>{
                        if(!!title){
                            if(item.key ==subItem){
                                title = title+","+item.label
                            }
                        }else{
                            if(item.key ==subItem ){
                                title = item.label
                            }
                        }
                         
                    })
                })
               }
               return title
        },
        init(){
           // debugger
            this.fieldModel = this.$attrs.value
            this.fieldModelTitle = this.getTitleByVal(this.$attrs.value)
        },
        // getDataFromCacheByKey(cacheKey){
        //     let ctrlData = null
        //     try {
        //         ctrlData = this.$store.state.ctrlData.value[cacheKey]||null
        //     } catch (error) {
        //         ctrlData = null
        //     }
        //     return ctrlData
        // },
        // // 获取下拉数据列表
        // async getOptionDataList(){
        //             console.log('=====start===getOptionDataList=========')
        //             let cacheKey ="reportSelect_getOptionDataList"
        //             let dataList =this.getDataFromCacheByKey(cacheKey)
        //             if ((dataList==null||dataList.length==0) && !!this.actionName && this.selectOptions.length==0){
        //                 console.log('====end====getOptionDataList=========')
        //                 dataList  = await this.useFormatParamsFn.getDBDataByActionName(this.actionName);
        //                 //hasLoadData_getOptionDataList = false
        //                 dataList = this.optionsDataFormat(dataList)
        //                 let tempData ={
        //                     key:cacheKey,
        //                     value:dataList
        //                 }
        //                 this.$store.commit("set_ctrlData",tempData)
        //             }
        //             this.selectOptions =   dataList
            
        // },
        // optionsDataFormat(dataList){
        //    // debugger
        //     let optionsList =[]
        //     if(dataList && dataList.length>0){
        //         dataList.forEach(item=>{
        //            let newItem ={
        //              key:item[this.optionConfig.key],
        //              label:!!item[this.optionConfig.label]?item[this.optionConfig.label]:item[this.optionConfig.key]
        //            }
        //            optionsList.push(newItem)
        //         })
               
        //     }
        //     return optionsList
        // },
        // // 获取下拉数组的KEY & VALUE
        // getOptionsKeyAndValue(){
        //     //console.log('=====start===getOptionsKeyAndValue=========')
        //     let cacheKey ="reportSelect_getOptionsKeyAndValue"
        //     let dataList =this.getDataFromCacheByKey(cacheKey)
        //     if(!!!dataList && this.CDATAS.length>0){
        //       //  console.log('=====end===getOptionsKeyAndValue=========')
        //        for(let i=0;i<this.CDATAS.length;i++){
        //            let item = this.CDATAS[i]
        //             if(!!item.isSelectTextField && !this.optionConfig.label){
        //                 this.optionConfig.label = item.field

        //             }
        //             if(!!item.isSelectKeyField && !this.optionConfig.key){
        //                 this.optionConfig.key = item.field
        //             }
        //        }
        //        let tempData ={
        //             key:cacheKey,
        //             value:this.optionConfig
        //         }
        //         this.$store.commit("set_ctrlData",tempData)
        //     }else{
        //         this.optionConfig =dataList
        //     }
        // }
    }
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

.full-width-input {
    width: 100% !important;
}

.main-select-el-tree {
    .custom-tree-node {
        font-size: 14px;
        //background-color: transparent;
    }
}

::v-deep .customform {
    .el-tag--info {
        width: auto;
        height: 24px;
        color: #1989FA;
        border-radius: 2px;
        border: 1px solid #1989FA;
        background: rgba(65, 158, 251, 0.10);

        .el-tag__close {
            font-size: 16px;
            color: #1989FA;
            background: none;

            &:hover {
                color: #1989FA;
                background: none;
            }
        }
    }
}
</style>