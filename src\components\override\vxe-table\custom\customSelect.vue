<template>
    <div>  
        <template v-if="renderType=='editRender'">
        <el-select @change="changeEvent"   v-model="fieldModel" v-bind="$attrs" v-on="$listeners"  placeholder="请选择">
            <el-option class=vxe-table--ignore-clear  v-for="item in selectOptions" :key="item.field" :label="item.title" :value="item.field">
            </el-option>
         </el-select>
    </template>
   <template v-else>
        <div v-text="fieldModelTitle"></div>
   </template>
    </div>
   
</template>
<script>
export default {
    name: "customSelect",
    props:{
        // 渲染类型
        renderType:{
            type:String,
            default:"editRender"
        },
        currentValue:{
            type:String,
            default:""
        },
        field:{
            type:String,
            default:""
        },
        controlType:{
            type:String,
            default:""
        },
        disabled:{
            type:Boolean,
            default:false
        },
        itemOptions:{
            type:Object,
            default(){
                return {}
            }
        },
          // 当前字段选择行
        rowData: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    data() {
        return {
            fieldModel:"",
            fieldModelTitle:"",
            selectOptions: [],
        }
    },
    computed:{
        onAfterChangeConfig() {
            let _onAfterChange = ""
            try {
                _onAfterChange = this.itemOptions.paramsItem.controlConfig.onAfterChange
            } catch (error) {
                _onAfterChange = ""
            }
            return _onAfterChange
        },
        // 控件配置信息
        controlConfig(){
            let _config ={}
            try {
                 _config =this.itemOptions.paramsItem.controlConfig    
            } catch (error) {
                _config ={}
            }
            return _config
        },
        // 下拉列表数据
        // field: "newOption1"
        // fieldOrder: 100
        // iisShowList: 0
        // isSelectKeyField: 0
        // isSelectTextField: 0
        // title: "新子项1"
        // width: 200
        CDATAS(){
            let _dataList =[]
            try {
                _dataList =this.itemOptions.paramsItem.controlConfig.CDATAS    
            } catch (error) {
                _dataList =[]
            }
            return _dataList
        }
    },

    mounted(){
        //debugger
        this.selectOptions = this.CDATAS
        this.init()
    },
    methods: {
        changeEvent(val){
            this.fieldModel = val
            this.fieldModelTitle = this.getTitleByVal(val)
            this.$nextTick(()=>{
                   //////////////////////////////表格>>标准弹框(onAfterChange)/////////////////////////////////////////
                   let onAfterChangeStr =  this.onAfterChangeConfig
                    let otherOptions={
                       currentRow:this.rowData,

                    }
                    let onAfterChange = new Function('paramsData', onAfterChangeStr)
                    //  选择弹框数据后，其它自定义操作 方法
                    if (!!onAfterChangeStr) {
                        try {
                            onAfterChange.call(this, otherOptions)
                        } catch (error) {
                            this.$message({
                                message: '表格>>下拉(onAfterChange)错误，请检查！！！',
                                type: 'error'
                            });
                        return
                        }
                     }
                    /////////////////////////////表格>>下拉(onAfterChange)//////////////////////////////////////////
            })
        },
        getTitleByVal(val){
             let title =""
             let list =  this.CDATAS
               if(list && list.length>0){
                list.forEach(item=>{
                    if(item.field ==val ){
                        title = item.title
                    }
                })
               }
               return title
        },
        init(){
            this.fieldModel = this.$attrs.value+""
            this.fieldModelTitle = this.getTitleByVal(this.$attrs.value+"")

        }
    }
}
</script>

<style lang="scss" scoped>
@import "../../../../styles/global.scss"; //* form-item-wrapper已引入，还需要重复引入吗？ *//

.full-width-input {
    width: 100% !important;
}

.main-select-el-tree {
    .custom-tree-node {
        font-size: 14px;
        //background-color: transparent;
    }
}

::v-deep .customform {
    .el-tag--info {
        width: auto;
        height: 24px;
        color: #1989FA;
        border-radius: 2px;
        border: 1px solid #1989FA;
        background: rgba(65, 158, 251, 0.10);

        .el-tag__close {
            font-size: 16px;
            color: #1989FA;
            background: none;

            &:hover {
                color: #1989FA;
                background: none;
            }
        }
    }
}
</style>