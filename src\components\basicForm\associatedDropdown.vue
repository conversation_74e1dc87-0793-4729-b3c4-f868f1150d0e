<template>
    <div class="selectTableRender">
        <!-- @focus="loadData()" -->
          <el-select 
            ref="elSelectRef"
            
            @change="changeEvent"
            v-bind="$attrs"
            v-model="currentItem"
            v-on="$listeners"
            :loading="loading"
            placeholder="请选择">
             <el-option  v-for="item in selectDataList"  :key="item.value" :label="item.label" :value="item.value+''">
            </el-option>
         </el-select>
    </div>
</template>
<script>
import cloneDeep from 'clone-deep';
import emitter from '@/libs/mitt'
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'associatedDropdown',
    components:{},
    props:{
        
          // 当前表单字段
      field:{
          type:String,
          default:""
      },
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
        // 当前表单对象
      formData:{
          type:Object,
          default(){
              return {}
          }
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
        // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
        dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
       // 当前字段:value
     currentValue:{
         type:[String,Number],
         default:""
     },
      // 当前字段:Text
     currentValueText:{
        type:[String,Number],
         default:""
     },
     // 是否可用
     disabled:{
         type:Boolean,
         default:false,
     }  
    }, 
    data(){
        return {
            config:config,
            //value1: [],
            loading: false,
            prefixIcon:"", // 头部图标,用于是否加载中 显示
            currentItem:'',// 当前选择项
            currentItemText:'',// 当前选择项描述
            selectDataList:[],// 当前列表数据
        }
    },
    computed:{ 
        dataSourceID(){
            let _id =""
            try {
                _id = this.dataSearchOptions.dbId
            } catch (error) {
                _id =""
            }
            return _id
        }
    },
    watch:{
    //     formData:{
    //         handler(n,o){
    //           // 监听表单数据变化，根据需求决定是否触发数据更新
    //             this.reloadDataList()
    //     },
    //     deep:true
    //   },
        currentValue(n,o){
            // 连续新增时，需要清空数据
            if(!!n){
               this.currentItem = n
            }else{
                // 清空数据
                 this.currentItem =''
               //  this.currentItemText =''
            }
            this.$nextTick(()=>{
                this.loadData()
            })
        },
    
        
    },
    beforeMount(){
         //  移除监听
         emitter.off("associatedDropdown")
    },
    mounted(){
        //首次赋值
      this.$nextTick(()=>{
        this.loadData()
      })
        // 注册监听 标准弹框事件
        emitter.on("associatedDropdown",(params)=>{
            this.associatedDropdownFn(params)
        })
    },
    destroyed(){
        // 组件销毁 移除监听
        emitter.off("associatedDropdown")
    },
    methods:{
        // 触发调用联动事件
        associatedDropdownFn(params){
            this.loadData(true,params)
        },
     
        // 格式化数据成下拉框所需的
        formatData(dataList=[]){
           // debugger
            let newData =[] 
            let matchField = this.paramsItem.matchField
           // let matchField = "bpArtId,bpArtCode"
            let keyValueArr = matchField.split(',')
            //debugger
            if(dataList && dataList.length>0){
                dataList.forEach(item=>{
                    let newItem ={
                        value:item[keyValueArr[0]]+"",
                        label:item[keyValueArr[1]]
                    }
                    newData.push(newItem)
                })
            }
          return newData
        },
       // 加载数据  e.g::::控件选择 下拉框（表），sourcekey:TBL_SYS_USER，转换：CID,CDISPLAY_NAME
       loadData(associatedData=false,postParams){
        
            if(this.selectDataList.length >1){
                return
            }
            let _self = this
            let reloadData =false // 重新加载数据？
            this.loading = true
            let sourceKey = this.paramsItem.sourceKey
            // 是否包含联动参数#分隔符号
            let searchParamsCopyDD =""//是否包含联动参数#分隔符号
            let searchParams = this.paramsItem.searchParams
            // if(this.paramsItem.searchParams.includes('#')){
            //         searchParams = searchParams.split('#')[0]
            //         searchParamsCopyDD = searchParams.split('#')[1]
            // }
            let _url=this.config.moduleSub+"ComboSource"
            
            let params = {
                   SourceType:"Table", //Dict Table Tree
                   SourceID:this.dataSourceID,
                   Params:{
                    sourceKey,// 表名
                    filter:{} // 字段过滤参数
                   }
                };
                if(!!searchParams){
                 
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                       let searchParamsArr = searchParams.split(',')
                       searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                          
                          if(paramItem[1].includes('#')){
                            // 如果值包含#，则说明是联动参数，需要重对应的控件中获取对应的值
                            if(!!associatedData){
                                let controlName = paramItem[1].replace('#','')
                                if(controlName == _self.field){
                                    reloadData = true
                                }
                                let controlValue = formData[controlName]
                                if(!!controlValue){
                                    params.Params.filter[paramItem[0]] =paramItem[1]
                                }
                               
                            }
                          }else{
                            params.Params.filter[paramItem[0]] =paramItem[1]
                          }
                       })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        
                        if(paramItem[1].includes('#')){
                            // 如果值包含#，则说明是联动参数，需要重对应的控件中获取对应的值
                            if(!!associatedData){
                                let controlName = paramItem[1].replace('#','')
                                if(controlName == _self.field){
                                    reloadData = true
                                }
                                let controlValue = formData[controlName]
                                if(!!controlValue){
                                    params.Params.filter[paramItem[0]] =paramItem[1]
                                }
                            }
                        }else{
                            params.Params.filter[paramItem[0]] =paramItem[1]
                        }
                    }
                }
           
            if(!!associatedData && !reloadData){
                return
            }
            request['post'](_url, params).then(res=>{
                //debugger
                if(res.Success){
                    let resData = res.Datas
                    _self.loading = false
                    _self.selectDataList = _self.formatData(resData)
                   // debugger
                    _self.$nextTick(()=>{

                        if(!!_self.currentValue){
                            _self.currentItem =_self.currentValue+''
                        }
                  })
                  
                }
            })
       },
       // 选择改变回调事件
       changeEvent(val){
           let _self = this
           this.$nextTick(()=>{
                let params ={
                    value:val,
                    text:_self.$refs["elSelectRef"].selectedLabel
                } 
                _self.$emit("changeEvent",params)
                // 触发事件
                emitter.emit("associatedDropdown",params)
           })
       }
    }
}
</script>


<style lang="scss">

</style>