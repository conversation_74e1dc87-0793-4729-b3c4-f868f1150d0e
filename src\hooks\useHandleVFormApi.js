
// 统一处理VFormApi
import { useFormatParams } from "@/hooks/useFormatParams"
// import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import request from '@/libs/request'
export function useHandleVFormApi(vueInstance, refFormName = 'preForm') {
    return {
        data: [],
        vueInstance: vueInstance,
        refFormName: refFormName,
        useFormatParamsFn: useFormatParams(vueInstance),
        // useHandleVFormEventFn: useHandleVFormEvent(vueInstance),
        testFn() {
            return this.data
        },
        // 检验是否为容器
        isContainerOrNot(type) {
            let containerList = ["splitpanes", "grid", "tab", "table", "'sub-form", "grid-sub-form", "split-pane"]
            let isContainer = containerList.includes(type)
            return isContainer
        },
        // 校验数据通过不同的容器类型
        checkContainerData(item, sameApiWidgetsList, actionName) {
            let type = item.type
            let dataList = []
            let isContainer = false
            let new_sameApiWidgetsList = []
            switch (type) {
                case "splitpanes":
                    dataList = item.panes
                    dataList.forEach(subItem => {
                        isContainer = this.isContainerOrNot(subItem.type)
                        if (isContainer) {
                            new_sameApiWidgetsList = this.checkContainerData(subItem, sameApiWidgetsList, actionName)

                        } else {
                            if (subItem && subItem.options && subItem.options.hasOwnProperty("requstConfig")) {
                                if (subItem.options.requstConfig.postUrl == actionName) {
                                    new_sameApiWidgetsList.push(subItem)
                                }
                            }
                        }
                    })
                    break;
                case "grid":

                    break;
                case "tab":

                    break;
                case "split-pane":
                    dataList = item.widgetList
                    dataList.forEach(subItem => {
                        isContainer = this.isContainerOrNot(subItem.type)
                        if (isContainer) {
                            new_sameApiWidgetsList = this.checkContainerData(subItem, sameApiWidgetsList)

                        } else {
                            if (subItem && subItem.options && subItem.options.hasOwnProperty("requstConfig")) {
                                if (subItem.options.requstConfig.postUrl == actionName) {
                                    new_sameApiWidgetsList.push(subItem)
                                }
                            }
                        }
                    })
                    break;
                default:
                    break;
            }
            //concat 连接两个或多个数组 返回一个新的数组
            sameApiWidgetsList = sameApiWidgetsList.concat(new_sameApiWidgetsList)
            return sameApiWidgetsList
        },
        // 获取相同API的组件列表
        getSameApiWidgets(actionName = "") {

            // 比较所有的组件，是否包含了即将要执行的API,如果包含了，就运行该组件的reLoadData()方法
            // actionName ="query1"
            let sameApiWidgetsList = [] // 如果为空则不存在相同API的组件
            // let widgetList = this.vueInstance.designer.widgetList
            let widgetList = null // this.vueInstance.pageInstance.$refs[this.refFormName].designer.widgetList
            if(!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxDrawerRef.editPreForm"){
                widgetList = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].designer.widgetList
             }else if(!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxRef.editPreForm"){
                widgetList = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].designer.widgetList
              }
             else{
                widgetList = this.vueInstance.pageInstance.$refs[this.refFormName].designer.widgetList
             }
            if (widgetList && widgetList.length > 0) {
                widgetList.forEach(item => {
                    // debugger
                    // 检验是否为容器，如果为容器 则检验容器下的数据，而非容器本身
                    let isContainer = this.isContainerOrNot(item.type)
                    if (isContainer) {
                        sameApiWidgetsList = this.checkContainerData(item, sameApiWidgetsList, actionName)

                    } else {
                        if (item && item.options && item.options.hasOwnProperty("requstConfig")) {
                            if (item.options.requstConfig.postUrl == actionName) {
                                sameApiWidgetsList.push(item)
                            }
                        }
                    }
                })
            }
            // debugger
            return sameApiWidgetsList
        },
        // 如果包含了相同的api，就运行该组件的reSearchData方法
        // controlItem 执行动作的控件，
        // sameApiWidgetsList 具有相同API的控件列表
        runSameApiWidgets(controlItem, sameApiWidgetsList) {
            if (sameApiWidgetsList && sameApiWidgetsList.length > 0) {
                for (let i = 0; i < sameApiWidgetsList.length; i++) {
                    let widgetItem = sameApiWidgetsList[i]
                    let ctrlName = widgetItem.options.name
                    this.reSearchDataByName(ctrlName)
                    // 保存需要执行 reLoadData() 组件 名称
                    // 自身组件参数
                    // let selfControlParams = this.useFormatParamsFn.getVFormParamsValue(controlItem);
                    //  if(!!selfControlParams && Object.keys(selfControlParams).length>0){
                    //     this.vueInstance.$store.commit("set_selfControlParams",selfControlParams)
                    //  }
                    // this.vueInstance.$store.commit("set_runCurrentWidget",widgetItem.options.name)
                    // this.vueInstance.$store.commit("set_actionType","iisRefresh")
                }
            }

        },
        // 根据控件REF名称刷新数据
        reSearchDataByName(triggerCtrlName, params = { resetPage: false }) {
            // debugger
            let controlRef =null // this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[triggerCtrlName]
            if(!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxDrawerRef.editPreForm"){
                controlRef = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[triggerCtrlName]
             }else if(!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState=="editContentBoxRef.editPreForm"){
                controlRef = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[triggerCtrlName]
              }
             else{
                controlRef = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[triggerCtrlName]
             }
            if (controlRef) {
                // 正常$refs 对象
                controlRef = controlRef.$refs[triggerCtrlName]
                if (controlRef) {
                    // 对应的控件必须暴露 reSearchData 这个方法
                    let { resetPage = false } = params
                    // debugger
                    controlRef.reSearchData({ resetPage })
                }
            }
        },
        /**
         * 
         * @param {*} subItem 
         *  actionParams: Object
         *  {
         *      actionName: "query1"
                query: {}
         *  }
            afterSuccessOrErrorEvents: Array(0)
            canRemove: false
            check: true
            label: "新增"
            otherParams: Object
            value: "iisAdd"
         */
        // async handleApiByItem_backupRemove(subItem,options) {
        //      // debugger
        //     let actionDesc = subItem.label
        //     let actionName = this.useFormatParamsFn.getActionParamsValue("actionName", subItem)
        //     // 集合类型（结果集：查询类；操作集:主要为添加，修改，删除等操作）
        //     let excuteEventType  = this.useFormatParamsFn.getOtherParamsValue("excuteEventType", subItem)
        //     let sameApiWidgetsList = this.getSameApiWidgets(actionName)
        //     //debugger
        //     if (sameApiWidgetsList && sameApiWidgetsList.length > 0) {
        //         // 如果包含了相同的api，就运行该组件的reLoadData()方法
        //         //console.log("has same sameApiWidgetsList");
        //         this.runSameApiWidgets(subItem, sameApiWidgetsList)
        //         return
        //     }
        //     // 列头查询 preUrl 路径固定不变
        //     let _url = this.useFormatParamsFn.getVFormDataSearchUrl(actionName);
        //     if (!!!_url) {
        //         _url = `api/MD/DataSet/GetListByDataSetId` // 结果集：查询类
        //         if(excuteEventType=="ExecuteByDataSetId"){
        //             _url = `api/MD/DataSet/ExecuteByDataSetId` // 操作集:主要为添加，修改，删除等操作
        //         }
        //     }
        //     // 公共参数
        //     let commonParams = this.useFormatParamsFn.getVFormSearchParams(actionName);
        //     // 自身组件参数
        //     let selfParams = this.useFormatParamsFn.getCommonParamsValue(subItem);
        //     // 合并参数 = （自身组件参数+公共参数），自身参数会覆盖公共参数，如果参数一样的话
        //     let params = Object.assign({}, commonParams, selfParams)
        //     let res = await request["post"](_url, params)
        //     if ([4, 5].includes(res.Data)) {
        //         res.Datas = JSON.parse(res.Datas)
        //     }
        //     //res.Datas = {\"Success\":true,\"Content\":\"\",\"code\":null,\"msg\":null,\"stackTrace\":null,\"Data\":null,\"Datas\":1,\"TotalRows\":1}
        //     if (res && !!res.Success) {
        //         if (res.Datas && !!res.Datas.Success) {
        //             this.vueInstance.$message.success(actionDesc + " " + (!!res.Content ? res.Content : '执行成功'))
        //             setTimeout(()=>{
        //                 this.afterSuccessEvents(res, subItem)
        //             },600)
        //             if(subItem.hasOwnProperty("afterSuccessOrErrorEvents")){
        //                 let eventList = subItem.afterSuccessOrErrorEvents
        //                 if(eventList && eventList.length>0){
        //                     for(let i=0;i<eventList.length;i++){
        //                         let eventItem = eventList[i]
        //                         console.log("======afterSuccessOrErrorEvents=========")
        //                         console.log(eventItem)
        //                         console.log("======afterSuccessOrErrorEvents=========")
        //                        // this.useHandleVFormEventFn.handleCommonClickEvent(eventItem)
        //                     }
        //                 }
                       
        //             }
                    
        //         } else {
        //             this.vueInstance.$message.error(actionDesc + "error " + (!!res.Content ? res.Content : '执行失败'))
        //         }

        //     } else {
        //         this.vueInstance.$message.error(actionDesc + "error " + (!!res.Content ? res.Content : '执行失败'))
        //     }

        // },
        // 成功后执行事件
        async afterSuccessEvents(res, subItem) {
            // 成功后待执行事件列表
            if (!subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
                return
            }
            let todolist_success = subItem.afterSuccessOrErrorEvents.filter(item => {
                let eventType = this.useFormatParamsFn.getOtherParamsValue("eventType", item)
                if (!!eventType && eventType == 'success') {
                    return item
                }
            })
            if (todolist_success && todolist_success.length > 0) {
                for (let i = 0; i < todolist_success.length; i++) {
                    let pendingItem = todolist_success[i]
                    let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", pendingItem)
                    if (actionType == 'api') {
                        let res = await this.executApi(pendingItem)
                    }
                }
            }
        },
        // 失败后执行事件
        afterErrorEvents() {

        },
     
        // 通用执行查询API
        async executApi(subItem) {
             //debugger
            let actionName = this.useFormatParamsFn.getActionParamsValue("actionName", subItem)
            let sameApiWidgetsList = this.getSameApiWidgets(actionName)
            //debugger
            if (sameApiWidgetsList && sameApiWidgetsList.length > 0) {
                // 如果包含了相同的api，就运行该组件的reLoadData()方法
                this.runSameApiWidgets(subItem, sameApiWidgetsList)
                return
            }
            // 列头查询 preUrl 路径固定不变
            let _url = this.useFormatParamsFn.getVFormDataSearchUrl(actionName);
            if (!!!_url) {
                _url = `api/MD/DataSet/GetListByDataSetId` // 固定取值地址
            }
            // 参数
            let params =await this.useFormatParamsFn.getVFormSearchParams(actionName);
            let hasRequireParamsNull = await this.useFormatParamsFn.checkRequireParamsIsNull(actionName);
            if(hasRequireParamsNull){
                // console.error(" 必填参数为空，跳过查询！！")
                return
            }
            let res = await request["post"](_url, params)
            //debugger
            return res
        }

    }
}