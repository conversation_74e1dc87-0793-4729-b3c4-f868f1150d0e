<template>
    <static-content-wrapper :designer="designer" :field="field" :design-state="designState"
                            :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                            :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
                <div :ref="field.options.name">
                  <el-skeleton :loading="loading_container" animated>
                    hello world
                  </el-skeleton>
              </div>
    </static-content-wrapper>
  </template>
  
  <script>
//   import descriptionsFieldNameItem from './descriptionsFieldNameItem.vue'
    import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
    import emitter from '@/utils/emitter'
    import i18n from "@/utils/i18n"
    import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
    import { useFormatParams } from "@/hooks/useFormatParams"
    export default {
      name: "groupSearch-widget",
      componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
      mixins: [emitter, fieldMixin, i18n],
       // 注入列表页面整体实体 
      inject: ['getPageInstance', 'sourceVFormRenderState'],
      props: {
        field: Object,
        parentWidget: Object,
        parentList: Array,
        indexOfParentList: Number,
        designer: Object,
  
        designState: {
          type: Boolean,
          default: false
        },
  
        subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
          type: Number,
          default: -1
        },
        subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
          type: String,
          default: ''
        },
  
      },
      data(){
        return{
          dataList:[{}],
          loading_container:false,
          maxLoadTime: 10,
          useFormatParamsFn: useFormatParams(this),
        }
      },
      computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
     
    },
      components: {
        StaticContentWrapper,
      },
      created() {
        this.registerToRefList()
        this.initEventHandler()
      },
      mounted(){
        let _self = this
        this.$nextTick(() => {
            // 是否被动加载数据
            // if (!!!this.field.options.isPassiveDataLoad) {
            //   _self.tryTillGetData()
            // }

        })
      },
      beforeDestroy() {
        this.unregisterFromRefList()
      },
      methods: {
        getTagType(typeText='',dataList){
         // debugger
          let _type='primary'
          if(!!typeText){
            if(dataList && dataList.length>0){
              let _selectItem = dataList.filter(item=>{
                if(item.tagValue ==typeText){
                  return item
                }
              })
              if(_selectItem && _selectItem.length>0){
                _type =  _selectItem[0].tagType
              }
            }
          }
          return _type
        },
           // 对外暴露事件，重新加载
         async reSearchData(paramsOptions = {}) {
             //await this.tryTillGetData()
             //console.log("========descriptions=====reSearchData=====")
        },
       
         async tryTillGetData() {
            // debugger
            let _self = this
            this.dataList =[]
            this.loading_container = true
            let dataObj = this.$refs[_self.field.options.name] 
            if (!!dataObj) {
                // 干正事
                this.dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
                this.loading_container = false
                this.maxLoadTime = 10
            } else {
                if (_self.maxLoadTime > 0) {
                    setTimeout(async () => {
                        _self.maxLoadTime = _self.maxLoadTime - 1
                        await this.tryTillGetData()
                    }, 1000)
                }
            }
        },
      }
    }
  </script>
  
  <style lang="scss" scoped>
::v-deep .el-descriptions-row {
    .el-descriptions-item__cell{
      width: 50px !important;
      overflow: hidden;
    }
  }
  </style>
  