<template>
    <div>
           <div class="flex justify-between">
            <div class="flex justify-center items-center">{{currentValue}}</div>
            <el-button @click ="showPopupTextarea=true"  type="text">查看</el-button>
           </div>
           <nvxeModal width="50%" ref="popupTextareaRef" :title="fieldTitle"  v-model="showPopupTextarea">
               <el-input
                type="textarea"
                :autosize="{ minRows: 20, maxRows: 20}"
                placeholder=""
                v-model="textareaInfo">
                </el-input>
                <template #footer>
                    <div></div>
                </template>
         </nvxeModal>
    </div>
</template>
<script>
import request from '@/libs/request'
export default {
    name:"popupTextarea",
    props: {
         // 当前字段 KEY
         field: {
            type: String,
            default: ""
        },
         // 当前字-title
         fieldTitle: {
            type: String,
            default: ""
        },
        // 当前字段值
        currentValue: {
            type: String,
            default: ""
        },
        // 当前字段配置信息
        fieldOptions: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段所在行信息
        row: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段 - 父表信息,也就是调用这个弹框数据的表格
        $tableInfo: {
            type: Object,
            default() {
                return {}
            }
        },

    },
    data(){
        return {
            textareaInfo:"",
            showPopupTextarea:false
        }
    },
    watch:{
        showPopupTextarea(n,o){
            // matchField: ""
            // pid: 119297697116230
            // searchParams: ""
            // sourceKey: ""
            //let fieldConfig = this.fieldOptions
            if(!!n){
                this.$nextTick(()=>{
                    this.loadData()
               })
            }
          
        }
    },
    mounted(){

    },
    methods:{
        // controlConfig: Object
        //     CDATAS: Array(0)
        //     CDATASET_ID: -1
        //     CDATASOURCE_ID: -1
        //     CIS_PAGE: "N"
        //     CMAP_PARAMETER: (...)
        //     CPAGE_SIZE: 10
        //     actionName: ""
        //     matchField: ""
        //     onAfterChange: ""
        //     onBeforeRender: ""
        //     searchParams: "type:0"
        //     sourceKey: "/api/UD/LogFile/GetLog"
       async loadData(){
            // debugger
            let _url= "/api/UD/LogFile/GetLog" ///api/UD/LogFile/GetLog?id=123456&type=20 文件类型(0:通用,10:AVI,20:AOI)
            if(!!this.fieldOptions.controlConfig && !!this.fieldOptions.controlConfig.sourceKey){
                _url= this.fieldOptions.controlConfig.sourceKey
            }
            let params={
                id:this.row.CID,
                type:0 //文件类型(0:通用,10:AVI,20:AOI)
            }
            if(!!this.fieldOptions.controlConfig && !!this.fieldOptions.controlConfig.searchParams){
                let searchParams =this.fieldOptions.controlConfig.searchParams
                // 参数分解>>"user:andy,age:18" 转换为JSON
                if(searchParams.includes(',')){
                        //多个参数
                    let searchParamsArr = searchParams.split(',')
                    searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                        params[paramItem[0]] =paramItem[1]
                    })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params[paramItem[0]] =paramItem[1]
                    }
            }
            
            await request["get"](_url, null,params).then(res => {
                if(!!res.Success){
                    this.textareaInfo = res.Datas
                }else{
                    this.$message.error(!!res.Content?res.Content:"读取文件失败！")
                }
            });
        }
    }
}
</script>