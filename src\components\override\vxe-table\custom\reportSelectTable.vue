<template>
    <el-select clearable ref="elSelectRef" @focus="focusEvent()" v-bind="$attrs" v-on="$listeners" :loading="loading"
        placeholder="请选择">
        <el-option v-for="(item, index) in selectOptions" :key="index" :label="item.label" :value="item.value"
        style="display: none"  />
         <vxe-grid @cell-click="cellClickEvent" size="mini" height="240px" border :data="gridOptions.data"
            :columns="gridOptions.columns">
            <template #top>
                <el-input @keyup.native="keyupEvent" size="mini" v-model="searchkey"
                    placeholder="请输入关键字 回车 ENTER"></el-input>
            </template>
                <template #pager>
                    <vxe-pager v-show="showPager" size="mini" :current-page.sync="tablePage.currentPage"
                        :page-size.sync="tablePage.pageSize" :total="tablePage.total" @page-change="handlePageChange">
                    </vxe-pager>
                </template>
            </vxe-grid>
    </el-select>
</template>
<script>
// import { orderBy } from "lodash-es";
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: 'reportSelectTable',
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    components: {},
    props: {
        currentValue: {
            type: String,
            default: ""
        },
        field: {
            type: String,
            default: ""
        },
        controlType: {
            type: String,
            default: ""
        },
        disabled: {
            type: Boolean,
            default: false
        },
        itemOptions: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前列字段名称
          fieldName:{
              type:String,
              default:""
          },
            //  当前编辑行
            rowData: {
                type: Object,
                default() {
                    return {}
                }
            }
    },
    data() {
        return {
            useFormatParamsFn: useFormatParams(this),
            currentItem: null,
            configFormItem: null,
            showPager: false,
            loading: false,
            displayField: [],// 需要显示的字段
            searchkey: "",
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            selectOptions: [],
            selectConfig: {
                key: "",
                text: "",
                searchkey:""
            },
            gridOptions: {
                columns: [],
                data: []
            }
        }
    },
    computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
        actionName() {
            let _actionName = ""
            try {
                _actionName = this.itemOptions.paramsItem.controlConfig.actionName || ""

            } catch (error) {
                _actionName = ""
            }
            return _actionName
        },
        // 控件配置信息
        // CDATAS: Array(24)
        // CDATASET_ID: -1 
        // CDATASOURCE_ID: -1
        // CIS_PAGE: "N"
        // CMAP_PARAMETER: Array(0)
        // CPAGE_SIZE: 10
        // actionName: "query2"
        // matchField: ""
        // searchParams: ""
        // sourceKey: ""
        controlConfig() {
            let _config = {}
            try {
                _config = this.itemOptions.paramsItem.controlConfig || {}
            } catch (error) {
                _config = {}
            }
            return _config
        },
        // 下拉列表数据
        CDATAS() {
            let _dataList = []
            try {
                _dataList = this.itemOptions.paramsItem.controlConfig.CDATAS || []
            } catch (error) {
                _dataList = []
            }
            return _dataList
        }


    },
    mounted() {
        this.showPager = (this.getConfigValueByKey('CIS_PAGE') == 'Y' ? true : false)
    },
    methods: {
        // 根据KEY 获取配置中的值
        getConfigValueByKey(dataKey) {
            let data = null
            try {
                data = this.itemOptions.paramsItem.controlConfig[dataKey] || null
            } catch (error) {
                data = null
            }
            return data
        },
        // 单击当前行
        cellClickEvent(tableInfo) {
            this.rowData[this.fieldName] = tableInfo.row[this.selectConfig.key]
           // this.rowData[this.fieldName] = tableInfo.row[this.selectConfig.text]
            this.$refs["elSelectRef"].blur();
        },
        // 表格查询框 回车键
        keyupEvent($event) {
            if ($event.keyCode === 13) {
                this.loadSelectOptions()
            }
        },
        async focusEvent(params) {
            //debugger
            // 初始化 下拉配置信息
            this.getSelectKeyText(this.CDATAS)
            if (this.selectOptions.length == 0) {
                let resData = await this.loadSelectOptions() // this.getOptionDataList()
            }
        },
        getCtrlDataFromCacheByKey(cacheKey) {
            let ctrlData = null
            try {
                ctrlData = this.$store.state.ctrlData.value[cacheKey] || null
            } catch (error) {
                ctrlData = null
            }
            return ctrlData
        },
        // 获取下拉数据列表
        async getOptionDataList() {
            console.log('=====start===getOptionDataList=========')
            let cacheKey = "reportSelectTable_getOptionDataList"
            let dataList = this.getCtrlDataFromCacheByKey(cacheKey)
            if ((dataList == null || dataList.length == 0) && !!this.actionName && this.selectOptions.length == 0) {
                console.log('====end====getOptionDataList=========')
                dataList = await this.useFormatParamsFn.getDBDataByActionName(this.actionName);
                //dataList = this.optionsDataFormat(dataList)
                let tempData = {
                    key: cacheKey,
                    value: dataList
                }
                this.$store.commit("set_ctrlData", tempData)
            }
            return dataList

        },
        optionsDataFormat(dataList) {
            // debugger
            let optionsList = []
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    let newItem = {
                        key: item[this.optionConfig.key],
                        label: !!item[this.optionConfig.label] ? item[this.optionConfig.label] : item[this.optionConfig.key]
                    }
                    optionsList.push(newItem)
                })

            }
            return optionsList
        },
        // 获取配置的KEY & TEXT
        getSelectKeyText(dataList) {
            //debugger
            //dataList = orderBy(dataList, ["fieldOrder"], ["asc"]); // 升序排序
            this.displayField = [] // 重置需要显示的字段
            this.gridOptions.columns = []
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (!!item.isSelectTextField) {
                        this.selectConfig.text = item.field
                    }
                    if (!!item.isSelectKeyField) {
                        this.selectConfig.key = item.field
                    }
                    if (!!item.iisShowList) {
                        let newItem = {
                            field: item.field,
                            headerAlign: 'center',
                            minWidth: Number(item.width),
                            title: !!item.title ? item.title : item.field
                        }
                        this.displayField.push(newItem)
                        this.gridOptions.columns.push(newItem)
                    }
                })
            } else {
                this.selectConfig = {
                    key: "",
                    text: "",
                    searchkey:""
                }
            }


        },
        // 设置下拉初始化功能
        async loadSelectOptions() {
            //debugger
            if (!!!this.actionName) {
                return
            }
            let CDATASET_ID = this.useFormatParamsFn.getVFormDataSetID(this.actionName)
            this.loading = true
            let _url = `api/MD/DataSet/GetListByDataSetId`
            let params = {
                Id: CDATASET_ID, //数据集ID
                Parameter: {}
            }
            if (!!this.showPager) {
                params.Parameter["PageIndex"] = this.tablePage.currentPage
                params.Parameter["PageSize"] = this.tablePage.pageSize
            }
            if (!!this.showPager) {
                params.Parameter["start"] = this.tablePage.currentPage
                params.Parameter["length"] = this.tablePage.pageSize
            }
            // 添加查询参数

            let baseParams =await this.useFormatParamsFn.getVFormSearchParams(this.actionName);
           
            params = Object.assign({}, baseParams, params)
        
            await request['post'](_url, params).then(res => {
                if (res.Datas && res.Datas.length > 0) {
                    if (!!this.showPager) {
                        //  是否分页
                        this.tablePage.total = res.TotalRows
                    }
                    if ([4, 5].includes(res.Data)) {
                        let data = this.formatDatas(JSON.parse(res.Datas))
                        this.selectOptions = data
                        this.gridOptions.data = JSON.parse(res.Datas)
                    } else {
                        this.selectOptions = this.formatDatas(res.Datas)
                        this.gridOptions.data = res.Datas
                    }
                } else {
                    this.selectOptions = [{ value: "", label: "请选择" }]
                }
            })
            this.loading = false
            setTimeout(() => {
                this.loading = false
            }, 10000)
        },
        // 格式化返回数据
        formatDatas(dataList) {
            let options = [{ value: "", label: "请选择" }]
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    if (!!oldItem[this.selectConfig.text]) {
                        let newItem = {
                            value: oldItem[this.selectConfig.key],
                            label: oldItem[this.selectConfig.text]
                        }
                        options.push(newItem)
                    }

                })
            }
            return options
        },
        handlePageChange(val) {
            this.tablePage.currentPage = val
            // this.loadSelectOptions()
        }
    }
}
</script>


<style lang="scss" scoped></style>