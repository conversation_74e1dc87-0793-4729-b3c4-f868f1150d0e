<template>
    <div v-if="formRenderConfig.formItems && formRenderConfig.formItems.length>0">
        <el-form ref="originFormRef" :rules="formRenderConfig.formRules"  :model="formRenderConfig.formData" label-width="110px">
                <div class="flex flex-wrap">
                    <template v-for="(item) in formRenderConfig.formItems">
                        <template v-if="item.controlType=='checkbox'">
                            <!-- 勾选框 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`"  v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                    <el-checkbox
                                    :disabled="!!item.iisReadOnly||disabledAll"
                                    :paramsItem="item"
                                    @change="changeEvent_common(item, $event)" :style="[{ width: formItemWidth + 'px' }]" v-model="formRenderConfig.formData[item.field]">
                                    </el-checkbox>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='select'">
                            <!-- 下拉框 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <selectRender 
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item" @changeEvent="changeEvent_select(item, $event)" 
                                :style="[{ width: formItemWidth + 'px' }]" 
                                :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]" 
                                :currentValueText="formRenderConfig.formData[item.field]"
                                >
                            </selectRender>
                            <!-- {{formRenderConfig.formData[item.field.replace('Text', '')]}}//{{formRenderConfig.formData[item.field]}} -->
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='selectTable'">
                            <!-- 下拉框 类型 selectTable -->
                             <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                            <selectTableRender 
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item" @changeEvent="changeEvent_select(item, $event)" 
                                :style="[{ width: formItemWidth + 'px' }]" 
                                :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]" 
                                :currentValueText="formRenderConfig.formData[item.field]"
                                >
                            </selectTableRender>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='selectTree'">
                            <!-- 下拉框 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`"  v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                            <selectTreeRender 
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item" @changeEvent="changeEvent_select(item, $event)" 
                                :style="[{ width: formItemWidth + 'px' }]" 
                                :currentValue="formRenderConfig.formData[item.field.replace('Text', '')]" 
                                :currentValueText="formRenderConfig.formData[item.field]"
                                >
                            </selectTreeRender>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='popTable'">
                            <!-- 表格弹框 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`"  v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <popTableRender
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item"
                                 :style="[{ width: formItemWidth + 'px' }]" 
                                  @changeEvent="changeEvent_popTable(item, $event)"
                                 v-model="formRenderConfig.formData[item.field]">
                                ></popTableRender>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='switch'">
                            <!-- 开关 -->
                             <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <el-switch
                                    :paramsItem="item"
                                    :disabled="!!item.iisReadOnly||disabledAll"
                                    :style="[{ width: formItemWidth + 'px' }]"
                                    v-model="formRenderConfig.formData[item.field]"
                                    @change="changeEvent_common(item, $event)"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949">
                                </el-switch>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='textarea'">
                            <!-- 文本域 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                
                                <el-input 
                                    :disabled="!!item.iisReadOnly||disabledAll"
                                    :paramsItem="item" :style="[{ width: formItemWidth + 'px' }]" type="textarea" :rows="2"  placeholder="请输入内容" @change="changeEvent_common(item, $event)" v-model="formRenderConfig.formData[item.field]">
                                </el-input>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='number'">
                            <!-- 计数器 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                    <el-input-number 
                                    :disabled="!!item.iisReadOnly||disabledAll"
                                    :paramsItem="item" :style="[{ width: formItemWidth + 'px' }]" v-model="formRenderConfig.formData[item.field]" @change="changeEvent_common(item, $event)">
                                </el-input-number>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='dateTime'">
                            <!-- 日期时间选择器 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`"  v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <el-date-picker
                                      :paramsItem="item"
                                      :disabled="!!item.iisReadOnly||disabledAll"
                                      :style="[{ width: formItemWidth + 'px' }]"
                                    v-model="formRenderConfig.formData[item.field]"
                                    type="datetime" 
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    @change="changeEvent_common(item, $event)"
                                    placeholder="选择日期时间"
                                   >
                               </el-date-picker>
                             </el-form-item>
                        </template>
                        <template v-else-if="item.controlType=='date'">
                            <!-- 选择日期 类型 -->
                             <el-form-item :label-width="`${item.labelWidth}px`"  v-if="!!item.iisShowEdit"  :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <el-date-picker
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item"
                                :style="[{ width: formItemWidth + 'px' }]"
                                    v-model="formRenderConfig.formData[item.field]"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    @change="changeEvent_common(item, $event)"
                                    placeholder="选择日期"
                                   >
                               </el-date-picker>
                             </el-form-item>
                        </template>
                        <template v-else>
                              <!-- 其它文本输入框 v-if="!!item.iisShowEdit"-->
                            <el-form-item :label-width="`${item.labelWidth}px`" v-if="!!item.iisShowEdit" :text-class-name="item.field + item.title" :label="!!item.title?item.title:item.field" :prop="item.field" :key="item.field">
                                <el-input
                                :disabled="!!item.iisReadOnly||disabledAll"
                                :paramsItem="item"
                                 :style="[{ width: formItemWidth + 'px' }]" 
                                  @change="changeEvent_common(item, $event)"
                                 v-model="formRenderConfig.formData[item.field]">
                                </el-input>
                             </el-form-item>
                        </template>
                    </template>
                 
                </div>
        </el-form>
    </div>
</template>
<script>
/**
 * @desc  表单渲染通用控件
 *
 * @params 参数
 * 
 * <AUTHOR> Huang
 *
 * @created 2022/10/17
 */
 import popTableRender from "@/components/basic/popTableRender.vue"
import selectRender from "@/components/basic/selectRender.vue"
import selectTableRender from "@/components/basic/selectTableRender.vue"
import selectTreeRender from "@/components/basic/selectTreeRender.vue"

export default {
    name:"formItemRender",
    components:{selectRender,selectTableRender,selectTreeRender,popTableRender},
    props:{
        // 控件默认宽度 209
        formItemWidth:{
            type:Number,
            default:209,
        },
         // 是否加载中
         isLoading:{
            type:Boolean,
            default:false,
        },
        // 是否禁用所有控件
        disabledAll:{
            type:Boolean,
            default:false,
        },
         // 弹框窗体是否已经显示
        showEditBox:{
            type:Boolean,
            default:false,
        },
        // 表单配置信息
        formRenderConfig:{
            type:Object,
            default(){
                return {
                    formData:{}, // 表单字段
                    formRules:{}, // 表單驗證規格
                    formItems:[] //表单字段渲染条件和默认值
                }
            }
        },
      
    },
    data(){
        return {
           // form: {}
          }
    },
    watch:{
        // showEditBox(n,o){
        //     console.log('===watch showEditBox====')
        // }
    },
    beforeMount(){
         //let tt = this.formRenderConfig
         //debugger
    },
    methods:{
        // 简单表格弹框 选中回调事件
        changeEvent_popTable(item,event){
            //debugger
            // 转换字段 
            let matchFieldList = event.matchField.split('|')
            let row = event.row
            let oldFieldsStr = matchFieldList[0]
            let newFieldsStr = matchFieldList[1]
            if(oldFieldsStr.includes(',')){
                // 多个字段 赋值 eg:CTABLE_NAME,CTID|CDISPLAY_NAME,CID【表单字段|弹框返回字段】
                let oldFieldsList = oldFieldsStr.split(',')
                let newFieldsList = newFieldsStr.split(',')
                oldFieldsList.forEach((item,index)=>{
                    let newItemName = newFieldsList[index] // 根据下标获取新字段名
                    this.formRenderConfig.formData[item] = row[newItemName]
                })

            }else{
                // 单个字段 赋值  ==》弹框数据 赋值 到 表单数据 eg:CTABLE_NAME|CDISPLAY_NAME
                this.formRenderConfig.formData[oldFieldsStr] = row[newFieldsStr]
            }
        },
        //下拉框 触发事件
        changeEvent_select(item,event){
            if(!event){
                return
            }
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: event.value,
                text:event.text,
                title: item.title,
            };
            this.formRenderConfig.formData[item.field.replace('Text', '')] = event.value
            this.formRenderConfig.formData[item.field] = event.text
            this.changeEvent(params)
        },
        //輸入框，勾选框，计数器 选择日期 触发事件
        changeEvent_common(item,event){
            //debugger
            let params = {
                field: item.field,
                componentType: item.controlType,// 注意此处 控件类型
                eventType: "change",
                value: event,
                title: item.title,
            };
            this.changeEvent(params)
        },
        // 通用事件改變 回調
        changeEvent(data){
            let params={
                data,
                form:this.formRenderConfig.formData
            }
            this.$emit("changeEvent",params)
        }
    }
}
</script>
<style scoped lang="scss">
    
</style>