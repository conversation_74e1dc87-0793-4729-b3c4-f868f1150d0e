<template>
  <div>
      <vxe-grid :scroll-y="{enabled: true,gt: 40}" ref="csoftiTableRef" v-bind="$attrs" v-on="$listeners">
          <!-- 重写插槽 empty -->
          <template v-slot:empty>
            <template v-if="$slots.empty">
              <slot name="empty"></slot>
            </template>
          </template>
          <template #inputBox_filter="{ column, $panel }">
                <input type="type" v-for="(option, index) in column.filters" :key="index" v-model="option.data" @input="$panel.changeOption($event, !!option.data, option)" @keyup.enter="enterFilterEvent({ column, $panel })">
          </template>
          <!-- <template v-slot:operate>
            <template v-if="$slots.operate">
              <slot name="operate"></slot>
            </template>
          </template> -->
          <template v-slot:operate="{ row }">
            <template v-if="$scopedSlots.operate">
            <!-- 自定义 操作 按钮 -->
            <slot :row="row" name="operate"></slot>
          </template>
        </template>
        <template v-slot:dragColumn="{ row }">
          <template v-if="$scopedSlots.dragColumn">
            <!-- 自定义 操作 按钮 -->
            <slot :row="row" name="dragColumn"></slot>
          </template>
        </template>
          <!-- 重写插槽 form -->
          <template v-slot:form>
            <template v-if="$slots.form">
              <slot name="form"></slot>
            </template>
          </template>
          <!-- 重写插槽 toolbar -->
          <template v-slot:toolbar>
            <template v-if="$slots.toolbar">
              <slot name="toolbar"></slot>
            </template>
          </template>
          <!-- 重写插槽 top -->
          <template v-slot:top>
            <template v-if="$slots.top">
              <slot name="top"></slot>
            </template>
          </template>
          <!-- 重写插槽 bottom -->
          <template v-slot:bottom>
            <template v-if="$slots.bottom">
              <slot name="bottom"></slot>
            </template>
          </template>
          <!-- 重写插槽 pager -->
          <template v-slot:pager>
            <template v-if="$slots.pager">
              <slot name="pager"></slot>
            </template>
          </template>
      </vxe-grid>
  </div>
</template>
  <script>
    export default {
      name: "vxe-grid-template",// 表格模板
      props: {},
      data() {
        return {};
      },
      methods: {
        enterFilterEvent ({ $panel, column }, event) {
        if (event.keyCode === 13) {
          console.log('筛选回车事件')
          $panel.confirmFilter()
        }
    },
      },
    };
</script>
  <style lang="scss" scoped>
</style>
  