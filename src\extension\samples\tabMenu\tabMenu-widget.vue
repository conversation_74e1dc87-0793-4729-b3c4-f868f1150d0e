<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <div style="border:1px solid #e8eaec;margin-right: 1px;" :style="{height:(contentBoxHeight-2)+'px'}">
      <div  :publicAttribute="publicAttribute" style="color:#606266;background-color: #fff;" class="flex flex-col overflow-hidden justify-start items-center " :style="{maxHeight:(contentBoxHeight-2)+'px'}">
          <div style="margin-top: 10px;"></div>
          <div v-html="tabsMemuStyle"></div> 

          <div @click="buttonOnClickEvent(item, index, $event)" v-for="(item,index) in tabMenuItemsList" style="cursor: pointer;  height: 40px;padding-left: 10px; " :style="{color: (currentSelected == item.fieldName ? '#fff':item.fontColor),backgroundColor:(currentSelected == item.fieldName ? activeItemColor:'')}" class="flex overflow-hidden seletedItem  items-center  w-full">
            <i :style="{color:item.iconColor,fontSize: item.iconSize +'px'}" style="" :class="item.iconUrl"></i> 
             <div class="ml-2">{{item.title}}</div>
          </div>
    </div>
    </div>
    
  </static-content-wrapper>
</template>

<script>
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
// const XEUtils = require('xe-utils') // vxeTable 通用库

export default {
  name: "tabMenu-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['getPageInstance', 'sourceVFormRenderState'],
  components: {
    StaticContentWrapper,
  },
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },
    previewState: { //是否表单预览状态
      type: Boolean,
      default: false
    },
    // 控件来源父集 add by andy
    contentBoxHeight: {
      type: [Number, String],
      default: 0
    },
    // 控件来源父集 add by andy
    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  data() {
    return {
      tabsMemuStyle:"",
      dataObj: {},
      currentSelected:"tabMenu1",
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
      publicAttribute:{
          value:"",
        },// 对外开发属性值
    }
  },
  computed: {
    activeItemColor(){
      let defaultColor= 'lightsalmon'
      if(this.field.options.activeColor){
        defaultColor = this.field.options.activeColor
      }
      return defaultColor
    },
    pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
    tabMenuItemsList() {
      let _list = this.field.options.tabMenuItems.filter(item => {
        //debugger
        if (!!item.check) {
          return item
        }
      })
      return _list
    },
    containerBoxHeight(){
      //debugger
      let testHeight = 70
      if (this.designState) {
        // 预览状态下，固定高度分配
        return testHeight 
      } else {
        if(!!this.field.options.height){
          return parseInt(this.field.options.height)
        }else{
          return  parseInt(this.contentBoxHeight) -2 //this.contentBoxHeight
        }
        
      }
    }

  },
  mounted() {
    let _self = this
    this.$nextTick(() => {
      setTimeout(() => {
        //_self.tryTillGetData()
        this.getTabSettingColor()
      }, 300)
    })
  
  },

  created() {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
     // 数据类型来源》》extension-schema.js
     buttonOnClickEvent(subItem) {
      // debugger
       this.currentSelected = subItem.fieldName
       //console.log("buttonOnClickEvent==currentSelected=:",this.currentSelected)
       this.handleChangeEvent(subItem)
    },
    getTabSettingColor(){
      // debugger
      this.tabsMemuStyle =""
      if(!!this.field.options.hoverColor){
            this.tabsMemuStyle =`<style type="text/css">
              .seletedItem:hover {
                  background-color: ${this.field.options.hoverColor} !important;
                  color: #fff !important;
                }
            </style>`
        }else{
          this.tabsMemuStyle =`<style type="text/css">
              .seletedItem:hover {
                  background-color: lightsalmon !important;
                  color: #fff !important;
                }
            </style>`
        }
        
    },
    // 对外暴露事件，重新加载
    async reSearchData(paramsOptions = {}) {
      //await this.tryTillGetData() 
    },
    getDataByFieldName(fieldName=''){
             if( this.dataObj && Object.keys(this.dataObj).length>0){
                if(this.dataObj.hasOwnProperty(fieldName)){
                  let formatValue = this.dataObj[fieldName]
                  return formatValue
                }
             } 
        },
    // 触发控件列表>>触发事件 >>循环直到获取到数据
    async tryTillGetData() {
      // 干正事
      let _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.field.options.actionName);
      if (_dataList && _dataList.length > 0) {
        this.dataObj = _dataList[0]
      }
    },
  }
}
</script>

<style lang="scss" scoped>
// .seletedItem:hover{
//   background-color: lightsalmon;
//   color: #fff !important;
// }
</style>