<template>
    <el-select @change="changeEvent" v-model="fieldModel_select" @focus="focusEvent" filterable clearable size="mini" v-bind="$attrs" v-on="$listeners"
        placeholder="请选择">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
    </el-select>
</template>
<script>
import baseMixin from './minxin'
export default {
    name: "reportCustomSelect",
    mixins:[baseMixin],
    props: {
        defaultValue:String, // 默认值
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: String,// 当前查询字段名称
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            fieldModel_select: "",
            loading: false,
            configFormItem: null,
            options: [],
            selectConfig: {
                key: "",
                text: ""
            }
        }
    },
    mounted(){
        this.init()
        console.log("options:",this.options)
        this.focusEvent()
        if(this.defaultValue){
            // 设置默认值
            this.fieldModel_select = this.defaultValue
        }
    },
    methods: {
        
        focusEvent(params) {
            // debugger
            // 初始化 下拉配置信息
            if (this.options.length == 0) {
                if (this.configOptions) {
                    this.configFormItem = this.configOptions
                    if (this.configFormItem.hasOwnProperty('CDATAS') && !!this.configFormItem.CDATAS) {
                        this.getSelectKeyText(this.configFormItem.CDATAS)
                    }
                }
                this.loadSelectOptions()
            }
        },
        // 获取配置的KEY & TEXT
        getSelectKeyText(dataList) {
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (!!item.isSelectTextField) {
                        this.selectConfig.text = item.field
                    }
                    if (!!item.isSelectKeyField) {
                        this.selectConfig.key = item.field
                    }
                })
            } else {
                this.selectConfig = {
                    key: "",
                    text: ""
                }
            }
        },
        // 设置下拉初始化功能
        loadSelectOptions() {
            let optionList = [{ value: "", label: "全部" }]
            if (this.configFormItem.CDATAS && this.configFormItem.CDATAS.length > 0) {
                this.configFormItem.CDATAS.forEach(oldItem => {
                    let newItem = {
                        value: oldItem.field,
                        label: !!oldItem.title ? oldItem.title : oldItem.field
                    }
                    optionList.push(newItem)
                })
            }
            this.options = optionList
        },
        // 格式化返回数据
        formatDatas(dataList) {
            let options = [{ value: "", label: "全部" }]
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    if (!!oldItem[this.selectConfig.text]) {
                        let newItem = {
                            value: oldItem[this.selectConfig.key],
                            label: oldItem[this.selectConfig.text]
                        }
                        options.push(newItem)
                    }

                })
            }
            return options
        }
    }
}

</script>