<!--
 * @Author: hufoyang
 * @Date: 2022-10-10 17:36:00
 * @LastEditTime: 2022-11-10 08:22:03
 * @LastEditors: foyang
 * @Description: 
 * @FilePath: \my-project\src\components\pagination\index.vue
 * 版权声明
-->
<template>
  <div class="pagerContainer">
    <vxe-pager
      background
      size="mini"
      v-bind="$attrs" v-on="$listeners"
      :total="Number(tablePage.total)"
      :page-size.sync="tablePage.pageSize"
      :current-page.sync="tablePage.currentPage"
      :layouts="
        ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']"
      @page-change="pageChange"
    >
    </vxe-pager>
  </div>
</template>

<script>

export default {
  name: 'vxePagerCom',
  props: {
    tablePage: {
      type: Object,
      default() {
        return {
          total: 0,
          currentPage: 1,
          pageSize: 10
        }
      }
    },
    totalResult: {
      type: Number
    },
    currentPage: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    background: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {},
  mounted() {
    // this.findList()
  },
  methods: {
    pageChange({ type, currentPage, pageSize, $event }) {
      this.$emit('pageChange', { type, currentPage, pageSize, $event})
      // this.pageFilter()
    },
    pageFilter() { 
      this.$parent.$parent.$parent.$refs.csoftiTableRef.loading = true
      setTimeout(() => {
        this.$parent.$parent.$parent.$refs.csoftiTableRef.loading = false
        let list = this.$parent.$parent.$parent.$refs.csoftiTableRef.getTableData()
        this.tablePage.total = list.length
        list.slice((this.tablePage.currentPage - 1) * this.tablePage.pageSize, this.tablePage.currentPage * this.tablePage.pageSize)
        this.$parent.$parent.$parent.$refs.csoftiTableRef.loadColumn(this.tableData)
      }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
.pagerContainer {
  width: 100%;
}
</style>
