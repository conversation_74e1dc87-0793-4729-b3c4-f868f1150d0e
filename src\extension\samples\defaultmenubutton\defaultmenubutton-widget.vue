<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <!-- 非首个容器，不需要置顶10PX -->
    <div :style="[{ 'background-color':field.options.bgColor,'color':field.options.textColor,'margin-top': (!!field.options.cleanMarginTop10PX ? 0 : 10) + 'px'}]"
      class="flex actionButtonSubClass"
      :class="[!!field.options.showMenuLeft ?'justify-start':'justify-between']"
      >
      <!-- 权限按钮 -->
      <div class="flex justify-start">
        <el-button v-show="$route.fullPath.includes('returnurl')" @click="goBackEvent()" type="primary" size="small">
          返回</el-button>
        <el-button :disabled="getDisabledOptions(item, index)" @click="buttonOnClickEvent(item, index)" v-show="item.check" :publicAttribute="publicAttribute"
          :size="!!item.size ? item.size : 'mini'" :title="item.label" :type="!!item.type ? item.type : ''"
          :icon="!!item.icon ? item.icon : ''" :key="index + item.label"
          v-for="(item, index) in field.options.menuButtonOptionItems">
          <span v-if="!item.showIconOnly">&nbsp;{{ item.label }}</span>
        </el-button>
        <el-button v-show="$route.fullPath.includes('retrunPath') && sourceVFormRender == ``"
          @click="jumpToPath('/designlist')" type="text" size="small">返回</el-button>
      </div>

       <div style="margin-right:0px" class="flex items-center justify-end">
        <div v-show="item.check" ref="searchGroupRef" class="flex items-center" :key="index + item.fieldName"
          v-for="(item, index) in field.options.searchInputItems">
          <!-- 通用查询 -->
          <span :style="{fontSize:!!field.options.fontSize?field.options.fontSize+'px':'12px' }">{{ item.label }}</span>
          <template v-if="(item.controlType == 'date')">
            <!-- 日期选择-->
            <reportDateAndTime  @changeEvent="changeEvent(item,$event)" :publicAttribute="publicAttribute"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}"
              v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :paramsItem="item"
              :controlType="item.controlType" :searchForm="searchInputForm" :fieldName="item.fieldName"
              :configOptions="item.searchItemConfig" :placeholder="item.placeholder" value-format="yyyy-MM-dd" type="date"
              :clearable="true">
            </reportDateAndTime>
          </template>
          <template v-else-if="(item.controlType == 'datetime'||item.controlType == 'dateTime')">
            <!-- 日期时间选择-->
            <reportDateAndTime @changeEvent="changeEvent(item,$event)" :publicAttribute="publicAttribute"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}"
              v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :paramsItem="item"
              :controlType="item.controlType" :searchForm="searchInputForm" :fieldName="item.fieldName"
              :configOptions="item.searchItemConfig" :placeholder="item.placeholder" type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss" :clearable="true">
            </reportDateAndTime>
          </template>
          <template v-else-if="(item.controlType == 'daterange')">
            <!-- 日期范围选择-->
            <reportDateAndTimeRange @changeEvent="changeEvent(item,$event)" :publicAttribute="publicAttribute" style="margin: 0px 10px;" :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'240px'}"
              v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :paramsItem="item"
              :controlType="item.controlType" :defaultValue="item.defaultValue" :searchForm="searchInputForm" :fieldName="item.fieldName"
              :configOptions="item.searchItemConfig" :placeholder="item.placeholder" value-format="yyyy-MM-dd" type="daterange"
              :clearable="true">
            </reportDateAndTimeRange>
          </template>
          <template v-else-if="(item.controlType == 'datetimerange')">
            <!-- 日期时间范围选择-->
            <reportDateAndTimeRange  @changeEvent="changeEvent(item,$event)" :publicAttribute="publicAttribute" style="margin: 0px 10px;" :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'280px'}"
              v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :paramsItem="item"
              :controlType="item.controlType" :defaultValue="item.defaultValue" :searchForm="searchInputForm" :fieldName="item.fieldName"
              :configOptions="item.searchItemConfig" :placeholder="item.placeholder" type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss" :clearable="true">
            </reportDateAndTimeRange>
          </template>
          <template v-else-if="item.controlType == 'reportSelect'">
            <reportSelect :refreshRandom="refreshRandom" @changeEvent="changeEvent(item,$event)"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" :paramsItem="item" :ref="item.fieldName"
              :controlType="item.controlType" :defaultValue="item.defaultValue" v-model="searchInputForm[item.fieldName]" :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig"></reportSelect>
          </template>
          <template v-else-if="item.controlType == 'reportSelectTable'">
            <reportSelectTable :refreshRandom="refreshRandom" @changeEvent="changeEvent(item,$event)"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" :paramsItem="item" :ref="item.fieldName"
              :controlType="item.controlType" :defaultValue="item.defaultValue"  v-model="searchInputForm[item.fieldName]" :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig"></reportSelectTable>
          </template>
          <template v-else-if="item.controlType == 'reportSelectTree'">
            <reportSelectTree :refreshRandom="refreshRandom" @changeEvent="changeEvent(item,$event)"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" :paramsItem="item" :ref="item.fieldName"
              :controlType="item.controlType" :defaultValue="item.defaultValue" v-model="searchInputForm[item.fieldName]"  :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig"></reportSelectTree>
          </template>
          <template v-else-if="item.controlType == 'customSelect'">
            <reportCustomSelect :refreshRandom="refreshRandom" @changeEvent="changeEvent(item,$event)"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" :paramsItem="item" :ref="item.fieldName"
              :controlType="item.controlType" :defaultValue="item.defaultValue"  v-model="searchInputForm[item.fieldName]" :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig"></reportCustomSelect>
          </template>
          <template v-else-if="item.controlType == 'customMulSelect'">
            <reportCustomMulSelect :refreshRandom="refreshRandom" @changeEvent="changeEvent(item,$event)"  style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" :paramsItem="item" :ref="item.fieldName"
              :controlType="item.controlType" :defaultValue="item.defaultValue" v-model="searchInputForm[item.fieldName]" :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig"></reportCustomMulSelect>
          </template>
          
          <template v-else-if="item.controlType == 'number'">
            <!-- 数字输入框-->
            <reportNumberInput @changeEvent="changeEvent(item,$event)" :publicAttribute="publicAttribute" :paramsItem="item" :controlType="item.controlType"
              :searchForm="searchInputForm" :fieldName="item.fieldName" :configOptions="item.searchItemConfig"
               style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" @keyup.native="inputEnterEvent(item, index, $event)" :clearable="true"
              size="mini" v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :placeholder="item.placeholder">
            </reportNumberInput>
          </template>

          <template v-else>
            <!-- 普通输入框 -->
            <reportStringInput @changeEvent="changeEvent(item,$event)" :paramsItem="item" :controlType="item.controlType" :searchForm="searchInputForm"
              :fieldName="item.fieldName" :configOptions="item.searchItemConfig" :publicAttribute="publicAttribute"
              style="margin: 0px 10px;"  :style="{width:!!item.ctrlWidth?item.ctrlWidth+'px':'180px'}" @keyup.native="inputEnterEvent(item, index, $event)" :clearable="true"
              size="mini" v-model="searchInputForm[item.fieldName]" :ref="item.fieldName" :placeholder="item.placeholder">
            </reportStringInput>
          </template>

        </div>

        <!-- 搜索按钮 -->
        <el-button @click="buttonOnClickEvent(item, index, $event)" v-show="item.check" :ref="item.fieldName"
          :publicAttribute="publicAttribute" :size="!!item.size ? item.size : 'small'" :title="item.label"
          :loading="item.isLoadding" :type="!!item.type ? item.type : 'primary'"
          :icon="!!item.icon ? item.icon : 'el-icon-search'" :key="index + item.value"
          v-for="(item, index) in field.options.searchBttonItems">
        </el-button>
        <div v-show="!!field.options.showSearchPro" style="margin: 0px 10px;">
          <el-button 
           @click="advancedQuery()"
            title="高级查询"
            type="primary"
            size="small"
            icon="el-icon-more" >
        </el-button>
        </div>
      </div>
    </div>

  </static-content-wrapper>
</template>
  
<script>

import dayjs from 'dayjs'
import reportStringInput from '@/components/basic/search/reportStringInput'
import reportNumberInput from '@/components/basic/search/reportNumberInput'
import reportDateAndTime from '@/components/basic/search/reportDateAndTime'
import reportDateAndTimeRange from '@/components/basic/search/reportDateAndTimeRange'
import reportSelect from '@/components/basic/search/reportSelect'
import reportCustomSelect from '@/components/basic/search/reportCustomSelect'
import reportCustomMulSelect from '@/components/basic/search/reportCustomMulSelect'
import reportSelectTable from '@/components/basic/search/reportSelectTable'
import reportSelectTree from '@/components/basic/search/reportSelectTree'
import StaticContentWrapper from '@/components/form-designer/form-widget/field-widget/static-content-wrapper'
import emitter from '@/utils/emitter'
import i18n from "@/utils/i18n"
import {
  useDebounceFn
} from '@vueuse/core'
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin"
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormApi } from "@/hooks/useHandleVFormApi"
import { useHandleVFormPopup } from "@/hooks/useHandleVFormPopup"
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
import { useHandleVFormComponents } from "@/hooks/useHandleVFormComponents"
// import request from '@/libs/request'
export default {
  name: "defaultmenubutton-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    reportDateAndTimeRange,reportSelectTree,StaticContentWrapper, reportSelect, reportCustomSelect,reportCustomMulSelect, reportSelectTable, reportDateAndTime, reportNumberInput, reportStringInput
  },
  mixins: [emitter, fieldMixin, i18n],
  // 注入列表页面整体实体 
  inject: ['getPageInstance', 'sourceVFormRenderState'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    // 控件来源父集 add by andy
    sourceVFormRender: {
      type: String,
      default: ""
    },
    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },

  },
  data() {
    return {
     
      refreshRandom:null, // 刷新
      windowFullPath: window.location.search,
      useFormatParamsFn: useFormatParams(this),
      useHandleVFormApiFn: useHandleVFormApi(this),
      useHandleVFormPopupFn: useHandleVFormPopup(this),
      useHandleVFormEventFn: useHandleVFormEvent(this),
      useHandleVFormComponentsFn: useHandleVFormComponents(this),// 注意这里 不再试this 而是顶层的vueInstance
      searchInputForm: {},// 搜索框表单
      publicAttribute: {
        // value:""
      },// 对外开发属性值
    }
  },
  computed: {
    standardBulletBoxChange_State() {
      return this.$store.state.standardBulletBoxChange
    },
    pageInstance() {
      // 获取列表示例
      return this.getPageInstance()
    },
    searchProDrawerBoxChange(){
      return this.$store.state.searchProDrawerBoxChange
    }
  },
  watch:{
    // 高级查询触发
    searchProDrawerBoxChange:{
      handler(n,o){
        console.log("===高级查询触发=====")
        this.searchAction()
      },
      deep:true,
      immediate:false,
    },
    standardBulletBoxChange_State: {
      handler(n, o) {
        // 回调函数
        let params = n.value
        if(params.fieldName && params.fieldName ==this.field.id && this.isCurrentPagePathID()){

          this.standardBulletBoxChangeFn(params)
        }
      },
      deep: true
    }
  },


  created() {
    this.registerToRefList()
    this.initEventHandler()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
  mounted() {
    this.debouncedFn_searchAction = useDebounceFn((val) => {
      this.searchAction(val)
    }, 300)
    setTimeout(() => {
      this.setSearchInputForm()
    }, 1)
    // 加载后立即触发
    if(!!this.field.options.execAfterDone){
        setTimeout(()=>{
          if (document.readyState === 'complete'){
             this.searchAction()
          }else{
            setTimeout(()=>{
              if (document.readyState === 'complete'){
                this.searchAction()
              }
            },1000)
          }
        },1000)
    }
  },
  methods: {
    // 对外暴露事件，重新加载
    async reSearchData(params) {
         console.log("========defaultmenubutton-=====reSearchData=====")
         this.refreshRandom = Math.floor(Math.random() * 10000 + 1)
    },
    // 标准弹框选择数据后的回到函数
   async standardBulletBoxChangeFn(params){
      let subItem = params.dataObj
      let treeNode = this.getCustomTreeNodeKey()
      let standarBoxData =  JSON.parse(params.postData) //params.postData = "{"data":[{"key":"Luoji03","label":"03# 锣机"}],"originData":[{"CDEVICE_NO":"Luoji03","CDEVICE_NAME":"03# 锣机","CDEVICE_TIME":"2023-02-23T23:59:39","CMACHINE":"Luoji03","CEVENT_NO":"E0405","CEVENT_CONTENT":"主軸 #4  刀具在筒夾內","_X_ROW_KEY":"row_13"}],"matchConfig":{"key":"CDEVICE_NO","label":"CDEVICE_NAME"}}"
      let formatData = ""
       standarBoxData.data.forEach(item=>{
        if(formatData==""){
          formatData = item.key
        }else{
          formatData =formatData+"¤"+item.key
        }
       })
      let postData={
        CID:treeNode.CID,
        CIDS:formatData
      }
      if(!postData.CID){
         this.$message({
            message: '请选择节点！',
            type: 'error'
          });
          return
      }
      if(!postData.CIDS){
          return
      }
     await this.useHandleVFormEventFn.executApi_standardBulletBox(subItem,postData)
     // 刷新页面

    },
    getCustomTreeNodeKey() {
      let _TreeNode = null
      try {
        let cachekey = this.$route.fullPath+'_'+"customTree"
        _TreeNode = this.$store.state.customTreeNode.value[cachekey] || null
      } catch (error) {
        _TreeNode = null
      }
      return _TreeNode
    },
     // 高级搜索
     advancedQuery() {
      if(!!this.field.options.searchProSettingID){
        this.$actions.setGlobalState({
                drawerShow: {
                    drawerShow: true,
                    tableId: this.field.options.searchProSettingID,// CID
                    CPARENT: this.field.options.searchProSettingCPARENT,// 组别ID
                    closeAfterSearchPro:this.field.options.closeAfterSearchPro,// 搜索后是否关闭
                },
                type: "drawerShow",
            });
      }else{
        this.$message({
            message: '暂未配置高级查询信息，请联系管理员配置！',
            type: 'error'
          });
      }  
    },
    getDisabledOptions(item, index){
      // debugger
      let flag =  false
      let paramsData = {
        index: index,
        buttonOptions: item,
      }
      let onBeforeDisabledStr = this.useFormatParamsFn.getOtherParamsValue("onBeforeDisabled", item)
      let onBeforeDisabled = new Function('paramsData', onBeforeDisabledStr)
      if (!!onBeforeDisabledStr) {
        //debugger
        try {
          flag = onBeforeDisabled.call(this, paramsData)
        } catch (error) {
          this.$message({
            message: '(defaultmenubutton===onBeforeDisabled) 错误，请检查！！！',
            type: 'error'
          });
          return false
        }
      }
      return flag
    },
    // 查询值改变，是否立即触发查询
    changeEvent(item,e){
     // debugger
      if(item.hasOwnProperty("execWhenChange") && !!item.execWhenChange && e.type!='init'){
        let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", item)
        if(!actionType || actionType=="-1"){
             // 如果没有配置其它事件，默认执行查询动作
            this.debouncedFn_searchAction()   
        }else{
          this.buttonOnClickEvent(item)
        }
      }
    },
    goBackEvent() {
      let returnurl = this.getRequest("returnurl",true)
      this.$parentRouter.push(returnurl)
    },
    getRequest(name,fullPath=false) {
      let urlStr = fullPath?this.$route.fullPath:window.location.search
      let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      let r = urlStr.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      };
      return null;
    },
    /// 设置查询字段的默认值
    // 时间范围默认格式：curdate#curdate，subtract-1-month#subtract-1-month，add-1-day#add-1-day，#作为分割符号
    // 注意：(如时间默认值 当前日期:curdate 当前日期-1月:subtract-1-month 当前日期+1天:add-1-day 当前日期-1年:add-2-year)
    setSearchInputForm() {
      this.searchInputForm = {}
      let dataList = this.field.options.searchInputItems
      // debugger
      if (dataList) {
        dataList.forEach(item => {
          if (!!item.defaultValue && !!item.check) {
            //debugger
            console.log('item.controlType',item.controlType)
            switch (item.controlType) {
              case "date":
              case "datetime":
              case "dateTime":
              case 'month':
                //debugger
                let newDefaultVal = this.setDateTimeDefaultVal(item.controlType, item.defaultValue)
                this.$set(this.searchInputForm, item.fieldName, newDefaultVal)
                break
                case "daterange":
                case "datetimerange":
                  //debugger
                let newDefaultVal_range = this.setDateTimeRangeDefaultVal(item.controlType, item.defaultValue)
                this.$set(this.searchInputForm, item.fieldName, newDefaultVal_range)
                break

              case 'number':
                let newDefaultVal2 = Number(item.defaultValue)
                this.$set(this.searchInputForm, item.fieldName, newDefaultVal2)
               
                break
              default:
                this.$set(this.searchInputForm, item.fieldName, item.defaultValue)
              
                break
            }
          } else {
            this.$set(this.searchInputForm, item.fieldName, "")
            //this.searchInputForm[item.fieldName] = ""
          }
        })
      }
      return this.searchInputForm
    },
    // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
    setDateTimeDefaultVal_V1(type, defaultVal) {
      let dValue = null
      let dateFormatStr = ""
      // 指定日期 格式化 格式
      switch (type) {
        case "date":
          dateFormatStr = 'YYYY-MM-DD'
          break;
        case "dateTime":
          dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
          break;
        case 'month':
          dateFormatStr = 'YYYY-MM'
          break
        default:
          dateFormatStr = ""
          break
      }
      if (!!defaultVal) {
        if (defaultVal == 'curdate') {
          // 当前日期
          dValue = dayjs().format(dateFormatStr);
        } else if (defaultVal.includes('-')) {
          // 指定日期加减
          //dayjs().add(7, 'day').format('YYYY-MM-DD');
          //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
          let daysetArray = defaultVal.split('-')
          dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
        } else {
          //空日期
          dValue = null
        }
      }

      return dValue
    },
      // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year)
    setDateTimeDefaultVal(type, defaultVal) {
           // debugger
            let dValue = null
            let dateFormatStr = ""
            // 指定日期 格式化 格式
            switch (type) {
                case "date":
                    dateFormatStr = 'YYYY-MM-DD'
                    break;
                case "datetime":
                case "dateTime":
                    dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                    break;
                case 'month':
                    dateFormatStr = 'YYYY-MM'
                    break
                default:
                    dateFormatStr = ""
                    break
            }
            if (!!defaultVal) {
                if (defaultVal == 'curdate') {
                    // 当前日期
                    dValue = dayjs().format(dateFormatStr);
                }
                else if (defaultVal.includes('$')) {
                    let type = defaultVal.replace('$', '')
                    let typeArray = type.split('-')
                    try {
                        if (typeArray.length > 2) {
                            // 固定函数 1.$startOf-month,2.$startOf-month-add-1-day 3.$endOf-month-add-1-day,4.$endOf-month
                            dValue = dayjs()[typeArray[0]](`${typeArray[1]}`)[typeArray[2]](Number(typeArray[3]), typeArray[4]).format(dateFormatStr)
                        } else {
                            // dayjs().startOf("month").format("YYYY-MM-DD"); //当月的1号
                            dValue = dayjs()[typeArray[0]](`${typeArray[1]}`).format(dateFormatStr)
                        }
                    } catch (error) {
                        //空日期
                        dValue = null
                    }

                }
                else if (defaultVal.includes('-')) {
                    // 指定日期加减
                    //dayjs().add(7, 'day').format('YYYY-MM-DD');
                    //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                    let daysetArray = defaultVal.split('-')
                    if (daysetArray.length == 2) {
                        let arr = daysetArray[1].split(":");
                        dValue = dayjs()
                            .hour(arr[0] || 0)
                            .minute(arr[1] || 0)
                            .second(arr[2] || 0)
                            .format(dateFormatStr);
                    } else {
                        if (!!daysetArray[0]) {
                            dValue = dayjs()
                            [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                .format(dateFormatStr);
                        }


                        if (!!daysetArray[3]) {
                            var arr = daysetArray[3].split(":");
                            dValue = dayjs()
                            [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                .hour(arr[0] || 0)
                                .minute(arr[1] || 0)
                                .second(arr[2] || 0)
                                .format(dateFormatStr);
                        }
                    }
                    // dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                }

                else {
                    //空日期
                    dValue = null
                }
            }
            //debugger
            let _dValue =dayjs(dValue).format(dateFormatStr);
            //debugger
            return  _dValue
        },
    //    // 设置时间范围格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
    // setDateTimeRangeDefaultVal(type, defaultVal) {
    //   const end = new Date();
    //   const start = new Date();

    //   try {

    //     let dValue = [start, end]
    //     let dateFormatStr = ""
    //     let defaultValList = defaultVal.split('#')
    //     // 指定日期 格式化 格式
    //     switch (type) {
    //       case "daterange":
    //         dateFormatStr = 'YYYY-MM-DD'
    //         break;
    //       case "datetimerange":
    //         dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
    //         break;
    //       default:
    //         dateFormatStr = ""
    //         break
    //     }
    //     // 开始时间 和 结束时间
    //     // curdate#curdate
    //     if (!!defaultValList[0]) {
    //       if (defaultValList[0] == 'curdate') {
    //         // 当前日期
    //         dValue[0] = dayjs().format(dateFormatStr);
    //       } else if (defaultValList[0].includes('-')) {
    //         // 指定日期加减
    //         //dayjs().add(7, 'day').format('YYYY-MM-DD');
    //         //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
    //         let daysetArray = defaultValList[0].split('-')
    //         dValue[0] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
    //       } else {
    //         //空日期
    //         dValue[0] = start
    //       }
    //     }
    //     if (!!defaultValList[1]) {
    //       if (defaultValList[1] == 'curdate') {
    //         // 当前日期
    //         dValue[1] = dayjs().format(dateFormatStr);
    //       } else if (defaultValList[1].includes('-')) {
    //         // 指定日期加减
    //         //dayjs().add(7, 'day').format('YYYY-MM-DD');
    //         //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
    //         let daysetArray = defaultValList[1].split('-')
    //         dValue[1] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
    //       } else {
    //         //空日期
    //         dValue[1] = end
    //       }
    //     }
    //     return dValue
    //   } catch (error) {
    //     return [start, end]
    //   }

    // },
    formatLastDateTime(dataTimeString,dateFormatStr){
      let returnDate =''
      let daysetArray = dataTimeString.split('-')
      if (daysetArray.length == 2) {
        let arr = daysetArray[1].split(":");
        returnDate = dayjs()
          .hour(arr[0] || 0)
          .minute(arr[1] || 0)
          .second(arr[2] || 0)
          .format(dateFormatStr);
      } else {
        returnDate = dayjs()
        [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
          .format(dateFormatStr);

        if (!!daysetArray[3]) {
          var arr = daysetArray[3].split(":");
          returnDate = dayjs()
          [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
            .hour(arr[0] || 0)
            .minute(arr[1] || 0)
            .second(arr[2] || 0)
            .format(dateFormatStr);
        }
      }
      return returnDate
    },
     // 设置时间范围格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+2年:add-2-year)
     setDateTimeRangeDefaultVal(type, defaultVal) {
           //debugger
            const end = new Date();
            const start = new Date();

            try {

                let dValue = [start, end]
                let dateFormatStr = ""
                let defaultValList = defaultVal.split('#')
                // 指定日期 格式化 格式
                switch (type) {
                    case "daterange":
                        dateFormatStr = 'YYYY-MM-DD'
                        break;
                    case "datetimerange":
                        dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
                        break;
                    default:
                        dateFormatStr = ""
                        break
                }
                // 开始时间 和 结束时间
                // curdate#curdate
                if (!!defaultValList[0]) {
                    if (defaultValList[0] == 'curdate') {
                        // 当前日期
                        dValue[0] = dayjs().format(dateFormatStr);
                    }
                    else if (defaultValList[0].includes('curdate-')) {
                       dValue[0] = this.formatLastDateTime(defaultValList[0],dateFormatStr);
                    }
                    else if (defaultValList[0].includes('$')) {
                        //debugger
                        let type = defaultValList[0].replace('$', '')
                        let typeArray = type.split('-')
                        try {
                            if (typeArray.length > 2) {
                                // 固定函数 1.$startOf-month,2.$startOf-month-add-1-day 3.$endOf-month-add-1-day,4.$endOf-month
                                dValue[0] = dayjs()[typeArray[0]](`${typeArray[1]}`)[typeArray[2]](Number(typeArray[3]), typeArray[4]).format(dateFormatStr)
                            } else {
                                // dayjs().startOf("month").format("YYYY-MM-DD"); //当月的1号
                                dValue[0] = dayjs()[typeArray[0]](`${typeArray[1]}`).format(dateFormatStr)
                            }
                        } catch (error) {
                            //空日期
                            dValue[0] = null
                        }

                    }
                    else if (defaultValList[0].includes('-')) {
                        // 指定日期加减
                        //dayjs().add(7, 'day').format('YYYY-MM-DD');
                        //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                        let daysetArray = defaultValList[0].split('-')
                        if (daysetArray.length == 2) {
                            let arr = daysetArray[1].split(":");
                            dValue[0] = dayjs()
                                .hour(arr[0] || 0)
                                .minute(arr[1] || 0)
                                .second(arr[2] || 0)
                                .format(dateFormatStr);
                        } else {
                            dValue[0] = dayjs()
                            [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                .format(dateFormatStr);

                            if (!!daysetArray[3]) {
                                var arr = daysetArray[3].split(":");
                                dValue[0] = dayjs()
                                [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                    .hour(arr[0] || 0)
                                    .minute(arr[1] || 0)
                                    .second(arr[2] || 0)
                                    .format(dateFormatStr);
                            }
                        }
                        // dValue[0] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
                    } else {
                        //空日期
                        dValue[0] = start
                    }
                }
                if (!!defaultValList[1]) {
                    if (defaultValList[1] == 'curdate') {
                        // 当前日期
                        dValue[1] = dayjs().format(dateFormatStr);
                    }
                    else if (defaultValList[1].includes('curdate-')) {
                       dValue[1] = this.formatLastDateTime(defaultValList[1],dateFormatStr);
                    }
                    else if (defaultValList[1].includes('$')) {
                        //debugger
                        let type = defaultValList[1].replace('$', '')
                        let typeArray = type.split('-')
                        try {
                            if (typeArray.length > 2) {
                                // 固定函数 1.$startOf-month,2.$startOf-month-add-1-day 3.$endOf-month-add-1-day,4.$endOf-month
                                dValue[1] = dayjs()[typeArray[0]](`${typeArray[1]}`)[typeArray[2]](Number(typeArray[3]), typeArray[4]).format(dateFormatStr)
                            } else {
                                // dayjs().startOf("month").format("YYYY-MM-DD"); //当月的1号
                                dValue[1] = dayjs()[typeArray[0]](`${typeArray[1]}`).format(dateFormatStr)
                            }
                        } catch (error) {
                            //空日期
                            dValue[1] = null
                        }

                    }
                    else if (defaultValList[1].includes('-')) {
                      //debugger
                        // 指定日期加减
                        //dayjs().add(7, 'day').format('YYYY-MM-DD');
                        //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
                        let daysetArray = defaultValList[1].split('-')
                        if (daysetArray.length == 2) {
                            let arr = daysetArray[1].split(":");
                            dValue[1] = dayjs()
                                .hour(arr[0] || 0)
                                .minute(arr[1] || 0)
                                .second(arr[2] || 0)
                                .format(dateFormatStr);
                        } else {
                             dValue[1] = dayjs()
                            [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                .format(dateFormatStr);

                            if (!!daysetArray[3]) {
                                var arr = daysetArray[3].split(":");
                                dValue[1] = dayjs()
                                [daysetArray[0]](Number(daysetArray[1]), daysetArray[2])
                                    .hour(arr[0] || 0)
                                    .minute(arr[1] || 0)
                                    .second(arr[2] || 0)
                                    .format(dateFormatStr);
                            }
                        }
                    } else {
                        //空日期
                        dValue[1] = end
                    }
                }
                return dValue
            } catch (error) {
                return [start, end]
            }

      },
    // 路由跳转
    jumpToPath(path) {
      this.$router.push(
        {
          path: path,
          // query:{
          //     formName 
          // }
        })
    },

    // 输入框回车 执行事件
    inputEnterEvent(item, index, e) {
      // 回车
      if (e.keyCode == 13) {
        let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", item)
        if(!actionType || actionType=="-1"){
             // 如果没有配置其它事件，默认执行查询动作
            this.searchAction()   
        }else{
          this.buttonOnClickEvent(item)
        }
      }
    },

    // 数据类型来源》》extension-schema.js
    buttonOnClickEvent(subItem) {
     // debugger
      let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", subItem) //this.getOtherParamsValue("actionType",item)
      if (!!actionType && actionType != "-1") {
      
        this.useHandleVFormEventFn.handleCommonClickEvent(subItem)
      } else {
      
        if (!!subItem.value && actionType != "-1") {
          // debugger
          // 默认内置功能：iisRefresh刷新 iisAdd 新增 iisEdit 编辑 iisEnable 启用 iisDisabled禁用iisImport 导入 iisView 预览iisExport 导出
          // this.$store.commit("set_actionType",subItem.value)
          let actionType = subItem.value
          switch (actionType) {
            // 重置搜索框
            case "iisReset":
              // 普通搜索
              this.$store.commit("set_searchType",'search')
              let searchfieldNameList = this.field.options.searchInputItems
              if (searchfieldNameList && searchfieldNameList.length > 0) {
                searchfieldNameList.forEach(item => {
                  if (item.check) {
                    //debugger
                    this.$refs[item.fieldName][0].clear()
                  }
                })
              }
              break;
            // 搜索按钮
            case "iisSearch":
              // 普通搜索
              this.$store.commit("set_searchType",'search')
              // 普通查询时，清空高级查询条件
              this.$store.commit("set_searchProDrawerBoxChange",null)
              this.searchAction()
              break;

            default:
              this.$message.error('启动内置功能,触发...' + subItem.value);
              break;
          }
        } else {
          // 自定义且未配置功能
          this.$message.error('暂未配置功能,请联系管理员！');
        }
      }
    },
    // 通用查询
    searchAction() {
      try {
          let params = {
                 pageCID:this.designer.formConfig.pageCID,
                 isSubPage:this.$route.fullPath.indexOf(this.designer.formConfig.pageCID) === -1?true:false,
                // sourceVFormRender: this.sourceVFormRender,
                 formCtrlName: "defaultmenubutton",
                // contrlName: this.field.options.name, // 当前为父控件
                triggerCtrlNames: this.field.options.triggerCtrlNames, //重点： 触发控件名称
                resetPage: true,// 是否重置分页
              }
        this.$store.commit("set_firstActionPageCID",this.designer.formConfig.pageCID)
        this.useHandleVFormEventFn.reSearchData(params);
      } catch (error) {
        // 兼容旧版本
        let params = {
                 formCtrlName: "defaultmenubutton",
                  triggerCtrlNames: this.field.options.triggerCtrlNames, //重点： 触发控件名称
                  resetPage: true,// 是否重置分页
                }
         this.useHandleVFormEventFn.reSearchData(params);
      }
     
    },

  }
}
</script>
  
<style lang="scss" scoped></style>
  