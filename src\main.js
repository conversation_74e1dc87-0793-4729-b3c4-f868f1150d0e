import 'babel-polyfill'
import './utils/debug-console'

import Vue from 'vue'
import axios from "axios";
import request from '@/libs/request'
import dayjs from 'dayjs'
import store from './store'
import App from './App.vue'
import ElementUI,{Table} from 'element-ui'
import './utils/directive'
import './icons'

import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/index.scss'
import '@/iconfont/iconfont.css'
// import 'font-awesome/css/font-awesome.min.css';
import VFormRender from '@/components/form-render/index'
import {loadExtension} from '@/extension/extension-loader'
// import {CLDatePicker} from 'cl-web-plugins-andy'
// Vue.component('CLDatePicker', CLDatePicker);
Vue.component('VFormRender', VFormRender)
loadExtension()
// Vue.use(VueWebpackUmd);
Vue.use(ElementUI, { size: 'small' })

if (typeof window !== 'undefined') {
  window.axios = axios
}

Vue.config.productionTip = false
// new Vue({
//   el: "#app",
//   render: h => h(App),
// })
//new2/////
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
// 导入自定义重写vxeTable组件,方便升级兼容
import '@/components/override/vxe-table'
// 导入自定义 VXETable 渲染控件包
import '@/components/override/vxe-table/renderConfig'
import { Splitpanes, Pane } from 'splitpanes'
import { getLocalStorageStore } from '@/util/setStore'
var UserInfo=getLocalStorageStore('userInfo')
import "@/assets/style/resSplitpanes.scss" // 重写resSplitpanes 拖拽样式
import "@/assets/style/baseCommon.scss" // 自定义基本样式类
// import statusColumn from '@/components/override/vxe-table/columnItem/statusCol.vue' 
// Vue.component('statusColumn', statusColumn)
Vue.component('Splitpanes', Splitpanes)
Vue.component('Pane', Pane)
// 解决 ElTable 自动(宽度,高度)导致的「ResizeObserver loop limit exceeded」问题
// const fixElTableErr = (Table) => {
//   const oldResizeListener = Table.methods.resizeListener;
//   Table.methods.resizeListener = function () {
//       window.requestAnimationFrame(oldResizeListener.bind(this));
//   };
// };
// // 一定要在Vue.use之前执行此函数
// fixElTableErr(Table);
VXETable.setup({
  // icon: {
  //   // table
  //   TABLE_EDIT: ""
  
  // }
})
Vue.use(VXETable)
// 自定义异步函数结构体
 //NEW ADD BY ANDY
import router from './router' // 乾坤三部曲 第二步
import actions from '@/micors/actions'
Vue.prototype.$actions = actions
Vue.prototype.$request = request // 内置请求
Vue.prototype.$axios = axios  // 原生请求
Vue.prototype.$dayjs = dayjs  // 原生请求
Vue.prototype.$UserInfo = UserInfo  // 登录用户信息
let instance = null;
function render(props) {
   
  if (props) actions.setActions(props)
  instance = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#appcustomform')
}
// 动态注入路径
if (window.__POWERED_BY_QIANKUN__) {
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
}
// 不使用乾坤
if (!window.__POWERED_BY_QIANKUN__) {
       var script = document.createElement('script');
        script.src = "https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.1/echarts.min.js";
    
        document.head.appendChild(script);
      let echarts = require('echarts');
      Vue.prototype.$echarts =echarts
  render();
}

// 子组件协议
export async function bootstrap(props) {
  console.log(props);
}
export async function mount(props) {
  Vue.prototype.$parentRouter = props.mainRouter;
  Vue.prototype.$echarts = props.echarts
  render(props)
    actions.setGlobalState({
      type: "loading",
    });
  
}
export async function unmount(props) {
  console.log(props);
  instance.$destroy()
}


