<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.dividerDirection')">
    <el-select v-model="optionModel.direction">
      <el-option label="horizontal" value="horizontal"></el-option>
      <el-option label="vertical" value="vertical"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="marginTop偏移量">
      <el-input-number v-model="optionModel.marginTop" :min="0"  label="marginTop偏移量"></el-input-number>
  </el-form-item>
  <el-form-item label="marginBottom偏移量">
      <el-input-number v-model="optionModel.marginBottom" :min="0"  label="marginBottom偏移量"></el-input-number>
  </el-form-item>
  <el-form-item label="marginLeft偏移量">
      <el-input-number v-model="optionModel.marginLeft" :min="0"  label="marginLeft偏移量"></el-input-number>
  </el-form-item>
  <el-form-item label="marginRight偏移量">
      <el-input-number v-model="optionModel.marginRight" :min="0"  label="marginRight偏移量"></el-input-number>
  </el-form-item>
  </div>
  
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "divider-direction-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
