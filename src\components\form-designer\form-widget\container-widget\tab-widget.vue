<!--
/**
 * author: vformAdmin
 * email: <EMAIL>
 * website: https://www.vform666.com
 * date: 2021.08.18
 * remark: 如果要分发VForm源码，需在本文件顶部保留此文件头信息！！
 */
-->

<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <div :style="{'padding': `0px ${!!widget.options.onPaddingRight?'10px':'0px'}  0px ${!!widget.options.onPaddingLeft?'10px':'0px'}`}" :key="widget.id" class="tab-container andyTabItem"
         :class="{'selected': selected}" @click.stop="selectWidget(widget)">
        
         <div v-html="tabItemColorStyle"></div>
         <div v-html="tabColorStyle"></div> 
         <div v-html="tabsHeaderStyle"></div> 
         <div v-html="tabsContentStyle"></div> 
         
      <el-tabs :id="`TABSTOP_${widget.id}`" :style="{'background-color': widget.options.bgColor,'border-top':`1px solid ${widget.options.bgColor}`}" :type="widget.displayType" v-model="activeTab" @tab-click="onTabClick">
       
        <el-tab-pane v-for="(tab, index) in widget.tabs" :key="index"
        style="overflow-x: hidden;overflow-y: auto;"
                     :style="[{ 'height': maxHeight + 'px' }]" 
         :label="tab.options.label" :name="tab.options.name"
                     @click.native.stop="selectWidget(widget)">
          <draggable :list="tab.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
                     handle=".drag-handler"
                     @add="(evt) => onContainerDragAdd(evt, tab.widgetList)"
                     @update="onContainerDragUpdate" :move="checkContainerMove">
            <transition-group name="fade" tag="div" class="form-widget-list">
              <template v-for="(subWidget, swIdx) in tab.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component :sourceVFormRender="sourceVFormRender" :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id" :parent-list="tab.widgetList"
                                    :index-of-parent-list="swIdx" :parent-widget="widget"></component>
                </template>
                <template v-else>
                  <component :sourceVFormRender="sourceVFormRender" :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id" :parent-list="tab.widgetList"
                                :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
                </template>
              </template>
            </transition-group>
          </draggable>
         
        </el-tab-pane>
       
      </el-tabs>
    </div>

  </container-wrapper>
</template>

<script>
  import Draggable from 'vuedraggable'
  import i18n from "@/utils/i18n"
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
  import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
  import refMixinDesign from "@/components/form-designer/refMixinDesign"

  export default {
    name: "tab-widget",
    componentName: 'ContainerWidget',
    mixins: [i18n, containerMixin, refMixinDesign],
    inject: ['refList','sourceVFormRenderState'],
    components: {
      ContainerWrapper,
      Draggable,

      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
      contentBoxHeight: {
        type: [Number, String],
        default: 0
      },
        // 控件来源父集 add by andy
      sourceVFormRender:{
        type: String,
        default: ""
      },
    },
    data() {
      return {
        tabColorStyle:"",
        tabItemColorStyle:"",
        tabsContentStyle:"",
        tabsHeaderStyle:"",
        activeTab: 'tab1',
        //
      }
    },
    computed: {
      maxHeight(){
        let _maxH = Number(this.contentBoxHeight-50)
        // 隐藏TAB项
        if(!!this.widget.options.hideTabsName){
           _maxH = _maxH + 47
        }
        // 高度偏移量
        if(!!this.widget.options.OffsetHeight){
          _maxH = _maxH + (this.widget.options.OffsetHeight)
        }
        return _maxH
      },
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },
      customTabActiveClass() {
        return this.widget.options.activeTabColor+this.widget.options.activeTabTextColor+this.widget.options.tabItemTextColor+this.widget.options.tabBarColor+this.widget.options.tabBodyColor
      },

    },
    watch: {
      customTabActiveClass(n,o){
        this.getTabSettingColor()
      }
    },
    created() {
      this.initRefList()
    },
    mounted() {
      this.getTabSettingColor()
    },
    methods: {
      getTabSettingColor(){
        this.tabColorStyle =""
        this.tabItemColorStyle =""
        this.tabsHeaderStyle =""
        this.tabsContentStyle =""
       if(!!this.widget.options.tabBodyColor){
            this.tabsContentStyle =`<style type="text/css">
              .andyTabItem .el-tabs__content {
                  background-color: ${this.widget.options.tabBodyColor} !important;
                }
            </style>`
        }
        if(!!this.widget.options.tabBarColor){
            this.tabsHeaderStyle =`<style type="text/css">
              .andyTabItem .el-tabs__header {
                  background-color: ${this.widget.options.tabBarColor} !important;
                }
            </style>`
        }
        // 隐藏TAB标签
        if(!!this.widget.options.hideTabsName){
                    this.tabsHeaderTopStyle =`<style type="text/css">
              #TABSTOP_${this.widget.id} .el-tabs__header div:first-child {
                  display:none;
                }
            </style>`
        }
        if(!!this.widget.options.tabItemTextColor){
            this.tabItemColorStyle =`<style type="text/css">
              .andyTabItem .el-tabs__item {
                color: ${this.widget.options.tabItemTextColor} !important;
                }
            </style>`
        }
        if(!!this.widget.options.activeTabColor || !!this.widget.options.activeTabTextColor){
          this.tabColorStyle =`<style type="text/css">
            .andyTabItem .is-active{
                color: ${this.widget.options.activeTabTextColor} !important;
                background-color: ${this.widget.options.activeTabColor} !important;
              }
          </style>`
        }
      },
      onTabClick(evt) {
        console.log('onTabClick', evt)
        let paneName = evt.name
        this.widget.tabs.forEach((tp) => {
          tp.options.active = tp.options.name === paneName;
        })
      },

    }
  }
</script>

<style lang="scss" scoped>
// 
.andyTabItem .el-tabs--border-card {
    ::v-deep .el-tabs__content {
      padding: 5px;
    }
  }
  .tab-container {
    //padding: 5px;
    margin: 2px;

    .form-widget-list {
      min-height: 28px;
     
    }
  }

  .tab-container.selected {
    outline: 2px solid $--color-primary !important;
  }

</style>
