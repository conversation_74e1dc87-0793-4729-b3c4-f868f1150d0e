<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.gridColHeight')">
      <span slot="label">{{i18nt('designer.setting.gridColHeight')}}
            <el-tooltip effect="light" content="栅格默认为空,为空时默认52，可以自定义设置42">
                <i class="el-icon-info"></i></el-tooltip>
            </span>
      <el-input type="number" v-model="optionModel.colHeight" @input.native="inputNumberHandler"
                min="0" class="hide-spin-button"></el-input>
    </el-form-item>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "colHeight-editor",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },

  }
</script>

<style scoped>

</style>
