<template>
  <div class="option-items-pane">

    <draggable tag="ul" :list="designer.formConfig.popupList"
      v-bind="{ group: 'optionsGroup', ghostClass: 'ghost', handle: '.drag-option' }">
      <li v-for="(option, idx) in designer.formConfig.popupList" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input @click.native="showEditDialogEvent(option)" readonly v-model="option.label" size="mini"
            style="width: 150px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger"
            @click="deleteOption(option, idx)" icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>



    <div>
      <el-button type="text" @click="addQueryOption">+添加弹框</el-button>
    </div>

    <el-dialog title="编辑 弹框属性" v-dialog-drag :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag"
      :show-close="true" class="small-padding-dialog" append-to-body :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <el-form :model="editItemForm" :rules="editItemFormRules" ref="editItemForm" label-width="150px">
        <el-form-item label="标签" prop="label">
          <el-input style="width: 205px;" v-model="editItemForm.label"></el-input>
        </el-form-item>
        <el-form-item label="唯一编码" prop="value">
          <el-input style="width: 205px;" disabled v-model="editItemForm.value"></el-input>
        </el-form-item>

        <el-form-item label="动作" prop="actionType">
          <el-select @change="actionChangeEvent" v-model="editItemForm.otherParams.actionType" placeholder="请选择动作">
            <el-option label="未选择" value="-1"></el-option>
            <el-option label="弹框应用" value="popup"></el-option>
            <el-option label="表单弹框" value="basicForm"></el-option>
            <el-option label="抽屉应用" value="drawer"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="showFooter">
          <span slot="label">显示底部
            <el-tooltip effect="light" content="可以勾选属性，显示底部的确认，取消按钮，确认按钮为提交表单的API,在表单设置哪里可以设置提交的接口">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </span>
          <el-switch v-model="editItemForm.showFooter"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="不允许窗口之外操作">
          <el-switch v-model="editItemForm.lockView"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="是否显示遮罩层">
          <el-switch v-model="editItemForm.showMask"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="允许点击遮罩层关闭">
          <el-switch v-model="editItemForm.maskClosable"></el-switch>
        </el-form-item>

        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="允许按 Esc 键关闭">
          <el-switch v-model="editItemForm.escClosable"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="允许窗口拖动">
          <el-switch v-model="editItemForm.draggable"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="显示最大化">
          <el-switch v-model="editItemForm.showZoom"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionTyp != 'drawer'" label="允许调整窗口大小">
          <el-switch v-model="editItemForm.resize"></el-switch>
        </el-form-item>

        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="显示关闭按钮">
          <el-switch v-model="editItemForm.showClose"></el-switch>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="允许双击头部放大">
          <el-switch v-model="editItemForm.dblclickZoom"></el-switch>
        </el-form-item>
        <!-- <el-form-item v-show="editItemForm.otherParams.actionType!='drawer'" label="打开时最大化">
           <el-switch v-model="editItemForm.fullscreen" 
               ></el-switch>
        </el-form-item> -->

        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="弹框顶部偏移量">
          <el-input-number style="width: 205px;" v-model="editItemForm.position_top" placeholder="top" :min="0"
            label="top"></el-input-number>
        </el-form-item>
        <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="弹框位置">
          <el-select v-model="editItemForm.positionType" placeholder="请选择">
            <el-option label="默认居中" value="center"></el-option>
            <el-option label="左边" value="left"></el-option>
            <el-option label="右边" value="right"></el-option>
          </el-select>
        </el-form-item>
        <!-- 请选择弹框 -->
        <div v-if="editItemForm.otherParams.actionType">
          <el-form-item
            v-show="editItemForm.otherParams.actionType == 'popup' || editItemForm.otherParams.actionType == 'drawer'"
            label="应用" prop="actionParams">

            <el-input disabled style="width:205px;" v-model="editItemForm.actionParams.actionNameText"></el-input>
            <el-button type="text" @click="selectPopupBox()">+ 选择应用</el-button>
          </el-form-item>
          <!-- 表单弹框配置 -->
          <el-form-item v-show="editItemForm.otherParams.actionType == 'basicForm'" label="表单弹框" prop="actionParams">
            <el-input style="width:205px;" disabled v-model="editItemForm.actionParams.actionNameText"></el-input>
            <el-button type="text" @click="showBasicFormBoxFn()">+ 选择表单</el-button>
            <el-button @click="reloadingParamsEvent()" :loading="isLoading_searchParamsRef" style="margin-left:5px;"
              type="primary">参数加载</el-button>
              <el-button @click="resetActionNameText()"  style="margin-left:5px;"
              type="primary">重置</el-button>
          </el-form-item>

          <el-form-item label="" prop="width">
            <span slot="label">
              <span v-show="editItemForm.otherParams.actionType == 'drawer'">宽度(50%)</span>
              <span v-show="editItemForm.otherParams.actionType != 'drawer'">宽度</span>
              <el-tooltip content="注意动作的不同，此处配置不同，抽屉弹框宽度为百分比 50%，普通编辑弹框为固定宽度 如：605" effect="light"> <i
                  class="el-icon-info"></i></el-tooltip>
              <!-- <el-tooltip effect="light" content="">
                <i class="el-icon-info"></i></el-tooltip> -->
            </span>
            <el-input style="width: 205px;" v-model="editItemForm.width"></el-input>
          </el-form-item>
          <el-form-item v-show="editItemForm.otherParams.actionType != 'drawer'" label="" prop="height">
            <span slot="label">
              <span v-show="editItemForm.otherParams.actionType != 'drawer'">高度</span>
              <el-tooltip content="默认0 取最高值，其它为固定高度 如：600" effect="light"> <i class="el-icon-info"></i></el-tooltip>
            </span>
            <el-input-number style="width: 205px;" v-model="editItemForm.height" :min="0" label="高度"></el-input-number>
          </el-form-item>
        </div>

        <!-- 动态参数 -->
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'"
          style="font-weight: bold;">参数</div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">

          <div :key="index" v-for="(item, index) in actionParamsList" style="margin-bottom: 5px;"
            class="flex justify-center items-center">
            <div>必填
              <el-tooltip content="勾选必填参数则这个参数必须有值，否则为空时，不执行调用此接口 " effect="light"> <i
                  class="el-icon-info"></i></el-tooltip>
            </div>
            <div style="margin:0 10px;" l-form-item label="必填" prop="requireParams">
              <el-switch size="mini" v-model="item.requireParams"></el-switch>
            </div>
            <div>KEY</div>
            <div style="margin:0 10px;" l-form-item label="KEY" prop="key">
              <el-input clearable placeholder="key" v-model="item.key"></el-input>
            </div>
            <div>VALUE</div>
            <div style="margin:0 10px;" label="VALUE" prop="value">
              <el-input clearable :placeholder="`${!!item.placeholder ? item.placeholder : ''}【固定值/+选择获取】`"
                v-model="item.value"></el-input>
            </div>
            <div>
              <el-button type="text" @click="setParams(item)">+选择</el-button>
            </div>
            <div style="margin-left:5px;cursor: pointer;"> <i @click="deleteParam(item, index)"
                class="el-icon-delete"></i></div>
          </div>
        </div>
        <div v-show="!!editItemForm.otherParams.actionType && editItemForm.otherParams.actionType != '-1'">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
          <el-tooltip content="后期扩展使用，弹框中暂未使用到" effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
        <div style="font-weight: bold;">提交后 成功或失败后事件处理
          <el-tooltip content="主要用来配置，弹框、抽屉关闭后的一些动作，如：刷新指定的表格，重新查询数据" effect="light"> <i
              class="el-icon-info"></i></el-tooltip>
        </div>
        <el-form-item label-width="0">
          <!-- 注意：成功后或失败后事件处理-->
          <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
            :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{ i18nt('designer.hint.confirm')
          }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="表单参数选择【双击选择】" width="75%" v-dialog-drag :visible.sync="showBasicFormBoxFlag" v-if="showBasicFormBoxFlag"
      :show-close="true" class="small-padding-dialog" append-to-body :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <div slot="title">
        <div>
          表单参数选择【双击选择】
          <el-tooltip content="选择需要弹框表单的页面" effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
      </div>
      <!-- 弹框表格 -->
      <vxe-form :data="formData_basicForm" >
       
        <vxe-form-item title="数据源" field="dbId" :item-render="{}">
          <template #default="{ data }">
            <vxe-select v-model="data.dbId" placeholder="请选择数据源" clearable>
              <vxe-option value="" label="请选择"></vxe-option>
              <vxe-option :key="index" v-for="(item,index) in dbIdList" :value="item.CID" :label="item.CNAME"></vxe-option>
            </vxe-select>
          </template>
        </vxe-form-item>
        <vxe-form-item title="表名" field="tableName" :item-render="{}">
          <template #default="{ data }">
            <vxe-input v-model="data.tableName" placeholder="请输入表名"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="描述" field="tableDesc" :item-render="{}">
          <template #default="{ data }">
            <vxe-input v-model="data.tableDesc" placeholder="请输入描述"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item title="CID" field="CID" :item-render="{}">
          <template #default="{ data }">
            <vxe-input v-model="data.tableDesc" placeholder="请输入CID"></vxe-input>
          </template>
        </vxe-form-item>
        <vxe-form-item>
          <template #default>
            <vxe-button @click="search_BasicFrom(true)" status="primary">查询</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
      <vxe-table height="300px" :loading="loading_basicForm" @cell-dblclick="cellDblclick_basicForm" :data="tableData_basicForm">
        <vxe-column header-align="center" align="center" type="seq" width="60"></vxe-column>
        <vxe-column header-align="center" align="center" field="tableName" title="表名"></vxe-column>
        <vxe-column header-align="center" align="center" field="tableDesc" title="描述"></vxe-column>
        <vxe-column header-align="center" align="center" field="tableType" title="表类型">
           <template #default="{ row }">
              <span v-if="row.tableType == 0">单表</span>
              <span v-else-if="row.tableType == 1">主表</span>
              <span v-else-if="row.tableType == 2">从表1</span>
              <span v-else-if="row.tableType == 3">从表2</span>
              <span v-else-if="row.tableType == 4">三方表</span>
              <span v-else-if="row.tableType == 5">虚拟表</span>
            </template>
        </vxe-column>
      </vxe-table>
      <vxe-pager  :current-page="tablePage_basicForm.currentPage"
        :page-size="tablePage_basicForm.pageSize" :total="tablePage_basicForm.totalResult"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange_basicForm">
      </vxe-pager>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button size="large" type="primary" @click="submitBasicForm()">{{ i18nt('designer.hint.confirm')
          }}</el-button> -->
        <el-button size="large" type="" @click="showBasicFormBoxFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="应用列表选择【双击选择】" v-dialog-drag :visible.sync="showPopupBoxFlag" v-if="showPopupBoxFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <div slot="title">
        <div>
          应用列表选择【双击选择】
          <el-tooltip content="选择需要弹框的页面" effect="light"> <i class="el-icon-info"></i></el-tooltip>
        </div>
      </div>
      <!-- 弹框表格 -->
      <nvxeGrid ref="mainTableRef" @cell-dblclick="cellDblclickEvent" :requstConfig="requstConfig"
        :columns="gridOptions.columns" height="350px">
        <template #top>
          <div style="margin-bottom:5px" class="flex">
            <el-input :clearable="true" placeholder="功能名称" style="width:305px;margin-right:5px;"
              v-model="tableDataSearchKey"></el-input>
            <el-button size="mini" type="primary" @click="tableDataSearchEvent()">查询</el-button>
            <el-button size="mini" type="primary" @click="resetTableDataSearchEvent()">重置</el-button>
          </div>
        </template>
      </nvxeGrid>


      <div slot="footer" class="dialog-footer">
        <!-- <el-button size="large" type="primary" @click="submitSelectFunction()">{{ i18nt('designer.hint.confirm')
          }}</el-button> -->
        <el-button size="large" type="" @click="showPopupBoxFlag = false">{{ i18nt('designer.hint.cancel')
          }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="参数控件值 选择" :visible.sync="showDataSourceialogFlag" v-if="showDataSourceialogFlag" v-dialog-drag
      append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <div>

        <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
          highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
      </div>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" size="large" @click="submitNodeEvent()">
          确定</el-button><el-button size="large" @click="showDataSourceialogFlag = false">
          取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const default_editItemForm = {
  label: "",// 标签
  value: "",
  width: 605,
  showFooter: false,
  lockView: true,
  position_top: 0,
  height: 600,
  showMask: true,
  maskClosable: false,
  escClosable: false,
  draggable: false,
  showZoom: false,
  resize: false,
  showClose: true,
  dblclickZoom: false,
  fullscreen: false,
  positionType: "center",
  otherParams: {
    hidden: false, // 隐藏
    disabled: false,// 激活
    actionType: "-1",// 动作
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
    actionNameText: "",
  },
}
import { deepClone, generateId } from "@/utils/util"
import request from '@/libs/request'
import afterSuccessOrErrorSetting from '@/components/form-designer/setting-panel/afterSuccessOrErrorSetting'
import cloneDeep from "clone-deep"
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n";
export default {
  name: "popupListTree",
  mixins: [i18n],
  components: {
    Draggable,
    afterSuccessOrErrorSetting,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    globalDsv: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      formData_basicForm:{
          tableType:5,// 只查询虚拟表的数据
          "dbId":null,
          "tableName":"",
          "tableDesc":"",
          "CID":""
      },
      dbIdList:[],
      loading_basicForm: false,
      tablePage_basicForm: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0
      },
      tableData_basicForm: [
        // { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        // { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        // { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        // { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' }
      ],
      showBasicFormBoxFlag: false,
      TableHeader_basicFormTempale: null,
      isLoading_searchParamsRef: false,
      showDataSourceialogFlag: false,// 弹框树 是否打开
      nodeCtrlValue: "",//当前节点选中内容
      curSetParamsItem: null,// 当前参数设置 编辑 项值
      tableDataSearchKey: "",// 过滤查询表格关键字
      // 主表查询配置
      requstConfig: {
        postUrl: `api/MD/VisualFormDesigner/`,
        actionName: "GetAll",
        postType: "get",
        postParams: {
          //?condition=&start=1&length=20
        },
      },
      loadingData: false, // 主表加载中...
      // 主表配置
      gridOptions: {
        columns: [{
          type: "seq",
          fixed: "left",
          title: "序号",
          width: 50,
        },
        {
          field: "CNAME",
          title: "功能名称",
          width: "220",
        },
        {
          field: "CCODE",
          title: "功能编码",
          width: "220",
        },

        {
          field: "CDESC",
          title: "功能说明",
          //width: "220",
        },

        ],
        data: [],

      },
      showPopupBoxFlag: false,// 弹框选择 是否显示
      showEditMenuDialogFlag: false,
      optionLines: '',
      currentEditOption: {},// 当前编辑菜单按钮
      editItemForm: Object.assign({}, default_editItemForm),
      actionParamsList: [],
      editItemFormRules: {
        label: [
          { required: true, message: '请输入标签名称', trigger: 'blur' },
        ],
        value: [
          { required: true, message: '请输入唯一编码', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
    formTemplateList() {
      return this.$store.state.formTemplateList
    }
  },
  methods: {
    // 基础表单双击选中
    cellDblclick_basicForm(tableInfo){
      // debugger
      this.editItemForm.actionParams.actionNameText = tableInfo.row.tableDesc
      this.editItemForm.actionParams.actionName = tableInfo.row.CID
      this.$nextTick(()=>{
        this.showBasicFormBoxFlag = false
      })
    },
    search_BasicFrom(resetpage=false){
      if(resetpage){
        this.tablePage_basicForm.currentPage =1
      }
      this.loadData_basicForm()
    },
    async GetDataSources(){
      let _url=`/api/md/form/GetDataSources`
      let params ={}
      let res = await request["post"](_url, params)
      if(res.Success){
        this.dbIdList = res.Datas 
      }
    },
    resetActionNameText(){
      this.editItemForm.actionParams.actionNameText = null
      this.editItemForm.actionParams.actionName = null
    },
    showBasicFormBoxFn(){
      this.showBasicFormBoxFlag = true
      this.$nextTick(()=>{
          this.GetDataSources()
          this.loadData_basicForm()
      })
    },
    async loadData_basicForm(){
      this.loading_basicForm = true
      setTimeout(()=>{
        this.loading_basicForm = false
      },10000)
      let _url=`/api/md/form/Page`
      let params ={
        "search":this.formData_basicForm,
        "PageIndex":this.tablePage_basicForm.currentPage,
        "PageSize":this.tablePage_basicForm.pageSize
      }
      let res = await request["post"](_url, params)
      this.loading_basicForm = false
      if(res.Success){
        this.tableData_basicForm = res.Datas
        this.tablePage_basicForm.totalResult = Number(res.TotalRows)
      }
    },

    handlePageChange_basicForm(valObj) {
      this.tablePage_basicForm.currentPage = valObj.currentPage
      this.tablePage_basicForm.pageSize = valObj.pageSize
      this.loadData_basicForm()
    },
    // 根据ID加载表单模板
    async loadFormTempaleItemByID() {
      let _url = `/api/md/form/Info`
      let params = {
        pid: this.editItemForm.actionParams.actionName//"196854017826886"
      }
      if (!params.pid) {
        return
      }
      let res = await request["post"](_url, params)
      this.TableHeader_basicFormTempale = res.Datas.MainTable.TableHeader
      //debugger
      // this.TableDetail = res.Datas.MainTable.TableDetail

    },
    // 在表单扩展属性中获取指定字段的值
    getValueByFieldInOtherParams(_fieldName = "", defaultValueWhenNull = "") {
      let _defaultVal = null
      try {
        let OtherParams = JSON.parse(this.TableHeader_basicFormTempale.otherParams)
        _defaultVal = OtherParams[_fieldName]
      } catch (error) {
        _defaultVal = defaultValueWhenNull
      }
      return _defaultVal
    },
    // 重新加载参数映射列表
    async reloadingParamsEvent() {
      // debugger
      if (!!this.editItemForm.actionParams.actionName) {
        await this.loadFormTempaleItemByID()// 获取指定的datasetId
        this.isLoading_searchParamsRef = true
        // "{"showLCheckBox":1,"showLSeq":1,"notPopupEditBox":0,"virtualTableDataSource":"5968973227622405","virtualTableGetDataAPI":"197230181507142","virtualTablePostDataAPI":"197230181507142"}"

        let datasetId = this.getValueByFieldInOtherParams("virtualTableGetDataAPI")// this.editItemForm.actionParams.actionName
        //debugger
        if (!datasetId) {
          this.$message.warning('没有获取对应的数据集API,请检查表单配置！')
          this.isLoading_searchParamsRef = false
          return
        }
        // 首次添加
        let _resDataList = await this.loadSearchParamsMap(datasetId)
        // debugger
        this.formatParamsMapData(_resDataList)
        this.isLoading_searchParamsRef = false
      } else {
        this.$message.warning('请填写表单弹框CID！')
      }

    },
    // 加载 参数 列表(查询参数映射列表)
    async loadSearchParamsMap(datasetId) {
      let params = {
        Id: datasetId
      }
      let ParameterMap = ""
      let _url = "api/MD/DataSet/GetInputParameterByDataSetId"
      await request['post'](_url, params).then(res => {
        // debugger
        if (res && res.Datas && res.Datas.length > 0) {
          ParameterMap = res.Datas
        }
      })
      return ParameterMap
    },
    // 格式化查询参数映射
    formatParamsMapData(dataList) {
      //debugger
      let newDataList = []
      if (dataList && dataList.length > 0) {
        dataList.forEach(oldItem => {
          let newItem = {
            key: oldItem.key,
            requireParams: false,
            execNow: false,
            value: !!oldItem.value ? oldItem.value : "",
            placeholder: !!oldItem.desc ? oldItem.desc : ""
          }

          newDataList.push(newItem)
        })
      }
      this.actionParamsList = newDataList
    },
    // 提交参数设置
    submitNodeEvent() {
      this.curSetParamsItem.value = this.nodeCtrlValue
      this.showDataSourceialogFlag = false
    },
    // 获取树 结构数据
    getNodeTreeData() {
      let copyData = cloneDeep(this.designer.nodeTreeData)
      let dataList = this.setPublicAttribute(copyData)
      return dataList
    },
    // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData) {
      let dataList = []
      for (let i = 0; i < nodeTreeData.length; i++) {
        let item = nodeTreeData[i]
        if (item.hasOwnProperty("children")) {
          this.setPublicAttribute(item.children)
          dataList.push(item)
        } else {
          let randomNum = Math.floor(Math.random() * 100000000 + 1)
          // 优先子集对外开放属性
          if (item.hasOwnProperty("publicSubAttribute")) {
            //debugger
            item.children = []

            if (item.publicSubAttribute && item.publicSubAttribute.length > 0) {
              item.publicSubAttribute.forEach((subItem, index) => {

                let valueItem = {
                  label: subItem.value,
                  id: subItem.value + "-" + randomNum + index
                }
                let keyItem = {
                  label: subItem.key,
                  id: subItem.key + "-" + randomNum + index,
                  children: [valueItem]
                }
                //debugger
                item.children.push(keyItem)
              })
              // debugger
              dataList.push(item)
            }

          } else {
            // 对外开放属性
            if (item.hasOwnProperty("publicAttribute")) {
              item.children = []
              if (item.publicAttribute && item.publicAttribute.length > 0) {
                item.publicAttribute.forEach((subItem, index) => {
                  let kidItem = {
                    label: subItem,
                    id: subItem + "-" + randomNum
                  }
                  item.children.push(kidItem)
                })
                dataList.push(item)
              }
            } else {
              dataList.push(item)
            }
          }
        }

      }
      return dataList

    },
    // 参数值设置 弹框 树节点 点击事件
    onNodeTreeClick(params) {
      //debugger
      this.nodeCtrlValue = ""
      let hasChildren = params.hasOwnProperty("children")
      let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
      let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
      let parentNodeLabel = parentsNode.data.label
      let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
      ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params)
      if (!hasChildren) {
        this.nodeCtrlValue = ctrlValue
        // this.curSetParamsItem.value = ctrlValue
      } else {
        this.$message.warning('此节点不可选！')
      }
    },
    // 获取第三层 控件名称
    tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params) {
      // debugger
      let newCtrlVal = ctrlValue
      let layoutList = ["defaultmenubutton"]//|| parentsNode.parent.data.label.includes('card'),"card"
      let level3NodeType = ""
      try {
        level3NodeType = parentsNode.parent.data.text
        //  if(layoutList.includes(level3NodeType)){
        if (layoutList.includes(level3NodeType)) {
          newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
        }
      } catch (error) {
        level3NodeType = ""
      }
      return newCtrlVal
    },
    // 弹框选择 参数 控件 来源
    setParams(item) {
      this.curSetParamsItem = item
      this.showDataSourceialogFlag = true
    },
    // 提交玄宗的应用
    submitSelectFunction() {
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {

      }
    },
    cellDblclickEvent(tableInfo) {
      this.editItemForm.actionParams.actionName = tableInfo.row.CID
      this.editItemForm.actionParams.actionNameText = tableInfo.row.CNAME
      this.editItemForm.label = tableInfo.row.CNAME
      this.$nextTick(() => {
        this.showPopupBoxFlag = false
      })
    },
    // 重置 并查询
    resetTableDataSearchEvent() {
      this.tableDataSearchKey = ""
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        // 覆盖原有的查询的参数
        this.requstConfig.postParams = {
          condition: this.tableDataSearchKey
        }
        mainTableRef.searchTableData()
      }
    },
    // 提交过滤查询
    tableDataSearchEvent() {
      //debugger
      let mainTableRef = this.$refs["mainTableRef"]
      if (mainTableRef) {
        // 覆盖原有的查询的参数
        this.requstConfig.postParams = {
          condition: this.tableDataSearchKey
        }
        mainTableRef.searchTableData()
      }
    },
    // 选择弹框
    selectPopupBox() {
      this.showPopupBoxFlag = true
    },
    // 切换动作时，重置动作的参数配置
    actionChangeEvent(params) {
      // 初始化 重置

      // this.editItemForm.actionParams = {
      //   //actionName:""
      // }
    },
    // 设置组合后的动态参数
    setpopupList() {
      let queryParams = {}
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach(item => {
          if (!!item.key) {
            queryParams[item.key] = item.value
          }
        })
      }
      //debugger
      return queryParams
    },
    // 获取组合后的动态参数
    getpopupList() {
      let queryParamsList = []
      //debugger
      if (this.editItemForm.actionParams && this.editItemForm.actionParams.query && Object.keys(this.editItemForm.actionParams.query).length > 0) {

        for (const [key, value] of Object.entries(this.editItemForm.actionParams.query)) {
          if (!!key) {
            let newItem = { key, value }
            queryParamsList.push(newItem)
          }
        }
      }
      return queryParamsList
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editItemForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editItemForm.label
          this.currentEditOption.value = this.editItemForm.value
          this.currentEditOption.width = this.editItemForm.width
          this.currentEditOption.showFooter = this.editItemForm.showFooter
          this.currentEditOption.positionType = this.editItemForm.positionType
          /////////////////////////////
          this.currentEditOption.lockView = this.editItemForm.lockView
          this.currentEditOption.position_top = this.editItemForm.position_top
          this.currentEditOption.height = this.editItemForm.height
          this.currentEditOption.showMask = this.editItemForm.showMask
          this.currentEditOption.maskClosable = this.editItemForm.maskClosable
          this.currentEditOption.escClosable = this.editItemForm.escClosable
          this.currentEditOption.draggable = this.editItemForm.draggable
          this.currentEditOption.showZoom = this.editItemForm.showZoom
          this.currentEditOption.resize = this.editItemForm.resize
          this.currentEditOption.showClose = this.editItemForm.showClose
          this.currentEditOption.dblclickZoom = this.editItemForm.dblclickZoom
          this.currentEditOption.fullscreen = this.editItemForm.fullscreen
          //////////////////////////////

          this.currentEditOption.otherParams = cloneDeep(this.editItemForm.otherParams)
          this.currentEditOption.actionParams = cloneDeep(this.editItemForm.actionParams)
          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setpopupList()

          this.showEditMenuDialogFlag = false
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    // 弹框编辑菜单属性--- 初始化弹框属性参数
    showEditDialogEvent(option) {
      this.currentEditOption = option // 当前菜单属性
      this.showEditMenuDialogFlag = true
      this.editItemForm.label = option.label
      this.editItemForm.value = option.value
      this.editItemForm.width = option.width
      this.editItemForm.showFooter = option.showFooter
      this.editItemForm.positionType = option.positionType
      ////////////////////////
      // 弹框属性参数
      this.editItemForm.lockView = option.lockView
      this.editItemForm.position_top = option.position_top
      this.editItemForm.height = option.height
      this.editItemForm.showMask = option.showMask
      this.editItemForm.maskClosable = option.maskClosable
      this.editItemForm.escClosable = option.escClosable
      this.editItemForm.draggable = option.draggable
      this.editItemForm.showZoom = option.showZoom
      this.editItemForm.resize = option.resize
      this.editItemForm.showClose = option.showClose
      this.editItemForm.dblclickZoom = option.dblclickZoom
      this.editItemForm.fullscreen = option.fullscreen
      /////////////////////////////

      this.actionParamsList = [] // 默认
      this.editItemForm.otherParams = cloneDeep(option.otherParams)
      this.editItemForm.actionParams = cloneDeep(option.actionParams)
      //debugger
      if (this.editItemForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getpopupList()
      }
      //debugger
      //let tt = this.editItemForm.otherParams.actionType

    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.designer.formConfig.popupList.splice(index, 1)
      }

    },
    // 添加查询
    addQueryOption() {
      let newValue = this.designer.formConfig.popupList.length + 1 + "-" + generateId()
      this.designer.formConfig.popupList.push(
        {
          label: '新弹框' + newValue, width: 605, value: "popup" + newValue,
          showFooter: false,
          lockView: true,
          position_top: 0,
          height: 0,
          showMask: true,
          maskClosable: false,
          escClosable: false,
          draggable: false,
          showZoom: false,
          resize: false,
          showClose: true,
          dblclickZoom: false,
          fullscreen: false,
          positionType: "center",
          check: false, canRemove: true, otherParams: {}, actionParams: {}
        }
      )
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1
      this.actionParamsList.push(
        { key: "", valueType: "1", value: "" }
      )
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1)
    },


  }
}
</script>

<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;

}
</style>