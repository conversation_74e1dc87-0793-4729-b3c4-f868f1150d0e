.cursor-pointer	{
    cursor: pointer;
}
.middle {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    left: 50%; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-right {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    right: -10px; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-left {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    left: -10px; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.static	{
    position: static;
}
.fixed	{
    position: fixed;
}
.absolute	{
    position: absolute;
}
.relative	{
    position: relative;
}
.sticky	{
    position: sticky;
}

.top-0	{
    top: 0px;
}
.right-0	{
    right: 0px;
}
.bottom-0	{
    bottom: 0px;
}
.left-0	{
    left: 0px;
}

.w-full {
    width: 100%;
}
.w-screen	{
    width: 100vw;
}
.h-full{
    height: 100%;
}	
.h-screen{
    height: 100vh;
}	

// .w-min	{
//     width: min-content;
// }
// .w-max	{
//     width: max-content;
// }
// .max-h-full	{
//     max-height: 100%;
// }
// .max-h-screen	{
//     max-height: 100vh;
// }
.flex{
    display: flex;
}
.inline-flex{
    display: inline-flex;
}
.flex-row	{
    flex-direction: row;
}
.flex-row-reverse	{
    flex-direction: row-reverse;
}
.flex-col	{
    flex-direction: column;
}
.flex-col-reverse	{
    flex-direction: column-reverse;
}
.justify-start	{
    justify-content: flex-start;
}
.justify-end{
    justify-content: flex-end;
}	
.justify-center	{
    justify-content: center;
}
.justify-between{
    justify-content: space-between;
}	
.justify-around	{
    justify-content: space-around;
}
.justify-evenly{
    justify-content: space-evenly;
}

.items-start	{
    align-items: flex-start;
}
.items-end	{
    align-items: flex-end;
}
.items-center	{
    align-items: center;
}
.items-baseline	{
    align-items: baseline;
}
.items-stretch	{
    align-items: stretch;
}

.flex-wrap	{
    flex-wrap: wrap;
}
.flex-wrap-reverse	{
    flex-wrap: wrap-reverse;
}
.flex-nowrap	{
    flex-wrap: nowrap;
}

.flex-1{
    flex: 1 1 0%;
}
.flex-auto	{
    flex: 1 1 auto;
}
.flex-initial	{
    flex: 0 1 auto;
}
.flex-none	{
    flex: none;
}
.shrink{
    flex-shrink: 1;
}	
shrink-0{
    flex-shrink: 0;
}	

/* .justify-items-start{
    justify-items: start;
}
.justify-items-end	{
    justify-items: end;
}
.justify-items-center{
    justify-items: center;
}
.justify-items-stretch	{
    justify-items: stretch;
} */
.m-0	{
    margin: 0px;
}
.m-px	{
    margin: 1px;
}
.mb-10px	{
    margin-bottom: 10px;
}
/* .m-0.5	{
    margin: 0.125rem;
} */
.m-1	{
    margin: 0.25rem;
}
/* .m-1.5	{
    margin: 0.375rem;
} */
.m-2	{
    margin: 0.5rem;
}
/* .m-2.5	{
    margin: 0.625rem;
} */
.m-3	{
    margin: 0.75rem;
}
/* .m-3.5	{
    margin: 0.875rem;
} */
.m-4	{
    margin: 1rem;
}
.m-5	{
    margin: 1.25rem;
}

.p-0	{
    padding: 0px;
}
.p-px	{
    padding: 1px;
}
.p-10px	{
    padding: 10px;
}
.p-20px	{
    padding: 20px;
}
/* .p-0.5	{
    padding: 0.125rem;
} */
.p-1	{
    padding: 0.25rem;
}
/* .p-1.5	{
    padding: 0.375rem;
} */
.p-2	{
    padding: 0.5rem;
}
/* .p-2.5	{
    padding: 0.625rem;
} */
.p-3	{
    padding: 0.75rem;
}
/* .p-3.5	{
    padding: 0.875rem;
} */
.p-4	{
    padding: 1rem;
}
.p-5	{
    padding: 1.25rem;
}

.ml-0	{
    margin-left: 0px;
}
.ml-px	{
    margin-left: 1px;
}
// .ml-0.5	{
//     margin-left: 0.125rem;
// }
.ml-1	{
    margin-left: 0.25rem;
}
// .ml-1.5	{
//     margin-left: 0.375rem;
// }
.ml-2	{
    margin-left: 0.5rem;
}
// .ml-2.5	{
//     margin-left: 0.625rem;
// }
.ml-3	{
    margin-left: 0.75rem;
}
// .ml-3.5{
//     margin-left: 0.875rem;
// }
.ml-4	{
    margin-left: 1rem;
}
.ml-5	{
    margin-left: 1.25rem;
}

.mr-0	{
    margin-right: 0px;
}
.mr-px	{
    margin-right: 1px;
}
// .mr-0.5	{
//     margin-right: 0.125rem;
// }
.mr-1	{
    margin-right: 0.25rem;
}
// .mr-1.5	{
//     margin-right: 0.375rem;
// }
.mr-2	{
    margin-right: 0.5rem;
}
// .mr-2.5	{
//     margin-right: 0.625rem;
// }
.mr-3	{
    margin-right: 0.75rem;
}
// .mr-3.5	{
//     margin-right: 0.875rem;
// }
.mr-4	{
    margin-right: 1rem;
}
.mr-5	{
    margin-right: 1.25rem;
}
//===========mt=============
.mt-0 {
    margin-top: 0px;
}

.mt-px {
    margin-top: 1px;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-5 {
    margin-top: 1.25rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-7 {
    margin-top: 1.75rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-9 {
    margin-top: 2.25rem;
}

.mt-10 {
    margin-top: 2.5rem;
}

.mt-11 {
    margin-top: 2.75rem;
}

.mt-12 {
    margin-top: 3rem;
}

.mt-14 {
    margin-top: 3.5rem;
}

.mt-16 {
    margin-top: 4rem;
}

.mt-20 {
    margin-top: 5rem;
}

.mt-auto {
    margin-top: auto;
}
.ml-2
{
    margin-left: 0.5rem;
}
.ml-3
{
    margin-left: 0.75rem;
}
.ml-4
{
    margin-left: 1rem;
}
.ml-5
{
    margin-left: 1.25rem;
}
.ml-6
{
    margin-left: 1.5rem;
}
.ml-7
{
    margin-left: 1.75rem;
}
// mt-24	margin-top: 6rem;
// mt-28	margin-top: 7rem;
// mt-32	margin-top: 8rem;
// mt-36	margin-top: 9rem;
// mt-40	margin-top: 10rem;
// mt-44	margin-top: 11rem;
// mt-48	margin-top: 12rem;
// mt-52	margin-top: 13rem;
// mt-56	margin-top: 14rem;
// mt-60	margin-top: 15rem;
// mt-64	margin-top: 16rem;
// mt-72	margin-top: 18rem;
// mt-80	margin-top: 20rem;
// mt-96	margin-top: 24rem;

//===========mt============
.overflow-auto	{
    overflow: auto;
}
.overflow-hidden	{
    overflow: hidden;
}
.overflow-visible	{
    overflow: visible;
}
.overflow-scroll	{
    overflow: scroll;
}
.overflow-x-auto	{
    overflow-x: auto;
}
.overflow-y-auto	{
    overflow-y: auto;
}
.overflow-x-hidden	{
    overflow-x: hidden;
}
.overflow-y-hidden	{
    overflow-y: hidden;
}
.overflow-x-visible	{
    overflow-x: visible;
}
.overflow-y-visible	{
    overflow-y: visible;
}
.overflow-x-scroll	{
    overflow-x: scroll;
}
.overflow-y-scroll	{
    overflow-y: scroll;
}


.border-red	{
    border:1px solid red;
}
.border-blue {
    border:1px solid blue;
}
.bg-white	{
    background-color: rgba(255, 255, 255);
}

// 总高度- 占用高度 h-content
.h-content{
    height: calc(100vh - 176px);//176
    //overflow: hidden;
}
.h-contentNew{
    height: calc(100vh - 76px);//176
    //overflow: hidden;
}
.paddingLR10{
    padding: 0px 10px;
    background-color: white;
}
.actionButtonClass{
    //border:1px solid red;
    margin-top: 10px;
    padding: 10px 10px;
    background-color: white;
    display: flex;
    overflow: hidden;
    //  height: 40px;
    //  line-height: 40px;
    //  font-size: 14px;
    //  color: #606266;
    //  background: #fff;
    //  text-align: left;
    //  padding: 10px
    //  16px
    //  10px
    //  16px
    // ;
    //  box-sizing: content-box;
    //  border-radius: 5px
    // ;
}

.actionButtonSubClass{
    padding: 10px 10px 10px 0px;
    background-color: white;
    display: flex;
    overflow: hidden;
}

