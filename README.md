# Variant Form
#### 一款高效的Vue低代码表单，可视化设计，一键生成源码，享受更多摸鱼时间。

![image](https://ks3-cn-beijing.ksyuncs.com/vform-static/img/vform_demo.gif)

<br/>

### 立即体验
[在线Demo](http://120.92.142.115/pro/)

### 友情链接
[Fantastic-admin](https://hooray.gitee.io/fantastic-admin/) —— 一款开箱即用的 Vue 中后台管理系统框架（支持Vue2/Vue3）

正式环境  http://8.135.117.94:6680/           

测试环境 http://8.135.117.94:6685/

正式就是6680的Web访问6681服务，  
测试就是6685的Web访问6686的服务
<br/>

### 功能一览
```
> 拖拽式表单设计；
> 支持PC、H5两种布局；
> 支持运行时动态加载表单；
> 支持表单复杂交互控制；
> 支持自定义CSS样式；
> 支持历史撤销、重做功能；
> 可导出Vue组件、HTML源码；
> 可导出Vue的SFC单文件组件；
> 更多功能等你探究...；
```
中络
http://218.95.67.64:6682/customform/designview?formName=188020593643589&retrunPath=designlist
CIMOM@2020
### 安装依赖
```
npm install --registry=https://registry.npm.taobao.org
```

### 开发调试
```
npm run serve
```
  <el-dialog :title="i18nt('designer.setting.tableColEdit')" :visible.sync="dialogVisible"
			v-if="dialogVisible" :show-close="true" class="small-padding-dialog"
			v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" append-to-body
			:destroy-on-close="true" width="1220px">
			<el-row :gutter="8">
					<el-col :span="6">
						<el-form-item  label="选择加载列头API">
							<el-select v-model="actionName" placeholder="请选择查询">
								<el-option :key="queryIndex+queryItem.value" v-for="(queryItem,queryIndex) in designer.formConfig.queryList" :label="queryItem.label" :value="queryItem.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item  label="">
							<el-button type="success">开始加载</el-button>
						</el-form-item>
					</el-col>
				
				</el-row>
			<el-table :data="optionModel.tableColumns" style="width: 100%" :cell-style="{padding:'1px 0'}"
				  height="500" border row-key="columnId" ref="singleTable" stripe>
				<el-table-column type="index" width="35" fixed="left"></el-table-column>
				<el-table-column label="" width="30">
					<i class="iconfont icon-drag drag-option"></i>
				</el-table-column>
			
				<el-table-column label="字段名" width="150" prop="field">
					<template slot-scope="scope">
						<el-input v-model="scope.row.field"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="中文描述" width="150" prop="title">
					<template slot-scope="scope">
						<el-input v-model="scope.row.title"></el-input>
					</template>
				</el-table-column>
				<!-- <el-table-column label="标题帮助" width="150" prop="titleHelp">
					<template slot-scope="scope">
						<el-input v-model="scope.row.titleHelp"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="默认值" width="150" prop="fieldDefault">
					<template slot-scope="scope">
						<el-input v-model="scope.row.fieldDefault"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="必填？" width="66" prop="iisRequired">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisRequired"></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="列表显示？" width="106" prop="iisShowList">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisShowList"></el-switch>
					</template>
				</el-table-column>
				<el-table-column :label="i18nt('designer.setting.columnWidth')" width="100" prop="width">
					<template slot-scope="scope">
						<el-input v-model="scope.row.width"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="编辑宽" width="100" prop="editWidth">
					<template slot-scope="scope">
						<el-input v-model="scope.row.editWidth"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="标题宽" width="100" prop="labelWidth">
					<template slot-scope="scope">
						<el-input v-model="scope.row.labelWidth"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="表头对齐" width="100" prop="titleAlign">
					<template slot-scope="scope">
						<el-select v-model="scope.row.titleAlign">
							<el-option value="left">left</el-option>
							<el-option value="center">center</el-option>
							<el-option value="right">right</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column label="表格对齐" width="100" prop="align">
					<template slot-scope="scope">
						<el-select v-model="scope.row.align">
							<el-option value="left">left</el-option>
							<el-option value="center">center</el-option>
							<el-option value="right">right</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column label="控件类型" width="100" prop="controlType">
					<template slot-scope="scope">
						<el-input v-model="scope.row.controlType"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="数据源key" width="100" prop="sourceKey">
					<template slot-scope="scope">
						<el-input v-model="scope.row.sourceKey"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="字段转换" width="100" prop="matchField">
					<template slot-scope="scope">
						<el-input v-model="scope.row.matchField"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="过滤参数" width="100" prop="searchParams">
					<template slot-scope="scope">
						<el-input v-model="scope.row.searchParams"></el-input>
					</template>
				</el-table-column>
				<el-table-column label="普通查？" width="86" prop="iisQuery">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisQuery"></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="高级查？" width="86" prop="iisHQuery">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisHQuery"></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="汇总？" width="66" prop="iisSummary">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisSummary"></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="只读？" width="66" prop="iisReadOnly">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisReadOnly"></el-switch>
					</template>
				</el-table-column>
				<el-table-column label="排序？" width="66" prop="iisSortable">
					<template slot-scope="scope">
						<el-switch v-model="scope.row.iisSortable"></el-switch>
					</template>
				</el-table-column> -->
				<el-table-column :label="i18nt('designer.setting.actionColumn')" width="100" fixed="right" align="center">
					<template slot-scope="scope">
						<el-button :title="i18nt('designer.setting.addTableColumn')" size="mini" type="" circle
									@click="addCol" icon="el-icon-plus"></el-button>
						<el-button :title="i18nt('designer.setting.deleteTableColumn')" size="mini" type="" circle
							@click="handleDelete(scope.$index, scope.row)" icon="el-icon-minus"></el-button>
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<div class="dialog-footer">
					<el-button size="large" type="primary" @click="colSubmit">{{i18nt('designer.hint.confirm')}}</el-button>
				  <el-button size="large" @click="dialogVisible = false">{{i18nt('designer.hint.cancel')}}</el-button>
				</div>
			</template>
			
		</el-dialog>
### 生产打包
```
npm run build
```

### 表单设计器 + 表单渲染器打包
```
npm run lib
```

### 表单渲染器打包
```
npm run lib-render
```

### 浏览器兼容性
```Chrome（及同内核的浏览器如QQ浏览器、360浏览器等等），Edge, Firefox，Safari，IE 11```

<br/>

### 跟Vue项目集成

<br/>

#### 1. 安装包
  ```bash
  npm i vform-builds
  ```
或
  ```bash
  yarn add vform-builds
  ```

<br/>

#### 2. 引入并全局注册VForm组件
```javascript
import Vue from 'vue'
import App from './App.vue'

import ElementUI from 'element-ui'  //引入element-ui库
import VForm from 'vform-builds'  //引入VForm库

import 'element-ui/lib/theme-chalk/index.css'  //引入element-ui样式
import 'vform-builds/dist/VFormDesigner.css'  //引入VForm样式

Vue.config.productionTip = false

Vue.use(ElementUI)  //全局注册element-ui
Vue.use(VForm)  //全局注册VForm(同时注册了v-form-designer和v-form-render组件)

new Vue({
  render: h => h(App),
}).$mount('#app')
```

<br/>

#### 3. 在Vue模板中使用表单设计器组件
```html
<template>
  <v-form-designer></v-form-designer>
</template>

<script>
  export default {
    data() {
      return {
      }
    }
  }
</script>

<style lang="scss">
body {
  margin: 0;  /* 如果页面出现垂直滚动条，则加入此行CSS以消除之 */
}
</style>
```

<br/>

#### 4. 在Vue模板中使用表单渲染器组件
```html
<template>
  <div>
    <v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vFormRef">
    </v-form-render>
    <el-button type="primary" @click="submitForm">Submit</el-button>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        formJson: {"widgetList":[],"formConfig":{"labelWidth":80,"labelPosition":"left","size":"","labelAlign":"label-left-align","cssCode":"","customClass":"","functions":"","layoutType":"PC","onFormCreated":"","onFormMounted":"","onFormDataChange":""}},
        formData: {},
        optionData: {}
      }
    },
    methods: {
      submitForm() {
        this.$refs.vFormRef.getFormData().then(formData => {
          // Form Validation OK
          alert( JSON.stringify(formData) )
        }).catch(error => {
          // Form Validation failed
          this.$message.error(error)
        })
      }
    }
  }
</script>
```

<br/>

### 资源链接
<hr>

文档官网：<a href="http://www.vform666.com/" target="_blank">http://www.vform666.com/</a>

在线演示：<a href="http://demo.vform666.com/" target="_blank">http://demo.vform666.com/</a>

VS Code插件：<a href="http://www.vform666.com/pages/plugin/" target="_blank">http://www.vform666.com/pages/plugin/</a>

Github仓库：<a href="https://github.com/vform666/variant-form" target="_blank">https://github.com/vform666/variant-form</a>

Gitee备份仓库：<a href="https://gitee.com/vdpadmin/variant-form" target="_blank">https://gitee.com/vdpadmin/variant-form</a>

更新日志：<a href="https://www.vform666.com/changelog.html" target="_blank">https://www.vform666.com/changelog.html</a>

技术交流群：微信搜索“vformAdmin”，或者扫如下二维码加群
 <div class="w-full h-full">
                    <el-steps :active="stepsActive" finish-status="success" simple >
                            <el-step title="基础设置" ></el-step>
                            <el-step title="表单设计" ></el-step>
                    </el-steps>
                </div>
![image](https://ks3-cn-beijing.ksyuncs.com/vform-static/img/vx-qrcode-242.png)
<div class="w-full h-full flex justify-between items-center">
                    <div class="flex" style="padding:2px;margin-left:5px;">
                        <!-- {{`${actionTitle} 表单模型`}} -->
                        <div>
                            <el-select style="width:80px;" v-model="templateType" placeholder="请选择">
                                <el-option
                                    v-for="item in templateOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                           </el-select>
                        </div>
                        <div>
                                 <el-input v-model="templateName" placeholder="请输入名称"></el-input>
                           </div>
                    </div>
                    <div class="flex  items-center">
                        <div  @click="hideAction('left')"><svg width="28px" height="28px" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="sc-kOcGyv hnIuVN"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(5.000000, 5.000000)" :stroke="formTemplatePanel.left?'#000000':'#DDDDDD'" stroke-width="1.5"><rect x="0.75" y="0.75" width="16.5" height="16.5" rx="2"></rect><line x1="7.5" y1="1.5" x2="7.5" y2="16.5" stroke-linecap="square"></line><line x1="1.5" y1="7" x2="4.5" y2="7" stroke-linecap="round"></line><line x1="1.5" y1="10.5" x2="4.5" y2="10.5" stroke-linecap="round"></line></g></g></svg></div>
                        <div  @click="hideAction('bottom')"><svg width="28px" height="28px" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="sc-hFxENk bAABtC"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><rect :stroke="formTemplatePanel.bottom?'#000000':'#DDDDDD'" stroke-width="1.5" x="5.75" y="5.75" width="16.5" height="16.5" rx="2"></rect><line x1="13.5" y1="8.5" x2="13.5" y2="23.5" :stroke="formTemplatePanel.bottom?'#000000':'#DDDDDD'" stroke-width="1.5" stroke-linecap="square" transform="translate(13.500000, 16.000000) rotate(-90.000000) translate(-13.500000, -16.000000) "></line><line x1="9.5" y1="19" x2="11.5" y2="19" :stroke="formTemplatePanel.bottom?'#000000':'#DDDDDD'" stroke-width="1.5" stroke-linecap="round"></line><line x1="15.5" y1="19" x2="18.5" y2="19" :stroke="formTemplatePanel.bottom?'#000000':'#DDDDDD'" stroke-width="1.5" stroke-linecap="round"></line></g></svg></div>
                        <div  @click="hideAction('right')"><svg width="28px" height="28px" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="sc-cnHmbd fMoJvE"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(14.000000, 14.000000) scale(-1, 1) translate(-14.000000, -14.000000) translate(5.000000, 5.000000)" :stroke="formTemplatePanel.right?'#000000':'#DDDDDD'" stroke-width="1.5"><rect x="0.75" y="0.75" width="16.5" height="16.5" rx="2"></rect><line x1="7.5" y1="1.5" x2="7.5" y2="16.5" stroke-linecap="square"></line><line x1="1.5" y1="7" x2="4.5" y2="7" stroke-linecap="round"></line><line x1="1.5" y1="10.5" x2="4.5" y2="10.5" stroke-linecap="round"></line></g></g></svg></div>
                    </div>
                   
                    <div style="padding:2px;" class="flex">
                        <el-button size="mini" :loading="loadingBtn" @click="preSubmitEvent()" type="success">保存</el-button>
                        <el-button size="mini"  @click="cancelBtn()" type="info">取消</el-button>
                    </div>
                </div>