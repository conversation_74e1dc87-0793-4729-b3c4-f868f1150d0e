
// 统一处理VFormPopup
import {useFormatParams} from "@/hooks/useFormatParams"
import {actionType} from '@/enum/enumData'
export function useHandleVFormPopup(vueInstance,refFormName='preForm') {
    return{
        data:[],
        actionType:actionType,
        vueInstance: vueInstance,
        refFormName:refFormName,
        useFormatParamsFn: useFormatParams(vueInstance),
        testFn(){
            return this.data
        },
    
        // actionParams: Object
            // actionName: (...)
            // query: {}
        // afterSuccessOrErrorEvents: Array(0)
        // canRemove: false
        // check: true
        // label: "新增"
        // otherParams: Object
        // value: "iisAdd"
        handlePopupByItem(subItem){
            // 临时保存弹框参数,主要用于添加，编辑
            this.vueInstance.$store.commit("set_popupParams",subItem)
                if(!!subItem.value){
                    //this.vueInstance.$store.commit("set_actionType", subItem.value)
                    if(Object.values(this.actionType).includes(subItem.value)){
                        this.vueInstance.$store.commit("set_actionType",subItem.value)
                    }else{
                        this.vueInstance.$store.commit("set_actionType","iisPopup")
                    }
                    
                }else{
                    this.vueInstance.$store.commit("set_actionType","iisPopup")
                }
        }
    }
}