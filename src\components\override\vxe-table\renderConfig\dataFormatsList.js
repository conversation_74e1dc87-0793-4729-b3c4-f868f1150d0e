// import VXETable from 'vxe-table'
const XEUtils = require('xe-utils') // vxeTable 通用库

export const commafy =function(num,{ spaceNumber= 3, separator= ',' }) {
    if(!num){
        return ''
    }
    num=num.split(".");  // 分隔小数点
    var arr=num[0].split("").reverse();  // 转换成字符数组并且倒序排列
    var res=[];
    for(var i=0,len=arr.length;i<len;i++){
    if(i%spaceNumber===0&&i!==0){
        res.push(separator);   // 添加分隔符
    }
    res.push(arr[i]);
    }
    res.reverse(); // 再次倒序成为正确的顺序
    if(num[1]){  // 如果有小数的话添加小数部分
        res=res.join("").concat("."+num[1]);
    }else{
        res=res.join("");
    }
    return res;
}

import dayjs from "dayjs";
export const commonFormat = {
    qffFormat(cellValue){
        // XEUtils.commafy 处理小数位 错误
        return commafy(XEUtils.toString(cellValue), { spaceNumber: 3, separator: ',' })
    },
    wffFormat(cellValue){
        return commafy(XEUtils.toString(cellValue), { spaceNumber: 4, separator: ',' })
    },
    decimalDigitsFormat (cellValue, digits) {
        // 四舍五入金额，每隔3位逗号分隔，默认2位数
        return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
    }
  }
  export const dataFormatsList = {
    // 格式化枚举字段
    formatterEnumColumns(data){
        //debugger
         let {cellValue,column,row} = data
         let {params,field} = column
         let showVal = cellValue
         //let item =null
         let newFieldName = field+"_NAME"
       
         if(params && Object.keys(params) && params.hasOwnProperty("controlType") && !!cellValue){
            let controlType = params.controlType
            switch (controlType) {
                case "select":
                case "selectTable":
                case "selectDictionaryBinary":
                case "selectTableBinary":
                  if(row.hasOwnProperty(newFieldName)){
                    showVal = row[newFieldName]
                  }     
                 break;
                default:
                    showVal = cellValue
                    break;
            }
         }
         return showVal

    },
         //  默认 時間格式化一
         YYYY_MM_DD_HH_mm_ss({ cellValue }){
            let formatValue =""
            if(!!cellValue){
                formatValue = dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
            }
            return formatValue
        },
             // 默认 時間格式化二
             YYYY_MM_DD({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('YYYY-MM-DD')
        }
        return formatValue
    },
       // 時間格式化一
    YY_MM_DD({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('YY-MM-DD')
        }
        return formatValue
    },
      // 時間格式化二
      YY_MM_DD_HH_mm({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('YY-MM-DD HH:mm')
        }
        return formatValue
    },
      // 時間格式化三
      YY_MM({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('YY-MM')
        }
        return formatValue
    },
      // 時間格式化四
      MM_DD({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('MM-DD')
        }
        return formatValue
    },
      // 時間格式化五
      MM_DD_HH_mm({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('MM-DD HH:mm')
        }
        return formatValue
    },
      // 時間格式化六
      YYYYMM({ cellValue }){
        let formatValue =""
        if(!!cellValue){
            formatValue = dayjs(cellValue).format('YYYYMM')
        }
        return formatValue
    },
     // 万分符
     NUMBER_WAN({ cellValue }){
        let formatValue = commonFormat.wffFormat(cellValue)
        return formatValue
    },
    // 千分符
    NUMBER_MARK({ cellValue }){
        let formatValue = commonFormat.qffFormat(cellValue)
        return formatValue
    },
    // #,###.00
    DECIMAL_DIGITS2({ cellValue }){
        let formatValue = commonFormat.decimalDigitsFormat(cellValue, 2)
        return formatValue
    },
    // #,###.000
    DECIMAL_DIGITS3({ cellValue }){
        let formatValue = commonFormat.decimalDigitsFormat(cellValue, 3)
        return formatValue==0.000?'':formatValue
    },
    // #,###.0000
    DECIMAL_DIGITS4({ cellValue }){
        let formatValue = commonFormat.decimalDigitsFormat(cellValue, 4)
        return formatValue==0.0000?'':formatValue
    },
    // #,###.000000
    DECIMAL_DIGITS6({ cellValue }){
        let formatValue = commonFormat.decimalDigitsFormat(cellValue, 6)
        return formatValue==0.000000?'':formatValue
    }
  }  

