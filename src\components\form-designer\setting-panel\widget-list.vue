<template>
      <div class="panel-container">
        <el-collapse v-model="activeNames" class="widget-collapse">
                <el-collapse-item name="1" :title="i18nt('designer.containerTitle')">
                  <draggable tag="ul" :list="containers" :group="{name: 'dragGroup', pull: 'clone', put: false}"
                            :clone="handleContainerWidgetClone" ghost-class="ghost" :sort="false"
                            :move="checkContainerMove" @end="onContainerDragEnd">
                    <li v-for="(ctn, index) in containers" :key="index" class="container-widget-item" :title="ctn.displayName"
                        @dblclick="addContainerByDbClick(ctn)">
                      <span><svg-icon :icon-class="ctn.icon" class-name="color-svg-icon" />{{i18n2t(`designer.widgetLabel.${ctn.type}`, `extension.widgetLabel.${ctn.type}`)}}</span>
                    </li>
                  </draggable>
                </el-collapse-item>
  
                <el-collapse-item name="2" :title="i18nt('designer.basicFieldTitle')">
                  <draggable tag="ul" :list="basicFields" :group="{name: 'dragGroup', pull: 'clone', put: false}"
                            :move="checkFieldMove"
                            @end="onContainerDragEnd"
                            :clone="handleFieldWidgetClone" ghost-class="ghost" :sort="false">
                    <li v-for="(fld, index) in basicFields" :key="index" class="field-widget-item" :title="fld.displayName"
                        @dblclick="addFieldByDbClick(fld)">
                      <span><svg-icon :icon-class="fld.icon" class-name="color-svg-icon" />{{i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)}}</span>
                    </li>
                  </draggable>
                </el-collapse-item>
  
                <el-collapse-item name="3" :title="i18nt('designer.advancedFieldTitle')">
                  <draggable tag="ul" :list="advancedFields" :group="{name: 'dragGroup', pull: 'clone', put: false}"
                            :move="checkFieldMove"
                            @end="onContainerDragEnd"
                            :clone="handleFieldWidgetClone" ghost-class="ghost" :sort="false">
                    <li v-for="(fld, index) in advancedFields" :key="index" class="field-widget-item" :title="fld.displayName"
                        @dblclick="addFieldByDbClick(fld)">
                      <span><svg-icon :icon-class="fld.icon" class-name="color-svg-icon" />{{i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)}}</span>
                    </li>
                  </draggable>
                </el-collapse-item>
  
                <!--andy 自定义控件-->
                <el-collapse-item name="4" :title="i18nt('designer.customFieldTitle')">
                  <draggable tag="ul" :list="customFields" :group="{name: 'dragGroup', pull: 'clone', put: false}"
                            :move="checkFieldMove"
                            @end="onContainerDragEnd"
                            :clone="handleFieldWidgetClone" ghost-class="ghost" :sort="false">
                    <li v-for="(fld, index) in customFields" :key="index" class="field-widget-item" :title="fld.displayName"
                        @dblclick="addFieldByDbClick(fld)">
                      <span>
                        <svg-icon :icon-class="fld.icon" class-name="color-svg-icon" />{{i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)}}</span>
                    </li>
                  </draggable>
                </el-collapse-item>
                <!-- -->
              <!--andy App控件列表-->
              <el-collapse-item name="5" :title="i18nt('designer.appFieldTitle')">
                  <draggable tag="ul" :list="AppFields" :group="{name: 'dragGroup', pull: 'clone', put: false}"
                            :move="checkFieldMove"
                            @end="onContainerDragEnd"
                            :clone="handleFieldWidgetClone" ghost-class="ghost" :sort="false">
                    <li v-for="(fld, index) in AppFields" :key="index" class="field-widget-item" :title="fld.displayName"
                        @dblclick="addFieldByDbClick(fld)">
                      <span>
                        <svg-icon :icon-class="fld.icon" class-name="color-svg-icon" />{{i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)}}</span>
                    </li>
                  </draggable>
                </el-collapse-item>
              </el-collapse>
      </div>

  </template>
  
  <script>
   /**
    * add by andy
    * 功能来自 widget-panel/index 页面
    * 组件库
   */
    import Draggable from 'vuedraggable'
    import SvgIcon from '@/components/svg-icon'
    import {containers, basicFields, advancedFields, customFields,AppFields} from "@/components/form-designer/widget-panel/widgetsConfig"
    import {formTemplates} from '@/components/form-designer/widget-panel/templatesConfig'
    import i18n from "@/utils/i18n"
    import axios from "axios"
    export default {
      name: "widget-list",
      mixins: [i18n],
      components: {
        Draggable,
        SvgIcon
      },
      props: {
        designer: Object,
      },
      inject: ['getBannedWidgets', 'getDesignerConfig'],
      data() {
        return {
          designerConfig: this.getDesignerConfig(),
  
          firstTab: 'componentLib',
  
          scrollerHeight: 0,
  
          activeNames: ['1', '2', '3', '4'],
  
          containers,
          basicFields,
          advancedFields,
          customFields,
          AppFields,
  
          formTemplates: formTemplates,
          ftImages: [

          ]
        }
      },
      computed: {
        //
      },
      mounted() {
        this.loadWidgets()
      },
      methods: {
        isBanned(wName) {
          return this.getBannedWidgets().indexOf(wName) > -1
        },
  
        showFormTemplates() {
          if (this.designerConfig['formTemplates'] === undefined) {
            return true
          }
  
          return !!this.designerConfig['formTemplates']
        },
  
        loadWidgets() {
          this.containers = this.containers.map(con => {
            return {
              ...con,
              displayName: this.i18n2t(`designer.widgetLabel.${con.type}`, `extension.widgetLabel.${con.type}`)
            }
          }).filter(con => {
            return !con.internal && !this.isBanned(con.type)
          })
  
          this.basicFields = this.basicFields.map(fld => {
            return {
              ...fld,
              displayName: this.i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)
            }
          }).filter(fld => {
            return !this.isBanned(fld.type)
          })
  
          this.advancedFields = this.advancedFields.map(fld => {
            return {
              ...fld,
              displayName: this.i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)
            }
          }).filter(fld => {
            return !this.isBanned(fld.type)
          })
  
          this.customFields = this.customFields.map(fld => {
            return {
              ...fld,
              displayName: this.i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)
            }
          }).filter(fld => {
            return !this.isBanned(fld.type)
          })
          this.AppFields = this.AppFields.map(fld => {
            return {
              ...fld,
              displayName: this.i18n2t(`designer.widgetLabel.${fld.type}`, `extension.widgetLabel.${fld.type}`)
            }
          }).filter(fld => {
            return !this.isBanned(fld.type)
          })
        },
  
        handleContainerWidgetClone(origin) {
          return this.designer.copyNewContainerWidget(origin)
        },
  
        handleFieldWidgetClone(origin) {
          return this.designer.copyNewFieldWidget(origin)
        },
  
        /* draggable组件的move钩子是在内部子组件被拖放到其他draggable组件时触发！！ */
        checkContainerMove(evt) {
          //console.log('checkContainerMove: andy....')
          return this.designer.checkWidgetMove(evt)
        },
  
        /* draggable组件的move钩子是在内部子组件被拖放到其他draggable组件时触发！！ */
        checkFieldMove(evt) {
          //console.log('checkFieldMove: andy....')
          return this.designer.checkFieldMove(evt)
        },
  
        onContainerDragEnd(evt) {
          //debugger
           console.log('Drag end of container: andy....')
          //  console.log(evt)
           // 控件拖拽结束
         let valueChange = Math.floor(Math.random() * 10000 + 1)
         this.$store.commit("set_containerDragEndChange",valueChange);
        },
        addContainerByDbClick(container) {
          this.designer.addContainerByDbClick(container)
        },
  
        addFieldByDbClick(widget) {
          this.designer.addFieldByDbClick(widget)
        },
  
        loadFormTemplate(jsonUrl) {
          this.$confirm(this.i18nt('designer.hint.loadFormTemplateHint'), this.i18nt('render.hint.prompt'), {
            confirmButtonText: this.i18nt('render.hint.confirm'),
            cancelButtonText: this.i18nt('render.hint.cancel')
          }).then(() => {
            axios.get(jsonUrl).then(res => {
              let modifiedFlag = false
              if (typeof res.data === 'string') {
                modifiedFlag = this.designer.loadFormJson( JSON.parse(res.data) )
              } else if (res.data.constructor === Object) {
                modifiedFlag = this.designer.loadFormJson(res.data)
              }
              if (modifiedFlag) {
                this.designer.emitHistoryChange()
              }
  
              this.$message.success(this.i18nt('designer.hint.loadFormTemplateSuccess'))
            }).catch(error => {
              this.$message.error(this.i18nt('designer.hint.loadFormTemplateFailed') + ':' + error)
            })
          }).catch(error => {
            console.error(error)
          })
        }
  
      }
  
    }
  </script>
  
  <style lang="scss" scoped>
    .color-svg-icon {
      color: $--color-primary;
    }
  
    .side-scroll-bar {
      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  
    div.panel-container {
      padding-bottom: 10px;
    }
  
    .no-bottom-margin ::v-deep .el-tabs__header {
      margin-bottom: 0;
    }
  
    .indent-left-margin {
      ::v-deep .el-tabs__nav {
        margin-left: 20px;
      }
    }
  
    .el-collapse-item ::v-deep ul > li {
      list-style: none;
    }
  
    .widget-collapse {
      border-top-width: 0;
  
      ::v-deep .el-collapse-item__header {
        margin-left: 8px;
        font-style: italic;
        font-weight: bold;
      }
  
      ::v-deep .el-collapse-item__content {
        padding-bottom: 6px;
  
        ul {
          padding-left: 10px;  /* 重置IE11默认样式 */
          margin: 0;  /* 重置IE11默认样式 */
          margin-block-start: 0;
          margin-block-end: 0.25em;
          padding-inline-start: 10px;
  
          &:after {
            content: "";
            display: block;
            clear: both;
          }
  
          .container-widget-item, .field-widget-item {
            display: inline-block;
            height: 28px;
            line-height: 28px;
            width: 115px;
            float: left;
            margin: 2px 6px 6px 0;
            cursor: move;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            background: #f1f2f3;
          }
  
          .container-widget-item:hover, .field-widget-item:hover {
            background: #EBEEF5;
            outline: 1px solid $--color-primary;
          }
  
          .drag-handler {
            position: absolute;
            top: 0;
            left: 160px;
            background-color: #dddddd;
            border-radius: 5px;
            padding-right: 5px;
            font-size: 11px;
            color: #666666;
          }
        }
      }
    }
  
    .el-card.ft-card {
      border: 1px solid #8896B3;
    }
  
    .ft-card {
      margin-bottom: 10px;
  
      .bottom {
        margin-top: 10px;
        line-height: 12px;
      }
  
      /*
      .image-zoom {
        height: 500px;
        width: 620px
      }
      */
  
      .ft-title {
        font-size: 13px;
        font-weight: bold;
      }
  
      .right-button {
        padding: 0;
        float: right;
      }
  
      .clear-fix:before, .clear-fix:after {
        display: table;
        content: "";
      }
  
      .clear-fix:after {
        clear: both;
      }
  
    }
  
  </style>
  