<template>
  <div>
    <div >
      <el-button @click="searchProSettingFn()" type="text">
      <span :style="[{ color: (!!optionModel.searchProSettingID) ? `green` : `` }]">{{ (!!optionModel.searchProSettingID)
        ?
        `编辑高级搜索` : `添加自定义高级搜索` }}</span>
    </el-button>
    </div>
   


    <vxe-modal title="配置高级搜索" width="80%" :position="{ top: 50 }" @confirm="submitEditDialog()"
      v-model="showEditDialogFlag" show-footer>
      <div class="flex justify-between">
        <div style="margin-bottom: 10px;" class="flex justify-center">
          <el-button style="margin-right: 10px;" type="primary" @click="addGroupClick"><i
              class="vxe-icon-add"></i>&nbsp;新增(条件组)</el-button>
          <el-button style="margin-right: 10px;" type="success" @click="addConditionClick"><i
              class="vxe-icon-add"></i>&nbsp;新增条件</el-button>
          <el-button v-show="!!optionModel.searchProSettingID" :loading="removeBtnLoading" style="margin-right: 10px;"
            type="danger" @click="removeConditionClick"><i class="vxe-icon-delete"></i>&nbsp;删除高级查询配置</el-button>
          <selectDataSourceApi :dataSetAllModel="false" :showSingleRowWithDiv="true" contrlType="commonSelect"
            :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"
            @afterLoadDataSetAllModel="afterLoadDataSetAllModel" @dataSourceChangeEvent="dataSourceChangeEvent">
          </selectDataSourceApi>
        </div>

      </div>
      <div class="tableTemplateClass">
        <vxe-table :loading="tableDataLoading" ref="vxeTableRef" :edit-rules="validRules" keep-source row-id="id"
          @current-change="currentChange" max-height="400px" resizable show-overflow border size="mini" row-key
          :highlight-current-row="true" :row-config="{ isHover: true, isCurrent: true }"
          :edit-config="{ trigger: 'click', mode: 'cell' }"
          :tree-config="{ transform: true, rowField: 'id', parentField: 'parentId', expandAll: true }" :data="tableData"
          :scroll-y="{ enabled: false }">
          <vxe-column field="field" title="参数字段" width="250" tree-node header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <span v-if="row.parentId == -1">+</span>
                <el-input :style="[{ width: (row.parentId == -1 ? '82%' : '86%') }]" v-model="row.field"
                  placeholder="参数字段" />
                <el-dropdown style="cursor:pointer;">
                  <span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="change_dropdownItem(item, row, index)" :key="index"
                      v-for="(item, index) in fieldDropdownItems">{{ item.title }}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
              <template v-else>
                <span>条件组</span>
              </template>
            </template>
          </vxe-column>
          <vxe-column field="title" title="字段描述" header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-input v-model="row.title" placeholder="字段描述" />
              </template>
            </template>
          </vxe-column>
          <vxe-column field="tableName" title="表名称" header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-input v-model="row.tableName" placeholder="表名称" />
              </template>
            </template>
          </vxe-column>
          <vxe-column field="controlType" title="控件类型" width="150" header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-select v-model="row.controlType" placeholder="请选择">
                  <el-option label="文本" value="text" />
                  <el-option label="数字" value="number" />
                  <el-option label="下拉框" value="reportSelect" />
                  <el-option label="下拉框(多选)" value="reportMulSelect" />
                  <el-option label="下拉框(表)" value="reportSelectTable" />
                  <el-option label="下拉框(自定义单选)" value="customSelect" />
                  <el-option label="下拉框(自定义多选)" value="customMulSelect" />
                  <el-option label="日期选择" value="date" />
                  <el-option label="日期时间" value="datetime" />
                  <el-option label="日期范围选择" value="daterange" />
                  <el-option label="日期时间范围" value="datetimerange" />
                </el-select>
              </template>
            </template>
          </vxe-column>
          <vxe-column field="controlOptions" title="控件配置" width="90" header-align="center" align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-button @click="showSearchProConfigEditFn(row)" type="text">
                  <span :style="[{ color: !!row.controlOptions ? `green` : `` }]">{{ !!row.controlOptions ? `编辑配置` :
                    `添加配置`
                  }}</span>
                </el-button>
              </template>
            </template>
          </vxe-column>
          <vxe-column field="comparator" title="比较符" width="125" header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-select @change="change_comparator(row,$event)" v-model="row.comparator" placeholder="请选择">
                  <el-option v-for="(item, index) in comparatorOptions" :key="index" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </template>
          </vxe-column>
          <vxe-column field="defaultValue" title="默认值" :title-help="{message: '设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期+1年:add-2-year),时间范围add-1-day#add-10-day,使用#分割'}" header-align="center">
            <template #default="{ row }">
              <template v-if="row.parentId != 0">
                <el-input v-model="row.defaultValue" placeholder="默认值" />
              </template>
            </template>
          </vxe-column>
          <vxe-column field="relationSign" title="关系" width="90" header-align="center">
            <template #default="{ row }">
              <!-- <template v-if="row.parentId != 0">
               
              </template> -->
              <el-select @change="change_relationSign(row,$event)" v-model="row.relationSign" placeholder="请选择">
                <el-option label="并" :value="0" />
                <el-option label="或" :value="1" />
                <el-option label="无" :value="-1" />
              </el-select>
            </template>
          </vxe-column>
          <vxe-column title="操作" width="80" header-align="center" align="center" show-overflow>
            <template #default="{ row }">
              <vxe-button style="color:red;" type="text" icon="vxe-icon-delete" @click="removeRow(row)">删除</vxe-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <template v-slot:footer>
        <el-button size="medium" @click="submitEditDialog()" type="primary">确定</el-button>
        <el-button size="medium" @click="showEditDialogFlag = false">取消</el-button>
      </template>
    </vxe-modal>
    <vxe-modal title="控件配置" width="70%" :position="{ top: 80 }" v-model="showSearchProConfigEdit" show-footer>
      <div class="flex justify-between">
        <el-form size="small" :inline="true" :model="configFormItem" :rules="configFormFules" label-width="80px">
          <el-form-item label-width="90px" label="">
            <span slot="label">选择加载API
              <el-tooltip effect="light" content="注意：此处选择加载API后，会把模型对应的查询参数保存起来，如果后期修改了查询列表中对应的API，此处不会同步，需要重新触发获取后保存。">
                <i style="color:red;" class="el-icon-info"></i></el-tooltip>
            </span>

            <el-select @change="change_sourceApi(configFormItem, $event)" v-model="configFormItem.actionName"
              style="width:193.33px" placeholder="请选择查询">
              <el-option label="请选择" value=""></el-option>
              <el-option :key="queryIndex + queryItem.value"
                v-for="(queryItem, queryIndex) in designer.formConfig.queryList" :label="queryItem.label"
                :value="queryItem.value"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label-width="80px" label="数据源key">
            <el-input style="width:193.33px" v-model="configFormItem.sourceKey"></el-input>
          </el-form-item>
          <el-form-item label-width="80px" label="字段转换">
            <el-input style="width:193.33px" v-model="configFormItem.matchField"></el-input>
          </el-form-item>
          <el-form-item label-width="90px" label="过 滤 参 数">
            <el-input style="width:193.33px" v-model="configFormItem.searchParams"></el-input>
          </el-form-item>

          <el-form-item label-width="80px" label="分页数" prop="CPAGE_SIZE">
            <el-input-number style="width:193.33px" v-model="configFormItem.CPAGE_SIZE" :min="1"></el-input-number>
          </el-form-item>
          <el-form-item label-width="80px" label="是否分页" prop="CIS_PAGE">
            <el-checkbox false-label="N" true-label="Y" v-model="configFormItem.CIS_PAGE"></el-checkbox>
          </el-form-item> -->
        </el-form>

      </div>
      <div class="tableTemplateClass">
        <vxe-table :loading="loading_tableData" ref="searchProConfigEditTableRef" row-id="id" max-height="400px" resizable
          show-overflow border size="mini" row-key :highlight-current-row="true"
          :row-config="{ isHover: true, isCurrent: true }" :edit-config="{ trigger: 'click', mode: 'cell' }"
          :data="searchProConfigEditTableData" :scroll-y="{ enabled: false }">

          <vxe-column field="field" title="字段名" header-align="center" align="center">
            <template #default="{ row }">

              <vxe-input v-model="row.field" placeholder="字段名"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column field="title" title="字段描述" header-align="center" align="center">
            <template #default="{ row }">
              <vxe-input v-model="row.title" placeholder="字段描述"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column field="iisShowList" width="80" title="是否显示" header-align="center" align="center">
            <template #default="{ row }">
              <el-checkbox size="mini" v-model="row.iisShowList"></el-checkbox>
            </template>
          </vxe-column>
          <vxe-column field="isSelectTextField" width="80" title="下拉Text" header-align="center" align="center">
            <template #default="{ row }">
              <el-checkbox size="mini" v-model="row.isSelectTextField"></el-checkbox>
            </template>
          </vxe-column>
          <vxe-column field="isSelectKeyField" width="80" title="下拉Key" header-align="center" align="center">
            <template #default="{ row }">

              <el-checkbox size="mini" v-model="row.isSelectKeyField"></el-checkbox>
            </template>
          </vxe-column>
          <vxe-column field="width" title="列宽" header-align="center" align="center">
            <template #default="{ row }">
              <vxe-input :controls=false type="number" v-model="row.width"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column field="fieldOrder" title="列表顺序" header-align="center" align="center">
            <template #default="{ row }">
              <vxe-input :controls=false type="number" v-model="row.fieldOrder"></vxe-input>
            </template>
          </vxe-column>
          <vxe-column title="操作" width="80" header-align="center" align="center" show-overflow>
            <template #default="{ row }">
              <vxe-button style="color:red;" type="text" icon="vxe-icon-delete"
                @click="removeSearchProConfigRow(row)">删除</vxe-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>

      <template v-slot:footer>
        <div class="flex justify-between">
          <div>
            <el-button type="primary" size="medium" icon="el-icon-plus" plain round
              @click="addNewField()">添加新字段</el-button>
          </div>
          <div>
            <el-button size="medium" type="danger" @click="resetConfig()">清空配置</el-button>
          </div>
          <div>
            <el-button size="medium" @click="showSearchProConfigEdit = false">取消</el-button>
            <el-button size="medium" @click="submitSearchProConfigEdit()" type="primary">确定</el-button>
          </div>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>
<script>
const defaultConfigForm = {
  actionName: "",
  CIS_PAGE: "N",// 是否分页
  CPAGE_SIZE: 10,// 分页数
  sourceKey: "",// 数据源key
  matchField: "",// 字段转换
  searchParams: "",// 过滤参数
  CDATAS: [],//  表格数据>>配置参数JSON,存储时，需要转换为字符串
  loadDataConfig: {},
}
import cloneDeep from "clone-deep"
import selectDataSourceApi from '@/components/form-designer/setting-panel/selectDataSourceApi.vue'
import request from '@/libs/request'
import i18n from "@/utils/i18n";
import dayjs from 'dayjs'
import { sqlServeSearchType } from "@/enum/enumData"
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
  name: "defaultmenubutton-searchPro",
  components: { selectDataSourceApi },
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      loadDataConfig: {},// 查询数据配置信息，来自查询列表，此处只能时固定值
      needConfigType: ["reportSelect", "reportSelectTable", "customSelect","customMulSelect", "standardFrame", "reportMulSelect", "customRenderCtrl"],
      currentEditSearchProConfigRow: null,// 当前编辑控件配置信息
      dataModelList: [],
      useFormatParamsFn: useFormatParams(this),
      loading_tableData: false,
      searchProConfigEditTableData: [],
      currentCtrlType: "text",// 当前控件类型
      configFormItem: Object.assign({}, defaultConfigForm),
      configFormFules: {},// 配置信息验证规则
      showSearchProConfigEdit: false,// 控件配置 弹框
      fieldDropdownItems: [],// 参数字段模型列表
      editResDataObj: null,// 编辑时，对象
      tableDataLoading: false,
      submitBtnLoading: false,
      removeBtnLoading: false,
      currentSelectedRow: null,
      currentSelectedItem: null,
      rowCheck: null,
      showEditDialogFlag: false,
      comparatorOptions: sqlServeSearchType,

      tableData: [],
      validRules: {
        field: [
          { required: true, message: '必填' },
        ]
      }
    }
  },
  methods: {
    // 比较符 改变对应的备份值，方便前端重置时使用
    change_comparator(row,$event){
      row.comparatorBackup =$event
    },
    // 关系 改变对应的备份值，方便前端重置时使用
    change_relationSign(row,$event){
      row.relationSignBackup =$event
    },
    // 添加新字段 控件配置表格信息
    async addNewField() {
      const $table = this.$refs["searchProConfigEditTableRef"]
      let randomIndex = Math.floor(Math.random() * 10000 + 1)
      let records = {
        field: "newField" + randomIndex,
        title: "新字段" + randomIndex,
        width: 200,
        fieldOrder: 100
      }
      await $table.insertAt(records, -1)
    },
    // 提交控件配置弹框
    submitSearchProConfigEdit() {
      this.configFormItem.CDATAS = this.getControlOptionsData()
      this.currentEditSearchProConfigRow.controlOptions = cloneDeep(this.configFormItem)
      this.showSearchProConfigEdit = false // 关闭当前弹框
    },
    // 获取控件类型配置表格信息
    getControlOptionsData() {
      let _tablefullData = []
      const $table = this.$refs["searchProConfigEditTableRef"]
      if ($table) {
        _tablefullData = $table.getTableData().visibleData //处理条件之后的全量表体数据
      }
      return _tablefullData
    },
    // 清空配置
    resetConfig() {
      this.$confirm('此操作将删除控件配置数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentEditSearchProConfigRow.controlOptions = null
        this.dataModelList = []
        this.searchProConfigEditTableData = []
        this.configFormItem = Object.assign({}, defaultConfigForm)
        setTimeout(() => {
          this.showSearchProConfigEdit = false // 关闭当前弹框
        }, 100)
      }).catch(() => {

      });

    },
    // 数据源API 改变
    async change_sourceApi(formItem, actionName) {
      this.configFormItem.loadDataConfig = this.getCtrlLoadDataConfig(actionName)
      this.searchProConfigEditTableData = []
      let dataSetId = this.useFormatParamsFn.getVFormDataSetID(this.configFormItem.actionName);
      // debugger
      if (dataSetId != -1 && !!this.configFormItem.actionName) {
        this.loading_tableData = true
        let _modelList = await this.getDataModelList(dataSetId)
        this.dataModelList = this.formatDataModel(_modelList)
        // debugger
        this.searchProConfigEditTableData = cloneDeep(this.dataModelList)
        this.loading_tableData = false
      } else {
        this.dataModelList = []
      }
    },
    // 获取控件API 配置参数
    getCtrlLoadDataConfig(actionName) {
      let _config = ""
      let queryList = this.designer.formConfig.queryList
      let currentQueryList = queryList.filter(item => {
        if (item.value == actionName) {
          return item
        }
      })
      if (currentQueryList && currentQueryList.length > 0) {
        _config = currentQueryList[0]
      }
      return _config
    },
    // 获取数据源数据
    async getDataModelList(datasetId) {
      let dataModelList = []
      let params = {
        condition: "",
        datasetId: datasetId
      }
      // let _url = "api/MD/DataSetModel/GetAll"
      // let _url = "api/MD/DataSetModel/GetAllDataSet"
      let _url = "api/MD/DataSetModel/GetList"
      await request['get'](_url, null, params).then(res => {
        if (res && res.Datas && res.Datas.length > 0) {
          dataModelList = res.Datas
        }
      })
      return dataModelList
    },
    // 格式化模型数据
    formatDataModel(dataList) {
      let newDataList = []
      if (dataList && dataList.length > 0) {
        let innerField = [
          "CID",
          "CDATETIME_CREATED",
          "CUSER_CREATED",
          "CDATETIME_MODIFIED",
          "CUSER_MODIFIED",
          "CINSTANCE_ID",
          "CROWREMARK",
          "CENTERPRISE_CODE",
          "CORG_CODE",
        ]
        dataList.forEach(oldItem => {
          let newItem = {
            field: oldItem.CCOLUMN_NAME,
            title: oldItem.CCOLUMN_DESC,
            CID: oldItem.CID,
            titleHelp: "",
            iisShowList: 0,
            isSelectTextField: 0,
            isSelectKeyField: 0,
            width: 200,
            titleAlign: "center",
            fieldOrder: 100,
            align: "center",
            controlType: "text",
            groupTitle: "",
            iisSummary: 0,
            fieldRules: ""
          }
          // 内置字段不显示
          if (innerField.includes(newItem.field)) {
            newItem.iisShowList = 0
          }
          newDataList.push(newItem)
        })
      }

      return newDataList
    },
    // 打开当前控件配置弹框
    showSearchProConfigEditFn(row) {
      if (!this.needConfigType.includes(row.controlType)) {
        this.$message({
          type: 'warning',
          message: '当前控件无需配置,请选择其它控件类型！ '
        });
        return
      }
      this.currentEditSearchProConfigRow = row
      // controlOptions: Object
      // CDATAS: Array(0)
      // CIS_PAGE: "N"
      // CPAGE_SIZE: 10
      // actionName: "query1-15968"
      // matchField: ""
      // searchParams: ""
      // sourceKey: ""
      try {
        if (row.controlOptions) {
          this.configFormItem = cloneDeep(row.controlOptions)
          if (this.configFormItem.CDATAS && this.configFormItem.CDATAS.length > 0) {
            this.searchProConfigEditTableData = this.configFormItem.CDATAS
          }

        }


      } catch (error) {

      }

      this.showSearchProConfigEdit = true
    },
    // 直接使用下拉获取对应字段的模型
    change_dropdownItem(item, row, index) {
      row.field = item.field
      row.title = item.title
    },
    // 点击加载模型后 回调事件
    afterLoadDataSetAllModel(dataList) {
      this.fieldDropdownItems = dataList
    },
    // 数据源切换后，获取当前查询的配置信息
    dataSourceChangeEvent(val) {
      // debugger
      if (!val) {
        return
      }
      let actionName = this.optionModel.actionName
      let queryList = this.designer.formConfig.queryList
      let currentQueryList = queryList.filter(item => {
        if (item.value == actionName) {
          return item
        }
      })
      if (currentQueryList && currentQueryList.length > 0) {
        this.loadDataConfig = currentQueryList[0]
      }
    },
    // 删除高级查询配置信息
    removeConditionClick() {
      this.$confirm('此操作将删除高级查询配置数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteData()
      }).catch(() => {

      });

    },
    async deleteData() {
      let _self = this
      this.removeBtnLoading = true
      let _url = `/api/SYSTEM/Common/Remove`

      let params = {
        "table": "TBL_SYS_ADVANCED_SEARCH", // 固定参数
        CID: this.optionModel.searchProSettingID // 页面ID
      }

      await request['post'](_url, params).then(res => {
        if (!!res.Success) {
          this.removeBtnLoading = false
          this.optionModel.searchProSettingID = null
          this.tableData = []
          this.$message({
            message: !!res.Content ? res.Content : '执行成功',
            type: 'success'
          });
          setTimeout(() => {
            // 关闭高级搜索配置弹框
            _self.showEditDialogFlag = false
          }, 300)
        }
      })
    },
    // 查询高级查询配置信息
    async loadConditionData() {
      // debugger
      this.tableDataLoading = true
      let _url = `/api/SYSTEM/Common/PageQuery`
      let params = {
        "table": "TBL_SYS_ADVANCED_SEARCH", // 固定参数
        "where": "CID=" + this.optionModel.searchProSettingID
      }
      await request['post'](_url, params).then(res => {
        if (!!res.Success && res.Datas && res.Datas.length > 0) {
          let defaultData = res.Datas[0]
          this.editResDataObj = res.Datas[0]
          let tableCCONDITION = JSON.parse(defaultData.CCONDITION)
          if (tableCCONDITION.tableData) {
            this.tableData = tableCCONDITION.tableData
          }
          if (tableCCONDITION.loadDataConfig) {
            this.loadDataConfig = tableCCONDITION.loadDataConfig
          }
        }
      })
      this.tableDataLoading = false
    },
    currentChange(tableInfo) {
      if (tableInfo.row.parentId == 0) {
        this.currentSelectedRow = tableInfo.row
      } else {
        this.currentSelectedRow = null
      }
      this.currentSelectedItem = tableInfo.row
    },
    // 设置时间格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
    setDateTimeDefaultVal(type, defaultVal) {
      let dValue = null
      let dateFormatStr = ""
      // 指定日期 格式化 格式
      switch (type) {
        case "date":
          dateFormatStr = 'YYYY-MM-DD'
          break;
        case "dateTime":
          dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
          break;
        case "daterange":
        dateFormatStr = 'YYYY-MM-DD'
        break;
        case "datetimerange":
            dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
            // if(!!subItem.pvalue){
            //   _defaultValue = this.setDateTimeRangeDefaultVal(subItem.ptype,subItem.pvalue) 
            // }
      
        break;
        default:
          dateFormatStr = ""
          break
      }
      if (!!defaultVal) {
        if (defaultVal == 'curdate') {
          // 当前日期
          dValue = dayjs().format(dateFormatStr);
        } else if (defaultVal.includes('-')) {
          // 指定日期加减
          //dayjs().add(7, 'day').format('YYYY-MM-DD');
          //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
          let daysetArray = defaultVal.split('-')
          dValue = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
        } else {
          //空日期
          dValue = null
        }
      }

      return dValue
    },
    // 设置时间范围格式默认值 (当前日期:curdate 当前日期-1月:subtract-1-month  当前日期+1天:add-1-day  当前日期-1年:add-2-year)
    setDateTimeRangeDefaultVal(type, defaultVal) {
      const end = new Date();
      const start = new Date();

      try {

        let dValue = [start, end]
        let dateFormatStr = ""
        let defaultValList = defaultVal.split('#')
        // 指定日期 格式化 格式
        switch (type) {
          case "daterange":
            dateFormatStr = 'YYYY-MM-DD'
            break;
          case "datetimerange":
            dateFormatStr = 'YYYY-MM-DD HH:mm:ss'
            break;
          default:
            dateFormatStr = ""
            break
        }
        // 开始时间 和 结束时间
        // curdate#curdate
        if (!!defaultValList[0]) {
          if (defaultValList[0] == 'curdate') {
            // 当前日期
            dValue[0] = dayjs().format(dateFormatStr);
          } else if (defaultValList[0].includes('-')) {
            // 指定日期加减
            //dayjs().add(7, 'day').format('YYYY-MM-DD');
            //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
            let daysetArray = defaultValList[0].split('-')
            dValue[0] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
          } else {
            //空日期
            dValue[0] = start
          }
        }
        if (!!defaultValList[1]) {
          if (defaultValList[1] == 'curdate') {
            // 当前日期
            dValue[1] = dayjs().format(dateFormatStr);
          } else if (defaultValList[1].includes('-')) {
            // 指定日期加减
            //dayjs().add(7, 'day').format('YYYY-MM-DD');
            //dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'); 
            let daysetArray = defaultValList[1].split('-')
            dValue[1] = dayjs()[daysetArray[0]](Number(daysetArray[1]), daysetArray[2]).format(dateFormatStr)
          } else {
            //空日期
            dValue[1] = end
          }
        }
        return dValue
      } catch (error) {
        return [start, end]
      }

    },
    // 添加条件
    async addConditionClick() {
      //debugger
      if (!!this.currentSelectedRow) {
        await this.insertRow(this.currentSelectedRow, 'bottom')
      } else {
        this.addCommonRow()
      }
    },
    async addCommonRow() {
      let randomparentId = Math.floor(Math.random() * 1000000 + 1)
      let randomID = 1000 + this.tableData.length + 1 + randomparentId
      let record = {
        id: randomID,
        parentId: -1,
        field: '',
        title: '',
        isCheck: true,
        controlType: 'text',
        defaultValue: '',
        tableName: '',
        comparator: 0,// Equal 等于
        relationSign: 0,
        comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        value: '',
        // dataFrom: 'local'
      }
      const $table = this.$refs["vxeTableRef"]
      //debugger
      if (this.currentSelectedItem && this.currentSelectedItem.parentId == -1) {
        const { row: newRow } = await $table.insertAt(record, this.currentSelectedItem)
        $table.setCurrentRow(newRow) // 插入子节点
      } else {
        const { row: newRow } = await $table.insertAt(record, -1)
        $table.setCurrentRow(newRow) // 插入子节点
      }

    },
    // 将 树状结构数据 转为 扁平数据
    toFlat(treeData = []) {
      let _self = this
      return treeData.reduce((result, node) => {
        if (node.children) {
          result.push(..._self.toFlat(node.children));
          // 如果有节点 添加前先移除
          delete node.children
        }
        result.push(node);
        return result;
      }, []);
    },
    // 添加条件组
    async addGroupClick() {
      let randomparentId = Math.floor(Math.random() * 1000000 + 1)
      let randomID = 1000 + this.tableData.length + 1 + randomparentId
      let record = {
        id: randomID,
        parentId: 0,
        field: '',
        title: '',
        isCheck: true,
        controlType: 'text',
        defaultValue: '',
        tableName: '',
        comparator: 0,// Equal 等于
        relationSign: 0,
        comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        value: '',
        //dataFrom: 'local'
      }
      const $table = this.$refs["vxeTableRef"]
      // insertAt 如果 -1 则插入到目标节点底部
      //$table.insertAt(newRow,-1)
      const { row: newRow } = await $table.insertAt(record, -1)
      $table.setCurrentRow(newRow) // 插入子节点
      this.currentSelectedRow = newRow
      this.currentSelectedItem = newRow
    },
    // 打开高级搜索配置
    async searchProSettingFn() {
      this.showEditDialogFlag = true
      this.tableData = []
      this.editResDataObj = null
      if (!!this.optionModel.searchProSettingID) {
        await this.loadConditionData()
        const $table = this.$refs["vxeTableRef"]
        if ($table) {
          $table.setAllTreeExpand(true)
        }

      }
    },
    // 将 移除 树状结构数据 下的children节点数据
    removeTreeChildren(treeData = []) {
      let newTreeData = []
      treeData.forEach(item => {
        delete item.children
        newTreeData.push(item)
      })
      return newTreeData
    },
    getConditionTableDataJSON() {
      let _tablefullData = []
      const $table = this.$refs["vxeTableRef"]
      if ($table) {
        _tablefullData = $table.getTableData().visibleData //处理条件之后的全量表体数据
      }
      // debugger
      // 将 移除 树状结构数据 下的children节点数据
      _tablefullData = this.removeTreeChildren(_tablefullData)
      let CCONDITION = {
        tableData: _tablefullData,
        loadDataConfig: this.loadDataConfig,
        // 方便扩展其它字段
      }
      return JSON.stringify(CCONDITION)
    },
    guid() {
      function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
      }
      return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
    },
    async submitEditDialog() {
      let _self = this
      this.submitBtnLoading = true
      let _url = `/api/SYSTEM/Common/Insert`
      // 编辑时候
      if (!!this.optionModel.searchProSettingID) {
        _url = `/api/SYSTEM/Common/Edit`
      }
      let defaultGUID = this.guid() //生成一个随机的GUID值来标识一个数据对象
      let modelJson = {
        CPID: "", // 页面ID
        CSCHEME_NAME: "系统默认",//方案名称
        CVISIBILITY_USER_ID: 0,//可见性(用户ID) NULL或o表示所有人,反之为用户Id
        CCONDITION: this.getConditionTableDataJSON(),//查询条件 json
        CTEMPLATE_ID: "", //模板ID
        // CGROUP: "" // 组
        CPARENT: defaultGUID // 组
      }

      // 编辑时候，需要把CID 赋值上
      if (!!this.optionModel.searchProSettingID) {
        modelJson.CID = this.optionModel.searchProSettingID
        modelJson = Object.assign({}, modelJson, this.editResDataObj)
        modelJson.CCONDITION = this.getConditionTableDataJSON()
      }
      // 移除多余字段
      delete modelJson.RowIndex
      // debugger
      let params = {
        "table": "TBL_SYS_ADVANCED_SEARCH", // 固定参数
        modelJson: JSON.stringify(modelJson)
      }
      // debugger
      await request['post'](_url, params).then(res => {
        if (!!res.Success) {
          this.submitBtnLoading = false
          // 添加时候
          if (!_self.optionModel.searchProSettingID) {
            _self.optionModel["searchProSettingID"] = res.Datas.CID
            _self.optionModel["searchProSettingCPARENT"] = defaultGUID
          }
          setTimeout(() => {
            // 关闭高级搜索配置弹框
            _self.showEditDialogFlag = false
          }, 300)
          this.$message({
            message: !!res.Content ? res.Content : '执行成功',
            type: 'success'
          });
        }
      })
    },
    async removeSearchProConfigRow(row) {
      const $table = this.$refs["searchProConfigEditTableRef"]
      await $table.remove(row)

    },
    async removeRow(row) {
      const $table = this.$refs["vxeTableRef"]
      await $table.remove(row)
      if (row.parentId == 0) {
        this.currentSelectedRow = null
        this.currentSelectedItem = null
      }

    },
    async insertRow(currRow, locat) {
      let randomID = 1000 + this.tableData.length + 1 + (Math.floor(Math.random() * 1000000 + 1))
      const $table = this.$refs["vxeTableRef"]
      // 如果 null 则插入到目标节点顶部
      // 如果 -1 则插入到目标节点底部
      // 如果 row 则有插入到效的目标节点该行的位置
      if (locat === 'current') {

        const record = {
          id: randomID,
          parentId: currRow.parentId,
          field: '',
          title: '',
          isCheck: true,
          controlType: 'text',
          defaultValue: '',
          tableName: '',
          comparator: 0,// Equal 等于
          relationSign: 0,
          comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
          value: '',
          //dataFrom: 'local'
        }
        const { row: newRow } = await $table.insertAt(record, currRow)
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      } else if (locat === 'top') {

        const record = {
          id: randomID,
          parentId: currRow.id,
          field: '',
          title: '',
          isCheck: true,
          controlType: 'text',
          defaultValue: '',
          tableName: '',
          comparator: 0,// Equal 等于
          relationSign: 0,
          comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
          value: '',
          //dataFrom: 'local'
        }
        const { row: newRow } = await $table.insert(record)
        await $table.setTreeExpand(currRow, true) // 将父节点展开
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      } else if (locat === 'bottom') {

        const record = {
          id: randomID,
          parentId: currRow.id,
          field: '',
          title: '',
          isCheck: true,
          controlType: 'text',
          defaultValue: '',
          tableName: '',
          comparator: 0,// Equal 等于
          relationSign: 0,
          comparatorBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
        relationSignBackup: 0,// 原始数值备份 主要用于前置 重置时，数值还原
          value: '',
          //dataFrom: 'local'
        }
        const { row: newRow } = await $table.insertAt(record, -1)
        await $table.setTreeExpand(currRow, true) // 将父节点展开
        await $table.setCurrentRow(newRow) // 插入子节点
        this.currentSelectedItem = newRow
      }
    }
  }
}
</script>