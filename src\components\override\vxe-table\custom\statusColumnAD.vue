<template>
    <div>
        <el-switch @change="changeEvent" :disabled="(disabled||localDisabled)"  v-model="currentItem" :active-value='activeValue' :inactive-value='inactiveValue' v-bind="$attrs" v-on="$listeners"></el-switch>
    </div>
</template>
<script>
import config from '@/config'
import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: "statusColumnAD",// 自定义 状态列 可更新空间 :disabled="true"
    props: {

       // 当前字段:其它配置信息
       paramsItem: {
           type: Object,
           default() {
               return {}
           }
       },
       disabled:{
            type:Boolean,
            default:false
        },
       // 当前字段:value
       currentValue: {
           type: [String, Number,Boolean],
           default: ""
       },
       field:{
            type:String,
            default:""
        },
        controlType:{
            type:String,
            default:""
        },
      
        itemOptions:{
            type:Object,
            default(){
                return {}
            }
        },
          // 当前字段选择行
        rowData: {
            type: Object,
            default() {
                return {}
            }
        },
   },
 
    data() {
        return {
            localDisabled:false,
            currentItem:"",
            config: config,
            activeValue:"A", // 默认值
            inactiveValue:"D",// 默认值
            useFormatParamsFn: useFormatParams(this),
        }
    },
    computed:{
        // 控件配置信息
        controlConfig(){
            let _config ={}
            try {
                 _config =this.itemOptions.paramsItem.controlConfig    
            } catch (error) {
                _config ={}
            }
            return _config
        },
    },
    watch:{
        currentValue(n, o) {
           // debugger
            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
            }
        },
      
    },
    created(){
      this.setActiveValueAndInactiveValue()
    },
    mounted() {
        this.$nextTick(() => {
            //debugger
            this.currentItem = this.currentValue
            try {
                this.localDisabled = !this.controlConfig.actionName?true:false
            } catch (error) {
                this.localDisabled = true
            }
            
        })
    },
    methods: {
        // 设置 activeValue 和 inactiveValue 的初始值，默认A/D
        setActiveValueAndInactiveValue(){
            //debugger
            if(!!this.controlConfig.sourceKey){
                if(this.controlConfig.sourceKey.includes('#')){
                    this.controlConfig.sourceKey.replace(" ",'').split('#').forEach((item,index)=>{
                        if(index==0){
                            this.activeValue =item.replace(" ",'')
                        }
                        if(index==1){
                            this.inactiveValue =item.replace(" ",'')
                        }
                    })
                }
            }
        },
       async updateData(){
            let actionName= this.controlConfig.actionName
            let datasetId = this.useFormatParamsFn.getVFormDataSetID(actionName)
           
            let _url=`api/MD/DataSet/ExecuteByDataSetId`
            let params ={
                Id: datasetId,
                Parameter: {
                    ...this.rowData,
                }
            }
            await request["post"](_url, params).then(res => {
                if (!!res.Success) {
                    this.$message({
                        type: 'success',
                        message: '操作成功'
                    });          
                }
            });
           
        },
        // 事件改变触发事件
        changeEvent(val) {
            //debugger
            let _self = this
            this.$nextTick(()=>{
                let params ={
                        value: val,
                        text: val,
                }
                this.rowData[this.field] =val
                this.$nextTick(()=>{
                    this.updateData()
                })
               // _self.$emit("changeEvent",params)
            })
            
        },
    }
}
</script>
<style lang="scss" scoped>

</style>