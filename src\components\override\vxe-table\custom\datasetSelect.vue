<template>
    <el-select clearable size="small" ref="elSelectRef" :loading="optoinsLoading" v-model="currentItem" v-bind="$attrs"
        v-on="$listeners" placeholder="请选择">
        <el-option v-for="(item, index) in options" :key="item.value + index" :label="item.label" :value="item.value"
            style="display: none" />
        <vxe-grid :loading="loading" @cell-click="cellClickEvent" size="mini" height="240px" border :data="gridOptions.data"
            :columns="gridOptions.columns">
            <template #top>
                <div class="flex">
                    <el-input clearable @keyup.native="keyupEvent" size="mini" v-model="searchkey"
                        placeholder="请输入关键词 回车 ENTER"></el-input>
                    <el-button :loading="loading" @click="searchData()" size="mini" type="primary">查询</el-button>
                </div>
            </template>
            <template #pager>
                <vxe-pager v-show="showPager" size="mini" :current-page.sync="tablePage.currentPage"
                    :page-size.sync="tablePage.pageSize" :total="tablePage.total" @page-change="handlePageChange">
                </vxe-pager>
            </template>
        </vxe-grid>
    </el-select>
</template>
<script>
// import { orderBy } from "lodash-es";
import request from '@/libs/request'
export default {
    name: 'datasetSelect',
    components: {},
    props: {
        // 数据集文本描述
        CDATASET_NAME:{
            type: String,
            default: ""
        },
        sourceId: {
            type: [String, Number],
            default: ""
        },
        fieldName: {
            type: String,
            default: ""
        },
        FormData: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    data() {
        return {
            currentItem: null,
            configFormItem: null,
            showPager: true,
            loading: false,
            optoinsLoading: false,
            displayField: [],// 需要显示的字段
            searchkey: "",
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            options: [],
            selectConfig: {
                key: "",
                text: ""
            },
            gridOptions: {
                columns: [
                    {
                        type: "seq",
                        fixed: "left",
                        title: "序号",
                        align: 'center',
                        headerAlign: 'center',
                        width: 60,
                    },

                    {
                        field: "CNAME",
                        title: "数据集",
                        headerAlign: 'center',
                        align: 'left',
                    },

                ],
                data: []
            }
        }
    },
    watch: {
        sourceId(n, o) {

            this.loadDatasetList(n)
        }
    },
    mounted(){
        // debugger
        // let tt = this.CDATASET_NAME
        if(!!this.$attrs.value){
            this.currentItem =this.$attrs.value
        }
        this.loadDatasetList(this.sourceId)
    },
    methods: {
        // 编辑时触发 下拉数据
        initOptions(){
            this.options = [{ value: this.$attrs.value, label: this.CDATASET_NAME }]
        },
        // 加载数据集
        async loadDatasetList(dataSourceId) {

            this.loading = true
            let dataSetList = []
            let params = {
                condition: `{CID:${dataSourceId}${!!this.searchkey ? ",keyword:\"" + this.searchkey +"\"": ""}}`,//keyword 全局搜索
                start: this.tablePage.currentPage,
                length: this.tablePage.pageSize
            }

            let _url = "api/MD/DataSet/GetPagelist"
            await request['get'](_url, null, params).then(res => {
                if (res && res.Datas && res.Datas.length > 0) {
                    //debugger
                    dataSetList = res.Datas
                    this.options = this.formatDatas(res.Datas)
                   
                    this.gridOptions.data = res.Datas
                    //  是否分页
                    this.tablePage.total = res.TotalRows
                } else {
                    this.gridOptions.data = []
                    this.options = [{ value: -1, label: "请选择" }]
                }
            })
            this.loading = false
            this.optoinsLoading = false
            setTimeout(() => {
                this.loading = false
                this.optoinsLoading = false
            }, 10000)
            return dataSetList
        },
        // 单击当前行
        cellClickEvent(tableInfo) {
            //debugger
            this.currentItem = tableInfo.row.CID
            this.FormData[this.fieldName]= tableInfo.row.CID
            this.FormData["CDATASET_NAME"]= tableInfo.row.CNAME
            let params = {
                field: this.fieldName,
                value: tableInfo.row.CID,
                text: tableInfo.row.CNAME
            }
            this.$emit("changeEvent", params)
            this.$refs["elSelectRef"].blur();
        },
        searchData() {
            // debugger
            this.loadDatasetList(this.sourceId)
        },
        // 表格查询框 回车键
        keyupEvent($event) {
            if ($event.keyCode === 13) {
                this.loadDatasetList(this.sourceId)
            }
        },

        // 格式化返回数据
        formatDatas(dataList) {
            let isExistDefaultItem = false
            let options = [{ value: -1, label: "请选择" }]
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                     if (!!oldItem.CNAME) {
                        let newItem = {
                            value: oldItem.CID,
                            label: oldItem.CNAME,
                        }
                        options.push(newItem)
                     }
                     if(oldItem.CID == this.$attrs.value){
                        // 当前页 是否存在 选择的数据
                        isExistDefaultItem = true
                     }

                })
                if(!isExistDefaultItem){
                    // 编辑时 当前页 不存在 选择的数据 ,添加选中的数据
                        let defaultItem = { value: this.$attrs.value, label: this.CDATASET_NAME }  
                        options.push(defaultItem)
                }
              
            }
            return options
        },
        handlePageChange(val) {
            //debugger
            this.tablePage.currentPage = val.currentPage
            this.loadDatasetList(this.sourceId)
        }
    }
}
</script>


<style lang="scss" scoped></style>