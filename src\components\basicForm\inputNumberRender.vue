<template>
    <el-input-number @change="changeEvent" v-model="currentItem" v-bind="$attrs" v-on="$listeners">
    </el-input-number>
</template>
<script>
const cloneDeep = require("clone-deep");
import emitter from '@/libs/mitt'
import config from '@/config'
import { useDebounceFn } from '@vueuse/core'
import request from '@/libs/request'
export default {
    name: 'selectRender',
    components: {},
    props: {
        
        // 当前编辑表单字段名
        field: {
            type: String,
            default: ""
        },
        // 当前表单对象
        formData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 数据来信:表单form,表格talbe, 搜索：search
        dataFrom: {
            type: String,
            default: "form"
        },
        // 当前字段:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段:value
        currentValue: {
            type: [String, Number],
            default: ""
        },

    },
    data() {
        return {

            config: config,
            loading: false,
            prefixIcon: "", // 头部图标,用于是否加载中 显示
            currentItem: '',// 当前选择项
            currentItemText: '',// 当前选择项描述
            selectDataList: [],// 当前列表数据
        }
    },
    computed: {
        // 其它配置参数，如 计算表达式参数 fieldExtension.Calculator
        CalculatorStr() {
            let _CalculatorStr = null
            try {
                if (this.paramsItem?.fieldExtension) {
                    let fieldExtension = JSON.parse(this.paramsItem.fieldExtension)
                    _CalculatorStr = fieldExtension.Calculator
                }

            } catch (error) {
                _CalculatorStr = null
            }
            return _CalculatorStr
        },
        // 其它配置参数，是否联动 计算 表达式 功能
        iisAssociated() {
            let _iisAssociated = false
            try {
                //debugger
                if (this.paramsItem?.fieldExtension) {
                    let fieldExtension = JSON.parse(this.paramsItem.fieldExtension)
                    _iisAssociated = fieldExtension.iisAssociated
                }

            } catch (error) {
                _iisAssociated = false
            }
            return _iisAssociated
        }

    },
    watch: {
        currentValue(n, o) {
            //debugger
            if (!!n) {
                this.currentItem = n
            } else {
                // 清空数据
                this.currentItem = ''
            }
        },

    },
    beforeMount() {
        //  移除监听
        emitter.off("CalculatorEmit")
    },
    mounted() {
       // console.log('mounted attrs!'+JSON.stringify(this.$attrs))
        this.$nextTick(() => {
           // debugger
            this.currentItem = this.currentValue
        })
        // 注册监听 标准弹框事件
        emitter.on("CalculatorEmit", (params) => {
            this.CalculatorEmitFn(params)
        })
        this.debouncedFn_Calculator = useDebounceFn((params) => {
            this.CalculatorForm(params)
        }, 200)
    },
    destroyed() {
        // 组件销毁 移除监听
        emitter.off("CalculatorEmit")
    },
    methods: {
        // 调用接口 计算表达式 并且返回绑定现在字段
        CalculatorForm(params){
            let _self = this
             console.log('CalculatorForm', params)
            let _url=  `/api/md/formdata/Calculator`
            let postParams ={
                TableDetail:this.formData, // 当前编辑表单对象实体
                Expression:{} // 需要计算的表达式，需要遵循C#的计算表达式规则
            }
            // 如果表达式为空，则不调用接口计算 
            // CalculatorStr>>字段A=(字段B+字段C)#字段E=(字段F*字段G)
            if(!this.CalculatorStr){
                return 
            }else{
                let  _CalculatorList =[]
                if(this.CalculatorStr.includes("#")){
                    _CalculatorList = this.CalculatorStr.split("#")
                }else{
                    _CalculatorList = [this.CalculatorStr]
                }
                _CalculatorList.forEach(item=>{
                    //字段A=(字段B+字段C)
                    let _ExpressionList = item.split("=") //字段A
                    postParams.Expression[_ExpressionList[0]] = _ExpressionList[1] //(字段B+字段C)
                })
            }
            request['post'](_url, postParams).then(res=>{
                let _DataObj = res.Datas
                for (const [key,val] of Object.entries(_DataObj)) {
                    _self.formData[key] = val
                }
            })
        },
        // 计算器回调
        CalculatorEmitFn(params) {
            // 只触发 当前编辑字段且是勾选联动功能
            if(this.iisAssociated && params.field == this.field){
                this.debouncedFn_Calculator(params)
            }
        },
        // debugger
        changeEvent(val) {
            let _self = this
            this.$nextTick(() => {
                let params = {
                    value: val,
                    text: val,
                    field:_self.field,
                }
                _self.$emit("changeEvent", params)
                if(!!_self.iisAssociated){
                    // 联动初始化>>触发联动事件
                    setTimeout(()=>{
                        emitter.emit("CalculatorEmit",params)
                    },300)
                }
            })
        }
    }

}
</script>


<style lang="scss"></style>