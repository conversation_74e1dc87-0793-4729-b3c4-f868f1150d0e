<template>
    <div class="flex w-full justify-between">
        <div class="flex justify-start">
           
            <el-button @click="buttonOnClickEvent(item, index)" v-show="item.check" :publicAttribute="publicAttribute"
                :size="(!!item.size ? item.size : 'small')" :title="item.label" :type="!!item.type ? item.type : ''"
                :icon="!!item.icon ? item.icon : ''" :key="index + item.label"
                v-for="(item, index) in footerButtons"><span>&nbsp;</span>{{ item.label }}
            </el-button>
        </div>
    </div>
</template>
<script>
import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
const { actionType } = require('@/enum/enumData')
export default {
    // 表格底部操作按钮列表
    name: "tableFooterActionBtttons",
    props: {
        // 底部按钮 列表
        footerButtons: {
            type: Array,
            default() {
                return []
            },
        },
    },
    components: {},
    // 注入列表页面整体实体 
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    data() {
        return {
            actionType: actionType,
            useHandleVFormEventFn: useHandleVFormEvent(this),
            publicAttribute: {
                // value:""
            },// 对外开发属性值
        }
    },
    computed: {
        pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
    },
    methods: {

        // 数据类型来源》
        buttonOnClickEvent(subItem, index, $event) {
            this.useHandleVFormEventFn.handleCommonClickEvent(subItem)
        },

    }
}
</script>
<style lang="scss" scoped></style>