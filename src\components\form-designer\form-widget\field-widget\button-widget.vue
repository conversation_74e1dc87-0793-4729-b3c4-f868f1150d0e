<template>
  <!--修改前字段名称 ref="fieldEditor" -->
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState" :display-style="field.options.displayStyle"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId">
    <el-button :ref="field.options.name"  :type="field.options.type" :size="field.options.size"
               :plain="field.options.plain" :round="field.options.round"
               :circle="field.options.circle" :icon="field.options.icon"
               :disabled="field.options.disabled"
               :publicAttribute="publicAttribute"
               style="margin-left:5px;"
               :loading="loadingBtn"
               @click.native="handleButtonWidgetClickEvent">
      {{field.options.label}}</el-button>
  </static-content-wrapper>
</template>

<script>
  import cloneDeep from "clone-deep"
  import StaticContentWrapper from './static-content-wrapper'
  import emitter from '@/utils/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import { useHandleVFormEvent } from "@/hooks/useHandleVFormEvent"
// import { entries } from "xe-utils";
  export default {
    name: "button-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    // 注入列表页面整体实体 
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },

    },
    data(){
      return {
        defaultPostFormData:null, // 如果为表单时候，设备保存默认表单的初始化数据，方便重置，回归
        loadingBtn:false,
        useHandleVFormEventFn: useHandleVFormEvent(this),
        publicAttribute:{
          value:"",
        },// 对外开发属性值
      }
    },
    components: {
      StaticContentWrapper,
    },
    computed: {
       pageInstance() {
            // 获取列表示例
            return this.getPageInstance()
        },
        // currentRow(){
        //   let _tableRow= this.$store.state.currentRow.value
        //   return _tableRow
        // }
    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initEventHandler()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
       setTimeout(()=>{
        this.initBtnPostForm()  
       },1000)
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {
      clearValidate(){
          //await this.initBtnPostForm()
        let _formData = Object.assign({},this.defaultPostFormData)
          if(_formData && Object.keys(_formData).length>0){
            for (const [key,val] of Object.entries(_formData)) {
              // 逐个清除验证信息，resetForm 不好使
              this.pageInstance.$refs['preForm'].clearValidate(key)
            }
           
          }
      },
        // 触发控件 事件 重新加载数据
       async reSearchData(params){
         // debugger
          let _self = this
          //除验证信息
           this.clearValidate()
           let _currentRow = cloneDeep(_self.getCurrentRow())
            if(_currentRow){
              _self.pageInstance.$refs['preForm'].setFormData(_currentRow)
            }
       
        },
         // 获取当前表格选中行模板数据
        getCurrentRow() {
            let routerName = this.$route.name
            let rowData = this.$store.state.currentRow.value[routerName];
            let currentRow = rowData
            //debugger
            return currentRow
        },
      // 初始化默认表单数据
      async initBtnPostForm(){
       // debugger
        try {
            if(this.field.options.innerEventType && (this.field.options.innerEventType =='resetForm'||this.field.options.innerEventType =='postForm')){
            let _tempData = await this.pageInstance.$refs['preForm'].getFormData(false)
            this.defaultPostFormData = cloneDeep(_tempData)
          }
        } catch (error) {
          
        }
     
      },
      // 验证表单，如果正确就提交
       preSubmitEvent() {
        //debugger
          this.loadingBtn = true
          this.pageInstance.$refs['preForm'].validateForm((valid) => {
              if (valid) {
                    this.submitEvent()
              } else {
                  this.$message.error("请输入必填项！")
                  this.loadingBtn = false
              }
          });
        },
        // 重置表单数据，并清除校验状态
        resetForm(){
          //除验证信息
          this.clearValidate()
          if(!!this.defaultPostFormData){
            let _formData = Object.assign({},this.defaultPostFormData)
            this.pageInstance.$refs['preForm'].setFormData(_formData)
          }else{
            
            this.pageInstance.$refs['preForm'].resetForm()
          }
          
        },
        // 获取点击事件配置信息
        getClickEventItem(){
            let _clickEventItem =null
            if(this.field.options.eventList && this.field.options.eventList.length>0){
                let clickEventItemList = this.field.options.eventList.filter(item=>{
                    if(item.otherParams && item.otherParams.eventType=='click'){
                      return item
                    }
                })
                if(clickEventItemList && clickEventItemList.length>0){
                  _clickEventItem =  clickEventItemList[0]
                }
            }
            return _clickEventItem
        },
        // 正式提交表单
        async submitEvent(){
            let subItem = this.getClickEventItem()
           
            await this.useHandleVFormEventFn.handleCommonClickEvent(subItem)
            this.loadingBtn = false
        },
      handleButtonWidgetClickEvent(){
       // debugger
        if (!!this.designState) { //设计状态不触发点击事件 innerEventType: "postForm"
          return
        }
        if(this.field.options.innerEventType && this.field.options.innerEventType =='postForm'){
          this.preSubmitEvent()
          return
        }
        if(this.field.options.innerEventType && this.field.options.innerEventType =='resetForm'){
          this.resetForm()
          return
        }
        if (!!this.field.options.onClick) {
          let customFn = new Function(this.field.options.onClick)
          customFn.call(this)
        } else {
          this.dispatch('VFormRender', 'buttonClick', [this]);
        }
      }
    }

  }
</script>

<style lang="scss" scoped>
  @import "../../../../styles/global.scss"; //* static-content-wrapper已引入，还需要重复引入吗？ *//

</style>
