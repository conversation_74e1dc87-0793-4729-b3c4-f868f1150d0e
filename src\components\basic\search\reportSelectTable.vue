<template>
    <el-select clearable size="mini" :publicAttribute="publicAttribute"  @change="changeEvent" ref="elSelectRef" @focus="focusEvent()" v-model="currentItem"
        v-bind="$attrs" v-on="$listeners" :loading="loading" placeholder="请选择">
        <el-option v-for="(item, index) in options" :key="item.value + index" :label="item.label" :value="item.value"
            style="display: none" />

            <div class="flex justify-between items-center">
                <el-input clearable style="padding:2px 5px;width: 73%"
                @keyup.native="keyupEvent" size="mini" 
                v-model="searchkey"
                placeholder="请输入关键字 回车 ENTER"></el-input>
                <el-button style="margin-right: 5px;" @click="loadSelectOptions()" size="mini" type="primary">查询</el-button>
            </div>
                <!-- 111 -->
        <el-table  :loading="loadingTable" @cell-click="cellClickEvent" size="mini" height="170px" :data="gridOptions.data" border>

            <el-table-column  :key="index + item.field" v-for="(item, index) in gridOptions.columns" header-align="center"
                :width="item.width" :prop="item.field" :label="item.title">
            </el-table-column>

        </el-table>
        <div class="flex justify-end items-center ">
            <el-pagination v-show="showPager" background small :current-page="tablePage.currentPage"
                :page-size="tablePage.pageSize" @current-change="handlePageChange" :layout=layout
                :total="tablePage.total">
            </el-pagination>
        </div>
      
    </el-select>
</template>
<script>
import baseMixin from './minxin'
// import request from '@/libs/request'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: 'reportSelectTable',
    mixins:[baseMixin],
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    components: {},
    props: {
        refreshRandom:{
            type:[Number,String],
            default:null
        }, // 刷新标记
        defaultValue:String, // 默认值
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: {
            type: String,
            default: ""
        },
        // 查询配置
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            //fieldModel_select: "",
            useFormatParamsFn: useFormatParams(this),
            value: "",// 当前选中的值的，对外暴露属性，仅供外部查询使用
            currentItem: null,
            configFormItem: null,
            showPager: false,
            loading: false,
            loadingTable:false,// 加载表格数据
            displayField: [],// 需要显示的字段
            searchkey: "",
            layout:"prev, pager, next",
            layoutAll:"total, sizes, prev, pager, next, jumper",
            //
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            options: [],
            selectConfig: {
                key: "",
                text: ""
            },
            gridOptions: {
                columns: [],
                data: []
            }
        }
    },
    computed: {
        // add by andy
        pageInstance() {
          // 获取列表示例
          return this.getPageInstance()
        },
    },
    watch:{
        // 是否刷新
        refreshRandom(n,o){
            if(n){
                if(!!this.isPassiveDataLoad){
                    this.focusEvent(true)
                }
                console.log("===reportSelectTable=====refreshRandom-======")
            }
        },
        searchkey(n,o){
            this.publicAttribute.searchKey = n
        },
        currentItem(n,o){
            this.publicAttribute.value = n
        },
        isPassiveDataLoad() {
            // debugger
            let isPassiveDataLoad =false
            try {
                isPassiveDataLoad = this.configOptions.isPassiveDataLoad
            } catch (error) {
                isPassiveDataLoad =false
            }
            return isPassiveDataLoad
        }
    },
    mounted(){
        this.init()
        this.publicAttribute.controlType ="reportSelectTable"
        // this.focusEvent()
        if(!this.isPassiveDataLoad){
            this.focusEvent()
        }
        if(this.defaultValue){
            // 设置默认值
            this.currentItem = this.defaultValue+""
        }
    },
    methods: {
       
        // 单击当前行
        cellClickEvent(tableInfo) {
            // debugger
            this.currentItem = tableInfo[this.selectConfig.key]
            // this.publicAttribute.value = this.currentItem
            let params = {
                field: this.fieldName,
                value: this.currentItem,
                text: tableInfo[this.selectConfig.text]
            }
            this.$emit("changeEvent", params)
            this.$refs["elSelectRef"].blur();
        },
        // 表格查询框 回车键
        keyupEvent($event) {
            if ($event.keyCode === 13) {
                this.loadSelectOptions()
            }
        },
        focusEvent(research=false) {
            //debugger
            //  CIS_PAGE: "N",// 是否分页
            //   CPAGE_SIZE: 10,// 分页数
            // 初始化 下拉配置信息
            if (this.options.length == 0 || research) {
                if (this.configOptions) {
                    this.configFormItem = this.configOptions
                    if (this.configFormItem.hasOwnProperty('CDATAS') && !!this.configFormItem.CDATAS) {
                        this.getSelectKeyText(this.configFormItem.CDATAS)
                        this.tablePage.pageSize = Number(this.configFormItem.CPAGE_SIZE)
                        this.showPager = (this.configFormItem.CIS_PAGE == 'Y' ? true : false)
                    }
                }
                this.loadSelectOptions()
            }
        },
        // 获取配置的KEY & TEXT
        getSelectKeyText(dataList) {
            //debugger
            //dataList = orderBy(dataList, ["fieldOrder"], ["asc"]); // 升序排序
            this.displayField = [] // 重置需要显示的字段
            this.gridOptions.columns = []
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (!!item.isSelectTextField) {
                        this.selectConfig.text = item.field
                    }
                    if (!!item.isSelectKeyField) {
                        this.selectConfig.key = item.field
                    }
                    // debugger
                    if (!!item.iisShowList) {
                        let newItem = {
                            field: item.field,
                            headerAlign: 'center',
                            width: Number(item.width),
                            title: !!item.title ? item.title : item.field
                        }
                        this.displayField.push(newItem)
                        this.gridOptions.columns.push(newItem)
                    }
                })
            } else {
                this.selectConfig = {
                    key: "",
                    text: ""
                }
            }


        },
        // 设置下拉初始化功能
        async loadSelectOptions() {
           // debugger
            //this.loading = true
            this.loadingTable = true
            this.gridOptions.data=[]
            let  _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.configFormItem.actionName,this.setTotalPage);
            if(_dataList && _dataList.length>0){
                this.options = this.formatDatas(_dataList)
                this.gridOptions.data = _dataList   
            }else{
                this.options = [{ value: "", label: "全部" }]
            }
          
            this.loading = false
            this.loadingTable = false
            setTimeout(() => {
                this.loading = false
                this.loadingTable = false
            }, 10000)
        },
        setTotalPage(params){
            //debugger
            if(params && params.hasOwnProperty('TotalRows')){
                this.tablePage.total = Number(params.TotalRows)
            }
        },
        // 格式化返回数据
        formatDatas(dataList) {
            let options = [{ value: "", label: "全部" }]
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    if (!!oldItem[this.selectConfig.text]) {
                        let newItem = {
                            value: oldItem[this.selectConfig.key]+"",
                            label: oldItem[this.selectConfig.text]
                        }
                        options.push(newItem)
                    }

                })
            }
            return options
        },
        handlePageChange(_currentPage) {
            //debugger
            // this.tablePage.pageSize  = val.pageSize;
             this.tablePage.currentPage = _currentPage
             this.publicAttribute.pageIndex = _currentPage
            // this.publicAttribute.pageSize = val.pageSize
            this.loadSelectOptions()
        }
    }
}
</script>


<style lang="scss" scoped></style>