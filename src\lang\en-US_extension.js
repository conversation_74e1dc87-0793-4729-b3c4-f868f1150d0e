export default {
  extension: {
    widgetLabel: {
      card: 'Card',
      alert: 'Alert',
      defaultmenubutton: 'default Menu',
      vxetable: 'vxetable',
      splitpanes: 'splitpanes',
      echarts: 'echarts',
      customTree: 'customTree',
      standardFrame: 'standardFrame',
      iconInfo: 'iconInfo',
      colorBlockInfo: 'colorBlockInfo',
      collapseDesc: 'collapseDesc',
      tabMenu: 'tabMenu',
      //divContainer: 'divContainer',
      descriptions: 'descriptions',
    },

    setting: {
      cardFolded: 'Folded',
      cardShowFold: 'Show Fold',
      cardWidth: 'Width Of Card',
      cardShadow: 'Shadow',

      alertTitle: 'Title',
      alertType: 'Type',
      description: 'Description',
      closable: 'Closable',
      closeText: 'Text On Close Btn',
      center: 'Center',
      showIcon: 'Show Icon',
      effect: 'Effect',

    },

  }
}
