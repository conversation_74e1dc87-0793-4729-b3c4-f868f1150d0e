
// 统一处理VFormPopup
import { useFormatParams } from "@/hooks/useFormatParams"
import { useHandleVFormApi } from "@/hooks/useHandleVFormApi"
import { useHandleVFormPopup } from "@/hooks/useHandleVFormPopup"
import { useHandleVFormComponents } from "@/hooks/useHandleVFormComponents"
import request from '@/libs/request'
export function useHandleVFormEvent(vueInstance, refFormName = 'preForm') {
   return {
      data: [],
      vueInstance: vueInstance,
      refFormName: refFormName,
      useFormatParamsFn: useFormatParams(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      useHandleVFormApiFn: useHandleVFormApi(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      useHandleVFormPopupFn: useHandleVFormPopup(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      useHandleVFormComponentsFn: useHandleVFormComponents(vueInstance),// 注意这里 不再试this 而是顶层的vueInstance
      testFn(params) {
         // return this.data
         //debugger
         console.log("hello world")
      },
      // 通用方法，重新加载数据，刷新数据
      // 注入列表页面整体实体
      // 使用界面必须注入 inject: ['getPageInstance'],
      reSearchData(params) {
         debugger
        // 原始数据,可用来获取自身参数等 获取临时参数后置空
         this.vueInstance.$store.commit("set_currentClickCtrlOptions",params.originSubItem)
         let ctrnameList = params.triggerCtrlNames
         if (ctrnameList && ctrnameList.length > 0) {
            // 可同时触发多个控件
            for (let i = 0; i < ctrnameList.length; i++) {
               let triggerCtrlName = ctrnameList[i].value
               if (!!!triggerCtrlName) {
                  // 如果为空跳过
                  return
               }

               this.reSearchDataByName(triggerCtrlName, params)
            }
         }

      },
      // 根据控件REF名称刷新数据
      reSearchDataByName(triggerCtrlName, params = { resetPage: false }) {
         //debugger
         params.currentTriggerCtrlName = triggerCtrlName
         let controlRef = null
         try {
            // 根据来源控件父集 判断使用
            if (!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxDrawerRef.editPreForm") {
               controlRef = this.vueInstance.pageInstance.$refs["editContentBoxDrawerRef"].$refs["editPreForm"].widgetRefList[triggerCtrlName]
            } else if (!!this.vueInstance.sourceVFormRenderState && this.vueInstance.sourceVFormRenderState == "editContentBoxRef.editPreForm") {
               controlRef = this.vueInstance.pageInstance.$refs["editContentBoxRef"].$refs["editPreForm"].widgetRefList[triggerCtrlName]
            }
            else {
               controlRef = this.vueInstance.pageInstance.$refs[this.refFormName].widgetRefList[triggerCtrlName]
            }

         } catch (error) {
            console.error("====reSearchDataByName===controlRef实例为空=")
            return
         }
         if (triggerCtrlName.includes('echarts')) {
            if (controlRef) {
               // 对应的控件必须暴露 reSearchData 这个方法
               //let { resetPage = false } = params
               // debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }

            }
         } else if(triggerCtrlName.includes('defaultmenubutton')){
            if (controlRef) {
               // 对应的控件必须暴露 reSearchData 这个方法
              // let { resetPage = false } = params
               // debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }

            }
         }
         
         else if(triggerCtrlName.includes('button') && !triggerCtrlName.includes('defaultmenubutton')){
            if (controlRef) {
               // 对应的控件必须暴露 reSearchData 这个方法
              // let { resetPage = false } = params
               // debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }

            }
         }
         else if(triggerCtrlName.includes('descriptions')){
            if (controlRef) {
               // 对应的控件必须暴露 reSearchData 这个方法
               //let { resetPage = false } = params
               // debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }

            }
         }
         else if(triggerCtrlName.includes('card')){
            //debugger
            if (controlRef) {
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }
            }
         }
         
         else if(triggerCtrlName.includes('colorBlockInfo')){
            //debugger
            if (controlRef) {
               //debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }
            }
         }
         else if(triggerCtrlName.includes('customTree')){
            //debugger
            if (controlRef) {
               //debugger
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }
            }
         }
         else if(triggerCtrlName.includes('collapseDesc')){
            //debugger
            if (controlRef) {
               //debugger
               if (controlRef.reSearchData) {
                 
                  controlRef.reSearchData(params)
               }
            }
         }
         // vxetable 包含了tab
         else if(triggerCtrlName.includes('tab') && !triggerCtrlName.includes('vxetable') && triggerCtrlName.length>5){
            //debugger
            if (controlRef) {
               if (controlRef.reSearchData) {
                  controlRef.reSearchData(params)
               }
            }
         }
         else {
           // debugger
            if (controlRef) {
               // 正常$refs 对象
               controlRef = controlRef.$refs[triggerCtrlName]
               if (controlRef) {
                  // 对应的控件必须暴露 reSearchData 这个方法
                  let { resetPage = false } = params
                  // debugger
                  if (controlRef.reSearchData) {
                     controlRef.reSearchData(params)
                  }

               }
            }
         }

      },
      // 通用处理点击事件
      // let triggerCtrlNames = {
      //    formCtrlName: "card",
      //    triggerCtrlNames: this.widget.options.triggerCtrlNames, //重点： 触发控件名称
      //    originSubItem:item,// 原始数据
      //  }
      //  subItem.triggerCtrlNames = triggerCtrlNames
     async handleCommonClickEvent(subItem,boxType='popup') {
         //  debugger
         let actionType = this.useFormatParamsFn.getOtherParamsValue("actionType", subItem)
         if (!!actionType && actionType != "-1") {
            // 自定义执行功能
            switch (actionType) {
               case "api": // 执行API
                 return await this.handleApiByItem(subItem)
                  break;
               case "popup": //弹框功能
                  this.useHandleVFormPopupFn.handlePopupByItem(subItem)
                  break;
                case "logicCode": //自定义逻辑代码
                  this.onClickFn(subItem)
                  break;
                case "standardBulletBox": //标准弹框
                  this.standardBulletBoxFn(subItem)
                  break; 
               // case "formEditBox": //打开基本表单弹框
               //    this.formEditBoxFn(subItem)
               //    break;     
               case "ctrlComp": //控件组件
                  this.useHandleVFormComponentsFn.executeActionFunction(subItem,boxType)
                  break;
               case "postParamsToCtrl": //传参数给控件列表
                  this.reSearchData(subItem.triggerCtrlNames)
                  break;
               case "openNewPage": //打开新页面 并传递指定参数
                  this.openNewPageFn(subItem)
                  break;   
               default:
                  break;
            }
         } else {
            console.error("未配置点击事件...WARNING")
         }
      },
     async openNewPageFn(subItem){
         let paramsStr=''
         let _fieldsDefalutValues = await this.useFormatParamsFn.getNormalParamsValue(subItem)
         if (_fieldsDefalutValues && Object.keys(_fieldsDefalutValues).length > 0) {
            for (const [key,val] of Object.entries(_fieldsDefalutValues)) {
               if(paramsStr==''){
                  paramsStr=paramsStr+'?'+key+'='+val
               }else{
                  paramsStr=paramsStr+'&'+key+'='+val
               }
            }
         }
         try {
             let _url= subItem.actionParams.newPageUrl
             if(_url){
               window.open(`${_url+paramsStr}`); 
             }
         } catch (error) {
            
         }
      
      },
   // 标准弹框执行API 固定参数,无需动态获取
   async executApi_standardBulletBox(subItem,params) {
      let actionName = subItem.actionParams.standardBulletBoxDataSetID
      //debugger
      let postParams ={
            Id:actionName,
            Parameter:params
      }
      let _url = `api/MD/DataSet/GetListByDataSetId` // 固定取值地址
      console.warn("标准弹框执行API,参数：",postParams)
      let res = await request["post"](_url, postParams)
      
      setTimeout(() => {
         this.useHandleVFormApiFn.afterSuccessEvents(res, subItem)
      }, 600)
      if (subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
         let eventList = subItem.afterSuccessOrErrorEvents
         if (eventList && eventList.length > 0) {
            for (let i = 0; i < eventList.length; i++) {
               let eventItem = eventList[i]
               this.handleCommonClickEvent(eventItem)
            }
         }

      }
      return res
   },
      // 标准弹框
   standardBulletBoxFn(subItem){
     // debugger
      this.vueInstance.$actions.setGlobalState({
         type: "standardBulletBox",
         title:subItem.actionParams.standardBulletBoxTitle,
         sourceKey: subItem.actionParams.standardBulletBoxCode,
         fieldName:subItem.actionParams.standardBulletBoxParentsID,
         rowField:!!subItem.actionParams.standardBulletBoxRowField?subItem.actionParams.standardBulletBoxRowField:null,
         parentField:!!subItem.actionParams.standardBulletBoxParentField?subItem.actionParams.standardBulletBoxParentField:null,
         fullPath:"defaultmenubutton",
         dataObj:subItem
     });
   }, 
   // 是否显示基础表单弹框
   formEditBoxFn(subItem){
      try {
         this.vueInstance.$store.commit("set_showBasicFormBoxParams",subItem)
         this.vueInstance.$store.commit("set_showBasicFormBoxState",true)
      } catch (error) {
         
      }
    
   },  
      // 当前行改变前 逻辑处理，仅供 custoomJSCode eventList
   async onClickFn(subItem) {
     // debugger
      /////////////////////////////click///按钮点击事件，拦截器////////////////////////////////////////
      if (subItem.otherParams && subItem.otherParams.eventType && subItem.otherParams.eventType == "click") {
        // 当前行改变前 事件列表，目前支持一个事件，多个事件时，只处理第一个 自定义代码控件
        let clickItem = subItem
        if (clickItem.otherParams && clickItem.otherParams.actionType && clickItem.otherParams.actionType == "logicCode") {
          // JSCODE 逻辑代码
          let paramsData = subItem
          let clickStr = this.useFormatParamsFn.getOtherParamsValue("click", clickItem)
          let clickFn = new Function('paramsData', clickStr)

          if (!!clickStr) {
            try {
              //bind的传参和call相同
               await clickFn.call(this.vueInstance, paramsData)
            } catch (error) {
              this.vueInstance.$message({
                message: '(=当前页面配置==click====) 错误，请检查！！！',
                type: 'error'
              });
            }
          }
        } 
      }
    },
      // (特殊处理) 禁用，启用 
      postFormatData_iisDisabled_iisEnable(commonParams,type="iisEnable"){
         // debugger
         try {
            if(commonParams.hasOwnProperty("Parameter") && commonParams.Parameter.hasOwnProperty("PostData")  && commonParams.Parameter.PostData.hasOwnProperty("CSTATE")){
               if(type=='iisEnable'){
                  commonParams.Parameter.PostData.CSTATE = "A"
               }
               if(type=='iisDisabled'){
                  commonParams.Parameter.PostData.CSTATE = "D"
               }
            }
         } catch (error) {
            return commonParams
         }
       
         return commonParams
      },
      /**
       * 
       * @param {*} subItem 
       *  actionParams: Object
       *  {
       *      actionName: "query1"
              query: {}
       *  }
          afterSuccessOrErrorEvents: Array(0)
          canRemove: false
          check: true
          label: "新增"
          otherParams: Object
          value: "iisAdd"
       */
      async handleApiByItem(subItem) {
         //debugger
         let actionDesc = subItem.label
         let actionName = this.useFormatParamsFn.getActionParamsValue("actionName", subItem)
         let hasRequireParamsNull = await this.useFormatParamsFn.checkRequireParamsIsNull(actionName);
         if(hasRequireParamsNull){
             // console.error(" 必填参数为空，跳过查询！！")
             return
         }
         // 集合类型（结果集：查询类；操作集:主要为添加，修改，删除等操作）
         let excuteEventType = this.useFormatParamsFn.getOtherParamsValue("excuteEventType", subItem)
         let sameApiWidgetsList = this.useHandleVFormApiFn.getSameApiWidgets(actionName)
         //debugger
         if (sameApiWidgetsList && sameApiWidgetsList.length > 0) {
            // 如果包含了相同的api，就运行该组件的reLoadData()方法
            //console.log("has same sameApiWidgetsList");
            this.useHandleVFormApiFn.runSameApiWidgets(subItem, sameApiWidgetsList)
            return
         }
         // 列头查询 preUrl 路径固定不变
         let _url = this.useFormatParamsFn.getVFormDataSearchUrl(actionName);
         if (!!!_url) {
            _url = `api/MD/DataSet/GetListByDataSetId` // 结果集：查询类
            if (excuteEventType == "ExecuteByDataSetId") {
               _url = `api/MD/DataSet/ExecuteByDataSetId` // 操作集:主要为添加，修改，删除等操作
            }
         }

         // 公共参数
         let commonParams = await this.useFormatParamsFn.getVFormSearchParams(actionName);
        // debugger
         // 特殊处理（启用、禁用）
         commonParams = this.postFormatData_iisDisabled_iisEnable(commonParams,subItem.value)
         // 自身组件参数
         let selfParams =await this.useFormatParamsFn.getCommonParamsValue(subItem);
         // 合并参数 = （自身组件参数+公共参数），自身参数会覆盖公共参数，如果参数一样的话
         let params = Object.assign({}, commonParams, selfParams)
         /////////////////////////////onBeforeSubmit///代码提交前，拦截器////////////////////////////////////////
         let onBeforeSubmitStr = subItem.onBeforeSubmit
         // debugger
         let onBeforeSubmit = new Function('paramsData', onBeforeSubmitStr)
         if (!!onBeforeSubmitStr) {
               try {
                  params = onBeforeSubmit.call(this, params)
               } catch (error) {
                 this.vueInstance.$message({
                   message: '（handleApiByItem====onBeforeSubmit）错误，请检查！！！',
                   type: 'error'
                 });
                 return
               }
             }
         if(!!!params){
            // 如果返回参数为空，直接返回，停止执行其它代码
            return
         }
          /////////////////////////////onBeforeSubmit///代码提交前，拦截器////////////////////////////////////////
          /////////////////////////////subItem.otherParams.onBeforeSubmit///API代码提交前，拦截器////////////////////////////////////////
          let btnOnBeforeSubmitStr = subItem.otherParams.onBeforeSubmit
          let btnOnBeforeSubmit = new Function('paramsData', btnOnBeforeSubmitStr)
          if (!!btnOnBeforeSubmitStr) {
                try {
                   params = btnOnBeforeSubmit.call(this, params)
                } catch (error) {
                  this.vueInstance.$message({
                    message: '（handleApiByItem====btnOnBeforeSubmitStr）错误，请检查！！！',
                    type: 'error'
                  });
                  return
                }
              }
          if(!!!params){
             // 如果返回参数为空，直接返回，停止执行其它代码
             return
          }
          /////////////////////////////onBeforeSubmit///代码提交前，拦截器////////////////////////////////////////
        try {
         let res = await request["post"](_url, params)
         if ([4, 5].includes(res.Data)) {
            res.Datas = JSON.parse(res.Datas)
         }
         //res.Datas = {\"Success\":true,\"Content\":\"\",\"code\":null,\"msg\":null,\"stackTrace\":null,\"Data\":null,\"Datas\":1,\"TotalRows\":1}
         if (res && !!res.Success) {
            if ([4, 5].includes(res.Data)){
                 // WEB API 返回结构
               if (res.Datas && !!res.Datas.Success) {
                  this.vueInstance.$message.success(actionDesc + " " + (!!res.Content ? res.Content : '执行成功'))
                  setTimeout(() => {
                     this.useHandleVFormApiFn.afterSuccessEvents(res, subItem)
                  }, 600)
                  if (subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
                     let eventList = subItem.afterSuccessOrErrorEvents
                     if (eventList && eventList.length > 0) {
                        for (let i = 0; i < eventList.length; i++) {
                           let eventItem = eventList[i]
                    
                           this.handleCommonClickEvent(eventItem)
                        }
                     }

                  }

               } else {
                  this.vueInstance.$message.error(actionDesc + "失败: " + (!!res.Datas.Content ? res.Datas.Content : '执行失败'))
               }
            }else{
               // 普通的API 如数据集
               this.vueInstance.$message.success(actionDesc + " " + (!!res.Content ? res.Content : '执行成功'))
               setTimeout(() => {
                  this.useHandleVFormApiFn.afterSuccessEvents(res, subItem)
               }, 600)
               if (subItem.hasOwnProperty("afterSuccessOrErrorEvents")) {
                  let eventList = subItem.afterSuccessOrErrorEvents
                  if (eventList && eventList.length > 0) {
                     for (let i = 0; i < eventList.length; i++) {
                        let eventItem = eventList[i]
                        this.handleCommonClickEvent(eventItem)
                     }
                  }

               }
            }

         } else {
            this.vueInstance.$message.error(actionDesc + "失败: " + (!!res.Content ? res.Content : '执行失败'))
         }
        } catch (error) {
            this.vueInstance.$message.error(actionDesc + "执行失败: ")
        }
       

      },
   }
}