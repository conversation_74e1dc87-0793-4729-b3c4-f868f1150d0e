<template>
    <div class="flex">
        <!-- <el-input readonly placeholder="请选择1" clearable v-bind="$attrs" v-on="$listeners">
            <el-button @click.native="popupButtonClickEvent" slot="append" icon="el-icon-zoom-in"></el-button>
        </el-input> disabled-->
        <el-select  v-bind="$attrs"  v-on="$listeners" placeholder="请选择">
            <el-option
                v-for="item in selectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.key">
            </el-option>
        </el-select>
        <el-button  @click.native="popupButtonClickEvent" slot="append" icon="el-icon-plus"></el-button>
        <!-- <standardFrameModal @change="changeEvent" :sourceKey="paramsItem.sourceKey" ref="standardFrameModalRef"></standardFrameModal> -->
        <!-- <vxe-modal title="标准弹框" :position="{ top: 50 }" v-model="showPopupModal" width="600" show-footer>
            <template #default>
                <vxe-grid ref="inputPopupGridRef" v-bind="gridOptions">
                    <template #top>
                        <div style="margin-bottom:5px" class="flex">
                            <el-input clearable @keyup.native="keyupEvent" size="mini" v-model="searchkey"
                                placeholder="请输入关键词"></el-input>
                            <el-button style="margin-left:10px" size="mini" :loading="loadingBtn" @click="searchEvent()"
                                type="primary">确定</el-button>
                        </div>

                    </template>
                    <template #pager>
                        <vxe-pager v-show="showPager" size="mini" :current-page.sync="tablePage.currentPage"
                            :page-size.sync="tablePage.pageSize" :total="tablePage.total" @page-change="handlePageChange">
                        </vxe-pager>
                    </template>
                </vxe-grid>
            </template>
            <template v-slot:footer>
                <el-button size="medium" @click="cancelBtn()">取消</el-button>
                <el-button size="medium" :loading="loadingBtn" @click="preSubmitEvent()" type="primary">确定</el-button>
            </template>
        </vxe-modal> -->
    </div>
</template>
<script>
import emitter from '@/libs/mitt'
// import { orderBy } from "lodash-es";
//import standardFrameModal from './standardFrameModal.vue'
// import request from '@/libs/request'
export default {
    name: "standardFrame",// 标准弹框
   // components:{standardFrameModal},
    props: {
         // 当前编辑字段名称
        field: {
            type: String,
            default: ""
        },
        // 数据来信:表单form,表格talbe
        dataFrom: {
            type: String,
            default: "form"
        },
        // 当前字段表单:其它配置信息
        formData: {
            type: Object,
            default() {
                return {}
            }
        },
        // 当前字段选择行:其它配置信息
        paramsItem: {
            type: Object,
            default() {
                return {}
            }
        },
         // 数据查询配置（数据来自表单tableHeader,主要配置分页，排序字段等信息）,用于保存布局
         dataSearchOptions: {
            type: Object,
            default() {
                return {};
            },
        },
    },
    data() {
        return {
            selectOptions:[],
            // searchkey: "",
            // // 设置 操作列 模板
            // checkBoxColumn: [{ fixed: 'left', type: 'checkbox', headerAlign: 'center', align: 'center', width: 60 }],
            // showPager: false,// 是否显示分页
            // tablePage: {
            //     total: 0,
            //     currentPage: 1,
            //     pageSize: 10
            // },
            // loadingBtn: false,
            // showPopupModal: false,
            // searchOptions: {
            //     TableHeader: {
            //         // "CID": "6C5F9190B0284269BEBABF2C6AE30145",
            //         // "CDATETIME_MODIFIED": "2022-04-09T13:59:30",
            //         // "CDATETIME_CREATED": "2022-04-09T13:59:30",
            //         // "CPOPUP_CODE": "APS_EQUIPMENT",
            //         // "CPOPUP_NAME": "计划排产-设备",
            //         // "CRETURN_VALUE": "CID",
            //         // "CRETURN_TEXT": "CEQUIPMENT_CODE",
            //         // "CDATA_SOURCE_CODE": "APS_EQUIPMENT",
            //         // "CDATA_SOURCE_ID": "65567BCB4DAD47C7A8B1159A58A7604C",
            //         // CDATA_SOURCE_SET_ID
            //         // "CDATA_SOURCE_NAME": "计划排产-设备",
            //         // "CIS_PAGER": "Y",
            //         // "CIS_MULTI": "N",
            //         // "CDATA_SOURCE_TYPE_NAME": "SQL类型",
            //         // "CDATA_SOURCE_CONTENT": "(SELECT DISTINCT C.CID, C.CEQUIPMENT_CODE, C.CEQUIPMENT_NAME from TBL_APS_WORK_CENTER A INNER JOIN TBL_APS_WORK_CENTER_ITEM B ON A.CID=B.CWC_ID INNER JOIN dbo.TBL_EAM_EQUIPMENT C ON B.CEQUIPMENT_ID=C.CID WHERE A.CSTATE='A') T",
            //         // "CSTATE": "A"
               
            //     },
            //     TableDetail: [
            //         // {
            //         //     "CPOPUP_ID": "6C5F9190B0284269BEBABF2C6AE30145",
            //         //     "CFIELD_NAME": "CID",
            //         //     "CFIELD_TEXT": "CID",
            //         //     "CIS_SHOW": "N",
            //         //     "CIS_QUERY": "N",
            //         //     "CSEQ": 1,
            //         //     "IS_READONLY": true,
            //         //     "IS_UPDATE": false,
            //         //     "CID": "655571511D414D5BB7721BEF1BD935B8",
            //         //     "CUSER_CREATED": "SYS",
            //         //     "CDATETIME_CREATED": "2022-04-13T10:23:34.033",
            //         //     "CUSER_MODIFIED": "SYS",
            //         //     "CDATETIME_MODIFIED": "2022-04-13T10:23:34.033",
            //         //     "CSTATE": "A",
            //         //     "CINSTANCE_ID": "",
            //         //     "CROWREMARK": "",
            //         //     "CENTERPRISE_CODE": "*",
            //         //     "CORG_CODE": "*"
            //         // },
            //         // {
            //         //     "CPOPUP_ID": "6C5F9190B0284269BEBABF2C6AE30145",
            //         //     "CFIELD_NAME": "CEQUIPMENT_CODE",
            //         //     "CFIELD_TEXT": "设备编码",
            //         //     "CIS_SHOW": "Y",
            //         //     "CIS_QUERY": "Y",
            //         //     "CSEQ": 2,
            //         //     "IS_READONLY": true,
            //         //     "IS_UPDATE": false,
            //         //     "CID": "2FF35DEAA9EB4F49B5FD2F0555BC3CA3",
            //         //     "CUSER_CREATED": "SYS",
            //         //     "CDATETIME_CREATED": "2022-04-13T10:23:34.033",
            //         //     "CUSER_MODIFIED": "SYS",
            //         //     "CDATETIME_MODIFIED": "2022-04-13T10:23:34.033",
            //         //     "CSTATE": "A",
            //         //     "CINSTANCE_ID": "",
            //         //     "CROWREMARK": "",
            //         //     "CENTERPRISE_CODE": "*",
            //         //     "CORG_CODE": "*"
            //         // },
            //         // {
            //         //     "CPOPUP_ID": "6C5F9190B0284269BEBABF2C6AE30145",
            //         //     "CFIELD_NAME": "CEQUIPMENT_NAME",
            //         //     "CFIELD_TEXT": "设备名称",
            //         //     "CIS_SHOW": "Y",
            //         //     "CIS_QUERY": "Y",
            //         //     "CSEQ": 3,
            //         //     "IS_READONLY": true,
            //         //     "IS_UPDATE": false,
            //         //     "CID": "7B622733F80A4D499A46F51800A15ADA",
            //         //     "CUSER_CREATED": "SYS",
            //         //     "CDATETIME_CREATED": "2022-04-13T10:23:34.033",
            //         //     "CUSER_MODIFIED": "SYS",
            //         //     "CDATETIME_MODIFIED": "2022-04-13T10:23:34.033",
            //         //     "CSTATE": "A",
            //         //     "CINSTANCE_ID": "",
            //         //     "CROWREMARK": "",
            //         //     "CENTERPRISE_CODE": "*",
            //         //     "CORG_CODE": "*"
            //         // }
            //     ]
            // },
            // //表格属性信息
            // gridOptions: {
            //     loading:false,
            //     border: true,
            //     resizable: true,
            //     showOverflow: true,
            //     rowConfig: {
            //         isCurrent: true //当鼠标点击行时，是否要高亮当前行
            //     },
            //     maxHeight: 400,// 使用height: 内部报错
            //     align: 'left',
            //     columns: [],
            //     data: [
            //         // { CID: '1111', CEQUIPMENT_CODE: 123, CEQUIPMENT_NAME: "设备名称" },
            //         // { CID: '2222', CEQUIPMENT_CODE: 456, CEQUIPMENT_NAME: "设备名称2" }
            //     ]
            // }
        }
    },
    computed:{ 
        dataSourceID(){
            let _id =""
            try {
                _id = this.dataSearchOptions.dbId
            } catch (error) {
                _id =""
            }
            return _id
        }
    },
    beforeMount(){
         //  移除监听
         emitter.off("getStandardBulletBox")
    },
    mounted(){
        this.init()
        // 注册监听 标准弹框事件
        emitter.on("getStandardBulletBox",(params)=>{
                this.changeEvent(params)
        })
    },
    destroyed(){
        // 组件销毁 移除监听
        emitter.off("getStandardBulletBox")
    },
    // watch: {
    //     showPopupModal(n, o) {
    //         // debugger
    //         if (!!n) {


    //         }else{
    //             // 退出时，清空数据
    //              this.gridOptions.data =[]
    //              this.loadingBtn = false
    //              this.gridOptions.loading = false
    //         }

    //     }
    // },
    methods: {
        init(){
            let defaultItem ={
                key:this.formData[this.field],
                label:!!this.formData[this.field+`_TEXT`]?this.formData[this.field+`_TEXT`]:this.formData[this.field]
            }
            //debugger
            this.selectOptions =[defaultItem]
        },
        // 表格查询框 回车键
        // keyupEvent($event) {
        //     if ($event.keyCode === 13) {
        //         this.loadPopupTableData()
        //     }
        // },
         // 标准弹框 点击事件
         popupButtonClickEvent() {
            this.$actions.setGlobalState({
                type: "standardBulletBox",
                //title:"andy标准弹框",
                sourceKey: this.paramsItem.sourceKey,
                fieldName:this.field,
                fullPath:this.$route.fullPath,
            });
            // let standardFrameModalRef = this.$refs["standardFrameModalRef"]
            // if(standardFrameModalRef){
            //     standardFrameModalRef.showPopupModal= true
            // }
        },
        // searchEvent() {
        //     this.loadPopupTableData()
        // },
        // // 分页触发事件
        // handlePageChange(val) {
        //    // debugger
        //     this.tablePage.currentPage = val.currentPage
        //     this.loadPopupTableData()
        // },
        // saveDataToVuex(cacheKey, data) {
        //     let tempData = {
        //         key: cacheKey,
        //         value: data
        //     }
        //     this.$store.commit("set_ctrlData", tempData)
        // },
        // async getPopupConfigByCode() {
        //    // debugger
        //     let _url = `api/SYS/Popup/GetPopupByCode` // 固定取值地址
        //     let params = {
        //         Condition: this.paramsItem.sourceKey
        //     }
        //     let dataConfig = null
        //     let cacheKey = "standardFrame_getPopupConfigByCode"
        //     let _tempCtrlData = this.getCtrlDataFromCacheByKey(cacheKey)
        //     if (!!!_tempCtrlData) {
        //         //
        //         await request["post"](_url, params).then(res => {
        //             //debugger
        //             if (res) {
        //                 dataConfig = res.Datas
        //                 this.searchOptions = dataConfig
        //                 this.getColumnsData()
        //                 this.saveDataToVuex(cacheKey, dataConfig)
        //             }
        //         });
        //     } else {
        //         this.getColumnsData()
        //         dataConfig = _tempCtrlData
        //     }

        // },
        // async getDBDataByCode() {
        //    // debugger
        //     let innerParams =[]
        //     let _url = `api/MD/DataSet/GetListByDataSetId` // 固定取值地址
        //     let params = {
        //         Id: this.searchOptions.TableHeader.CDATA_SOURCE_SET_ID, //数据集ID
        //         Parameter: { }
        //     }
        //     // 关键词查询
        //     // params.Parameter[this.searchOptions.TableHeader.CRETURN_TEXT] = this.searchkey
        //     if(!!this.searchOptions.TableHeader.CMAP_PARAMETER){
        //         let _CMAP_PARAMETER= JSON.parse(this.searchOptions.TableHeader.CMAP_PARAMETER) //.datasourceParams
        //         if(_CMAP_PARAMETER.hasOwnProperty("datasourceParams")){
        //             innerParams = _CMAP_PARAMETER.datasourceParams
        //         }
        //     }
        //     // 添加固定查询参数和查询关键词
        //        if (innerParams.length > 0) {
        //         innerParams.forEach(item => {
        //             if (!!item.mfield && item.mfield != "$searchKey") {
        //                 params.Parameter[item.pfield] = item.mfield
        //             }
                    
        //              if (item.mfield == "$searchKey") {
        //                 if (!!this.searchkey) {
        //                     params.Parameter[item.pfield] = this.searchkey
        //                 }
        //             }
        //             //debugger
        //             if (item.mfield == "$PageIndex" && this.searchOptions.TableHeader.CIS_PAGER == 'Y') {
        //                 params.Parameter[item.pfield] = this.tablePage.currentPage
        //             }
        //             if (item.mfield == "$PageSize" && this.searchOptions.TableHeader.CIS_PAGER == 'Y') {
        //                 params.Parameter[item.pfield] = this.tablePage.pageSize
        //             }
        //             // if (item.pfield == "PageIndex") {
        //             //     params.Parameter[item.pfield] = item.mfield
        //             // }
        //             // if (item.pfield == "PageSize") {
        //             //     params.Parameter[item.pfield] = item.mfield
        //             // }
        //             // if (item.pfield == "start") {
        //             //     params.Parameter[item.pfield] = item.mfield
        //             // }
        //             // if (item.pfield == "length") {
        //             //     params.Parameter[item.pfield] = item.mfield
        //             // }
        //         })
        //     }
        //     // 分页参数 固定处理模式
        //     // if (this.searchOptions.TableHeader.CIS_PAGER == 'Y' && params.Parameter.hasOwnProperty("PageIndex")) {
        //     //     params.Parameter["PageIndex"] = this.tablePage.currentPage
        //     //     params.Parameter["PageSize"] = this.tablePage.pageSize
        //     // }
        //     // if (this.searchOptions.TableHeader.CIS_PAGER=='Y'&& params.Parameter.hasOwnProperty("start")) {
        //     //     params.Parameter["start"] = this.tablePage.currentPage
        //     //     params.Parameter["length"] = this.tablePage.pageSize
        //     // }
        //     let dataList = []
        //     await request["post"](_url, params).then(res => {

        //         if ([4, 5, 6].includes(res.Data)) {
        //             res.Datas = JSON.parse(res.Datas)
        //         }
        //         if (res && res.Datas) {
        //             dataList = res.Datas
        //         }
        //         if (this.showPager) {
        //             //  是否分页
        //             this.tablePage.total = res.TotalRows
        //         }
        //     });
        //     return dataList
        // },
        // // 获取选择数据
        // getSelectedData() {
        //     //debugger
        //     let _selectData = {}
        //     let _originDataList =[]
        //     let inputPopupGridRef = this.$refs["inputPopupGridRef"]
        //     let _matchConfig = {
        //         key: this.searchOptions.TableHeader.CRETURN_VALUE,
        //         label: this.searchOptions.TableHeader.CRETURN_TEXT,
        //     }
        //     if (this.searchOptions.TableHeader.CIS_MULTI == 'Y') {
        //         let inputPopupGridRef = this.$refs["inputPopupGridRef"]
        //         if (inputPopupGridRef) {
        //             // 用于 type=checkbox，获取当前已选中的行数据（当前列表，如果 isFull=true 则获取全表已选中的数据）
        //             _originDataList = inputPopupGridRef.getCheckboxRecords()
        //             if(_originDataList.length ==0){
        //                 return _selectData
        //             }
        //             _selectData={
        //                 data:this.getSelectedFormat(_originDataList),
        //                 originData:_originDataList,
        //                 matchConfig:_matchConfig
        //             }
        //         }

        //     } else {
        //         if (inputPopupGridRef) {
        //             _originDataList = [inputPopupGridRef.getCurrentRecord()]
                    
        //             if(!!!inputPopupGridRef.getCurrentRecord()){
        //                 return _selectData
        //             }
        //             _selectData={
        //                 data:this.getSelectedFormat(_originDataList),
        //                 originData: _originDataList, // 用于 row-config.isCurrent，获取高亮的当前行数据
        //                 matchConfig:_matchConfig,
        //             }
        //         }
        //     }
            
        //     return _selectData
        // },
        // // 格式化需要返回的数据列表
        // getSelectedFormat(dataList) {
        //     let _dataList = []
        //     let _config = {
        //         key: this.searchOptions.TableHeader.CRETURN_VALUE,
        //         label: this.searchOptions.TableHeader.CRETURN_TEXT,
        //     }
        //     if (dataList && dataList.length > 0) {
        //         dataList.forEach(item => {
        //             let newItem = {
        //                 key :item[_config.key],
        //                 label:item[_config.label]
        //             }
        //             _dataList.push(newItem)
        //         })
        //     }
        //     return _dataList
        // },
        // // 查询表格数据
        // async loadPopupTableData() {
        //    // debugger
        //     let cacheKey = "standardFrame_loadPopupTableData"
        //     let dataList = this.getCtrlDataFromCacheByKey(cacheKey)
        //     if ((dataList == null || dataList.length == 0)) {
        //         this.gridOptions.loading = true
        //         dataList = await this.getDBDataByCode();
        //         // 不分页的时候，可存储数据作为缓存
        //         if (this.searchOptions.TableHeader.CIS_PAGER != 'Y') {
        //             // this.$store.commit("set_ctrlData", tempData)
        //             this.saveDataToVuex(cacheKey, dataList)
        //         }
        //     }
        //     //this.gridOptions.data = dataList
        //     let inputPopupGridRef = this.$refs["inputPopupGridRef"]
        //     if(inputPopupGridRef){
        //         inputPopupGridRef.reloadData(dataList)
        //         if(dataList && dataList.length>0){
        //            this.$nextTick(()=>{
        //             inputPopupGridRef.setCurrentRow(dataList[0])
        //            })
        //         }
        //     }
        //     this.gridOptions.loading = false
        //    // return dataList
        // },
        // // 获取表格列头信息数据
        // getColumnsData() {
        //     // debugger
        //     let cacheKey = "standardFrame_getColumnsData"
        //     this.showPager = (this.searchOptions.TableHeader.CIS_PAGER == 'Y' ? true : false)
        //     let _tempCtrlData = this.getCtrlDataFromCacheByKey(cacheKey)
        //     if (!!_tempCtrlData) {
        //         this.gridOptions.columns = _tempCtrlData
        //     } else {
        //         let _dataColumnsList = this.formatColumns(this.searchOptions.TableDetail)
        //         // 是否添加序号列
        //         if (this.searchOptions.TableHeader.CIS_MULTI != 'N') {
        //             // 检查是否存在 默认下标
        //             let checkboxColumnIndex = _dataColumnsList.findIndex(item => {
        //                 return item.type == 'checkbox'
        //             })
        //             if (checkboxColumnIndex == -1) {
        //                 // 默认下标  插入 第一个位置
        //                 _dataColumnsList.unshift(...this.checkBoxColumn)
        //             }
        //         }
        //         this.gridOptions.columns = _dataColumnsList
        //         this.saveDataToVuex(cacheKey, this.gridOptions.columns)
        //     }
        //     this.loadPopupTableData()
        // },
        // // 获取到可查询字段列表 
        // getQueryFields(dataList = []) {
        //     let _dataList = []
        //     if (dataList && dataList.length > 0) {
        //         dataList.forEach(item => {
        //             if (item["CIS_QUERY"] == 'Y') {
        //                 _dataList.push(item)
        //             }
        //         })
        //     }
        //     return _dataList
        // },
        // // 格式化表格列头信息   
        // formatColumns(dataList = []) {
        //     let _dataList = []
        //     if (dataList && dataList.length > 0) {
        //         dataList.forEach(item => {
        //             let newItem = {
        //                 field: item.CFIELD_NAME,
        //                 title: !!item.CFIELD_TEXT ? item.CFIELD_TEXT : item.CFIELD_NAME,
        //                 CSEQ: item.CSEQ, // 排序字段
        //                 width: 200,
        //                 align: "left",// 默认居左
        //                 headerAlign: "center",//默认居中
        //                 visible: item["CIS_SHOW"] == 'Y' ? true : false,// 是否列表显示,默认都显示
        //                 treeNode: false,// 默认非节点字段
        //                 params: {},// 自定义参数
        //             }
        //             _dataList.push(newItem)
        //         })
        //     }
        //     _dataList = orderBy(_dataList, ["CSEQ"], ["asc"]); // 升序排序，由小到大
        //     return _dataList
        // },
        // // 通过关键词 从缓存中获取数据
        // getCtrlDataFromCacheByKey(cacheKey) {
        //     let ctrlData = null
        //     try {
        //         ctrlData = this.$store.state.ctrlData.value[cacheKey] || null
        //     } catch (error) {
        //         ctrlData = null
        //     }
        //     return ctrlData
        // },
       
        // cancelBtn() {
        //     this.showPopupModal = false
        // },
        // 弹框选择改变 触发事件
        changeEvent(params){
           let _self = this
            if(!!params){
               let postData = JSON.parse(params) 
               this.selectOptions = postData.data
                this.$nextTick(()=>{
                    let matchField = _self.paramsItem.matchField
                    if(!!matchField){
                        if(_matchFieldList.includes(',')){
                              // 使用配置的字段绑定需要的字段，返回KEY,VALUE
                              let _matchFieldList = matchField.split(",")
                              this.formData[_matchFieldList[0]] = postData.data[0].key // key
                              this.formData[_matchFieldList[1]] = postData.data[0].label // value
                        }else{
                            // 默认 使用选中的VALUE 绑定当前字段
                             this.formData[this.field] = postData.data[0].key 
                        }
                    }else{
                        // 默认 使用选中的VALUE 绑定当前字段
                        this.formData[this.field] = postData.data[0].key
                    }
                   
                })
            }
        },
        // preSubmitEvent() {
        //     let _self = this
        //     this.loadingBtn = true
        //     let postData = this.getSelectedData()
        //    // debugger
        //     if (postData && Object.keys(postData).length == 0) {
        //         this.$message.error("请先选择数据！！")
        //         _self.loadingBtn = false
        //         return
        //     }
          
        //   this.selectOptions = postData.data
        //  this.$nextTick(()=>{
        //     this.formData[this.field] = postData.data[0].key
        //   })
        //     setTimeout(() => {
        //         //_self.$emit("changeEvent", JSON.stringify(postData))
        //         _self.loadingBtn = false
        //         _self.showPopupModal = false
        //     }, 1000)
        // }
    }
}
</script>