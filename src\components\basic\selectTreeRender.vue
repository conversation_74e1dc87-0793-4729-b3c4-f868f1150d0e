<template>
    <div class="selectTreeRender">
         <el-select
              ref="elSelectRef"
              @focus="loadData()"
              v-model="currentItem"
              style="width: 100%"
              v-bind="$attrs"
              v-on="$listeners"
              placeholder="请选择"
            >
              <el-option
                v-for="item in formatDataList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                style="display: none"
              />
              <el-tree
                class="main-select-el-tree"
                ref="selecteltree"
                :data="selectDataList"
                node-key="id"
                highlight-current
                :props="defaultProps"
                @node-click="handleNodeClick"
                :current-node-key="currentNodeKeyValue"
                :expand-on-click-node="false"
                default-expand-all
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <i class=""></i> {{ data.label }}</span
                >
              </el-tree>
            </el-select>
    </div>
</template>
<script>
 import config from '@/config'
 import request from '@/libs/request'
export default {
    name:'selectTreeRender',
    components:{},
    props:{
     // 编辑状态是否变更>>仅用于表格talbe中 
    // updateFlag:{
    //     type: [String,Boolean],
    //     default: "",
    // },    
       // 是否立即加载数据
    //  isLoadNow:{
    //      type:Boolean,
    //      default:false,
    //  },    
    //    // 每次点击都查询
    //  reSearch:{
    //      type:Boolean,
    //      default:false,
    //  },  
    //   // 表格信息>>dataFrom=talbe 才有效
    //   tableInfo:{
    //       type:Object,
    //       default(){
    //           return {}
    //       } 
    //   },
      // 数据来信:表单form,表格talbe
      dataFrom:{
          type:String,
          default:"form"
      },
      // 当前字段:其它配置信息
      paramsItem:{
          type:Object,
          default(){
              return {}
          }
      },
       // 当前字段:value
     currentValue:{
         type:[String,Number],
         default:""
     },
      // 当前字段:Text
     currentValueText:{
        type:[String,Number],
         default:""
     },
     // 是否可用
     disabled:{
         type:Boolean,
         default:false,
     }  
    }, 
    data(){
        return {
            config:config,
            currentNodeKeyValue: "",
            //expandOnClickNode: false,
            defaultProps: {
                children: "children",
                label: "label",
            },
            loading: false,
            prefixIcon:"", // 头部图标,用于是否加载中 显示
            currentItem:'',// 当前选择项
            currentItemText:'',// 当前选择项描述
            selectDataList:[],// 当前列表数据
            formatDataList:[],
            testDataTree: [
                {
                id: 1,
                label: "表单设计",
                children: [
                    {
                    id: 2,
                    label: "表单页",
                    },
                ],
                },
                {
                id: 3,
                label: "项目管理",
                children: [
                    { id: 4, label: "参数配置维护" },
                    { id: 5, label: "员工薪资维护" },
                ],
                },
                {
                id: 6,
                label: "系统管理",
                children: [
                    { id: 7, label: "系统菜单" },
                    { id: 8, label: "系统图标" },
                ],
                },
                {
                id: 9,
                label: "项目管理",
                children: [
                    {
                    id: 10,
                    label: "表格示例",
                    children: [
                        { id: 11, label: "普通表格" },
                        { id: 12, label: "表格统计" },
                    ],
                    },
                    {
                    id: 13,
                    label: "表单示例",
                    children: [
                        { id: 14, label: "表单验证" },
                        { id: 15, label: "表单字段" },
                    ],
                    },
                ],
                },
           ],
        }
    },
    watch:{
        currentValue(n,o){
            //debugger
            if(!!n){
               this.currentItem = n
            }else{
                // 清空数据
                 this.currentItem =''
                 this.currentItemText =''
            }
        },
         currentValueText(n,o){
             //debugger
            if(!!n){
               this.currentItemText = n
            }else{
                // 清空数据
                this.currentItemText =''
            }
        }
        
    },
    mounted(){
        //首次赋值
      this.$nextTick(()=>{
        // 设置默认值
          if(!!this.currentValueText){
            let newItem ={
                        value: this.currentValue,
                        label:this.currentValueText
                    }
            this.selectDataList.push(newItem)
            this.currentItem = this.currentValue
          }
      })
    },
    methods:{
         //格式化结构 （处理后全部为一级数据）
        formatData(data) {
            let options = [];
            for(let i=0;i< data.length;i++){
                let item = data[i]
                options.push({ label: item.label, value: item.id })
                if(item && item.children && item.children.length>0){
                   let dataList = this.formatData(item.children)
                   options = options.concat(dataList)
                }
                }
            return options;
        },
        handleNodeClick(node) {
           // debugger
            this.currentItem = node.label;
            this.currentItem = node.label;
            let params ={
                    value:node.id,
                    text:node.label
                } 
            this.$emit("changeEvent",params)
            this.$refs["elSelectRef"].blur();
        },
      
       // 加载数据
       loadData(){
            if(this.selectDataList.length >1){
                return
            }
           this.loading = true
            let sourceKey = this.paramsItem.sourceKey
            let searchParams = this.paramsItem.searchParams
            let _url=this.config.virModule+"Data/ComboSource"
            let params = {
                SourceType:"Tree", //Dict Table Tree
                Params:{
                    sourceKey,
                    filter:{}
                 }
                };
                if(!!searchParams){
                    // 参数分解>>"user:andy,age:18" 转换为JSON
                    if(searchParams.includes(',')){
                        //多个参数
                    let searchParamsArr = searchParams.split(',')
                    searchParamsArr.forEach(item=>{
                            let paramItem = item.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    })
                    }else{
                        //单个参数
                        let paramItem = searchParams.split(':')
                        params.Params.filter[paramItem[0]] =paramItem[1]
                    }
                }
            request['post'](_url, params).then(res=>{
                // if(res){
                //     this.loading = false
                //     this.selectDataList =  this.testDataTree 
                //     this.formatDataList = this.formatData(this.selectDataList)
                //     //debugger
                // }
            })
       },
       // 选择改变回调事件
       changeEvent(val){
       // debugger
           let _self = this
           this.$nextTick(()=>{
                let params ={
                    value:val,
                    text:_self.$refs["elSelectRef"].selectedLabel
                } 
                _self.$emit("changeEvent",params)
           })
       }
    }
}
</script>


<style lang="scss" scoped>
    .main-select-el-tree {
    .custom-tree-node {
      font-size: 14px;
      //background-color: transparent;
    }
  }
</style>