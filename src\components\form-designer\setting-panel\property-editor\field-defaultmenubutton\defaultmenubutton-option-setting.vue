<template>
  <div class="option-items-pane">
    <draggable tag="ul" :list="optionModel.menuButtonOptionItems" v-bind="{
      group: 'optionsGroup',
      ghostClass: 'ghost',
      handle: '.drag-option',
    }">
      <li v-for="(option, idx) in optionModel.menuButtonOptionItems" :key="idx">
        <el-checkbox v-model="option.check">
          <el-input @click.native="showEditMenuDialogEvent(option)" readonly v-model="option.label" size="mini"
            style="width: 160px"></el-input>
          <i class="iconfont icon-drag drag-option"></i>

          <el-button circle plain size="mini" type="info" @click="customJsEventCodeOption(option, idx)"
            icon="el-icon-edit"></el-button>
          <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
            icon="el-icon-minus" class="col-delete-button"></el-button>
        </el-checkbox>
      </li>
    </draggable>

    <div>
      <el-button type="text" @click="addOption">+添加按钮</el-button>
    </div>

    <el-dialog title="编辑操作按钮" :visible.sync="showEditMenuDialogFlag" v-if="showEditMenuDialogFlag" :show-close="true"
      class="small-padding-dialog" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      :destroy-on-close="true">
      <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px">
        <el-form-item label="标签" prop="label">
          <el-input v-model="editMenuForm.label"></el-input>
        </el-form-item>
        <menuButtonType :designer="designer" :optionModel="currentEditOption" :selectedWidget="selectedWidget">
        </menuButtonType>
      
        <eventTypeByCtrlDialog contrlType="common" :editItemForm="editMenuForm" :designer="designer"
          :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>
        <!-- 动态参数 -->
        <div v-show="!!editMenuForm.otherParams.actionType &&
          editMenuForm.otherParams.actionType != '-1'
          " style="font-weight: bold">
          参数
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType &&
          editMenuForm.otherParams.actionType != '-1'
          ">
          <div :key="index" v-for="(item, index) in actionParamsList" class="flex justify-center items-center">
            <el-form-item label="KEY" prop="key">
              <el-input placeholder="key" v-model="item.key"></el-input>
            </el-form-item>
            <el-form-item label="VALUE" prop="value">
              <el-input placeholder="value" v-model="item.value"></el-input>
            </el-form-item>

            <div style="margin-bottom: 20px; margin-left: 5px">
              <el-button style="margin-right:5px;" type="text" @click="setParams(item)">+选择</el-button>
              <i @click="deleteParam(item, index)" style="cursor: pointer;" class="el-icon-delete"></i>
            </div>
          </div>
        </div>
        <div v-show="!!editMenuForm.otherParams.actionType &&
          editMenuForm.otherParams.actionType != '-1'
          ">
          <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
        </div>
        <div style="font-weight: bold">成功或失败后事件处理
          <el-tooltip effect="light" content="一般指执行查询API后，需要执行的后续动作，如：刷新表格列表数据，或其它">
                         <i class="el-icon-info"></i></el-tooltip>
        </div>
        <el-form-item label-width="0">
          <!-- 注意：成功后或失败后事件处理-->
          <afterSuccessOrErrorSetting :optionModel="currentEditOption" :designer="designer"
            :selected-widget="selectedWidget"></afterSuccessOrErrorSetting>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="large" type="primary" @click="submitEditMenu()">{{
          i18nt("designer.hint.confirm")
        }}</el-button>
        <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{ i18nt("designer.hint.cancel")
        }}</el-button>
      </div>
    </el-dialog>
    <commonEventDialog :eventCodeConfig="currentEditSettingBtnOption" @submitEvent="submitCommonEventDialog"
      ref="commonEventDialogRef"></commonEventDialog>
    <el-dialog title="参数控件值 选择" :visible.sync="showDataSourceialogFlag" v-if="showDataSourceialogFlag" v-dialog-drag
      append-to-body :show-close="true" custom-class="drag-dialog small-padding-dialog" :close-on-click-modal="false"
      :close-on-press-escape="false" :destroy-on-close="true">
      <div>
        <!-- <el-button type="text" @click="addQueryOption">展开全部</el-button> -->
        <el-tree default-expand-all ref="nodeTree" :data="getNodeTreeData()" node-key="id" :expand-on-click-node="false"
          highlight-current class="node-tree" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick"></el-tree>
      </div>
      <div slot="footer" class="dialog-footer">

        <el-button type="primary" size="large" @click="submitNodeEvent()">
          确定</el-button><el-button size="large" @click="showDataSourceialogFlag = false">
          取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
const default_editMenuForm = {
  label: "", // 标签
  otherParams: {
    hidden: false, // 隐藏
    disabled: false, // 激活
    actionType: "-1", // 动作
    condition: "", // 条件
    debounceOrThrottleTime: 1, // 防抖、节流 时间
  },
  actionParams: {
    actionName: "",
    // query:"",
    // hash:""
  },
};
import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
import commonEventDialog from "@/components/form-designer/setting-panel/commonEventDialog.vue"
import menuButtonType from '@/extension/samples/defaultmenubutton/property-editor/menuButtonType.vue'
import cloneDeep from "clone-deep";
import Draggable from "vuedraggable";
import CodeEditor from "@/components/code-editor/index";
import i18n from "@/utils/i18n";
import { deepClone, generateId } from "@/utils/util";
import afterSuccessOrErrorSetting from "@/components/form-designer/setting-panel/afterSuccessOrErrorSetting";
export default {
  name: "menuButtonOptionItemsSetting",
  mixins: [i18n],
  components: {
    commonEventDialog,
    eventTypeByCtrlDialog,
    Draggable,
    menuButtonType,
    //CodeEditor: () => import('@/components/code-editor/index'),
    CodeEditor,
    afterSuccessOrErrorSetting,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
  },
  data() {
    return {
      curSetParamsItem: null,// 当前参数设置 编辑 项值
      nodeCtrlValue: "",
      showDataSourceialogFlag: false,//弹框树 是否打开
      showEditMenuDialogFlag: false,
      optionLines: "",
      currentEditSettingBtnOption: {},// 当前操作按钮配置
      currentEditOption: {}, // 当前编辑菜单按钮
      editMenuForm: Object.assign({}, default_editMenuForm),
      actionParamsList: [],
      editMenuFormRules: {
        label: [{ required: true, message: "请输入标签名称", trigger: "blur" }],
      },
    };
  },
  computed: {
    optionModel() {
      return this.selectedWidget.options;
    },
  },
  watch: {
    "selectedWidget.options": {
      deep: true,
      handler(val) {
        //console.log('888888', 'Options change!')
      },
    },
  },
  methods: {
    // 弹框选择 参数 控件 来源
    setParams(item) {
      this.curSetParamsItem = item
      this.showDataSourceialogFlag = true
    },
    // 提交参数设置
    submitNodeEvent() {
      this.curSetParamsItem.value = this.nodeCtrlValue
      this.showDataSourceialogFlag = false
    },
    getNodeTreeData() {
      let copyData = cloneDeep(this.designer.nodeTreeData)
      let dataList = this.setPublicAttribute(copyData)
      return dataList
    },
    // 设置控件的公共属性，对外属性
    setPublicAttribute(nodeTreeData) {
      let dataList = []
      for (let i = 0; i < nodeTreeData.length; i++) {
        let item = nodeTreeData[i]
        if (item.hasOwnProperty("children")) {
          this.setPublicAttribute(item.children)
          dataList.push(item)
        } else {
          let randomNum = Math.floor(Math.random() * 100000000 + 1)
          // 优先子集对外开放属性
          if (item.hasOwnProperty("publicSubAttribute")) {
            //debugger
            item.children = []

            if (item.publicSubAttribute && item.publicSubAttribute.length > 0) {
              item.publicSubAttribute.forEach((subItem, index) => {

                let valueItem = {
                  label: subItem.value,
                  id: subItem.value + "-" + randomNum + index
                }
                let keyItem = {
                  label: subItem.key,
                  id: subItem.key + "-" + randomNum + index,
                  children: [valueItem]
                }
                //debugger
                item.children.push(keyItem)
              })
              // debugger
              dataList.push(item)
            }

          } else {
            // 对外开放属性
            if (item.hasOwnProperty("publicAttribute")) {
              item.children = []
              if (item.publicAttribute && item.publicAttribute.length > 0) {
                item.publicAttribute.forEach((subItem, index) => {
                  let kidItem = {
                    label: subItem,
                    id: subItem + "-" + randomNum
                  }
                  item.children.push(kidItem)
                })
                dataList.push(item)
              }
            } else {
              dataList.push(item)
            }
          }
        }

      }
      return dataList

    },
    // 参数值设置 弹框 树节点 点击事件
    onNodeTreeClick(params) {
      //debugger
      this.nodeCtrlValue = ""
      let hasChildren = params.hasOwnProperty("children")
      let currentNode = this.$refs["nodeTree"].getNode(params.id) // 获取当前节点
      let parentsNode = this.$refs["nodeTree"].getNode(currentNode.parent.data.id) // 获取当前节点的父节点
      let parentNodeLabel = parentsNode.data.label
      let ctrlValue = `{{${parentNodeLabel}.${params.label}}}`
      ctrlValue = this.tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params)
      if (!hasChildren) {
        this.nodeCtrlValue = ctrlValue
      } else {
        this.$message.warning('此节点不可选！')
      }
    },
    // 获取第三层 控件名称
    tryGetTop3LayoutCtrlName(ctrlValue, parentsNode, params) {
      // debugger
      let newCtrlVal = ctrlValue
      let layoutList = ["defaultmenubutton"] //|| parentsNode.parent.data.label.includes('card'),"card"
      let level3NodeType = ""
      try {
        level3NodeType = parentsNode.parent.data.text
        if (layoutList.includes(level3NodeType)) {
          newCtrlVal = `{{${parentsNode.parent.data.label}.${parentsNode.data.label}.${params.label}}}`
        }
      } catch (error) {
        level3NodeType = ""
      }
      return newCtrlVal
    },
    // 提交
    submitCommonEventDialog(params) {
      let code = params.code
      /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
      this.$set(this.currentEditSettingBtnOption, "onSubmit", code)
    },
    // 自定义代码功能
    customJsEventCodeOption(option, idx) {
      this.currentEditSettingBtnOption = option; // 当前菜单按钮属性
      let commonEventDialogRef = this.$refs["commonEventDialogRef"]
      if (commonEventDialogRef) {
        commonEventDialogRef.showDialog = true
      }
    },
    // 切换动作时，重置动作的参数配置
    actionChangeEvent(params) {
      // 初始化 重置
      this.editMenuForm.actionParams = {
        //actionName:""
      };
    },
    // 设置组合后的动态参数
    setQueryList() {
      let queryParams = {};
      if (this.actionParamsList && this.actionParamsList.length > 0) {
        this.actionParamsList.forEach((item) => {
          if (!!item.key) {
            queryParams[item.key] = item.value;
          }
        });
      }
      //debugger
      return queryParams;
    },
    // 获取组合后的动态参数
    getQueryList() {
      let queryParamsList = [];
      //debugger
      if (
        this.editMenuForm.actionParams &&
        this.editMenuForm.actionParams.query &&
        Object.keys(this.editMenuForm.actionParams.query).length > 0
      ) {
        for (const [key, value] of Object.entries(
          this.editMenuForm.actionParams.query
        )) {
          if (!!key) {
            let newItem = { key, value };
            queryParamsList.push(newItem);
          }
        }
      }
      return queryParamsList;
    },
    // 提交修改按钮菜单属性
    submitEditMenu() {
      this.$refs["editMenuForm"].validate((valid) => {
        if (valid) {
          this.currentEditOption.label = this.editMenuForm.label;
          this.currentEditOption.otherParams = cloneDeep(
            this.editMenuForm.otherParams
          );
          this.currentEditOption.actionParams = cloneDeep(
            this.editMenuForm.actionParams
          );
          // 动态添加参数列表
          this.currentEditOption.actionParams["query"] = this.setQueryList();

          this.showEditMenuDialogFlag = false;
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 弹框编辑菜单属性--- 初始化弹框属性参数
    showEditMenuDialogEvent(option) {
      this.currentEditOption = option; // 当前菜单属性
      this.showEditMenuDialogFlag = true;
      this.editMenuForm.label = option.label;
      this.actionParamsList = []; // 默认
      this.editMenuForm.otherParams = cloneDeep(option.otherParams);
      this.editMenuForm.actionParams = cloneDeep(option.actionParams);
      //debugger
      if (this.editMenuForm.actionParams.hasOwnProperty("query")) {
        this.actionParamsList = this.getQueryList();
      }
      // debugger
      // let tt = this.editMenuForm.otherParams.actionType
    },
    deleteOption(option, index) {
      // 是否可以移除
      if (!!option.canRemove) {
        this.optionModel.menuButtonOptionItems.splice(index, 1);
      }
    },

    // 添加按钮
    addOption() {
      let newValue = this.optionModel.menuButtonOptionItems.length + 1;
      let fieldNameRandom = "btnName" + generateId();
      this.optionModel.menuButtonOptionItems.push({
        label: "新按钮",
        fieldName: fieldNameRandom,
        value: "",
        disabled: false,
        check: false,
        size: "mini",
        type: "primary",
        icon: "",
        onSubmit: '',
        canRemove: true,
        otherParams: {},
        actionParams: {},
      });
    },
    // 添加参数
    addParamsEvent() {
      let newValue = this.actionParamsList.length + 1;
      this.actionParamsList.push({ key: "", value: "" });
    },
    // 移除参数
    deleteParam(item, index) {
      this.actionParamsList.splice(index, 1);
    },
  },
};
</script>
  
<style lang="scss" scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px;
  /* 重置IE11默认样式 */
}

li.ghost {
  background: #fff;
  border: 2px dotted $--color-primary;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;
}
</style>
  