<template>
    <el-select :publicAttribute="publicAttribute" collapse-tags :multiple="true" @change="changeEvent" v-model="fieldModel_select" @focus="focusEvent" filterable clearable size="mini" v-bind="$attrs" v-on="$listeners"
        placeholder="请选择">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
    </el-select>
</template>
<script>
import baseMixin from './minxin'
import { useFormatParams } from "@/hooks/useFormatParams"
export default {
    name: "reportCustomMulSelect",
    mixins:[baseMixin],
    inject: ['getPageInstance', 'sourceVFormRenderState'],
    props: {
        refreshRandom:{
            type:[Number,String],
            default:null
        }, // 刷新标记
        defaultValue:String, // 默认值
        controlType: String, // 控件类型
        searchForm: Object,// 查询FORM 对象
        paramsItem:Object, // rowItem 配置信息对象
        fieldName: String,// 当前查询字段名称
        configOptions: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            useFormatParamsFn: useFormatParams(this),
            fieldModel_select:[],
            //value: "",// 当前选中的值的，对外暴露属性，仅供外部查询使用
            loading: false,
            configFormItem: null,
            options: [],
            selectConfig: {
                key: "",
                text: ""
            }
        }
    },
    computed: {
        // add by andy
        pageInstance() {
          // 获取列表示例
          return this.getPageInstance()
        },
        isPassiveDataLoad() {
            // debugger
            let isPassiveDataLoad =false
            try {
                isPassiveDataLoad = this.configOptions.isPassiveDataLoad
            } catch (error) {
                isPassiveDataLoad =false
            }
            return isPassiveDataLoad
        }
    },
    watch:{
        fieldModel_select(n,o){
            this.publicAttribute.value = n
        },
         // 是否刷新
         refreshRandom(n,o){
            if(n){
                if(!!this.isPassiveDataLoad){
                    this.focusEvent(true)
                }
                console.log("===reportSelect=====refreshRandom-======")
            }
        },
    },
    mounted(){
        this.init()
        //this.focusEvent()
        if(!this.isPassiveDataLoad){
            this.focusEvent()
        }
        if(this.defaultValue ){
            // 设置默认值
            if(this.defaultValue.includes(',')){
                this.fieldModel_select = this.defaultValue.split(',')
            }else{
                this.fieldModel_select = [this.defaultValue]
            } 
        }
        this.publicAttribute.controlType ="customMulSelect"
    },
    methods: {
       
        focusEvent(params) {
            // debugger
            // 初始化 下拉配置信息
            if (this.options.length == 0) {
                if (this.configOptions) {
                    this.configFormItem = this.configOptions
                    if (this.configFormItem.hasOwnProperty('CDATAS') && !!this.configFormItem.CDATAS) {
                        this.getSelectKeyText(this.configFormItem.CDATAS)
                    }
                }
                if(this.configFormItem.actionName){
                    // 数据源加载
                    this.loadSelectOptionsByAction()
                }else{
                    // 自定义数据源
                    this.loadSelectOptions()
                }
               
            }
        },
        // 获取配置的KEY & TEXT
        getSelectKeyText(dataList) {
            if (dataList && dataList.length > 0) {
                dataList.forEach(item => {
                    if (!!item.isSelectTextField) {
                        this.selectConfig.text = item.field
                    }
                    if (!!item.isSelectKeyField) {
                        this.selectConfig.key = item.field
                    }
                })
            } else {
                this.selectConfig = {
                    key: "",
                    text: ""
                }
            }
            // debugger
            // let tt = this.selectConfig
            // debugger 
        },
        // 设置下拉初始化功能
        loadSelectOptions() {
            let optionList = [] //{ value: null, label: "全部" }
            if (this.configFormItem.CDATAS && this.configFormItem.CDATAS.length > 0) {
                this.configFormItem.CDATAS.forEach(oldItem => {
                    let newItem = {
                        value: oldItem.field,
                        label: !!oldItem.title ? oldItem.title : oldItem.field
                    }
                   // debugger
                    optionList.push(newItem)
                })
            }
            this.options = optionList
        },
         // 设置下拉初始化功能
        async loadSelectOptionsByAction() {
            this.loading = true
             let  _dataList = await this.useFormatParamsFn.getDBDataByActionName(this.configFormItem.actionName);
             if(_dataList && _dataList.length>0){
                 this.options = this.formatDatas(_dataList)
             }else{
                 this.options = []//{ value: null, label: "全部" }
             }
             console.log("reportCustomMulSelect this.options:", this.options)
            this.loading = false
            setTimeout(() => {
                this.loading = false
            }, 10000)
        },
        // 格式化返回数据
        formatDatas(dataList) {
            let options = [] //{ value: null, label: "全部" }
            if (dataList && dataList.length > 0) {
                dataList.forEach(oldItem => {
                    if (!!oldItem[this.selectConfig.text]) {
                        let newItem = {
                            value: oldItem[this.selectConfig.key],
                            label: oldItem[this.selectConfig.text]
                        }
                        options.push(newItem)
                    }

                })
            }
            return options
        }
    }
}

</script>