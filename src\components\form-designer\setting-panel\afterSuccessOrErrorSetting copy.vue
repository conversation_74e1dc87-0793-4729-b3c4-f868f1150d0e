<template>
    <div class="option-items-pane">
     
    
        <draggable tag="ul" :list="optionModel.afterSuccessOrErrorEvents"
                   v-bind="{group:'optionsGroup', ghostClass: 'ghost', handle: '.drag-option'}">
          <li v-for="(option, idx) in optionModel.afterSuccessOrErrorEvents" :key="idx">
            <!-- <el-checkbox v-model="option.check" > -->
              <el-input @click.native="showEditMenuDialogEvent(option)" readonly v-model="option.label" size="mini" style="width: 200px"></el-input>
              <i class="iconfont icon-drag drag-option"></i>
              <el-button v-show="!!option.canRemove" circle plain size="mini" type="danger" @click="deleteOption(option, idx)"
                         icon="el-icon-minus" class="col-delete-button"></el-button>
            <!-- </el-checkbox> -->
          </li>
        </draggable>
  
   
  
      <div>
        <el-button type="text" @click="addOption">+添加事件</el-button>
      </div>
  
      <el-dialog title="编辑成功、失败后事件" :visible.sync="showEditMenuDialogFlag"
                 v-if="showEditMenuDialogFlag" :show-close="true" class="small-padding-dialog" append-to-body
                 :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
        <el-form :model="editMenuForm" :rules="editMenuFormRules" ref="editMenuForm" label-width="100px" >
          <el-form-item label="标签" prop="label">
            <el-input v-model="editMenuForm.label"></el-input>
          </el-form-item>
        
          <el-form-item label="事件">
            <el-select @change="eventTypeChangeEvent" v-model="editMenuForm.otherParams.eventType" placeholder="请选择事件">
                <el-option label="未选择" value="-1"></el-option>
                <el-option label="成功后" value="success"></el-option>
                <el-option label="失败后" value="error"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="动作" prop="actionType">
            <el-select @change="actionChangeEvent" v-model="editMenuForm.otherParams.actionType" placeholder="请选择动作">
                <el-option label="未选择" value="-1"></el-option>
                <el-option label="执行查询" value="api"></el-option>
                <el-option label="弹框应用" value="popup"></el-option>
                <el-option label="控制组件" value="ctrlComp"></el-option>
            </el-select>
          </el-form-item>
          <!-- 请选择查询 -->
          <div v-if="editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType=='api'">
            <el-form-item v-show="editMenuForm.otherParams.actionType=='api'" label="--选择查询" prop="actionParams">
                <el-select v-model="editMenuForm.actionParams.actionName" placeholder="请选择查询">
                    <!-- <el-option label="api1" value="1"></el-option>
                    <el-option label="api2" value="2"></el-option> -->
                    <el-option label="请选择" value=""></el-option>
                    <el-option :key="queryIndex+queryItem.value" v-for="(queryItem,queryIndex) in designer.formConfig.queryList" :label="queryItem.label" :value="queryItem.value"></el-option>
                </el-select>
            </el-form-item>
          </div>
         <!-- 请选择弹框sss -->
         <div v-if="editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType=='popup'">
            <el-form-item v-show="editMenuForm.otherParams.actionType=='popup'" label="--选择弹框" prop="actionParams">
                <el-select v-model="editMenuForm.actionParams.actionName" placeholder="请选择弹框">
                    <!-- <el-option label="弹框1" value="1"></el-option>
                    <el-option label="弹框2" value="2"></el-option> -->
                    <el-option label="请选择" value=""></el-option>
                    <el-option :key="popupIndex+popupItem.value" v-for="(popupItem,popupIndex) in designer.formConfig.popupList" :label="popupItem.label" :value="popupItem.value"></el-option>
                </el-select>
            </el-form-item>
          </div>
          <eventTypeByCtrlDialog contrlType="afterSuccessOrError" :editItemForm="editMenuForm" :designer="designer"
          :selectedWidget="selectedWidget"></eventTypeByCtrlDialog>
          <!-- 动态参数 -->
          <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType!='-1'" style="font-weight: bold;">参数</div>
          <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType!='-1'">
              <div :key="index" v-for="(item,index) in actionParamsList"  class="flex justify-center items-center">
                <el-form-item label="KEY" prop="key">
                  <el-input placeholder="key" v-model="item.key"></el-input>
                </el-form-item>
                <el-form-item label="VALUE" prop="value">
                  
                    <el-input placeholder="value" v-model="item.value"></el-input>
                </el-form-item>
                <!-- v-show="actionParamsList.length>1"  -->
                <div  style="margin-bottom:20px;margin-left:5px"> <i @click="deleteParam(item,index)" class="el-icon-delete"></i></div>
              </div>
          </div>
           <div v-show="!!editMenuForm.otherParams.actionType && editMenuForm.otherParams.actionType!='-1'">
             <el-button type="text" @click="addParamsEvent">+添加参数</el-button>
          </div>
          <!-- <el-form-item label="执行条件" prop="condition">
            <el-input v-model="editMenuForm.otherParams.condition"></el-input>
          </el-form-item>
          <el-form-item label="防抖/秒" prop="debounceOrThrottleTime">
            <el-input-number v-model="editMenuForm.otherParams.debounceOrThrottleTime" controls-position="right" :min="1" :max="10"></el-input-number>
          </el-form-item> -->
        
        
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="large" type="primary" @click="submitEditMenu()">{{i18nt('designer.hint.confirm')}}</el-button>
          <el-button size="large" type="" @click="showEditMenuDialogFlag = false">{{i18nt('designer.hint.cancel')}}</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  const default_editMenuForm={
            label:"",// 标签
            otherParams:{
              hidden:false, // 隐藏
              disabled:false,// 激活
              actionType:"-1",// 动作
              condition:"", // 条件
              debounceOrThrottleTime:1, // 防抖、节流 时间
            },
            actionParams:{
                actionName:"",
                // query:"",
                // hash:""
            },
          }
  import eventTypeByCtrlDialog from '@/components/form-designer/setting-panel/eventTypeByCtrlDialog.vue'
  import cloneDeep from "clone-deep"
    import Draggable from 'vuedraggable'
    import CodeEditor from '@/components/code-editor/index'
    import i18n from "@/utils/i18n";
  
    export default {
      name: "operationButtonsSetting",
      mixins: [i18n],
      components: {
        Draggable,
        //CodeEditor: () => import('@/components/code-editor/index'),
        CodeEditor,
        eventTypeByCtrlDialog,
      },
      props: {
        designer: Object,
        optionModel:Object,
        selectedWidget: Object,
      },
      data() {
        return {
          showEditMenuDialogFlag: false,
          optionLines: '',
          currentMenuOption:{},// 当前编辑菜单按钮
          editMenuForm:Object.assign({},default_editMenuForm),
          actionParamsList:[],
          editMenuFormRules:{
            label: [
                    { required: true, message: '请输入标签名称', trigger: 'blur' },
                ],
          }
        }
      },
      // computed: {
      //   optionModel() {
      //     return this.selectedWidget.options
      //   },
  
      // },
      watch: {
        optionModel: {
          deep: true,
          handler(val) {
            //console.log('888888', 'Options change!')
          }
        },
        // 'selectedWidget.options': {
        //   deep: true,
        //   handler(val) {
        //     //console.log('888888', 'Options change!')
        //   }
        // },
      },
      methods: {
        // 事件类型改变
        eventTypeChangeEvent(params){

        },
        // 切换动作时，重置动作的参数配置
        actionChangeEvent(params){
          // 初始化 重置
            this.editMenuForm.actionParams ={
              //actionName:""
            }
        },
        // 设置组合后的动态参数
        setQueryList(){
          let queryParams ={}
          if(this.actionParamsList && this.actionParamsList.length>0){
            this.actionParamsList.forEach(item=>{
              if(!!item.key){
                queryParams[item.key] = item.value
              }
            })
          }
          //debugger
          return queryParams
        },
        // 获取组合后的动态参数
        getQueryList(){
          let queryParamsList =[]
         //debugger
          if(this.editMenuForm.actionParams && this.editMenuForm.actionParams.query && Object.keys(this.editMenuForm.actionParams.query).length>0){
           
            for (const [key,value] of Object.entries(this.editMenuForm.actionParams.query)) {
                  if(!!key){
                    let newItem ={key,value}
                    queryParamsList.push(newItem)
                  }
            }
          }
          return queryParamsList
        },
        // 提交修改按钮菜单属性
        submitEditMenu(){
          this.$refs["editMenuForm"].validate((valid) => {
            if (valid) {
                this.currentMenuOption.label = this.editMenuForm.label
                this.currentMenuOption.otherParams = cloneDeep(this.editMenuForm.otherParams) 
                this.currentMenuOption.actionParams = cloneDeep(this.editMenuForm.actionParams) 
                // 动态添加参数列表
                this.currentMenuOption.actionParams["query"] = this.setQueryList()
                
                this.showEditMenuDialogFlag = false
            } else {
                console.log('error submit!!');
                return false;
            }
          });
        
        },
       // 弹框编辑菜单属性--- 初始化弹框属性参数
        showEditMenuDialogEvent(option){
          this.currentMenuOption = option // 当前菜单属性
          this.showEditMenuDialogFlag = true
          this.editMenuForm.label = option.label
          this.actionParamsList=[] // 默认
          this.editMenuForm.otherParams = cloneDeep(option.otherParams) 
          this.editMenuForm.actionParams = cloneDeep(option.actionParams) 
          //debugger
          if(this.editMenuForm.actionParams.hasOwnProperty("query")){
            this.actionParamsList= this.getQueryList()
          }
          //debugger
         // let tt = this.editMenuForm.otherParams.actionType
        
        },
        deleteOption(option, index) {
          // 是否可以移除
          if(!!option.canRemove){
             this.optionModel.afterSuccessOrErrorEvents.splice(index, 1)
          }
          
        },
        // 添加按钮
        addOption() {
             /* 注意：如果是options新增的事件属性，保存事件代码必须使用$set方法，！！ */
            // this.$set(this.formConfig, this.curEventName, this.commonEventHandlerCode)
          if(!this.optionModel.hasOwnProperty("afterSuccessOrErrorEvents")){
            this.$set(this.optionModel, "afterSuccessOrErrorEvents", [])
          }
          let newValue = this.optionModel.afterSuccessOrErrorEvents.length + 1
          this.optionModel.afterSuccessOrErrorEvents.push(
            {label: '新事件'+newValue, value: "",check:false,canRemove:true,otherParams:{},actionParams:{}}
            )
        },
        // 添加参数
        addParamsEvent(){
          let newValue = this.actionParamsList.length + 1
          this.actionParamsList.push(
            {key: '', value: ""}
            )
        },
        // 移除参数
        deleteParam(item, index){
          this.actionParamsList.splice(index, 1)
        },
     
  
      }
    }
  </script>
  
  <style lang="scss" scoped>
    .option-items-pane ul {
      padding-inline-start: 6px;
      padding-left: 6px;  /* 重置IE11默认样式 */
      list-style: none;
    }
  
    li.ghost{
      background: #fff;
      border: 2px dotted $--color-primary;
    }
  
    .drag-option {
      cursor: move;
    }
  
    .small-padding-dialog ::v-deep .el-dialog__body {
      padding: 10px 15px;
    }
  
    .dialog-footer .el-button {
      width: 100px;
      
    }
  </style>
  