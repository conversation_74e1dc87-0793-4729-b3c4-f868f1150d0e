<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.colOffsetTitle')">
    <!-- <el-input-number v-model.number="optionModel.offset" :min="0" :max="24"
                     style="width: 100%"></el-input-number> -->
                     {{ optionModel.offset }}
  </el-form-item>
  <el-form-item label-width="22px">
                <el-slider  :min="0" :max="24" show-stops :step="2" v-model="optionModel.offset"></el-slider>
  </el-form-item>
  </div>
 
</template>

<script>
  import i18n from "@/utils/i18n";

  export default {
    name: "grid-col-offset-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
